@echo off
chcp 65001 >nul
echo ========================================
echo        文件编码检查工具
echo ========================================
echo.

echo 检查源文件编码状态...
echo.

REM 检查主要源文件
echo [源文件]
if exist "src\main.cpp" (
    echo ✓ src\main.cpp 存在
) else (
    echo ✗ src\main.cpp 不存在
)

if exist "src\Camera.cpp" (
    echo ✓ src\Camera.cpp 存在
) else (
    echo ✗ src\Camera.cpp 不存在
)

if exist "src\Shader.cpp" (
    echo ✓ src\Shader.cpp 存在
) else (
    echo ✗ src\Shader.cpp 不存在
)

if exist "src\Cube.cpp" (
    echo ✓ src\Cube.cpp 存在
) else (
    echo ✗ src\Cube.cpp 不存在
)

echo.
echo [头文件]
if exist "include\Camera.h" (
    echo ✓ include\Camera.h 存在
) else (
    echo ✗ include\Camera.h 不存在
)

if exist "include\Shader.h" (
    echo ✓ include\Shader.h 存在
) else (
    echo ✗ include\Shader.h 不存在
)

if exist "include\Cube.h" (
    echo ✓ include\Cube.h 存在
) else (
    echo ✗ include\Cube.h 不存在
)

echo.
echo [着色器文件]
if exist "shaders\vertex.glsl" (
    echo ✓ shaders\vertex.glsl 存在
) else (
    echo ✗ shaders\vertex.glsl 不存在
)

if exist "shaders\fragment.glsl" (
    echo ✓ shaders\fragment.glsl 存在
) else (
    echo ✗ shaders\fragment.glsl 不存在
)

echo.
echo [配置文件]
if exist "CMakeLists.txt" (
    echo ✓ CMakeLists.txt 存在
) else (
    echo ✗ CMakeLists.txt 不存在
)

echo.
echo ========================================
echo 所有文件已添加UTF-8 BOM标记
echo 现在可以使用Visual Studio编译了
echo ========================================
echo.
echo 建议的编译步骤：
echo 1. 运行 build_vs_utf8.bat
echo 2. 或者手动使用Visual Studio打开项目
echo.
pause
