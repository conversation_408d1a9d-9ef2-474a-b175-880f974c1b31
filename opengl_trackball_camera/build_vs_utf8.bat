@echo off
chcp 65001 >nul
echo ========================================
echo   OpenGL 轨迹球相机 - Visual Studio 构建
echo   (UTF-8编码支持版本)
echo ========================================
echo.

REM 检查SDK目录是否存在
if not exist "sdk" (
    echo 错误：找不到sdk目录！
    echo 请确保sdk目录包含所需的库文件。
    pause
    exit /b 1
)

REM 检查是否存在build目录，如果存在则删除
if exist "build" (
    echo 删除现有的build目录...
    rmdir /s /q build
)

REM 创建build目录
echo 创建build目录...
mkdir build
cd build

REM 检测并使用合适的Visual Studio版本
echo 检测Visual Studio版本...

REM 尝试Visual Studio 2022
echo 尝试Visual Studio 2022...
cmake .. -G "Visual Studio 17 2022" -A x64 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 使用 Visual Studio 2022
    goto :build
)

REM 尝试Visual Studio 2019
echo 尝试Visual Studio 2019...
cmake .. -G "Visual Studio 16 2019" -A x64 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 使用 Visual Studio 2019
    goto :build
)

REM 尝试Visual Studio 2017
echo 尝试Visual Studio 2017...
cmake .. -G "Visual Studio 15 2017" -A x64 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 使用 Visual Studio 2017
    goto :build
)

REM 如果都失败了
echo ✗ 未找到合适的Visual Studio版本！
echo.
echo 请安装以下之一：
echo - Visual Studio 2017 (15.0+) with C++ tools
echo - Visual Studio 2019 (16.0+) with C++ tools  
echo - Visual Studio 2022 (17.0+) with C++ tools
echo - Build Tools for Visual Studio
echo.
echo 下载地址：https://visualstudio.microsoft.com/downloads/
pause
exit /b 1

:build
echo.
echo 开始编译项目（Release模式）...
cmake --build . --config Release --parallel

REM 检查编译是否成功
if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo           编译成功！
    echo ========================================
    echo.
    
    REM 查找可执行文件
    if exist "bin\Release\TrackballCamera.exe" (
        echo 可执行文件位置: build\bin\Release\TrackballCamera.exe
        set "EXE_PATH=bin\Release\TrackballCamera.exe"
    ) else if exist "Release\TrackballCamera.exe" (
        echo 可执行文件位置: build\Release\TrackballCamera.exe
        set "EXE_PATH=Release\TrackballCamera.exe"
    ) else if exist "bin\TrackballCamera.exe" (
        echo 可执行文件位置: build\bin\TrackballCamera.exe
        set "EXE_PATH=bin\TrackballCamera.exe"
    ) else (
        echo 警告：找不到可执行文件！
        set "EXE_PATH="
    )
    
    echo.
    echo 操作说明：
    echo - 左键拖拽: 旋转视角
    echo - 滚轮: 缩放
    echo - R键: 重置相机
    echo - ESC键: 退出程序
    echo.
    
    if defined EXE_PATH (
        echo 按任意键运行程序...
        pause >nul
        echo 启动程序...
        "%EXE_PATH%"
    ) else (
        echo 请手动查找并运行可执行文件。
        pause
    )
    
) else (
    echo.
    echo ========================================
    echo           编译失败！
    echo ========================================
    echo.
    echo 请检查上面的错误信息。
    echo 常见问题：
    echo 1. 确保安装了Visual Studio的C++工具
    echo 2. 确保sdk目录包含正确的库文件
    echo 3. 检查文件编码是否为UTF-8 with BOM
    pause
    exit /b 1
)

echo.
pause
