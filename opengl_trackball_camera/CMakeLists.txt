cmake_minimum_required(VERSION 3.10)
project(TrackballCamera)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# 设置SDK路径
set(SDK_DIR "${CMAKE_SOURCE_DIR}/sdk")

# 查找OpenGL
find_package(OpenGL REQUIRED)

# 设置包含目录
include_directories(${CMAKE_SOURCE_DIR}/include)
include_directories(${SDK_DIR}/include)

# 设置库目录
link_directories(${SDK_DIR}/lib)

# 添加GLAD源文件
set(GLAD_SOURCES ${SDK_DIR}/glad.c)

# 添加可执行文件
add_executable(TrackballCamera
    src/main.cpp
    src/Camera.cpp
    src/Shader.cpp
    src/Cube.cpp
    ${GLAD_SOURCES}
)

# Windows特定配置 - 专门针对Visual Studio
if(WIN32)
    # 链接库
    target_link_libraries(TrackballCamera
        OpenGL::GL
        glfw3
        glew32
        opengl32
        gdi32
        user32
        kernel32
    )

    # 复制DLL到输出目录
    add_custom_command(TARGET TrackballCamera POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${SDK_DIR}/glew32.dll"
        $<TARGET_FILE_DIR:TrackballCamera>)

    # MSVC特定设置
    if(MSVC)
        # 设置运行时库
        set_property(TARGET TrackballCamera PROPERTY
            MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")

        # 禁用一些警告
        target_compile_options(TrackballCamera PRIVATE
            /wd4996  # 禁用不安全函数警告
            /wd4244  # 禁用类型转换警告
        )
    endif()

else()
    # Linux/macOS配置
    target_link_libraries(TrackballCamera
        OpenGL::GL
        glfw3
        GLEW
        pthread
        dl
    )
endif()

# 设置调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(TrackballCamera PRIVATE DEBUG)
endif()

# 打印配置信息
message(STATUS "SDK directory: ${SDK_DIR}")
message(STATUS "OpenGL found: ${OPENGL_FOUND}")
message(STATUS "Include directories: ${CMAKE_SOURCE_DIR}/include, ${SDK_DIR}/include")
message(STATUS "Library directory: ${SDK_DIR}/lib")
