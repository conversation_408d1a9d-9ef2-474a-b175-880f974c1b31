cmake_minimum_required(VERSION 3.10)
project(TrackballCamera)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# 设置SDK路径
set(SDK_DIR "${CMAKE_SOURCE_DIR}/sdk")

# 查找OpenGL
find_package(OpenGL REQUIRED)

# 设置包含目录
include_directories(${CMAKE_SOURCE_DIR}/include)
include_directories(${SDK_DIR}/include)

# 设置库目录
link_directories(${SDK_DIR}/lib)

# 添加GLAD源文件
set(GLAD_SOURCES ${SDK_DIR}/glad.c)

# 添加可执行文件
add_executable(TrackballCamera
    src/main.cpp
    src/Camera.cpp
    src/Shader.cpp
    src/Cube.cpp
    ${GLAD_SOURCES}
)

# Windows特定配置
if(WIN32)
    # 检测编译器类型
    if(MINGW)
        message(STATUS "使用MinGW编译器，配置MinGW兼容的库")

        # 对于MinGW，我们使用静态链接和系统库
        target_link_libraries(TrackballCamera
            OpenGL::GL
            opengl32
            gdi32
            user32
            kernel32
            winmm
            shell32
            ws2_32
        )

        # 添加MinGW特定的链接选项
        target_link_options(TrackballCamera PRIVATE
            -static-libgcc
            -static-libstdc++
        )

        # 定义预处理器宏
        target_compile_definitions(TrackballCamera PRIVATE
            GLFW_INCLUDE_NONE
        )

    else()
        # MSVC编译器配置
        message(STATUS "使用MSVC编译器")
        target_link_libraries(TrackballCamera
            OpenGL::GL
            glfw3
            glew32
            opengl32
            gdi32
            user32
            kernel32
        )

        # 复制DLL到输出目录
        add_custom_command(TARGET TrackballCamera POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "${SDK_DIR}/glew32.dll"
            $<TARGET_FILE_DIR:TrackballCamera>)
    endif()

else()
    # Linux/macOS配置
    target_link_libraries(TrackballCamera
        OpenGL::GL
        glfw3
        GLEW
        pthread
        dl
    )
endif()

# 设置调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(TrackballCamera PRIVATE DEBUG)
endif()

# 打印配置信息
message(STATUS "SDK directory: ${SDK_DIR}")
message(STATUS "OpenGL found: ${OPENGL_FOUND}")
message(STATUS "Include directories: ${CMAKE_SOURCE_DIR}/include, ${SDK_DIR}/include")
message(STATUS "Library directory: ${SDK_DIR}/lib")
