# OpenGL 轨迹球相机交互系统

这是一个基于现代OpenGL实现的轨迹球相机交互系统，模拟VTK的vtkInteractorStyleTrackballCamera交互行为。

## 功能特点

- **轨迹球相机交互**：实现与VTK相似的相机控制方式
- **现代OpenGL**：使用OpenGL 3.3+和着色器管道
- **彩色立方体**：作为3D场景的演示对象
- **流畅交互**：支持鼠标拖拽旋转和滚轮缩放

## 操作说明

- **左键拖拽**：旋转视角
  - 水平拖拽：方位角(Azimuth)变化
  - 垂直拖拽：仰角(Elevation)变化
- **滚轮**：缩放视图
- **R键**：重置相机到默认位置
- **ESC键**：退出程序

## 依赖库

- **OpenGL 3.3+**：图形渲染
- **GLFW**：窗口管理和输入处理
- **GLEW**：OpenGL扩展加载
- **GLM**：数学库（矩阵和向量运算）

## 编译说明

### Windows (使用包含的SDK)
本项目已包含所有必要的依赖库在 `sdk` 目录中，无需额外安装。

**推荐方式（Visual Studio）：**
```bash
# 检查文件编码
check_encoding.bat

# 使用Visual Studio编译（推荐）
build_vs_utf8.bat
```

**通用方式：**
```bash
# 检查环境（可选）
test_environment.bat

# 编译项目
build.bat
```

**要求：**
- CMake 3.10+
- 编译器：Visual Studio 2017/2019/2022（推荐）或 MinGW-w64
- 所有源文件已设置为UTF-8 with BOM编码

**手动编译：**
```bash
mkdir build
cd build
# 推荐使用 Visual Studio
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

**编码说明：**
- 所有源文件已添加UTF-8 BOM标记
- CMakeLists.txt已配置`/utf-8`编译选项
- 解决了中文注释的编码问题

### Linux (需要安装依赖)
```bash
# 安装依赖
sudo apt-get update
sudo apt-get install build-essential cmake
sudo apt-get install libglfw3-dev libglew-dev libglm-dev

# 编译项目
mkdir build
cd build
cmake ..
make

# 运行
./bin/TrackballCamera
```

### macOS (需要安装依赖)
```bash
# 使用Homebrew安装依赖
brew install cmake glfw glew glm

# 编译项目
mkdir build
cd build
cmake ..
make

# 运行
./bin/TrackballCamera
```

## 项目结构

```
opengl_trackball_camera/
├── CMakeLists.txt          # CMake构建配置
├── README.md               # 项目说明文档
├── include/                # 头文件目录
│   ├── Camera.h           # 相机类声明
│   ├── Shader.h           # 着色器类声明
│   └── Cube.h             # 立方体类声明
├── src/                   # 源文件目录
│   ├── main.cpp           # 主程序
│   ├── Camera.cpp         # 相机类实现
│   ├── Shader.cpp         # 着色器类实现
│   └── Cube.cpp           # 立方体类实现
└── shaders/               # 着色器文件目录
    ├── vertex.glsl        # 顶点着色器
    └── fragment.glsl      # 片段着色器
```

## 技术实现

### 相机系统
- 使用球面坐标系统管理相机位置
- 支持方位角和仰角的独立控制
- 自动计算视图矩阵和投影矩阵

### 交互机制
- 鼠标事件处理和状态管理
- 坐标转换和灵敏度控制
- 与VTK轨迹球行为保持一致

### 渲染管线
- 现代OpenGL着色器管道
- VAO/VBO几何数据管理
- 深度测试和颜色混合

## 与VTK的对比

| 功能 | VTK | 本实现 |
|------|-----|--------|
| 轨迹球旋转 | ✓ | ✓ |
| 方位角控制 | ✓ | ✓ |
| 仰角控制 | ✓ | ✓ |
| 缩放功能 | ✓ | ✓ |
| 多视口支持 | ✓ | ✗ |
| 复杂场景 | ✓ | 基础实现 |

## 扩展建议

1. **添加平移功能**：实现鼠标中键平移
2. **多视口支持**：支持多个渲染区域
3. **更复杂场景**：加载3D模型文件
4. **光照系统**：添加光照和材质
5. **性能优化**：实现视锥体裁剪

## 许可证

本项目仅供学习和研究使用。
