@echo off
chcp 65001 >nul
echo ========================================
echo     相机旋转修复测试
echo ========================================
echo.

echo 修复内容：
echo ✓ 修正了上下旋转方向（现在与VTK一致）
echo ✓ 放宽了仰角限制（从±89°改为±80°）
echo ✓ 添加了可配置的角度限制
echo ✓ 添加了调试信息功能
echo.

echo 测试说明：
echo 1. 左键拖拽测试旋转方向
echo 2. 向上拖拽鼠标 → 相机向上看
echo 3. 向下拖拽鼠标 → 相机向下看
echo 4. 左右拖拽测试方位角旋转
echo 5. 按D键查看当前相机角度
echo 6. 按R键重置相机位置
echo.

echo 开始编译和测试...
call build_vs_utf8.bat

echo.
echo ========================================
echo 测试完成！
echo.
echo 如果旋转方向仍然不正确，请报告具体问题：
echo - 上下旋转方向是否正确？
echo - 是否还有角度限制问题？
echo - 在哪个角度范围内出现问题？
echo ========================================
pause
