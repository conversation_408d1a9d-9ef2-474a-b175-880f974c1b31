@echo off
chcp 65001 >nul
echo === OpenGL 轨迹球相机项目构建脚本 ===
echo 使用SDK目录中的依赖库...

REM 检查SDK目录是否存在
if not exist "sdk" (
    echo 错误：找不到sdk目录！
    echo 请确保sdk目录包含所需的库文件。
    pause
    exit /b 1
)

REM 检查是否存在build目录，如果存在则删除
if exist "build" (
    echo 删除现有的build目录...
    rmdir /s /q build
)

REM 创建build目录
echo 创建build目录...
mkdir build
cd build

REM 运行CMake配置
echo 运行CMake配置...
cmake .. -G "MinGW Makefiles"

REM 检查CMake是否成功
if %errorlevel% neq 0 (
    echo CMake配置失败！
    echo 尝试使用Visual Studio生成器...
    cmake .. -G "Visual Studio 16 2019" -A x64
    if %errorlevel% neq 0 (
        echo CMake配置仍然失败！
        echo 请检查：
        echo 1. 是否安装了CMake
        echo 2. 是否安装了编译器 (MinGW 或 Visual Studio)
        echo 3. sdk目录是否包含所需文件
        pause
        exit /b 1
    )
)

REM 编译项目
echo 编译项目...
cmake --build . --config Release

REM 检查编译是否成功
if %errorlevel% equ 0 (
    echo.
    echo === 编译成功！ ===
    echo.
    echo 查找可执行文件...
    if exist "bin\TrackballCamera.exe" (
        echo 可执行文件位置: ./bin/TrackballCamera.exe
        echo 运行程序: cd build ^&^& bin\TrackballCamera.exe
    ) else if exist "Release\TrackballCamera.exe" (
        echo 可执行文件位置: ./Release/TrackballCamera.exe
        echo 运行程序: cd build ^&^& Release\TrackballCamera.exe
    ) else if exist "TrackballCamera.exe" (
        echo 可执行文件位置: ./TrackballCamera.exe
        echo 运行程序: cd build ^&^& TrackballCamera.exe
    ) else (
        echo 找不到可执行文件，请检查编译输出。
    )
    echo.
    echo 操作说明：
    echo - 左键拖拽: 旋转视角
    echo - 滚轮: 缩放
    echo - R键: 重置相机
    echo - ESC键: 退出程序
    echo.
    echo 按任意键尝试运行程序...
    pause >nul

    REM 尝试运行程序
    if exist "bin\TrackballCamera.exe" (
        bin\TrackballCamera.exe
    ) else if exist "Release\TrackballCamera.exe" (
        Release\TrackballCamera.exe
    ) else if exist "TrackballCamera.exe" (
        TrackballCamera.exe
    )
) else (
    echo 编译失败！请检查错误信息。
    pause
    exit /b 1
)
