@echo off
chcp 65001 >nul
echo === OpenGL 轨迹球相机项目构建脚本 ===
echo 使用SDK目录中的依赖库...

REM 检查SDK目录是否存在
if not exist "sdk" (
    echo 错误：找不到sdk目录！
    echo 请确保sdk目录包含所需的库文件。
    pause
    exit /b 1
)

REM 检查是否存在build目录，如果存在则删除
if exist "build" (
    echo 删除现有的build目录...
    rmdir /s /q build
)

REM 创建build目录
echo 创建build目录...
mkdir build
cd build

REM 运行CMake配置 - 优先使用Visual Studio
echo 运行CMake配置...
echo 尝试使用Visual Studio 2022...
cmake .. -G "Visual Studio 17 2022" -A x64

REM 检查CMake是否成功
if %errorlevel% neq 0 (
    echo Visual Studio 2022 未找到，尝试Visual Studio 2019...
    cmake .. -G "Visual Studio 16 2019" -A x64
    if %errorlevel% neq 0 (
        echo Visual Studio 2019 未找到，尝试Visual Studio 2017...
        cmake .. -G "Visual Studio 15 2017" -A x64
        if %errorlevel% neq 0 (
            echo 所有Visual Studio版本都未找到！
            echo 请确保安装了以下之一：
            echo - Visual Studio 2017 (15.0+)
            echo - Visual Studio 2019 (16.0+)
            echo - Visual Studio 2022 (17.0+)
            echo 或者安装 "Build Tools for Visual Studio"
            pause
            exit /b 1
        )
    )
)

REM 编译项目
echo 编译项目...
cmake --build . --config Release

REM 检查编译是否成功
if %errorlevel% equ 0 (
    echo.
    echo === 编译成功！ ===
    echo.
    echo 查找可执行文件...
    if exist "bin\TrackballCamera.exe" (
        echo 可执行文件位置: ./bin/TrackballCamera.exe
        echo 运行程序: cd build ^&^& bin\TrackballCamera.exe
    ) else if exist "Release\TrackballCamera.exe" (
        echo 可执行文件位置: ./Release/TrackballCamera.exe
        echo 运行程序: cd build ^&^& Release\TrackballCamera.exe
    ) else if exist "TrackballCamera.exe" (
        echo 可执行文件位置: ./TrackballCamera.exe
        echo 运行程序: cd build ^&^& TrackballCamera.exe
    ) else (
        echo 找不到可执行文件，请检查编译输出。
    )
    echo.
    echo 操作说明：
    echo - 左键拖拽: 旋转视角
    echo - 滚轮: 缩放
    echo - R键: 重置相机
    echo - ESC键: 退出程序
    echo.
    echo 按任意键尝试运行程序...
    pause >nul

    REM 尝试运行程序
    if exist "bin\TrackballCamera.exe" (
        bin\TrackballCamera.exe
    ) else if exist "Release\TrackballCamera.exe" (
        Release\TrackballCamera.exe
    ) else if exist "TrackballCamera.exe" (
        TrackballCamera.exe
    )
) else (
    echo 编译失败！请检查错误信息。
    pause
    exit /b 1
)
