@echo off
chcp 65001 >nul
echo === 环境检测脚本 ===

echo 检查CMake...
cmake --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ CMake 已安装
    cmake --version
) else (
    echo ✗ CMake 未安装或不在PATH中
)

echo.
echo 检查编译器...
gcc --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ GCC 已安装
    gcc --version | findstr gcc
) else (
    echo ✗ GCC 未安装
)

cl >nul 2>&1
if %errorlevel% neq 9009 (
    echo ✓ MSVC 已安装
) else (
    echo ✗ MSVC 未安装
)

echo.
echo 检查SDK目录结构...
if exist "sdk" (
    echo ✓ sdk 目录存在
    
    if exist "sdk\include" (
        echo ✓ sdk\include 目录存在
    ) else (
        echo ✗ sdk\include 目录不存在
    )
    
    if exist "sdk\lib" (
        echo ✓ sdk\lib 目录存在
    ) else (
        echo ✗ sdk\lib 目录不存在
    )
    
    if exist "sdk\glad.c" (
        echo ✓ glad.c 文件存在
    ) else (
        echo ✗ glad.c 文件不存在
    )
    
    if exist "sdk\include\glad\glad.h" (
        echo ✓ glad.h 头文件存在
    ) else (
        echo ✗ glad.h 头文件不存在
    )
    
    if exist "sdk\include\GLFW" (
        echo ✓ GLFW 头文件目录存在
    ) else (
        echo ✗ GLFW 头文件目录不存在
    )
    
    if exist "sdk\include\glm" (
        echo ✓ GLM 头文件目录存在
    ) else (
        echo ✗ GLM 头文件目录不存在
    )
    
    if exist "sdk\lib\glfw3.lib" (
        echo ✓ GLFW 库文件存在
    ) else (
        echo ✗ GLFW 库文件不存在
    )
    
) else (
    echo ✗ sdk 目录不存在
)

echo.
echo 检查源文件...
if exist "src\main.cpp" (
    echo ✓ main.cpp 存在
) else (
    echo ✗ main.cpp 不存在
)

if exist "include\Camera.h" (
    echo ✓ Camera.h 存在
) else (
    echo ✗ Camera.h 不存在
)

echo.
echo === 环境检测完成 ===
echo.
echo 如果所有项目都显示 ✓，则可以尝试编译项目。
echo 运行 build.bat 开始编译。

pause
