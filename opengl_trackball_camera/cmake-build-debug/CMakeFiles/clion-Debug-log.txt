F:\clion\CLion-2025.1.2.win\bin\cmake\win\x64\bin\cmake.exe -DCMAKE_BUILD_TYPE=Debug -DCMAKE_MAKE_PROGRAM=F:/clion/CLion-2025.1.2.win/bin/ninja/win/x64/ninja.exe -G Ninja -S D:\zjj\vtk-source\VTK-8.2.0\opengl_trackball_camera -B D:\zjj\vtk-source\VTK-8.2.0\opengl_trackball_camera\cmake-build-debug
-- SDK directory: D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/sdk
-- OpenGL found: TRUE
-- Include directories: D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/include, D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/sdk/include
-- Library directory: D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/sdk/lib
-- Configuring done (0.6s)
-- Generating done (0.3s)
-- Build files have been written to: D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug
