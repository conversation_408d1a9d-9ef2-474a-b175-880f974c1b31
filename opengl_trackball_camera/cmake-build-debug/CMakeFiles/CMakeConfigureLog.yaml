
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: F:/clion/CLion-2025.1.2.win/bin/mingw/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug/CMakeFiles/3.31.6/CompilerIdC/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: F:/clion/CLion-2025.1.2.win/bin/mingw/bin/g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug/CMakeFiles/3.31.6/CompilerIdCXX/a.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-ufs12u"
      binary: "D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-ufs12u"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-ufs12u'
        
        Run Build Command(s): F:/clion/CLion-2025.1.2.win/bin/ninja/win/x64/ninja.exe -v cmTC_c6951
        [1/2] F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\gcc.exe   -fdiagnostics-color=always   -v -o CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj -c F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\gcc.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 13.1.0 (GCC) 
        COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_c6951.dir/'
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/cc1.exe -quiet -v -iprefix F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/ -D_REENTRANT F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_c6951.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=nocona -version -fdiagnostics-color=always -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccPzWvu4.s
        GNU C17 (GCC) version 13.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 13.1.0, GMP version 6.2.1, MPFR version 4.2.0-p4, MPC version 1.3.1, isl version none
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include"
        ignoring nonexistent directory "/win/include"
        ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../include"
        ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed"
        ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:\\msys64\\mingw64
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        Compiler executable checksum: 2aa4fcf5c9208168c5e2d38a58fc2a97
        COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_c6951.dir/'
         as -v -o CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccPzWvu4.s
        GNU assembler version 2.40 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.40
        COMPILER_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/
        LIBRARY_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/;D:/msys64/mingw64/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../\x0d
        COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.'\x0d
        [2/2] C:\\windows\\system32\\cmd.exe /C "cd . && F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\gcc.exe  -v -Wl,-v CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj -o cmTC_c6951.exe -Wl,--out-implib,libcmTC_c6951.dll.a -Wl,--major-image-version,0,--minor-image-version,0   && cd ."
        Using built-in specs.
        COLLECT_GCC=F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\gcc.exe
        COLLECT_LTO_WRAPPER=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 13.1.0 (GCC) 
        COMPILER_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/
        LIBRARY_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/;D:/msys64/mingw64/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_c6951.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_c6951.'
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/collect2.exe -m i386pep -Bdynamic -o cmTC_c6951.exe F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib -LD:/msys64/mingw64 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../.. -v CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_c6951.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lkernel32 F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o
        collect2 version 13.1.0
        F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin/ld.exe -m i386pep -Bdynamic -o cmTC_c6951.exe F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib -LD:/msys64/mingw64 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../.. -v CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_c6951.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lkernel32 F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o
        GNU ld (GNU Binutils) 2.40
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_c6951.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_c6951.'\x0d
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/msys64/mingw64]
          add: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include]
          add: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include]
          add: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed]
          add: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [D:/msys64/mingw64] ==> [D:/msys64/mingw64]
        collapse include dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include]
        collapse include dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/include]
        collapse include dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed]
        collapse include dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/include]
        implicit include dirs: [D:/msys64/mingw64;F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include;F:/clion/CLion-2025.1.2.win/bin/mingw/include;F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed;F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: 'D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-ufs12u']
        ignore line: []
        ignore line: [Run Build Command(s): F:/clion/CLion-2025.1.2.win/bin/ninja/win/x64/ninja.exe -v cmTC_c6951]
        ignore line: [[1/2] F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\gcc.exe   -fdiagnostics-color=always   -v -o CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj -c F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\gcc.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 13.1.0 (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_c6951.dir/']
        ignore line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/cc1.exe -quiet -v -iprefix F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/ -D_REENTRANT F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_c6951.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=nocona -version -fdiagnostics-color=always -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccPzWvu4.s]
        ignore line: [GNU C17 (GCC) version 13.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 13.1.0  GMP version 6.2.1  MPFR version 4.2.0-p4  MPC version 1.3.1  isl version none]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include"]
        ignore line: [ignoring nonexistent directory "/win/include"]
        ignore line: [ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../include"]
        ignore line: [ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed"]
        ignore line: [ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:\\msys64\\mingw64]
        ignore line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include]
        ignore line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include]
        ignore line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed]
        ignore line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: 2aa4fcf5c9208168c5e2d38a58fc2a97]
        ignore line: [COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_c6951.dir/']
        ignore line: [ as -v -o CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccPzWvu4.s]
        ignore line: [GNU assembler version 2.40 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.40]
        ignore line: [COMPILER_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/]
        ignore line: [LIBRARY_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/]
        ignore line: [D:/msys64/mingw64/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../\x0d]
        ignore line: [COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.'\x0d]
        ignore line: [[2/2] C:\\windows\\system32\\cmd.exe /C "cd . && F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\gcc.exe  -v -Wl -v CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj -o cmTC_c6951.exe -Wl --out-implib libcmTC_c6951.dll.a -Wl --major-image-version 0 --minor-image-version 0   && cd ."]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\gcc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 13.1.0 (GCC) ]
        ignore line: [COMPILER_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/]
        ignore line: [LIBRARY_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/]
        ignore line: [D:/msys64/mingw64/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_c6951.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_c6951.']
        link line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/collect2.exe -m i386pep -Bdynamic -o cmTC_c6951.exe F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib -LD:/msys64/mingw64 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../.. -v CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_c6951.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lkernel32 F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o]
          arg [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/collect2.exe] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_c6951.exe] ==> ignore
          arg [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o] ==> obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o]
          arg [-LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0] ==> dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0]
          arg [-LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc] ==> dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc]
          arg [-LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib] ==> dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib]
          arg [-LD:/msys64/mingw64] ==> dir [D:/msys64/mingw64]
          arg [-LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../..] ==> dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_c6951.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-liconv] ==> lib [iconv]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o] ==> obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o]
          arg [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o] ==> obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o]
        ignore line: [collect2 version 13.1.0]
        ignore line: [F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin/ld.exe -m i386pep -Bdynamic -o cmTC_c6951.exe F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib -LD:/msys64/mingw64 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../.. -v CMakeFiles/cmTC_c6951.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_c6951.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lkernel32 F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o]
        linker tool for 'C': F:/clion/CLion-2025.1.2.win/bin/mingw/bin/ld.exe
        remove lib [gcc_eh]
        remove lib [msvcrt]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        collapse obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o]
        collapse obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib/default-manifest.o]
        collapse obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o]
        collapse library dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0]
        collapse library dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc]
        collapse library dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib]
        collapse library dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib]
        collapse library dir [D:/msys64/mingw64] ==> [D:/msys64/mingw64]
        collapse library dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib]
        collapse library dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../..] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib]
        implicit libs: [mingw32;gcc;moldname;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc;moldname;mingwex;kernel32]
        implicit objs: [F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib/crt2.o;F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o;F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib/default-manifest.o;F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o]
        implicit dirs: [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0;F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc;F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib;F:/clion/CLion-2025.1.2.win/bin/mingw/lib;D:/msys64/mingw64]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "F:/clion/CLion-2025.1.2.win/bin/mingw/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.40
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-l73jlt"
      binary: "D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-l73jlt"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-l73jlt'
        
        Run Build Command(s): F:/clion/CLion-2025.1.2.win/bin/ninja/win/x64/ninja.exe -v cmTC_3bedd
        [1/2] F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\g++.exe   -fdiagnostics-color=always   -v -o CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj -c F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\g++.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 13.1.0 (GCC) 
        COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_3bedd.dir/'
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/cc1plus.exe -quiet -v -iprefix F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/ -D_REENTRANT F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_3bedd.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=nocona -version -fdiagnostics-color=always -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccscgH0q.s
        GNU C++17 (GCC) version 13.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 13.1.0, GMP version 6.2.1, MPFR version 4.2.0-p4, MPC version 1.3.1, isl version none
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++"
        ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32"
        ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward"
        ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include"
        ignoring nonexistent directory "/win/include"
        ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../include"
        ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed"
        ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:\\msys64\\mingw64
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        Compiler executable checksum: e75de627edc3c57e31324b930b15b056
        COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_3bedd.dir/'
         as -v -o CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccscgH0q.s
        GNU assembler version 2.40 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.40
        COMPILER_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/
        LIBRARY_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/;D:/msys64/mingw64/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../\x0d
        COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.'\x0d
        [2/2] C:\\windows\\system32\\cmd.exe /C "cd . && F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\g++.exe  -v -Wl,-v CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_3bedd.exe -Wl,--out-implib,libcmTC_3bedd.dll.a -Wl,--major-image-version,0,--minor-image-version,0   && cd ."
        Using built-in specs.
        COLLECT_GCC=F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\g++.exe
        COLLECT_LTO_WRAPPER=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 13.1.0 (GCC) 
        COMPILER_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/
        LIBRARY_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/;D:/msys64/mingw64/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/;F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_3bedd.exe' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_3bedd.'
         F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/collect2.exe -m i386pep -Bdynamic -o cmTC_3bedd.exe F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib -LD:/msys64/mingw64 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../.. -v CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_3bedd.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lkernel32 F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o
        collect2 version 13.1.0
        F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin/ld.exe -m i386pep -Bdynamic -o cmTC_3bedd.exe F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib -LD:/msys64/mingw64 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../.. -v CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_3bedd.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lkernel32 F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o
        GNU ld (GNU Binutils) 2.40
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_3bedd.exe' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_3bedd.'\x0d
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/msys64/mingw64]
          add: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++]
          add: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32]
          add: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward]
          add: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include]
          add: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include]
          add: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed]
          add: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [D:/msys64/mingw64] ==> [D:/msys64/mingw64]
        collapse include dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++]
        collapse include dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32]
        collapse include dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward]
        collapse include dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include]
        collapse include dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/include]
        collapse include dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed]
        collapse include dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/include]
        implicit include dirs: [D:/msys64/mingw64;F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++;F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32;F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward;F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include;F:/clion/CLion-2025.1.2.win/bin/mingw/include;F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed;F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: 'D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-l73jlt']
        ignore line: []
        ignore line: [Run Build Command(s): F:/clion/CLion-2025.1.2.win/bin/ninja/win/x64/ninja.exe -v cmTC_3bedd]
        ignore line: [[1/2] F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\g++.exe   -fdiagnostics-color=always   -v -o CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj -c F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\g++.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 13.1.0 (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_3bedd.dir/']
        ignore line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/cc1plus.exe -quiet -v -iprefix F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/ -D_REENTRANT F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_3bedd.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=nocona -version -fdiagnostics-color=always -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccscgH0q.s]
        ignore line: [GNU C++17 (GCC) version 13.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 13.1.0  GMP version 6.2.1  MPFR version 4.2.0-p4  MPC version 1.3.1  isl version none]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++"]
        ignore line: [ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32"]
        ignore line: [ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward"]
        ignore line: [ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include"]
        ignore line: [ignoring nonexistent directory "/win/include"]
        ignore line: [ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../include"]
        ignore line: [ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed"]
        ignore line: [ignoring duplicate directory "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:\\msys64\\mingw64]
        ignore line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++]
        ignore line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32]
        ignore line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward]
        ignore line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include]
        ignore line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include]
        ignore line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed]
        ignore line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: e75de627edc3c57e31324b930b15b056]
        ignore line: [COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_3bedd.dir/']
        ignore line: [ as -v -o CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccscgH0q.s]
        ignore line: [GNU assembler version 2.40 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.40]
        ignore line: [COMPILER_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/]
        ignore line: [LIBRARY_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/]
        ignore line: [D:/msys64/mingw64/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../\x0d]
        ignore line: [COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.'\x0d]
        ignore line: [[2/2] C:\\windows\\system32\\cmd.exe /C "cd . && F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\g++.exe  -v -Wl -v CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_3bedd.exe -Wl --out-implib libcmTC_3bedd.dll.a -Wl --major-image-version 0 --minor-image-version 0   && cd ."]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin\\g++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 13.1.0 (GCC) ]
        ignore line: [COMPILER_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/]
        ignore line: [LIBRARY_PATH=F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/]
        ignore line: [D:/msys64/mingw64/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_3bedd.exe' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_3bedd.']
        link line: [ F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/collect2.exe -m i386pep -Bdynamic -o cmTC_3bedd.exe F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib -LD:/msys64/mingw64 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../.. -v CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_3bedd.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lkernel32 F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o]
          arg [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/collect2.exe] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_3bedd.exe] ==> ignore
          arg [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o] ==> obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o]
          arg [-LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0] ==> dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0]
          arg [-LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc] ==> dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc]
          arg [-LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib] ==> dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib]
          arg [-LD:/msys64/mingw64] ==> dir [D:/msys64/mingw64]
          arg [-LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../..] ==> dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_3bedd.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-liconv] ==> lib [iconv]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o] ==> obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o]
          arg [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o] ==> obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o]
        ignore line: [collect2 version 13.1.0]
        ignore line: [F:\\clion\\CLion-2025.1.2.win\\bin\\mingw\\bin/ld.exe -m i386pep -Bdynamic -o cmTC_3bedd.exe F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib -LD:/msys64/mingw64 -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib -LF:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../.. -v CMakeFiles/cmTC_3bedd.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_3bedd.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lkernel32 F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o]
        linker tool for 'CXX': F:/clion/CLion-2025.1.2.win/bin/mingw/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o]
        collapse obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib/default-manifest.o]
        collapse obj [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o]
        collapse library dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0]
        collapse library dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc]
        collapse library dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib]
        collapse library dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib]
        collapse library dir [D:/msys64/mingw64] ==> [D:/msys64/mingw64]
        collapse library dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib]
        collapse library dir [F:/clion/CLion-2025.1.2.win/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../..] ==> [F:/clion/CLion-2025.1.2.win/bin/mingw/lib]
        implicit libs: [stdc++;mingw32;gcc_s;gcc;moldname;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc_s;gcc;moldname;mingwex;kernel32]
        implicit objs: [F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib/crt2.o;F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o;F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib/default-manifest.o;F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o]
        implicit dirs: [F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0;F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc;F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib;F:/clion/CLion-2025.1.2.win/bin/mingw/lib;D:/msys64/mingw64]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "F:/clion/CLion-2025.1.2.win/bin/mingw/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.40
...
