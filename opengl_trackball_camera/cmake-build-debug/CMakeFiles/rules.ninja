# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: TrackballCamera
# Configurations: Debug
# =============================================================================
# =============================================================================

#############################################
# Rule for compiling C files.

rule C_COMPILER__TrackballCamera_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}F:\clion\CLion-2025.1.2.win\bin\mingw\bin\gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__TrackballCamera_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}F:\clion\CLion-2025.1.2.win\bin\mingw\bin\g++.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__TrackballCamera_Debug
  command = C:\windows\system32\cmd.exe /C "$PRE_LINK && F:\clion\CLion-2025.1.2.win\bin\mingw\bin\g++.exe $FLAGS $LINK_FLAGS $in -o $TARGET_FILE -Wl,--out-implib,$TARGET_IMPLIB -Wl,--major-image-version,0,--minor-image-version,0 $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking CXX executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = F:\clion\CLion-2025.1.2.win\bin\cmake\win\x64\bin\cmake.exe --regenerate-during-build -SD:\zjj\vtk-source\VTK-8.2.0\opengl_trackball_camera -BD:\zjj\vtk-source\VTK-8.2.0\opengl_trackball_camera\cmake-build-debug
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = F:\clion\CLion-2025.1.2.win\bin\ninja\win\x64\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = F:\clion\CLion-2025.1.2.win\bin\ninja\win\x64\ninja.exe -t targets
  description = All primary targets available:

