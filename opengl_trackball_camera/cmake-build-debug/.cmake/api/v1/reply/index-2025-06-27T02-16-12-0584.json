{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/bin/cmake.exe", "cpack": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/bin/cpack.exe", "ctest": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/bin/ctest.exe", "root": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 6, "string": "3.31.6", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-30aca2b20a6c1bf46447.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-240dc0d754b06fe172e1.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-c39075a99eaf7436a08d.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-0a5a02cb7a4a8bc18c0e.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-240dc0d754b06fe172e1.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-c39075a99eaf7436a08d.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-30aca2b20a6c1bf46447.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, "toolchains-v1": {"jsonFile": "toolchains-v1-0a5a02cb7a4a8bc18c0e.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}