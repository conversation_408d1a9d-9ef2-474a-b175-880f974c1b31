{"artifacts": [{"path": "bin/TrackballCamera.exe"}, {"path": "bin/TrackballCamera.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "link_directories", "target_link_libraries", "target_compile_definitions", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 27, "parent": 0}, {"command": 1, "file": 0, "line": 21, "parent": 0}, {"command": 2, "file": 0, "line": 38, "parent": 0}, {"command": 3, "file": 0, "line": 80, "parent": 0}, {"command": 4, "file": 0, "line": 17, "parent": 0}, {"command": 4, "file": 0, "line": 18, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++11 -fdiagnostics-color=always"}], "defines": [{"backtrace": 4, "define": "DEBUG"}], "includes": [{"backtrace": 5, "path": "D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/include"}, {"backtrace": 6, "path": "D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/sdk/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0, 1, 2, 3]}, {"compileCommandFragments": [{"fragment": "-g -fdiagnostics-color=always"}], "defines": [{"backtrace": 4, "define": "DEBUG"}], "includes": [{"backtrace": 5, "path": "D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/include"}, {"backtrace": 6, "path": "D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/sdk/include"}], "language": "C", "sourceIndexes": [4]}], "id": "TrackballCamera::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "-LD:\\zjj\\vtk-source\\VTK-8.2.0\\opengl_trackball_camera\\sdk\\lib", "role": "libraryPath"}, {"backtrace": 3, "fragment": "-lopengl32", "role": "libraries"}, {"backtrace": 3, "fragment": "-lglfw3", "role": "libraries"}, {"backtrace": 3, "fragment": "-lglew32", "role": "libraries"}, {"backtrace": 3, "fragment": "-lopengl32", "role": "libraries"}, {"backtrace": 3, "fragment": "-lgdi32", "role": "libraries"}, {"backtrace": 3, "fragment": "-luser32", "role": "libraries"}, {"backtrace": 3, "fragment": "-lkernel32", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "TrackballCamera", "nameOnDisk": "TrackballCamera.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Camera.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Shader.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Cube.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "sdk/glad.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}