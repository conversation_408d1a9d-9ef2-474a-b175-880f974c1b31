{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["D:/msys64/mingw64", "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include", "F:/clion/CLion-2025.1.2.win/bin/mingw/include", "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed", "F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/include"], "linkDirectories": ["F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0", "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc", "F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib", "F:/clion/CLion-2025.1.2.win/bin/mingw/lib", "D:/msys64/mingw64"], "linkFrameworkDirectories": [], "linkLibraries": ["mingw32", "gcc", "moldname", "mingwex", "kernel32", "pthread", "advapi32", "shell32", "user32", "kernel32", "iconv", "mingw32", "gcc", "moldname", "mingwex", "kernel32"]}, "path": "F:/clion/CLion-2025.1.2.win/bin/mingw/bin/gcc.exe", "version": "13.1.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["D:/msys64/mingw64", "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++", "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32", "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward", "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include", "F:/clion/CLion-2025.1.2.win/bin/mingw/include", "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed", "F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/include"], "linkDirectories": ["F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0", "F:/clion/CLion-2025.1.2.win/bin/mingw/lib/gcc", "F:/clion/CLion-2025.1.2.win/bin/mingw/x86_64-w64-mingw32/lib", "F:/clion/CLion-2025.1.2.win/bin/mingw/lib", "D:/msys64/mingw64"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "mingw32", "gcc_s", "gcc", "moldname", "mingwex", "kernel32", "pthread", "advapi32", "shell32", "user32", "kernel32", "iconv", "mingw32", "gcc_s", "gcc", "moldname", "mingwex", "kernel32"]}, "path": "F:/clion/CLion-2025.1.2.win/bin/mingw/bin/g++.exe", "version": "13.1.0"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}, {"compiler": {"implicit": {}, "path": "F:/clion/CLion-2025.1.2.win/bin/mingw/bin/windres.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}