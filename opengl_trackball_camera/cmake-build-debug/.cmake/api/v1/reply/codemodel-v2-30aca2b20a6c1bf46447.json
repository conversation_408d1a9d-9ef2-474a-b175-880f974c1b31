{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "TrackballCamera", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "TrackballCamera::@6890427a1f51a3e7e1df", "jsonFile": "target-TrackballCamera-Debug-ac5c65b8d4a4d5727ccb.json", "name": "TrackballCamera", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug", "source": "D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera"}, "version": {"major": 2, "minor": 7}}