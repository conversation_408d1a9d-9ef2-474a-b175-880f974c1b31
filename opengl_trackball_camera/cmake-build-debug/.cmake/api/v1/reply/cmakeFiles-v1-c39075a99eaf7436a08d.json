{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.31.6/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.31.6/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.31.6/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.31.6/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU-C-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Linker/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU-CXX-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Linker/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/FindOpenGL.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}], "kind": "cmakeFiles", "paths": {"build": "D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug", "source": "D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera"}, "version": {"major": 1, "minor": 1}}