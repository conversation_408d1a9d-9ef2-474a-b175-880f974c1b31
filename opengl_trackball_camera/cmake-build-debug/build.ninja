# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: TrackballCamera
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug/
# =============================================================================
# Object build statements for EXECUTABLE target TrackballCamera


#############################################
# Order-only phony target for TrackballCamera

build cmake_object_order_depends_target_TrackballCamera: phony || .

build CMakeFiles/TrackballCamera.dir/src/main.cpp.obj: CXX_COMPILER__TrackballCamera_unscanned_Debug D$:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/src/main.cpp || cmake_object_order_depends_target_TrackballCamera
  DEFINES = -DDEBUG
  DEP_FILE = CMakeFiles\TrackballCamera.dir\src\main.cpp.obj.d
  FLAGS = -g -std=gnu++11 -fdiagnostics-color=always
  INCLUDES = -ID:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/include -ID:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/sdk/include
  OBJECT_DIR = CMakeFiles\TrackballCamera.dir
  OBJECT_FILE_DIR = CMakeFiles\TrackballCamera.dir\src

build CMakeFiles/TrackballCamera.dir/src/Camera.cpp.obj: CXX_COMPILER__TrackballCamera_unscanned_Debug D$:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/src/Camera.cpp || cmake_object_order_depends_target_TrackballCamera
  DEFINES = -DDEBUG
  DEP_FILE = CMakeFiles\TrackballCamera.dir\src\Camera.cpp.obj.d
  FLAGS = -g -std=gnu++11 -fdiagnostics-color=always
  INCLUDES = -ID:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/include -ID:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/sdk/include
  OBJECT_DIR = CMakeFiles\TrackballCamera.dir
  OBJECT_FILE_DIR = CMakeFiles\TrackballCamera.dir\src

build CMakeFiles/TrackballCamera.dir/src/Shader.cpp.obj: CXX_COMPILER__TrackballCamera_unscanned_Debug D$:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/src/Shader.cpp || cmake_object_order_depends_target_TrackballCamera
  DEFINES = -DDEBUG
  DEP_FILE = CMakeFiles\TrackballCamera.dir\src\Shader.cpp.obj.d
  FLAGS = -g -std=gnu++11 -fdiagnostics-color=always
  INCLUDES = -ID:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/include -ID:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/sdk/include
  OBJECT_DIR = CMakeFiles\TrackballCamera.dir
  OBJECT_FILE_DIR = CMakeFiles\TrackballCamera.dir\src

build CMakeFiles/TrackballCamera.dir/src/Cube.cpp.obj: CXX_COMPILER__TrackballCamera_unscanned_Debug D$:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/src/Cube.cpp || cmake_object_order_depends_target_TrackballCamera
  DEFINES = -DDEBUG
  DEP_FILE = CMakeFiles\TrackballCamera.dir\src\Cube.cpp.obj.d
  FLAGS = -g -std=gnu++11 -fdiagnostics-color=always
  INCLUDES = -ID:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/include -ID:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/sdk/include
  OBJECT_DIR = CMakeFiles\TrackballCamera.dir
  OBJECT_FILE_DIR = CMakeFiles\TrackballCamera.dir\src

build CMakeFiles/TrackballCamera.dir/sdk/glad.c.obj: C_COMPILER__TrackballCamera_unscanned_Debug D$:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/sdk/glad.c || cmake_object_order_depends_target_TrackballCamera
  DEFINES = -DDEBUG
  DEP_FILE = CMakeFiles\TrackballCamera.dir\sdk\glad.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -ID:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/include -ID:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/sdk/include
  OBJECT_DIR = CMakeFiles\TrackballCamera.dir
  OBJECT_FILE_DIR = CMakeFiles\TrackballCamera.dir\sdk


# =============================================================================
# Link build statements for EXECUTABLE target TrackballCamera


#############################################
# Link the executable bin\TrackballCamera.exe

build bin/TrackballCamera.exe: CXX_EXECUTABLE_LINKER__TrackballCamera_Debug CMakeFiles/TrackballCamera.dir/src/main.cpp.obj CMakeFiles/TrackballCamera.dir/src/Camera.cpp.obj CMakeFiles/TrackballCamera.dir/src/Shader.cpp.obj CMakeFiles/TrackballCamera.dir/src/Cube.cpp.obj CMakeFiles/TrackballCamera.dir/sdk/glad.c.obj
  FLAGS = -g
  LINK_LIBRARIES = -lopengl32  -lglfw3  -lglew32  -lopengl32  -lgdi32  -luser32  -lkernel32  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  LINK_PATH = -LD:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/sdk/lib
  OBJECT_DIR = CMakeFiles\TrackballCamera.dir
  POST_BUILD = C:\windows\system32\cmd.exe /C "cd /D D:\zjj\vtk-source\VTK-8.2.0\opengl_trackball_camera\cmake-build-debug && F:\clion\CLion-2025.1.2.win\bin\cmake\win\x64\bin\cmake.exe -E copy_if_different D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/sdk/glew32.dll D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug/bin"
  PRE_LINK = cd .
  TARGET_FILE = bin\TrackballCamera.exe
  TARGET_IMPLIB = libTrackballCamera.dll.a
  TARGET_PDB = TrackballCamera.exe.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\windows\system32\cmd.exe /C "cd /D D:\zjj\vtk-source\VTK-8.2.0\opengl_trackball_camera\cmake-build-debug && F:\clion\CLion-2025.1.2.win\bin\cmake\win\x64\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\windows\system32\cmd.exe /C "cd /D D:\zjj\vtk-source\VTK-8.2.0\opengl_trackball_camera\cmake-build-debug && F:\clion\CLion-2025.1.2.win\bin\cmake\win\x64\bin\cmake.exe --regenerate-during-build -SD:\zjj\vtk-source\VTK-8.2.0\opengl_trackball_camera -BD:\zjj\vtk-source\VTK-8.2.0\opengl_trackball_camera\cmake-build-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build TrackballCamera: phony bin/TrackballCamera.exe

build TrackballCamera.exe: phony bin/TrackballCamera.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/cmake-build-debug

build all: phony bin/TrackballCamera.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles/3.31.6/CMakeCCompiler.cmake CMakeFiles/3.31.6/CMakeCXXCompiler.cmake CMakeFiles/3.31.6/CMakeRCCompiler.cmake CMakeFiles/3.31.6/CMakeSystem.cmake D$:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/CMakeLists.txt F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCXXInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeGenericSystem.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeRCInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Compiler/GNU-C.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Compiler/GNU-CXX.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Compiler/GNU.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/FindOpenGL.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/FindPackageMessage.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Linker/GNU-C.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Linker/GNU-CXX.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Linker/GNU.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Linker/GNU.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU-C.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU-CXX.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU-C-ABI.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU-C.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU-CXX-ABI.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU-CXX.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-Initialize.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-windres.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/WindowsPaths.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.31.6/CMakeCCompiler.cmake CMakeFiles/3.31.6/CMakeCXXCompiler.cmake CMakeFiles/3.31.6/CMakeRCCompiler.cmake CMakeFiles/3.31.6/CMakeSystem.cmake D$:/zjj/vtk-source/VTK-8.2.0/opengl_trackball_camera/CMakeLists.txt F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCXXInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeGenericSystem.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeRCInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Compiler/GNU-C.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Compiler/GNU-CXX.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Compiler/GNU.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/FindOpenGL.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/FindPackageMessage.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Linker/GNU-C.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Linker/GNU-CXX.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Linker/GNU.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Linker/GNU.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU-C.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU-CXX.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU-C-ABI.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU-C.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU-CXX-ABI.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU-CXX.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-GNU.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-Initialize.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows-windres.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/Windows.cmake F$:/clion/CLion-2025.1.2.win/bin/cmake/win/x64/share/cmake-3.31/Modules/Platform/WindowsPaths.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
