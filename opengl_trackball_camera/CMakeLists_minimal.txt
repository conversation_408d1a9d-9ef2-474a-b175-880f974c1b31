cmake_minimum_required(VERSION 3.10)
project(TrackballCamera)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# 设置SDK路径
set(SDK_DIR "${CMAKE_SOURCE_DIR}/sdk")

# 查找OpenGL
find_package(OpenGL REQUIRED)

# 设置包含目录
include_directories(${CMAKE_SOURCE_DIR}/include)
include_directories(${SDK_DIR}/include)

# 添加GLAD源文件
set(GLAD_SOURCES ${SDK_DIR}/glad.c)

# 创建GLFW的最小实现（使用Windows API）
set(GLFW_MINIMAL_SOURCES
    ${CMAKE_SOURCE_DIR}/src/glfw_minimal.cpp
)

# 添加可执行文件
add_executable(TrackballCamera 
    src/main_minimal.cpp
    src/Camera.cpp
    src/Shader.cpp
    src/Cube.cpp
    ${GLAD_SOURCES}
    ${GLFW_MINIMAL_SOURCES}
)

# Windows配置
if(WIN32)
    target_link_libraries(TrackballCamera 
        OpenGL::GL
        opengl32
        gdi32
        user32
        kernel32
        winmm
        shell32
    )
    
    # 添加编译定义
    target_compile_definitions(TrackballCamera PRIVATE
        WIN32_LEAN_AND_MEAN
        NOMINMAX
        _CRT_SECURE_NO_WARNINGS
    )
    
    # MinGW特定配置
    if(MINGW)
        target_link_options(TrackballCamera PRIVATE
            -static-libgcc
            -static-libstdc++
            -mwindows
        )
    endif()
endif()

# 打印配置信息
message(STATUS "SDK directory: ${SDK_DIR}")
message(STATUS "OpenGL found: ${OPENGL_FOUND}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
