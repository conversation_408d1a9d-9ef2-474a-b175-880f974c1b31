#ifndef SHADER_H
#define SHADER_H

#include <glad/glad.h>
#include <glm/glm.hpp>
#include <string>

class Shader {
public:
    unsigned int ID;
    
    // 构造函数
    Shader(const char* vertexSource, const char* fragmentSource);
    
    // 从文件构造
    Shader(const std::string& vertexPath, const std::string& fragmentPath);
    
    // 析构函数
    ~Shader();
    
    // 使用着色器
    void use();
    
    // uniform工具函数
    void setBool(const std::string &name, bool value) const;
    void setInt(const std::string &name, int value) const;
    void setFloat(const std::string &name, float value) const;
    void setVec3(const std::string &name, const glm::vec3 &value) const;
    void setVec3(const std::string &name, float x, float y, float z) const;
    void setMat4(const std::string &name, const glm::mat4 &mat) const;
    
private:
    // 编译着色器的辅助函数
    unsigned int compileShader(const char* source, GLenum type);
    
    // 从文件读取着色器源码
    std::string readFile(const std::string& filePath);
    
    // 检查编译/链接错误
    void checkCompileErrors(unsigned int shader, std::string type);
};

#endif // SHADER_H
