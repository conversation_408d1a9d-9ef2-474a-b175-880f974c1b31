module;

// #define GLM_GTC_INLINE_NAMESPACE to inline glm::gtc into glm
// #define GLM_EXT_INLINE_NAMESPACE to inline glm::ext into glm
// #define GLM_GTX_INLINE_NAMESPACE to inline glm::gtx into glm

#include <glm/glm.hpp>
#include <glm/ext.hpp>

export module glm;

export namespace glm {
	// Base types
	using glm::qualifier;
	using glm::precision;
	using glm::vec;
	using glm::mat;
	using glm::qua;
#	if GLM_HAS_TEMPLATE_ALIASES
	using glm::tvec1;
	using glm::tvec2;
	using glm::tvec3;
	using glm::tvec4;
	using glm::tmat2x2;
	using glm::tmat2x3;
	using glm::tmat2x4;
	using glm::tmat3x2;
	using glm::tmat3x3;
	using glm::tmat3x4;
	using glm::tmat4x2;
	using glm::tmat4x3;
	using glm::tmat4x4;
	using glm::tquat;
#	endif

	using glm::int8;
	using glm::int16;
	using glm::int32;
	using glm::int64;
	using glm::uint8;
	using glm::uint16;
	using glm::uint32;
	using glm::uint64;
	using glm::lowp_i8;
	using glm::mediump_i8;
	using glm::highp_i8;
	using glm::i8;
	using glm::lowp_int8;
	using glm::mediump_int8;
	using glm::highp_int8;
	using glm::lowp_int8_t;
	using glm::mediump_int8_t;
	using glm::highp_int8_t;
	using glm::int8_t;
	using glm::lowp_i16;
	using glm::mediump_i16;
	using glm::highp_i16;
	using glm::i16;
	using glm::lowp_int16;
	using glm::mediump_int16;
	using glm::highp_int16;
	using glm::lowp_int16_t;
	using glm::mediump_int16_t;
	using glm::highp_int16_t;
	using glm::int16_t;
	using glm::lowp_i32;
	using glm::mediump_i32;
	using glm::highp_i32;
	using glm::i32;
	using glm::lowp_int32;
	using glm::mediump_int32;
	using glm::highp_int32;
	using glm::lowp_int32_t;
	using glm::mediump_int32_t;
	using glm::highp_int32_t;
	using glm::int32_t;
	using glm::lowp_i64;
	using glm::mediump_i64;
	using glm::highp_i64;
	using glm::i64;
	using glm::lowp_int64;
	using glm::mediump_int64;
	using glm::highp_int64;
	using glm::lowp_int64_t;
	using glm::mediump_int64_t;
	using glm::highp_int64_t;
	using glm::int64_t;
	using glm::uint;
	using glm::lowp_u8;
	using glm::mediump_u8;
	using glm::highp_u8;
	using glm::u8;
	using glm::lowp_uint8;
	using glm::mediump_uint8;
	using glm::highp_uint8;
	using glm::lowp_uint8_t;
	using glm::mediump_uint8_t;
	using glm::highp_uint8_t;
	using glm::uint8_t;
	using glm::lowp_u16;
	using glm::mediump_u16;
	using glm::highp_u16;
	using glm::u16;
	using glm::lowp_uint16;
	using glm::mediump_uint16;
	using glm::highp_uint16;
	using glm::lowp_uint16_t;
	using glm::mediump_uint16_t;
	using glm::highp_uint16_t;
	using glm::uint16_t;
	using glm::lowp_u32;
	using glm::mediump_u32;
	using glm::highp_u32;
	using glm::u32;
	using glm::lowp_uint32;
	using glm::mediump_uint32;
	using glm::highp_uint32;
	using glm::lowp_uint32_t;
	using glm::mediump_uint32_t;
	using glm::highp_uint32_t;
	using glm::uint32_t;
	using glm::lowp_u64;
	using glm::mediump_u64;
	using glm::highp_u64;
	using glm::u64;
	using glm::lowp_uint64;
	using glm::mediump_uint64;
	using glm::highp_uint64;
	using glm::lowp_uint64_t;
	using glm::mediump_uint64_t;
	using glm::highp_uint64_t;
	using glm::uint64_t;
	using glm::lowp_f32;
	using glm::mediump_f32;
	using glm::highp_f32;
	using glm::f32;
	using glm::lowp_float32;
	using glm::mediump_float32;
	using glm::highp_float32;
	using glm::float32;
	using glm::lowp_float32_t;
	using glm::mediump_float32_t;
	using glm::highp_float32_t;
	using glm::float32_t;
	using glm::lowp_f64;
	using glm::mediump_f64;
	using glm::highp_f64;
	using glm::f64;
	using glm::lowp_float64;
	using glm::mediump_float64;
	using glm::highp_float64;
	using glm::float64;
	using glm::lowp_float64_t;
	using glm::mediump_float64_t;
	using glm::highp_float64_t;
	using glm::float64_t;
	using glm::lowp_bvec1;
	using glm::lowp_bvec2;
	using glm::lowp_bvec3;
	using glm::lowp_bvec4;
	using glm::mediump_bvec1;
	using glm::mediump_bvec2;
	using glm::mediump_bvec3;
	using glm::mediump_bvec4;
	using glm::highp_bvec1;
	using glm::highp_bvec2;
	using glm::highp_bvec3;
	using glm::highp_bvec4;
	using glm::bvec1;
	using glm::bvec2;
	using glm::bvec3;
	using glm::bvec4;
	using glm::lowp_ivec1;
	using glm::lowp_ivec2;
	using glm::lowp_ivec3;
	using glm::lowp_ivec4;
	using glm::mediump_ivec1;
	using glm::mediump_ivec2;
	using glm::mediump_ivec3;
	using glm::mediump_ivec4;
	using glm::highp_ivec1;
	using glm::highp_ivec2;
	using glm::highp_ivec3;
	using glm::highp_ivec4;
	using glm::ivec1;
	using glm::ivec2;
	using glm::ivec3;
	using glm::ivec4;
	using glm::lowp_i8vec1;
	using glm::lowp_i8vec2;
	using glm::lowp_i8vec3;
	using glm::lowp_i8vec4;
	using glm::mediump_i8vec1;
	using glm::mediump_i8vec2;
	using glm::mediump_i8vec3;
	using glm::mediump_i8vec4;
	using glm::highp_i8vec1;
	using glm::highp_i8vec2;
	using glm::highp_i8vec3;
	using glm::highp_i8vec4;
	using glm::i8vec1;
	using glm::i8vec2;
	using glm::i8vec3;
	using glm::i8vec4;
	using glm::lowp_i16vec1;
	using glm::lowp_i16vec2;
	using glm::lowp_i16vec3;
	using glm::lowp_i16vec4;
	using glm::mediump_i16vec1;
	using glm::mediump_i16vec2;
	using glm::mediump_i16vec3;
	using glm::mediump_i16vec4;
	using glm::highp_i16vec1;
	using glm::highp_i16vec2;
	using glm::highp_i16vec3;
	using glm::highp_i16vec4;
	using glm::i16vec1;
	using glm::i16vec2;
	using glm::i16vec3;
	using glm::i16vec4;
	using glm::lowp_i32vec1;
	using glm::lowp_i32vec2;
	using glm::lowp_i32vec3;
	using glm::lowp_i32vec4;
	using glm::mediump_i32vec1;
	using glm::mediump_i32vec2;
	using glm::mediump_i32vec3;
	using glm::mediump_i32vec4;
	using glm::highp_i32vec1;
	using glm::highp_i32vec2;
	using glm::highp_i32vec3;
	using glm::highp_i32vec4;
	using glm::i32vec1;
	using glm::i32vec2;
	using glm::i32vec3;
	using glm::i32vec4;
	using glm::lowp_i64vec1;
	using glm::lowp_i64vec2;
	using glm::lowp_i64vec3;
	using glm::lowp_i64vec4;
	using glm::mediump_i64vec1;
	using glm::mediump_i64vec2;
	using glm::mediump_i64vec3;
	using glm::mediump_i64vec4;
	using glm::highp_i64vec1;
	using glm::highp_i64vec2;
	using glm::highp_i64vec3;
	using glm::highp_i64vec4;
	using glm::i64vec1;
	using glm::i64vec2;
	using glm::i64vec3;
	using glm::i64vec4;
	using glm::lowp_uvec1;
	using glm::lowp_uvec2;
	using glm::lowp_uvec3;
	using glm::lowp_uvec4;
	using glm::mediump_uvec1;
	using glm::mediump_uvec2;
	using glm::mediump_uvec3;
	using glm::mediump_uvec4;
	using glm::highp_uvec1;
	using glm::highp_uvec2;
	using glm::highp_uvec3;
	using glm::highp_uvec4;
	using glm::uvec1;
	using glm::uvec2;
	using glm::uvec3;
	using glm::uvec4;
	using glm::lowp_u8vec1;
	using glm::lowp_u8vec2;
	using glm::lowp_u8vec3;
	using glm::lowp_u8vec4;
	using glm::mediump_u8vec1;
	using glm::mediump_u8vec2;
	using glm::mediump_u8vec3;
	using glm::mediump_u8vec4;
	using glm::highp_u8vec1;
	using glm::highp_u8vec2;
	using glm::highp_u8vec3;
	using glm::highp_u8vec4;
	using glm::u8vec1;
	using glm::u8vec2;
	using glm::u8vec3;
	using glm::u8vec4;
	using glm::lowp_u16vec1;
	using glm::lowp_u16vec2;
	using glm::lowp_u16vec3;
	using glm::lowp_u16vec4;
	using glm::mediump_u16vec1;
	using glm::mediump_u16vec2;
	using glm::mediump_u16vec3;
	using glm::mediump_u16vec4;
	using glm::highp_u16vec1;
	using glm::highp_u16vec2;
	using glm::highp_u16vec3;
	using glm::highp_u16vec4;
	using glm::u16vec1;
	using glm::u16vec2;
	using glm::u16vec3;
	using glm::u16vec4;
	using glm::lowp_u32vec1;
	using glm::lowp_u32vec2;
	using glm::lowp_u32vec3;
	using glm::lowp_u32vec4;
	using glm::mediump_u32vec1;
	using glm::mediump_u32vec2;
	using glm::mediump_u32vec3;
	using glm::mediump_u32vec4;
	using glm::highp_u32vec1;
	using glm::highp_u32vec2;
	using glm::highp_u32vec3;
	using glm::highp_u32vec4;
	using glm::u32vec1;
	using glm::u32vec2;
	using glm::u32vec3;
	using glm::u32vec4;
	using glm::lowp_u64vec1;
	using glm::lowp_u64vec2;
	using glm::lowp_u64vec3;
	using glm::lowp_u64vec4;
	using glm::mediump_u64vec1;
	using glm::mediump_u64vec2;
	using glm::mediump_u64vec3;
	using glm::mediump_u64vec4;
	using glm::highp_u64vec1;
	using glm::highp_u64vec2;
	using glm::highp_u64vec3;
	using glm::highp_u64vec4;
	using glm::u64vec1;
	using glm::u64vec2;
	using glm::u64vec3;
	using glm::u64vec4;
	using glm::lowp_vec1;
	using glm::lowp_vec2;
	using glm::lowp_vec3;
	using glm::lowp_vec4;
	using glm::mediump_vec1;
	using glm::mediump_vec2;
	using glm::mediump_vec3;
	using glm::mediump_vec4;
	using glm::highp_vec1;
	using glm::highp_vec2;
	using glm::highp_vec3;
	using glm::highp_vec4;
	using glm::vec1;
	using glm::vec2;
	using glm::vec3;
	using glm::vec4;
	using glm::lowp_fvec1;
	using glm::lowp_fvec2;
	using glm::lowp_fvec3;
	using glm::lowp_fvec4;
	using glm::mediump_fvec1;
	using glm::mediump_fvec2;
	using glm::mediump_fvec3;
	using glm::mediump_fvec4;
	using glm::highp_fvec1;
	using glm::highp_fvec2;
	using glm::highp_fvec3;
	using glm::highp_fvec4;
	using glm::fvec1;
	using glm::fvec2;
	using glm::fvec3;
	using glm::fvec4;
	using glm::lowp_f32vec1;
	using glm::lowp_f32vec2;
	using glm::lowp_f32vec3;
	using glm::lowp_f32vec4;
	using glm::mediump_f32vec1;
	using glm::mediump_f32vec2;
	using glm::mediump_f32vec3;
	using glm::mediump_f32vec4;
	using glm::highp_f32vec1;
	using glm::highp_f32vec2;
	using glm::highp_f32vec3;
	using glm::highp_f32vec4;
	using glm::f32vec1;
	using glm::f32vec2;
	using glm::f32vec3;
	using glm::f32vec4;
	using glm::lowp_dvec1;
	using glm::lowp_dvec2;
	using glm::lowp_dvec3;
	using glm::lowp_dvec4;
	using glm::mediump_dvec1;
	using glm::mediump_dvec2;
	using glm::mediump_dvec3;
	using glm::mediump_dvec4;
	using glm::highp_dvec1;
	using glm::highp_dvec2;
	using glm::highp_dvec3;
	using glm::highp_dvec4;
	using glm::dvec1;
	using glm::dvec2;
	using glm::dvec3;
	using glm::dvec4;
	using glm::lowp_f64vec1;
	using glm::lowp_f64vec2;
	using glm::lowp_f64vec3;
	using glm::lowp_f64vec4;
	using glm::mediump_f64vec1;
	using glm::mediump_f64vec2;
	using glm::mediump_f64vec3;
	using glm::mediump_f64vec4;
	using glm::highp_f64vec1;
	using glm::highp_f64vec2;
	using glm::highp_f64vec3;
	using glm::highp_f64vec4;
	using glm::f64vec1;
	using glm::f64vec2;
	using glm::f64vec3;
	using glm::f64vec4;
	using glm::lowp_mat2;
	using glm::lowp_mat3;
	using glm::lowp_mat4;
	using glm::mediump_mat2;
	using glm::mediump_mat3;
	using glm::mediump_mat4;
	using glm::highp_mat2;
	using glm::highp_mat3;
	using glm::highp_mat4;
	using glm::mat2;
	using glm::mat3;
	using glm::mat4;
	using glm::lowp_fmat2;
	using glm::lowp_fmat3;
	using glm::lowp_fmat4;
	using glm::mediump_fmat2;
	using glm::mediump_fmat3;
	using glm::mediump_fmat4;
	using glm::highp_fmat2;
	using glm::highp_fmat3;
	using glm::highp_fmat4;
	using glm::fmat2;
	using glm::fmat3;
	using glm::fmat4;
	using glm::lowp_f32mat2;
	using glm::lowp_f32mat3;
	using glm::lowp_f32mat4;
	using glm::mediump_f32mat2;
	using glm::mediump_f32mat3;
	using glm::mediump_f32mat4;
	using glm::highp_f32mat2;
	using glm::highp_f32mat3;
	using glm::highp_f32mat4;
	using glm::f32mat2;
	using glm::f32mat3;
	using glm::f32mat4;
	using glm::lowp_dmat2;
	using glm::lowp_dmat3;
	using glm::lowp_dmat4;
	using glm::mediump_dmat2;
	using glm::mediump_dmat3;
	using glm::mediump_dmat4;
	using glm::highp_dmat2;
	using glm::highp_dmat3;
	using glm::highp_dmat4;
	using glm::dmat2;
	using glm::dmat3;
	using glm::dmat4;
	using glm::lowp_f64mat2;
	using glm::lowp_f64mat3;
	using glm::lowp_f64mat4;
	using glm::mediump_f64mat2;
	using glm::mediump_f64mat3;
	using glm::mediump_f64mat4;
	using glm::highp_f64mat2;
	using glm::highp_f64mat3;
	using glm::highp_f64mat4;
	using glm::f64mat2;
	using glm::f64mat3;
	using glm::f64mat4;
	using glm::lowp_mat2x2;
	using glm::lowp_mat2x3;
	using glm::lowp_mat2x4;
	using glm::lowp_mat3x2;
	using glm::lowp_mat3x3;
	using glm::lowp_mat3x4;
	using glm::lowp_mat4x2;
	using glm::lowp_mat4x3;
	using glm::lowp_mat4x4;
	using glm::mediump_mat2x2;
	using glm::mediump_mat2x3;
	using glm::mediump_mat2x4;
	using glm::mediump_mat3x2;
	using glm::mediump_mat3x3;
	using glm::mediump_mat3x4;
	using glm::mediump_mat4x2;
	using glm::mediump_mat4x3;
	using glm::mediump_mat4x4;
	using glm::highp_mat2x2;
	using glm::highp_mat2x3;
	using glm::highp_mat2x4;
	using glm::highp_mat3x2;
	using glm::highp_mat3x3;
	using glm::highp_mat3x4;
	using glm::highp_mat4x2;
	using glm::highp_mat4x3;
	using glm::highp_mat4x4;
	using glm::mat2x2;
	using glm::mat2x3;
	using glm::mat2x4;
	using glm::mat3x2;
	using glm::mat3x3;
	using glm::mat3x4;
	using glm::mat4x2;
	using glm::mat4x3;
	using glm::mat4x4;
	using glm::lowp_fmat2x2;
	using glm::lowp_fmat2x3;
	using glm::lowp_fmat2x4;
	using glm::lowp_fmat3x2;
	using glm::lowp_fmat3x3;
	using glm::lowp_fmat3x4;
	using glm::lowp_fmat4x2;
	using glm::lowp_fmat4x3;
	using glm::lowp_fmat4x4;
	using glm::mediump_fmat2x2;
	using glm::mediump_fmat2x3;
	using glm::mediump_fmat2x4;
	using glm::mediump_fmat3x2;
	using glm::mediump_fmat3x3;
	using glm::mediump_fmat3x4;
	using glm::mediump_fmat4x2;
	using glm::mediump_fmat4x3;
	using glm::mediump_fmat4x4;
	using glm::highp_fmat2x2;
	using glm::highp_fmat2x3;
	using glm::highp_fmat2x4;
	using glm::highp_fmat3x2;
	using glm::highp_fmat3x3;
	using glm::highp_fmat3x4;
	using glm::highp_fmat4x2;
	using glm::highp_fmat4x3;
	using glm::highp_fmat4x4;
	using glm::fmat2x2;
	using glm::fmat2x3;
	using glm::fmat2x4;
	using glm::fmat3x2;
	using glm::fmat3x3;
	using glm::fmat3x4;
	using glm::fmat4x2;
	using glm::fmat4x3;
	using glm::fmat4x4;
	using glm::lowp_f32mat2x2;
	using glm::lowp_f32mat2x3;
	using glm::lowp_f32mat2x4;
	using glm::lowp_f32mat3x2;
	using glm::lowp_f32mat3x3;
	using glm::lowp_f32mat3x4;
	using glm::lowp_f32mat4x2;
	using glm::lowp_f32mat4x3;
	using glm::lowp_f32mat4x4;

	using glm::mediump_f32mat2x2;
	using glm::mediump_f32mat2x3;
	using glm::mediump_f32mat2x4;
	using glm::mediump_f32mat3x2;
	using glm::mediump_f32mat3x3;
	using glm::mediump_f32mat3x4;
	using glm::mediump_f32mat4x2;
	using glm::mediump_f32mat4x3;
	using glm::mediump_f32mat4x4;
	using glm::highp_f32mat2x2;
	using glm::highp_f32mat2x3;
	using glm::highp_f32mat2x4;
	using glm::highp_f32mat3x2;
	using glm::highp_f32mat3x3;
	using glm::highp_f32mat3x4;
	using glm::highp_f32mat4x2;
	using glm::highp_f32mat4x3;
	using glm::highp_f32mat4x4;
	using glm::f32mat2x2;
	using glm::f32mat2x3;
	using glm::f32mat2x4;
	using glm::f32mat3x2;
	using glm::f32mat3x3;
	using glm::f32mat3x4;
	using glm::f32mat4x2;
	using glm::f32mat4x3;
	using glm::f32mat4x4;
	using glm::lowp_dmat2x2;
	using glm::lowp_dmat2x3;
	using glm::lowp_dmat2x4;
	using glm::lowp_dmat3x2;
	using glm::lowp_dmat3x3;
	using glm::lowp_dmat3x4;
	using glm::lowp_dmat4x2;
	using glm::lowp_dmat4x3;
	using glm::lowp_dmat4x4;
	using glm::mediump_dmat2x2;
	using glm::mediump_dmat2x3;
	using glm::mediump_dmat2x4;
	using glm::mediump_dmat3x2;
	using glm::mediump_dmat3x3;
	using glm::mediump_dmat3x4;
	using glm::mediump_dmat4x2;
	using glm::mediump_dmat4x3;
	using glm::mediump_dmat4x4;
	using glm::highp_dmat2x2;
	using glm::highp_dmat2x3;
	using glm::highp_dmat2x4;
	using glm::highp_dmat3x2;
	using glm::highp_dmat3x3;
	using glm::highp_dmat3x4;
	using glm::highp_dmat4x2;
	using glm::highp_dmat4x3;
	using glm::highp_dmat4x4;
	using glm::dmat2x2;
	using glm::dmat2x3;
	using glm::dmat2x4;
	using glm::dmat3x2;
	using glm::dmat3x3;
	using glm::dmat3x4;
	using glm::dmat4x2;
	using glm::dmat4x3;
	using glm::dmat4x4;
	using glm::lowp_f64mat2x2;
	using glm::lowp_f64mat2x3;
	using glm::lowp_f64mat2x4;
	using glm::lowp_f64mat3x2;
	using glm::lowp_f64mat3x3;
	using glm::lowp_f64mat3x4;
	using glm::lowp_f64mat4x2;
	using glm::lowp_f64mat4x3;
	using glm::lowp_f64mat4x4;
	using glm::mediump_f64mat2x2;
	using glm::mediump_f64mat2x3;
	using glm::mediump_f64mat2x4;
	using glm::mediump_f64mat3x2;
	using glm::mediump_f64mat3x3;
	using glm::mediump_f64mat3x4;
	using glm::mediump_f64mat4x2;
	using glm::mediump_f64mat4x3;
	using glm::mediump_f64mat4x4;
	using glm::highp_f64mat2x2;
	using glm::highp_f64mat2x3;
	using glm::highp_f64mat2x4;
	using glm::highp_f64mat3x2;
	using glm::highp_f64mat3x3;
	using glm::highp_f64mat3x4;
	using glm::highp_f64mat4x2;
	using glm::highp_f64mat4x3;
	using glm::highp_f64mat4x4;
	using glm::f64mat2x2;
	using glm::f64mat2x3;
	using glm::f64mat2x4;
	using glm::f64mat3x2;
	using glm::f64mat3x3;
	using glm::f64mat3x4;
	using glm::f64mat4x2;
	using glm::f64mat4x3;
	using glm::f64mat4x4;
	using glm::lowp_imat2x2;
	using glm::lowp_imat2x3;
	using glm::lowp_imat2x4;
	using glm::lowp_imat3x2;
	using glm::lowp_imat3x3;
	using glm::lowp_imat3x4;
	using glm::lowp_imat4x2;
	using glm::lowp_imat4x3;
	using glm::lowp_imat4x4;
	using glm::mediump_imat2x2;
	using glm::mediump_imat2x3;
	using glm::mediump_imat2x4;
	using glm::mediump_imat3x2;
	using glm::mediump_imat3x3;
	using glm::mediump_imat3x4;
	using glm::mediump_imat4x2;
	using glm::mediump_imat4x3;
	using glm::mediump_imat4x4;
	using glm::highp_imat2x2;
	using glm::highp_imat2x3;
	using glm::highp_imat2x4;
	using glm::highp_imat3x2;
	using glm::highp_imat3x3;
	using glm::highp_imat3x4;
	using glm::highp_imat4x2;
	using glm::highp_imat4x3;
	using glm::highp_imat4x4;
	using glm::imat2x2;
	using glm::imat2x3;
	using glm::imat2x4;
	using glm::imat3x2;
	using glm::imat3x3;
	using glm::imat3x4;
	using glm::imat4x2;
	using glm::imat4x3;
	using glm::imat4x4;
	using glm::lowp_i8mat2x2;
	using glm::lowp_i8mat2x3;
	using glm::lowp_i8mat2x4;
	using glm::lowp_i8mat3x2;
	using glm::lowp_i8mat3x3;
	using glm::lowp_i8mat3x4;
	using glm::lowp_i8mat4x2;
	using glm::lowp_i8mat4x3;
	using glm::lowp_i8mat4x4;
	using glm::mediump_i8mat2x2;
	using glm::mediump_i8mat2x3;
	using glm::mediump_i8mat2x4;
	using glm::mediump_i8mat3x2;
	using glm::mediump_i8mat3x3;
	using glm::mediump_i8mat3x4;
	using glm::mediump_i8mat4x2;
	using glm::mediump_i8mat4x3;
	using glm::mediump_i8mat4x4;
	using glm::highp_i8mat2x2;
	using glm::highp_i8mat2x3;
	using glm::highp_i8mat2x4;
	using glm::highp_i8mat3x2;
	using glm::highp_i8mat3x3;
	using glm::highp_i8mat3x4;
	using glm::highp_i8mat4x2;
	using glm::highp_i8mat4x3;
	using glm::highp_i8mat4x4;
	using glm::i8mat2x2;
	using glm::i8mat2x3;
	using glm::i8mat2x4;
	using glm::i8mat3x2;
	using glm::i8mat3x3;
	using glm::i8mat3x4;
	using glm::i8mat4x2;
	using glm::i8mat4x3;
	using glm::i8mat4x4;
	using glm::lowp_i16mat2x2;
	using glm::lowp_i16mat2x3;
	using glm::lowp_i16mat2x4;
	using glm::lowp_i16mat3x2;
	using glm::lowp_i16mat3x3;
	using glm::lowp_i16mat3x4;
	using glm::lowp_i16mat4x2;
	using glm::lowp_i16mat4x3;
	using glm::lowp_i16mat4x4;
	using glm::mediump_i16mat2x2;
	using glm::mediump_i16mat2x3;
	using glm::mediump_i16mat2x4;
	using glm::mediump_i16mat3x2;
	using glm::mediump_i16mat3x3;
	using glm::mediump_i16mat3x4;
	using glm::mediump_i16mat4x2;
	using glm::mediump_i16mat4x3;
	using glm::mediump_i16mat4x4;
	using glm::highp_i16mat2x2;
	using glm::highp_i16mat2x3;
	using glm::highp_i16mat2x4;
	using glm::highp_i16mat3x2;
	using glm::highp_i16mat3x3;
	using glm::highp_i16mat3x4;
	using glm::highp_i16mat4x2;
	using glm::highp_i16mat4x3;
	using glm::highp_i16mat4x4;
	using glm::i16mat2x2;
	using glm::i16mat2x3;
	using glm::i16mat2x4;
	using glm::i16mat3x2;
	using glm::i16mat3x3;
	using glm::i16mat3x4;
	using glm::i16mat4x2;
	using glm::i16mat4x3;
	using glm::i16mat4x4;
	using glm::lowp_i32mat2x2;
	using glm::lowp_i32mat2x3;
	using glm::lowp_i32mat2x4;
	using glm::lowp_i32mat3x2;
	using glm::lowp_i32mat3x3;
	using glm::lowp_i32mat3x4;
	using glm::lowp_i32mat4x2;
	using glm::lowp_i32mat4x3;
	using glm::lowp_i32mat4x4;
	using glm::mediump_i32mat2x2;
	using glm::mediump_i32mat2x3;
	using glm::mediump_i32mat2x4;
	using glm::mediump_i32mat3x2;
	using glm::mediump_i32mat3x3;
	using glm::mediump_i32mat3x4;
	using glm::mediump_i32mat4x2;
	using glm::mediump_i32mat4x3;
	using glm::mediump_i32mat4x4;
	using glm::highp_i32mat2x2;
	using glm::highp_i32mat2x3;
	using glm::highp_i32mat2x4;
	using glm::highp_i32mat3x2;
	using glm::highp_i32mat3x3;
	using glm::highp_i32mat3x4;
	using glm::highp_i32mat4x2;
	using glm::highp_i32mat4x3;
	using glm::highp_i32mat4x4;
	using glm::i32mat2x2;
	using glm::i32mat2x3;
	using glm::i32mat2x4;
	using glm::i32mat3x2;
	using glm::i32mat3x3;
	using glm::i32mat3x4;
	using glm::i32mat4x2;
	using glm::i32mat4x3;
	using glm::i32mat4x4;
	using glm::lowp_i64mat2x2;
	using glm::lowp_i64mat2x3;
	using glm::lowp_i64mat2x4;
	using glm::lowp_i64mat3x2;
	using glm::lowp_i64mat3x3;
	using glm::lowp_i64mat3x4;
	using glm::lowp_i64mat4x2;
	using glm::lowp_i64mat4x3;
	using glm::lowp_i64mat4x4;
	using glm::mediump_i64mat2x2;
	using glm::mediump_i64mat2x3;
	using glm::mediump_i64mat2x4;
	using glm::mediump_i64mat3x2;
	using glm::mediump_i64mat3x3;
	using glm::mediump_i64mat3x4;
	using glm::mediump_i64mat4x2;
	using glm::mediump_i64mat4x3;
	using glm::mediump_i64mat4x4;
	using glm::highp_i64mat2x2;
	using glm::highp_i64mat2x3;
	using glm::highp_i64mat2x4;
	using glm::highp_i64mat3x2;
	using glm::highp_i64mat3x3;
	using glm::highp_i64mat3x4;
	using glm::highp_i64mat4x2;
	using glm::highp_i64mat4x3;
	using glm::highp_i64mat4x4;
	using glm::i64mat2x2;
	using glm::i64mat2x3;
	using glm::i64mat2x4;
	using glm::i64mat3x2;
	using glm::i64mat3x3;
	using glm::i64mat3x4;
	using glm::i64mat4x2;
	using glm::i64mat4x3;
	using glm::i64mat4x4;
	using glm::lowp_umat2x2;
	using glm::lowp_umat2x3;
	using glm::lowp_umat2x4;
	using glm::lowp_umat3x2;
	using glm::lowp_umat3x3;
	using glm::lowp_umat3x4;
	using glm::lowp_umat4x2;
	using glm::lowp_umat4x3;
	using glm::lowp_umat4x4;
	using glm::mediump_umat2x2;
	using glm::mediump_umat2x3;
	using glm::mediump_umat2x4;
	using glm::mediump_umat3x2;
	using glm::mediump_umat3x3;
	using glm::mediump_umat3x4;
	using glm::mediump_umat4x2;
	using glm::mediump_umat4x3;
	using glm::mediump_umat4x4;
	using glm::highp_umat2x2;
	using glm::highp_umat2x3;
	using glm::highp_umat2x4;
	using glm::highp_umat3x2;
	using glm::highp_umat3x3;
	using glm::highp_umat3x4;
	using glm::highp_umat4x2;
	using glm::highp_umat4x3;
	using glm::highp_umat4x4;
	using glm::umat2x2;
	using glm::umat2x3;
	using glm::umat2x4;
	using glm::umat3x2;
	using glm::umat3x3;
	using glm::umat3x4;
	using glm::umat4x2;
	using glm::umat4x3;
	using glm::umat4x4;
	using glm::lowp_u8mat2x2;
	using glm::lowp_u8mat2x3;
	using glm::lowp_u8mat2x4;
	using glm::lowp_u8mat3x2;
	using glm::lowp_u8mat3x3;
	using glm::lowp_u8mat3x4;
	using glm::lowp_u8mat4x2;
	using glm::lowp_u8mat4x3;
	using glm::lowp_u8mat4x4;
	using glm::mediump_u8mat2x2;
	using glm::mediump_u8mat2x3;
	using glm::mediump_u8mat2x4;
	using glm::mediump_u8mat3x2;
	using glm::mediump_u8mat3x3;
	using glm::mediump_u8mat3x4;
	using glm::mediump_u8mat4x2;
	using glm::mediump_u8mat4x3;
	using glm::mediump_u8mat4x4;
	using glm::highp_u8mat2x2;
	using glm::highp_u8mat2x3;
	using glm::highp_u8mat2x4;
	using glm::highp_u8mat3x2;
	using glm::highp_u8mat3x3;
	using glm::highp_u8mat3x4;
	using glm::highp_u8mat4x2;
	using glm::highp_u8mat4x3;
	using glm::highp_u8mat4x4;
	using glm::u8mat2x2;
	using glm::u8mat2x3;
	using glm::u8mat2x4;
	using glm::u8mat3x2;
	using glm::u8mat3x3;
	using glm::u8mat3x4;
	using glm::u8mat4x2;
	using glm::u8mat4x3;
	using glm::u8mat4x4;
	using glm::lowp_u16mat2x2;
	using glm::lowp_u16mat2x3;
	using glm::lowp_u16mat2x4;
	using glm::lowp_u16mat3x2;
	using glm::lowp_u16mat3x3;
	using glm::lowp_u16mat3x4;
	using glm::lowp_u16mat4x2;
	using glm::lowp_u16mat4x3;
	using glm::lowp_u16mat4x4;
	using glm::mediump_u16mat2x2;
	using glm::mediump_u16mat2x3;
	using glm::mediump_u16mat2x4;
	using glm::mediump_u16mat3x2;
	using glm::mediump_u16mat3x3;
	using glm::mediump_u16mat3x4;
	using glm::mediump_u16mat4x2;
	using glm::mediump_u16mat4x3;
	using glm::mediump_u16mat4x4;
	using glm::highp_u16mat2x2;
	using glm::highp_u16mat2x3;
	using glm::highp_u16mat2x4;
	using glm::highp_u16mat3x2;
	using glm::highp_u16mat3x3;
	using glm::highp_u16mat3x4;
	using glm::highp_u16mat4x2;
	using glm::highp_u16mat4x3;
	using glm::highp_u16mat4x4;
	using glm::u16mat2x2;
	using glm::u16mat2x3;
	using glm::u16mat2x4;
	using glm::u16mat3x2;
	using glm::u16mat3x3;
	using glm::u16mat3x4;
	using glm::u16mat4x2;
	using glm::u16mat4x3;
	using glm::u16mat4x4;
	using glm::lowp_u32mat2x2;
	using glm::lowp_u32mat2x3;
	using glm::lowp_u32mat2x4;
	using glm::lowp_u32mat3x2;
	using glm::lowp_u32mat3x3;
	using glm::lowp_u32mat3x4;
	using glm::lowp_u32mat4x2;
	using glm::lowp_u32mat4x3;
	using glm::lowp_u32mat4x4;
	using glm::mediump_u32mat2x2;
	using glm::mediump_u32mat2x3;
	using glm::mediump_u32mat2x4;
	using glm::mediump_u32mat3x2;
	using glm::mediump_u32mat3x3;
	using glm::mediump_u32mat3x4;
	using glm::mediump_u32mat4x2;
	using glm::mediump_u32mat4x3;
	using glm::mediump_u32mat4x4;
	using glm::highp_u32mat2x2;
	using glm::highp_u32mat2x3;
	using glm::highp_u32mat2x4;
	using glm::highp_u32mat3x2;
	using glm::highp_u32mat3x3;
	using glm::highp_u32mat3x4;
	using glm::highp_u32mat4x2;
	using glm::highp_u32mat4x3;
	using glm::highp_u32mat4x4;
	using glm::u32mat2x2;
	using glm::u32mat2x3;
	using glm::u32mat2x4;
	using glm::u32mat3x2;
	using glm::u32mat3x3;
	using glm::u32mat3x4;
	using glm::u32mat4x2;
	using glm::u32mat4x3;
	using glm::u32mat4x4;
	using glm::lowp_u64mat2x2;
	using glm::lowp_u64mat2x3;
	using glm::lowp_u64mat2x4;
	using glm::lowp_u64mat3x2;
	using glm::lowp_u64mat3x3;
	using glm::lowp_u64mat3x4;
	using glm::lowp_u64mat4x2;
	using glm::lowp_u64mat4x3;
	using glm::lowp_u64mat4x4;
	using glm::mediump_u64mat2x2;
	using glm::mediump_u64mat2x3;
	using glm::mediump_u64mat2x4;
	using glm::mediump_u64mat3x2;
	using glm::mediump_u64mat3x3;
	using glm::mediump_u64mat3x4;
	using glm::mediump_u64mat4x2;
	using glm::mediump_u64mat4x3;
	using glm::mediump_u64mat4x4;
	using glm::highp_u64mat2x2;
	using glm::highp_u64mat2x3;
	using glm::highp_u64mat2x4;
	using glm::highp_u64mat3x2;
	using glm::highp_u64mat3x3;
	using glm::highp_u64mat3x4;
	using glm::highp_u64mat4x2;
	using glm::highp_u64mat4x3;
	using glm::highp_u64mat4x4;
	using glm::u64mat2x2;
	using glm::u64mat2x3;
	using glm::u64mat2x4;
	using glm::u64mat3x2;
	using glm::u64mat3x3;
	using glm::u64mat3x4;
	using glm::u64mat4x2;
	using glm::u64mat4x3;
	using glm::u64mat4x4;
	using glm::lowp_quat;
	using glm::mediump_quat;
	using glm::highp_quat;
	using glm::quat;
	using glm::lowp_fquat;
	using glm::mediump_fquat;
	using glm::highp_fquat;
	using glm::fquat;
	using glm::lowp_f32quat;
	using glm::mediump_f32quat;
	using glm::highp_f32quat;
	using glm::f32quat;
	using glm::lowp_dquat;
	using glm::mediump_dquat;
	using glm::highp_dquat;
	using glm::dquat;
	using glm::lowp_f64quat;
	using glm::mediump_f64quat;
	using glm::highp_f64quat;
	using glm::f64quat;

	// Operators
	using glm::operator+;
	using glm::operator-;
	using glm::operator*;
	using glm::operator/;
	using glm::operator%;
	using glm::operator^;
	using glm::operator&;
	using glm::operator|;
	using glm::operator~;
	using glm::operator<<;
	using glm::operator>>;
	using glm::operator==;
	using glm::operator!=;
	using glm::operator&&;
	using glm::operator||;

	// Core functions
	using glm::abs;
	using glm::acos;
	using glm::acosh;
	using glm::all;
	using glm::any;
	using glm::asin;
	using glm::asinh;
	using glm::atan;
	using glm::atanh;
	using glm::bitCount;
	using glm::bitfieldExtract;
	using glm::bitfieldInsert;
	using glm::bitfieldReverse;
	using glm::ceil;
	using glm::clamp;
	using glm::cos;
	using glm::cosh;
	using glm::cross;
	using glm::degrees;
	using glm::determinant;
	using glm::distance;
	using glm::dot;
	using glm::equal;
	using glm::exp;
	using glm::exp2;
	using glm::faceforward;
	using glm::findLSB;
	using glm::findMSB;
	using glm::floatBitsToInt;
	using glm::floatBitsToUint;
	using glm::floor;
	using glm::fma;
	using glm::fract;
	using glm::frexp;
	using glm::greaterThan;
	using glm::greaterThanEqual;
	using glm::imulExtended;
	using glm::intBitsToFloat;
	using glm::inverse;
	using glm::inversesqrt;
	using glm::isinf;
	using glm::isnan;
	using glm::ldexp;
	using glm::length;
	using glm::lessThan;
	using glm::lessThanEqual;
	using glm::log;
	using glm::log2;
	using glm::matrixCompMult;
	using glm::max;
	using glm::min;
	using glm::mix;
	using glm::mod;
	using glm::modf;
	using glm::normalize;
	using glm::notEqual;
	using glm::not_;
	using glm::outerProduct;
	using glm::packDouble2x32;
	using glm::packHalf2x16;
	using glm::packSnorm2x16;
	using glm::packSnorm4x8;
	using glm::packUnorm2x16;
	using glm::packUnorm4x8;
	using glm::pow;
	using glm::radians;
	using glm::reflect;
	using glm::refract;
	using glm::round;
	using glm::roundEven;
	using glm::sign;
	using glm::sin;
	using glm::sinh;
	using glm::smoothstep;
	using glm::sqrt;
	using glm::step;
	using glm::tan;
	using glm::tanh;
	using glm::transpose;
	using glm::trunc;
	using glm::uaddCarry;
	using glm::uintBitsToFloat;
	using glm::umulExtended;
	using glm::unpackDouble2x32;
	using glm::unpackHalf2x16;
	using glm::unpackSnorm2x16;
	using glm::unpackSnorm4x8;
	using glm::unpackUnorm2x16;
	using glm::unpackUnorm4x8;
	using glm::usubBorrow;

#   ifdef GLM_GTC_INLINE_NAMESPACE
	inline
#   endif
	namespace gtc {
#       if GLM_CONFIG_ALIGNED_GENTYPES == GLM_ENABLE
		using glm::aligned_highp_vec1;
		using glm::aligned_mediump_vec1;
		using glm::aligned_lowp_vec1;
		using glm::aligned_highp_dvec1;
		using glm::aligned_mediump_dvec1;
		using glm::aligned_lowp_dvec1;
		using glm::aligned_highp_ivec1;
		using glm::aligned_mediump_ivec1;
		using glm::aligned_lowp_ivec1;
		using glm::aligned_highp_uvec1;
		using glm::aligned_mediump_uvec1;
		using glm::aligned_lowp_uvec1;
		using glm::aligned_highp_bvec1;
		using glm::aligned_mediump_bvec1;
		using glm::aligned_lowp_bvec1;
		using glm::packed_highp_vec1;
		using glm::packed_mediump_vec1;
		using glm::packed_lowp_vec1;
		using glm::packed_highp_dvec1;
		using glm::packed_mediump_dvec1;
		using glm::packed_lowp_dvec1;
		using glm::packed_highp_ivec1;
		using glm::packed_mediump_ivec1;
		using glm::packed_lowp_ivec1;
		using glm::packed_highp_uvec1;
		using glm::packed_mediump_uvec1;
		using glm::packed_lowp_uvec1;
		using glm::packed_highp_bvec1;
		using glm::packed_mediump_bvec1;
		using glm::packed_lowp_bvec1;
		using glm::aligned_highp_vec2;
		using glm::aligned_mediump_vec2;
		using glm::aligned_lowp_vec2;
		using glm::aligned_highp_dvec2;
		using glm::aligned_mediump_dvec2;
		using glm::aligned_lowp_dvec2;
		using glm::aligned_highp_ivec2;
		using glm::aligned_mediump_ivec2;
		using glm::aligned_lowp_ivec2;
		using glm::aligned_highp_uvec2;
		using glm::aligned_mediump_uvec2;
		using glm::aligned_lowp_uvec2;
		using glm::aligned_highp_bvec2;
		using glm::aligned_mediump_bvec2;
		using glm::aligned_lowp_bvec2;
		using glm::packed_highp_vec2;
		using glm::packed_mediump_vec2;
		using glm::packed_lowp_vec2;
		using glm::packed_highp_dvec2;
		using glm::packed_mediump_dvec2;
		using glm::packed_lowp_dvec2;
		using glm::packed_highp_ivec2;
		using glm::packed_mediump_ivec2;
		using glm::packed_lowp_ivec2;
		using glm::packed_highp_uvec2;
		using glm::packed_mediump_uvec2;
		using glm::packed_lowp_uvec2;
		using glm::packed_highp_bvec2;
		using glm::packed_mediump_bvec2;
		using glm::packed_lowp_bvec2;
		using glm::aligned_highp_vec3;
		using glm::aligned_mediump_vec3;
		using glm::aligned_lowp_vec3;
		using glm::aligned_highp_dvec3;
		using glm::aligned_mediump_dvec3;
		using glm::aligned_lowp_dvec3;
		using glm::aligned_highp_ivec3;
		using glm::aligned_mediump_ivec3;
		using glm::aligned_lowp_ivec3;
		using glm::aligned_highp_uvec3;
		using glm::aligned_mediump_uvec3;
		using glm::aligned_lowp_uvec3;
		using glm::aligned_highp_bvec3;
		using glm::aligned_mediump_bvec3;
		using glm::aligned_lowp_bvec3;
		using glm::packed_highp_vec3;
		using glm::packed_mediump_vec3;
		using glm::packed_lowp_vec3;
		using glm::packed_highp_dvec3;
		using glm::packed_mediump_dvec3;
		using glm::packed_lowp_dvec3;
		using glm::packed_highp_ivec3;
		using glm::packed_mediump_ivec3;
		using glm::packed_lowp_ivec3;
		using glm::packed_highp_uvec3;
		using glm::packed_mediump_uvec3;
		using glm::packed_lowp_uvec3;
		using glm::packed_highp_bvec3;
		using glm::packed_mediump_bvec3;
		using glm::packed_lowp_bvec3;
		using glm::aligned_highp_vec4;
		using glm::aligned_mediump_vec4;
		using glm::aligned_lowp_vec4;
		using glm::aligned_highp_dvec4;
		using glm::aligned_mediump_dvec4;
		using glm::aligned_lowp_dvec4;
		using glm::aligned_highp_ivec4;
		using glm::aligned_mediump_ivec4;
		using glm::aligned_lowp_ivec4;
		using glm::aligned_highp_uvec4;
		using glm::aligned_mediump_uvec4;
		using glm::aligned_lowp_uvec4;
		using glm::aligned_highp_bvec4;
		using glm::aligned_mediump_bvec4;
		using glm::aligned_lowp_bvec4;
		using glm::packed_highp_vec4;
		using glm::packed_mediump_vec4;
		using glm::packed_lowp_vec4;
		using glm::packed_highp_dvec4;
		using glm::packed_mediump_dvec4;
		using glm::packed_lowp_dvec4;
		using glm::packed_highp_ivec4;
		using glm::packed_mediump_ivec4;
		using glm::packed_lowp_ivec4;
		using glm::packed_highp_uvec4;
		using glm::packed_mediump_uvec4;
		using glm::packed_lowp_uvec4;
		using glm::packed_highp_bvec4;
		using glm::packed_mediump_bvec4;
		using glm::packed_lowp_bvec4;
		using glm::aligned_highp_mat2;
		using glm::aligned_mediump_mat2;
		using glm::aligned_lowp_mat2;
		using glm::aligned_highp_dmat2;
		using glm::aligned_mediump_dmat2;
		using glm::aligned_lowp_dmat2;
		using glm::packed_highp_mat2;
		using glm::packed_mediump_mat2;
		using glm::packed_lowp_mat2;
		using glm::packed_highp_dmat2;
		using glm::packed_mediump_dmat2;
		using glm::packed_lowp_dmat2;
		using glm::aligned_highp_mat3;
		using glm::aligned_mediump_mat3;
		using glm::aligned_lowp_mat3;
		using glm::aligned_highp_dmat3;
		using glm::aligned_mediump_dmat3;
		using glm::aligned_lowp_dmat3;
		using glm::packed_highp_mat3;
		using glm::packed_mediump_mat3;
		using glm::packed_lowp_mat3;
		using glm::packed_highp_dmat3;
		using glm::packed_mediump_dmat3;
		using glm::packed_lowp_dmat3;
		using glm::aligned_highp_mat4;
		using glm::aligned_mediump_mat4;
		using glm::aligned_lowp_mat4;
		using glm::aligned_highp_dmat4;
		using glm::aligned_mediump_dmat4;
		using glm::aligned_lowp_dmat4;
		using glm::packed_highp_mat4;
		using glm::packed_mediump_mat4;
		using glm::packed_lowp_mat4;
		using glm::packed_highp_dmat4;
		using glm::packed_mediump_dmat4;
		using glm::packed_lowp_dmat4;
		using glm::aligned_highp_mat2x2;
		using glm::aligned_mediump_mat2x2;
		using glm::aligned_lowp_mat2x2;
		using glm::aligned_highp_dmat2x2;
		using glm::aligned_mediump_dmat2x2;
		using glm::aligned_lowp_dmat2x2;
		using glm::packed_highp_mat2x2;
		using glm::packed_mediump_mat2x2;
		using glm::packed_lowp_mat2x2;
		using glm::packed_highp_dmat2x2;
		using glm::packed_mediump_dmat2x2;
		using glm::packed_lowp_dmat2x2;
		using glm::aligned_highp_mat2x3;
		using glm::aligned_mediump_mat2x3;
		using glm::aligned_lowp_mat2x3;
		using glm::aligned_highp_dmat2x3;
		using glm::aligned_mediump_dmat2x3;
		using glm::aligned_lowp_dmat2x3;
		using glm::packed_highp_mat2x3;
		using glm::packed_mediump_mat2x3;
		using glm::packed_lowp_mat2x3;
		using glm::packed_highp_dmat2x3;
		using glm::packed_mediump_dmat2x3;
		using glm::packed_lowp_dmat2x3;
		using glm::aligned_highp_mat2x4;
		using glm::aligned_mediump_mat2x4;
		using glm::aligned_lowp_mat2x4;
		using glm::aligned_highp_dmat2x4;
		using glm::aligned_mediump_dmat2x4;
		using glm::aligned_lowp_dmat2x4;
		using glm::packed_highp_mat2x4;
		using glm::packed_mediump_mat2x4;
		using glm::packed_lowp_mat2x4;
		using glm::packed_highp_dmat2x4;
		using glm::packed_mediump_dmat2x4;
		using glm::packed_lowp_dmat2x4;
		using glm::aligned_highp_mat3x2;
		using glm::aligned_mediump_mat3x2;
		using glm::aligned_lowp_mat3x2;
		using glm::aligned_highp_dmat3x2;
		using glm::aligned_mediump_dmat3x2;
		using glm::aligned_lowp_dmat3x2;
		using glm::packed_highp_mat3x2;
		using glm::packed_mediump_mat3x2;
		using glm::packed_lowp_mat3x2;
		using glm::packed_highp_dmat3x2;
		using glm::packed_mediump_dmat3x2;
		using glm::packed_lowp_dmat3x2;
		using glm::aligned_highp_mat3x3;
		using glm::aligned_mediump_mat3x3;
		using glm::aligned_lowp_mat3x3;
		using glm::aligned_highp_dmat3x3;
		using glm::aligned_mediump_dmat3x3;
		using glm::aligned_lowp_dmat3x3;
		using glm::packed_highp_mat3x3;
		using glm::packed_mediump_mat3x3;
		using glm::packed_lowp_mat3x3;
		using glm::packed_highp_dmat3x3;
		using glm::packed_mediump_dmat3x3;
		using glm::packed_lowp_dmat3x3;
		using glm::aligned_highp_mat3x4;
		using glm::aligned_mediump_mat3x4;
		using glm::aligned_lowp_mat3x4;
		using glm::aligned_highp_dmat3x4;
		using glm::aligned_mediump_dmat3x4;
		using glm::aligned_lowp_dmat3x4;
		using glm::packed_highp_mat3x4;
		using glm::packed_mediump_mat3x4;
		using glm::packed_lowp_mat3x4;
		using glm::packed_highp_dmat3x4;
		using glm::packed_mediump_dmat3x4;
		using glm::packed_lowp_dmat3x4;
		using glm::aligned_highp_mat4x2;
		using glm::aligned_mediump_mat4x2;
		using glm::aligned_lowp_mat4x2;
		using glm::aligned_highp_dmat4x2;
		using glm::aligned_mediump_dmat4x2;
		using glm::aligned_lowp_dmat4x2;
		using glm::packed_highp_mat4x2;
		using glm::packed_mediump_mat4x2;
		using glm::packed_lowp_mat4x2;
		using glm::packed_highp_dmat4x2;
		using glm::packed_mediump_dmat4x2;
		using glm::packed_lowp_dmat4x2;
		using glm::aligned_highp_mat4x3;
		using glm::aligned_mediump_mat4x3;
		using glm::aligned_lowp_mat4x3;
		using glm::aligned_highp_dmat4x3;
		using glm::aligned_mediump_dmat4x3;
		using glm::aligned_lowp_dmat4x3;
		using glm::packed_highp_mat4x3;
		using glm::packed_mediump_mat4x3;
		using glm::packed_lowp_mat4x3;
		using glm::packed_highp_dmat4x3;
		using glm::packed_mediump_dmat4x3;
		using glm::packed_lowp_dmat4x3;
		using glm::aligned_highp_mat4x4;
		using glm::aligned_mediump_mat4x4;
		using glm::aligned_lowp_mat4x4;
		using glm::aligned_highp_dmat4x4;
		using glm::aligned_mediump_dmat4x4;
		using glm::aligned_lowp_dmat4x4;
		using glm::packed_highp_mat4x4;
		using glm::packed_mediump_mat4x4;
		using glm::packed_lowp_mat4x4;
		using glm::packed_highp_dmat4x4;
		using glm::packed_mediump_dmat4x4;
		using glm::packed_lowp_dmat4x4;
#       if(defined(GLM_PRECISION_LOWP_FLOAT))
		using glm::aligned_vec1;
		using glm::aligned_vec2;
		using glm::aligned_vec3;
		using glm::aligned_vec4;
		using glm::packed_vec1;
		using glm::packed_vec2;
		using glm::packed_vec3;
		using glm::packed_vec4;
		using glm::aligned_mat2;
		using glm::aligned_mat3;
		using glm::aligned_mat4;
		using glm::packed_mat2;
		using glm::packed_mat3;
		using glm::packed_mat4;
		using glm::aligned_mat2x2;
		using glm::aligned_mat2x3;
		using glm::aligned_mat2x4;
		using glm::aligned_mat3x2;
		using glm::aligned_mat3x3;
		using glm::aligned_mat3x4;
		using glm::aligned_mat4x2;
		using glm::aligned_mat4x3;
		using glm::aligned_mat4x4;
		using glm::packed_mat2x2;
		using glm::packed_mat2x3;
		using glm::packed_mat2x4;
		using glm::packed_mat3x2;
		using glm::packed_mat3x3;
		using glm::packed_mat3x4;
		using glm::packed_mat4x2;
		using glm::packed_mat4x3;
		using glm::packed_mat4x4;
#       elif(defined(GLM_PRECISION_MEDIUMP_FLOAT))
		using glm::aligned_vec1;
		using glm::aligned_vec2;
		using glm::aligned_vec3;
		using glm::aligned_vec4;
		using glm::packed_vec1;
		using glm::packed_vec2;
		using glm::packed_vec3;
		using glm::packed_vec4;
		using glm::aligned_mat2;
		using glm::aligned_mat3;
		using glm::aligned_mat4;
		using glm::packed_mat2;
		using glm::packed_mat3;
		using glm::packed_mat4;
		using glm::aligned_mat2x2;
		using glm::aligned_mat2x3;
		using glm::aligned_mat2x4;
		using glm::aligned_mat3x2;
		using glm::aligned_mat3x3;
		using glm::aligned_mat3x4;
		using glm::aligned_mat4x2;
		using glm::aligned_mat4x3;
		using glm::aligned_mat4x4;
		using glm::packed_mat2x2;
		using glm::packed_mat2x3;
		using glm::packed_mat2x4;
		using glm::packed_mat3x2;
		using glm::packed_mat3x3;
		using glm::packed_mat3x4;
		using glm::packed_mat4x2;
		using glm::packed_mat4x3;
		using glm::packed_mat4x4;
#       else //defined(GLM_PRECISION_HIGHP_FLOAT)
		using glm::aligned_vec1;
		using glm::aligned_vec2;
		using glm::aligned_vec3;
		using glm::aligned_vec4;
		using glm::packed_vec1;
		using glm::packed_vec2;
		using glm::packed_vec3;
		using glm::packed_vec4;
		using glm::aligned_mat2;
		using glm::aligned_mat3;
		using glm::aligned_mat4;
		using glm::packed_mat2;
		using glm::packed_mat3;
		using glm::packed_mat4;
		using glm::aligned_mat2x2;
		using glm::aligned_mat2x3;
		using glm::aligned_mat2x4;
		using glm::aligned_mat3x2;
		using glm::aligned_mat3x3;
		using glm::aligned_mat3x4;
		using glm::aligned_mat4x2;
		using glm::aligned_mat4x3;
		using glm::aligned_mat4x4;
		using glm::packed_mat2x2;
		using glm::packed_mat2x3;
		using glm::packed_mat2x4;
		using glm::packed_mat3x2;
		using glm::packed_mat3x3;
		using glm::packed_mat3x4;
		using glm::packed_mat4x2;
		using glm::packed_mat4x3;
		using glm::packed_mat4x4;
#       endif//GLM_PRECISION
#       if(defined(GLM_PRECISION_LOWP_DOUBLE))
		using glm::aligned_dvec1;
		using glm::aligned_dvec2;
		using glm::aligned_dvec3;
		using glm::aligned_dvec4;
		using glm::packed_dvec1;
		using glm::packed_dvec2;
		using glm::packed_dvec3;
		using glm::packed_dvec4;
		using glm::aligned_dmat2;
		using glm::aligned_dmat3;
		using glm::aligned_dmat4;
		using glm::packed_dmat2;
		using glm::packed_dmat3;
		using glm::packed_dmat4;
		using glm::aligned_dmat2x2;
		using glm::aligned_dmat2x3;
		using glm::aligned_dmat2x4;
		using glm::aligned_dmat3x2;
		using glm::aligned_dmat3x3;
		using glm::aligned_dmat3x4;
		using glm::aligned_dmat4x2;
		using glm::aligned_dmat4x3;
		using glm::aligned_dmat4x4;
		using glm::packed_dmat2x2;
		using glm::packed_dmat2x3;
		using glm::packed_dmat2x4;
		using glm::packed_dmat3x2;
		using glm::packed_dmat3x3;
		using glm::packed_dmat3x4;
		using glm::packed_dmat4x2;
		using glm::packed_dmat4x3;
		using glm::packed_dmat4x4;
#       elif(defined(GLM_PRECISION_MEDIUMP_DOUBLE))
		using glm::aligned_dvec1;
		using glm::aligned_dvec2;
		using glm::aligned_dvec3;
		using glm::aligned_dvec4;
		using glm::packed_dvec1;
		using glm::packed_dvec2;
		using glm::packed_dvec3;
		using glm::packed_dvec4;
		using glm::aligned_dmat2;
		using glm::aligned_dmat3;
		using glm::aligned_dmat4;
		using glm::packed_dmat2;
		using glm::packed_dmat3;
		using glm::packed_dmat4;
		using glm::aligned_dmat2x2;
		using glm::aligned_dmat2x3;
		using glm::aligned_dmat2x4;
		using glm::aligned_dmat3x2;
		using glm::aligned_dmat3x3;
		using glm::aligned_dmat3x4;
		using glm::aligned_dmat4x2;
		using glm::aligned_dmat4x3;
		using glm::aligned_dmat4x4;
		using glm::packed_dmat2x2;
		using glm::packed_dmat2x3;
		using glm::packed_dmat2x4;
		using glm::packed_dmat3x2;
		using glm::packed_dmat3x3;
		using glm::packed_dmat3x4;
		using glm::packed_dmat4x2;
		using glm::packed_dmat4x3;
		using glm::packed_dmat4x4;
#       else //defined(GLM_PRECISION_HIGHP_DOUBLE)
		using glm::aligned_dvec1;
		using glm::aligned_dvec2;
		using glm::aligned_dvec3;
		using glm::aligned_dvec4;
		using glm::packed_dvec1;
		using glm::packed_dvec2;
		using glm::packed_dvec3;
		using glm::packed_dvec4;
		using glm::aligned_dmat2;
		using glm::aligned_dmat3;
		using glm::aligned_dmat4;
		using glm::packed_dmat2;
		using glm::packed_dmat3;
		using glm::packed_dmat4;
		using glm::aligned_dmat2x2;
		using glm::aligned_dmat2x3;
		using glm::aligned_dmat2x4;
		using glm::aligned_dmat3x2;
		using glm::aligned_dmat3x3;
		using glm::aligned_dmat3x4;
		using glm::aligned_dmat4x2;
		using glm::aligned_dmat4x3;
		using glm::aligned_dmat4x4;
		using glm::packed_dmat2x2;
		using glm::packed_dmat2x3;
		using glm::packed_dmat2x4;
		using glm::packed_dmat3x2;
		using glm::packed_dmat3x3;
		using glm::packed_dmat3x4;
		using glm::packed_dmat4x2;
		using glm::packed_dmat4x3;
		using glm::packed_dmat4x4;
#       endif//GLM_PRECISION
#       if(defined(GLM_PRECISION_LOWP_INT))
		using glm::aligned_ivec1;
		using glm::aligned_ivec2;
		using glm::aligned_ivec3;
		using glm::aligned_ivec4;
#       elif(defined(GLM_PRECISION_MEDIUMP_INT))
		using glm::aligned_ivec1;
		using glm::aligned_ivec2;
		using glm::aligned_ivec3;
		using glm::aligned_ivec4;
#       else //defined(GLM_PRECISION_HIGHP_INT)
		using glm::aligned_ivec1;
		using glm::aligned_ivec2;
		using glm::aligned_ivec3;
		using glm::aligned_ivec4;
		using glm::packed_ivec1;
		using glm::packed_ivec2;
		using glm::packed_ivec3;
		using glm::packed_ivec4;
#       endif//GLM_PRECISION
#       if(defined(GLM_PRECISION_LOWP_UINT))
		using glm::aligned_uvec1;
		using glm::aligned_uvec2;
		using glm::aligned_uvec3;
		using glm::aligned_uvec4;
#       elif(defined(GLM_PRECISION_MEDIUMP_UINT))
		using glm::aligned_uvec1;
		using glm::aligned_uvec2;
		using glm::aligned_uvec3;
		using glm::aligned_uvec4;
#       else //defined(GLM_PRECISION_HIGHP_UINT)
		using glm::aligned_uvec1;
		using glm::aligned_uvec2;
		using glm::aligned_uvec3;
		using glm::aligned_uvec4;
		using glm::packed_uvec1;
		using glm::packed_uvec2;
		using glm::packed_uvec3;
		using glm::packed_uvec4;
#       endif//GLM_PRECISION
#       if(defined(GLM_PRECISION_LOWP_BOOL))
		using glm::aligned_bvec1;
		using glm::aligned_bvec2;
		using glm::aligned_bvec3;
		using glm::aligned_bvec4;
#       elif(defined(GLM_PRECISION_MEDIUMP_BOOL))
		using glm::aligned_bvec1;
		using glm::aligned_bvec2;
		using glm::aligned_bvec3;
		using glm::aligned_bvec4;
#       else //defined(GLM_PRECISION_HIGHP_BOOL)
		using glm::aligned_bvec1;
		using glm::aligned_bvec2;
		using glm::aligned_bvec3;
		using glm::aligned_bvec4;
		using glm::packed_bvec1;
		using glm::packed_bvec2;
		using glm::packed_bvec3;
		using glm::packed_bvec4;
#       endif//GLM_PRECISION
#       endif


		using glm::abs;
		using glm::acos;
		using glm::acosh;
		using glm::acot;
		using glm::acoth;
		using glm::acsc;
		using glm::acsch;
		using glm::affineInverse;
		using glm::all;
		using glm::angle;
		using glm::angleAxis;
		using glm::any;
		using glm::asec;
		using glm::asech;
		using glm::asin;
		using glm::asinh;
		using glm::atan;
		using glm::atanh;
		using glm::axis;
		using glm::ballRand;
		using glm::bitCount;
		using glm::bitfieldDeinterleave;
		using glm::bitfieldExtract;
		using glm::bitfieldFillOne;
		using glm::bitfieldFillZero;
		using glm::bitfieldInsert;
		using glm::bitfieldInterleave;
		using glm::bitfieldReverse;
		using glm::bitfieldRotateLeft;
		using glm::bitfieldRotateRight;
		using glm::ceil;
		using glm::ceilMultiple;
		using glm::ceilPowerOfTwo;
		using glm::circularRand;
		using glm::clamp;
		using glm::column;
		using glm::conjugate;
		using glm::convertLinearToSRGB;
		using glm::convertSRGBToLinear;
		using glm::cos;
		using glm::cos_one_over_two;
		using glm::cosh;
		using glm::cot;
		using glm::coth;
		using glm::cross;
		using glm::csc;
		using glm::csch;
		using glm::degrees;
		using glm::determinant;
		using glm::diskRand;
		using glm::distance;
		using glm::dot;
		using glm::e;
		using glm::epsilon;
		using glm::epsilonEqual;
		using glm::epsilonNotEqual;
		using glm::equal;
		using glm::euler;
		using glm::eulerAngles;
		using glm::exp;
		using glm::exp2;
		using glm::faceforward;
		using glm::fclamp;
		using glm::findLSB;
		using glm::findMSB;
		using glm::floatBitsToInt;
		using glm::floatBitsToUint;
		using glm::float_distance;
		using glm::floor;
		using glm::floorMultiple;
		using glm::floorPowerOfTwo;
		using glm::fma;
		using glm::fmax;
		using glm::fmin;
		using glm::four_over_pi;
		using glm::fract;
		using glm::frexp;
		using glm::frustum;
		using glm::frustumLH;
		using glm::frustumLH_NO;
		using glm::frustumLH_ZO;
		using glm::frustumNO;
		using glm::frustumRH;
		using glm::frustumRH_NO;
		using glm::frustumRH_ZO;
		using glm::frustumZO;
		using glm::gaussRand;
		using glm::golden_ratio;
		using glm::greaterThan;
		using glm::greaterThanEqual;
		using glm::half_pi;
		using glm::identity;
		using glm::imulExtended;
		using glm::infinitePerspective;
		using glm::infinitePerspectiveLH;
		using glm::infinitePerspectiveRH;
		using glm::intBitsToFloat;
		using glm::inverse;
		using glm::inverseTranspose;
		using glm::inversesqrt;
		using glm::iround;
		using glm::isinf;
		using glm::isnan;
		using glm::ldexp;
		using glm::length;
		using glm::lerp;
		using glm::lessThan;
		using glm::lessThanEqual;
		using glm::linearRand;
		using glm::ln_ln_two;
		using glm::ln_ten;
		using glm::ln_two;
		using glm::log;
		using glm::log2;
		using glm::lookAt;
		using glm::lookAtLH;
		using glm::lookAtRH;
		using glm::make_mat2;
		using glm::make_mat2x2;
		using glm::make_mat2x3;
		using glm::make_mat2x4;
		using glm::make_mat3;
		using glm::make_mat3x2;
		using glm::make_mat3x3;
		using glm::make_mat3x4;
		using glm::make_mat4;
		using glm::make_mat4x2;
		using glm::make_mat4x3;
		using glm::make_mat4x4;
		using glm::make_quat;
		using glm::make_vec1;
		using glm::make_vec2;
		using glm::make_vec3;
		using glm::make_vec4;
		using glm::mask;
		using glm::mat3_cast;
		using glm::mat4_cast;
		using glm::matrixCompMult;
		using glm::max;
		using glm::min;
		using glm::mirrorClamp;
		using glm::mirrorRepeat;
		using glm::mix;
		using glm::mod;
		using glm::modf;
		using glm::next_float;
		using glm::normalize;
		using glm::notEqual;
		using glm::not_;
		using glm::one;
		using glm::one_over_pi;
		using glm::one_over_root_two;
		using glm::one_over_two_pi;
		using glm::ortho;
		using glm::orthoLH;
		using glm::orthoLH_NO;
		using glm::orthoLH_ZO;
		using glm::orthoNO;
		using glm::orthoRH;
		using glm::orthoRH_NO;
		using glm::orthoRH_ZO;
		using glm::orthoZO;
		using glm::outerProduct;
		using glm::packF2x11_1x10;
		using glm::packF3x9_E1x5;
		using glm::packHalf;
		using glm::packHalf1x16;
		using glm::packHalf4x16;
		using glm::packI3x10_1x2;
		using glm::packInt2x16;
		using glm::packInt2x32;
		using glm::packInt2x8;
		using glm::packInt4x16;
		using glm::packInt4x8;
		using glm::packRGBM;
		using glm::packSnorm;
		using glm::packSnorm1x16;
		using glm::packSnorm1x8;
		using glm::packSnorm2x8;
		using glm::packSnorm3x10_1x2;
		using glm::packSnorm4x16;
		using glm::packU3x10_1x2;
		using glm::packUint2x16;
		using glm::packUint2x32;
		using glm::packUint2x8;
		using glm::packUint4x16;
		using glm::packUint4x8;
		using glm::packUnorm;
		using glm::packUnorm1x16;
		using glm::packUnorm1x5_1x6_1x5;
		using glm::packUnorm1x8;
		using glm::packUnorm2x3_1x2;
		using glm::packUnorm2x4;
		using glm::packUnorm2x8;
		using glm::packUnorm3x10_1x2;
		using glm::packUnorm3x5_1x1;
		using glm::packUnorm4x16;
		using glm::packUnorm4x4;
		using glm::perlin;
		using glm::perspective;
		using glm::perspectiveFov;
		using glm::perspectiveFovLH;
		using glm::perspectiveFovLH_NO;
		using glm::perspectiveFovLH_ZO;
		using glm::perspectiveFovNO;
		using glm::perspectiveFovRH;
		using glm::perspectiveFovRH_NO;
		using glm::perspectiveFovRH_ZO;
		using glm::perspectiveFovZO;
		using glm::perspectiveLH;
		using glm::perspectiveLH_NO;
		using glm::perspectiveLH_ZO;
		using glm::perspectiveNO;
		using glm::perspectiveRH;
		using glm::perspectiveRH_NO;
		using glm::perspectiveRH_ZO;
		using glm::perspectiveZO;
		using glm::pi;
		using glm::pickMatrix;
		using glm::pitch;
		using glm::pow;
		using glm::prev_float;
		using glm::project;
		using glm::projectNO;
		using glm::projectZO;
		using glm::quarter_pi;
		using glm::quatLookAt;
		using glm::quatLookAtLH;
		using glm::quatLookAtRH;
		using glm::quat_cast;
		using glm::radians;
		using glm::reflect;
		using glm::refract;
		using glm::repeat;
		using glm::roll;
		using glm::root_five;
		using glm::root_half_pi;
		using glm::root_ln_four;
		using glm::root_pi;
		using glm::root_three;
		using glm::root_two;
		using glm::root_two_pi;
		using glm::rotate;
		using glm::round;
		using glm::roundEven;
		using glm::roundMultiple;
		using glm::roundPowerOfTwo;
		using glm::row;
		using glm::scale;
		using glm::sec;
		using glm::sech;
		using glm::sign;
		using glm::simplex;
		using glm::sin;
		using glm::sinh;
		using glm::slerp;
		using glm::smoothstep;
		using glm::sphericalRand;
		using glm::sqrt;
		using glm::step;
		using glm::tan;
		using glm::tanh;
		using glm::third;
		using glm::three_over_two_pi;
		using glm::translate;
		using glm::transpose;
		using glm::trunc;
		using glm::tweakedInfinitePerspective;
		using glm::two_over_pi;
		using glm::two_over_root_pi;
		using glm::two_pi;
		using glm::two_thirds;
		using glm::uaddCarry;
		using glm::uintBitsToFloat;
		using glm::umulExtended;
		using glm::unProject;
		using glm::unProjectNO;
		using glm::unProjectZO;
		using glm::unpackF2x11_1x10;
		using glm::unpackF3x9_E1x5;
		using glm::unpackHalf;
		using glm::unpackHalf1x16;
		using glm::unpackHalf4x16;
		using glm::unpackI3x10_1x2;
		using glm::unpackInt2x16;
		using glm::unpackInt2x32;
		using glm::unpackInt2x8;
		using glm::unpackInt4x16;
		using glm::unpackInt4x8;
		using glm::unpackRGBM;
		using glm::unpackSnorm;
		using glm::unpackSnorm1x16;
		using glm::unpackSnorm1x8;
		using glm::unpackSnorm2x8;
		using glm::unpackSnorm3x10_1x2;
		using glm::unpackSnorm4x16;
		using glm::unpackU3x10_1x2;
		using glm::unpackUint2x16;
		using glm::unpackUint2x32;
		using glm::unpackUint2x8;
		using glm::unpackUint4x16;
		using glm::unpackUint4x8;
		using glm::unpackUnorm;
		using glm::unpackUnorm1x16;
		using glm::unpackUnorm1x5_1x6_1x5;
		using glm::unpackUnorm1x8;
		using glm::unpackUnorm2x3_1x2;
		using glm::unpackUnorm2x4;
		using glm::unpackUnorm2x8;
		using glm::unpackUnorm3x10_1x2;
		using glm::unpackUnorm3x5_1x1;
		using glm::unpackUnorm4x16;
		using glm::unpackUnorm4x4;
		using glm::uround;
		using glm::usubBorrow;
		using glm::value_ptr;
		using glm::yaw;
		using glm::zero;
	}

#   ifdef GLM_EXT_INLINE_NAMESPACE
	inline
#   endif
	namespace ext {
		using glm::abs;
		using glm::acos;
		using glm::acosh;
		using glm::acot;
		using glm::acoth;
		using glm::acsc;
		using glm::acsch;
		using glm::all;
		using glm::angle;
		using glm::angleAxis;
		using glm::any;
		using glm::asec;
		using glm::asech;
		using glm::asin;
		using glm::asinh;
		using glm::atan;
		using glm::atanh;
		using glm::axis;
		using glm::ceil;
		using glm::clamp;
		using glm::conjugate;
		using glm::cos;
		using glm::cos_one_over_two;
		using glm::cosh;
		using glm::cot;
		using glm::coth;
		using glm::cross;
		using glm::csc;
		using glm::csch;
		using glm::degrees;
		using glm::determinant;
		using glm::distance;
		using glm::dot;
		using glm::e;
		using glm::epsilon;
		using glm::equal;
		using glm::euler;
		using glm::exp;
		using glm::exp2;
		using glm::faceforward;
		using glm::fclamp;
		using glm::findNSB;
		using glm::floatBitsToInt;
		using glm::floatBitsToUint;
		using glm::floatDistance;
		using glm::floor;
		using glm::fma;
		using glm::fmax;
		using glm::fmin;
		using glm::four_over_pi;
		using glm::fract;
		using glm::frexp;
		using glm::frustum;
		using glm::frustumLH;
		using glm::frustumLH_NO;
		using glm::frustumLH_ZO;
		using glm::frustumNO;
		using glm::frustumRH;
		using glm::frustumRH_NO;
		using glm::frustumRH_ZO;
		using glm::frustumZO;
		using glm::golden_ratio;
		using glm::greaterThan;
		using glm::greaterThanEqual;
		using glm::half_pi;
		using glm::identity;
		using glm::infinitePerspective;
		using glm::infinitePerspectiveLH;
		using glm::infinitePerspectiveRH;
		using glm::intBitsToFloat;
		using glm::inverse;
		using glm::inversesqrt;
		using glm::iround;
		using glm::isMultiple;
		using glm::isPowerOfTwo;
		using glm::isinf;
		using glm::isnan;
		using glm::ldexp;
		using glm::length;
		using glm::lerp;
		using glm::lessThan;
		using glm::lessThanEqual;
		using glm::ln_ln_two;
		using glm::ln_ten;
		using glm::ln_two;
		using glm::log;
		using glm::log2;
		using glm::lookAt;
		using glm::lookAtLH;
		using glm::lookAtRH;
		using glm::matrixCompMult;
		using glm::max;
		using glm::min;
		using glm::mirrorClamp;
		using glm::mirrorRepeat;
		using glm::mix;
		using glm::mod;
		using glm::modf;
		using glm::nextFloat;
		using glm::nextMultiple;
		using glm::nextPowerOfTwo;
		using glm::normalize;
		using glm::notEqual;
		using glm::not_;
		using glm::one;
		using glm::one_over_pi;
		using glm::one_over_root_two;
		using glm::one_over_two_pi;
		using glm::ortho;
		using glm::orthoLH;
		using glm::orthoLH_NO;
		using glm::orthoLH_ZO;
		using glm::orthoNO;
		using glm::orthoRH;
		using glm::orthoRH_NO;
		using glm::orthoRH_ZO;
		using glm::orthoZO;
		using glm::outerProduct;
		using glm::perspective;
		using glm::perspectiveFov;
		using glm::perspectiveFovLH;
		using glm::perspectiveFovLH_NO;
		using glm::perspectiveFovLH_ZO;
		using glm::perspectiveFovNO;
		using glm::perspectiveFovRH;
		using glm::perspectiveFovRH_NO;
		using glm::perspectiveFovRH_ZO;
		using glm::perspectiveFovZO;
		using glm::perspectiveLH;
		using glm::perspectiveLH_NO;
		using glm::perspectiveLH_ZO;
		using glm::perspectiveNO;
		using glm::perspectiveRH;
		using glm::perspectiveRH_NO;
		using glm::perspectiveRH_ZO;
		using glm::perspectiveZO;
		using glm::pi;
		using glm::pickMatrix;
		using glm::pow;
		using glm::prevFloat;
		using glm::prevMultiple;
		using glm::prevPowerOfTwo;
		using glm::project;
		using glm::projectNO;
		using glm::projectZO;
		using glm::quarter_pi;
		using glm::radians;
		using glm::reflect;
		using glm::refract;
		using glm::repeat;
		using glm::root_five;
		using glm::root_half_pi;
		using glm::root_ln_four;
		using glm::root_pi;
		using glm::root_three;
		using glm::root_two;
		using glm::root_two_pi;
		using glm::rotate;
		using glm::round;
		using glm::roundEven;
		using glm::scale;
		using glm::sec;
		using glm::sech;
		using glm::sign;
		using glm::sin;
		using glm::sinh;
		using glm::slerp;
		using glm::smoothstep;
		using glm::sqrt;
		using glm::step;
		using glm::tan;
		using glm::tanh;
		using glm::third;
		using glm::three_over_two_pi;
		using glm::translate;
		using glm::transpose;
		using glm::trunc;
		using glm::tweakedInfinitePerspective;
		using glm::two_over_pi;
		using glm::two_over_root_pi;
		using glm::two_pi;
		using glm::two_thirds;
		using glm::uintBitsToFloat;
		using glm::unProject;
		using glm::unProjectNO;
		using glm::unProjectZO;
		using glm::uround;
		using glm::zero;
	}

#   ifdef GLM_ENABLE_EXPERIMENTAL
#   ifdef GLM_GTX_INLINE_NAMESPACE
	inline
#   endif
	namespace gtx {
		using glm::io::order_type;
		using glm::io::format_punct;
		using glm::io::basic_state_saver;
		using glm::io::basic_format_saver;
		using glm::io::precision;
		using glm::io::width;
		using glm::io::delimeter;
		using glm::io::order;
		using glm::io::get_facet;
		using glm::io::formatted;
		using glm::io::unformatted;
		using glm::io::operator<<;
		using glm::operator<<;
		using glm::tdualquat;

#       if !((GLM_COMPILER & GLM_COMPILER_CUDA) || (GLM_COMPILER & GLM_COMPILER_HIP))
		using glm::to_string;
#       endif
#       if GLM_HAS_TEMPLATE_ALIASES
		using glm::operator*;
		using glm::operator/;
#       endif
#       if GLM_HAS_RANGE_FOR
		using glm::components;
		using glm::begin;
		using glm::end;
#       endif

		using glm::abs;
		using glm::acos;
		using glm::acosh;
		using glm::adjugate;
		using glm::all;
		using glm::angle;
		using glm::angleAxis;
		using glm::any;
		using glm::areCollinear;
		using glm::areOrthogonal;
		using glm::areOrthonormal;
		using glm::asin;
		using glm::asinh;
		using glm::associatedMax;
		using glm::associatedMin;
		using glm::atan;
		using glm::atanh;
		using glm::axis;
		using glm::axisAngle;
		using glm::axisAngleMatrix;
		using glm::backEaseIn;
		using glm::backEaseInOut;
		using glm::backEaseOut;
		using glm::bitCount;
		using glm::bitfieldDeinterleave;
		using glm::bitfieldExtract;
		using glm::bitfieldFillOne;
		using glm::bitfieldFillZero;
		using glm::bitfieldInsert;
		using glm::bitfieldInterleave;
		using glm::bitfieldReverse;
		using glm::bitfieldRotateLeft;
		using glm::bitfieldRotateRight;
		using glm::bounceEaseIn;
		using glm::bounceEaseInOut;
		using glm::bounceEaseOut;
		using glm::catmullRom;
		using glm::ceil;
		using glm::circularEaseIn;
		using glm::circularEaseInOut;
		using glm::circularEaseOut;
		using glm::clamp;
		using glm::closeBounded;
		using glm::closestPointOnLine;
		using glm::colMajor2;
		using glm::colMajor3;
		using glm::colMajor4;
		using glm::compAdd;
		using glm::compMax;
		using glm::compMin;
		using glm::compMul;
		using glm::compNormalize;
		using glm::compScale;
		using glm::computeCovarianceMatrix;
		using glm::conjugate;
		using glm::convertD65XYZToD50XYZ;
		using glm::convertD65XYZToLinearSRGB;
		using glm::convertLinearSRGBToD50XYZ;
		using glm::convertLinearSRGBToD65XYZ;
		using glm::cos;
		using glm::cos_one_over_two;
		using glm::cosh;
		using glm::cross;
		using glm::cubic;
		using glm::cubicEaseIn;
		using glm::cubicEaseInOut;
		using glm::cubicEaseOut;
		using glm::decompose;
		using glm::degrees;
		using glm::derivedEulerAngleX;
		using glm::derivedEulerAngleY;
		using glm::derivedEulerAngleZ;
		using glm::determinant;
		using glm::diagonal2x2;
		using glm::diagonal2x3;
		using glm::diagonal2x4;
		using glm::diagonal3x2;
		using glm::diagonal3x3;
		using glm::diagonal3x4;
		using glm::diagonal4x2;
		using glm::diagonal4x3;
		using glm::diagonal4x4;
		using glm::distance;
		using glm::distance2;
		using glm::dot;
		using glm::dual_quat_identity;
		using glm::dualquat_cast;
		using glm::e;
		using glm::elasticEaseIn;
		using glm::elasticEaseInOut;
		using glm::elasticEaseOut;
		using glm::epsilon;
		using glm::epsilonEqual;
		using glm::epsilonNotEqual;
		using glm::equal;
		using glm::euclidean;
		using glm::euler;
		using glm::eulerAngleX;
		using glm::eulerAngleXY;
		using glm::eulerAngleXYX;
		using glm::eulerAngleXYZ;
		using glm::eulerAngleXZ;
		using glm::eulerAngleXZX;
		using glm::eulerAngleXZY;
		using glm::eulerAngleY;
		using glm::eulerAngleYX;
		using glm::eulerAngleYXY;
		using glm::eulerAngleYXZ;
		using glm::eulerAngleYZ;
		using glm::eulerAngleYZX;
		using glm::eulerAngleYZY;
		using glm::eulerAngleZ;
		using glm::eulerAngleZX;
		using glm::eulerAngleZXY;
		using glm::eulerAngleZXZ;
		using glm::eulerAngleZY;
		using glm::eulerAngleZYX;
		using glm::eulerAngleZYZ;
		using glm::eulerAngles;
		using glm::exp;
		using glm::exp2;
		using glm::exponentialEaseIn;
		using glm::exponentialEaseInOut;
		using glm::exponentialEaseOut;
		using glm::extend;
		using glm::extractEulerAngleXYX;
		using glm::extractEulerAngleXYZ;
		using glm::extractEulerAngleXZX;
		using glm::extractEulerAngleXZY;
		using glm::extractEulerAngleYXY;
		using glm::extractEulerAngleYXZ;
		using glm::extractEulerAngleYZX;
		using glm::extractEulerAngleYZY;
		using glm::extractEulerAngleZXY;
		using glm::extractEulerAngleZXZ;
		using glm::extractEulerAngleZYX;
		using glm::extractEulerAngleZYZ;
		using glm::extractMatrixRotation;
		using glm::extractRealComponent;
		using glm::faceforward;
		using glm::factorial;
		using glm::fastAcos;
		using glm::fastAsin;
		using glm::fastAtan;
		using glm::fastCos;
		using glm::fastDistance;
		using glm::fastExp;
		using glm::fastExp2;
		using glm::fastInverseSqrt;
		using glm::fastLength;
		using glm::fastLog;
		using glm::fastLog2;
		using glm::fastMix;
		using glm::fastNormalize;
		using glm::fastNormalizeDot;
		using glm::fastPow;
		using glm::fastSin;
		using glm::fastSqrt;
		using glm::fastTan;
		using glm::fclamp;
		using glm::findLSB;
		using glm::findMSB;
		using glm::fliplr;
		using glm::flipud;
		using glm::floatBitsToInt;
		using glm::floatBitsToUint;
		using glm::floor;
		using glm::floor_log2;
		using glm::fma;
		using glm::fmax;
		using glm::fmin;
		using glm::fmod;
		using glm::four_over_pi;
		using glm::fract;
		using glm::frexp;
		using glm::frustum;
		using glm::frustumLH;
		using glm::frustumLH_NO;
		using glm::frustumLH_ZO;
		using glm::frustumNO;
		using glm::frustumRH;
		using glm::frustumRH_NO;
		using glm::frustumRH_ZO;
		using glm::frustumZO;
		using glm::gauss;
		using glm::golden_ratio;
		using glm::greaterThan;
		using glm::greaterThanEqual;
		using glm::half_pi;
		using glm::hermite;
		using glm::highestBitValue;
		using glm::hsvColor;
		using glm::identity;
		using glm::imulExtended;
		using glm::infinitePerspective;
		using glm::infinitePerspectiveLH;
		using glm::infinitePerspectiveRH;
		using glm::intBitsToFloat;
		using glm::intermediate;
		using glm::interpolate;
		using glm::intersectLineSphere;
		using glm::intersectLineTriangle;
		using glm::intersectRayPlane;
		using glm::intersectRaySphere;
		using glm::intersectRayTriangle;
		using glm::inverse;
		using glm::inversesqrt;
		using glm::iround;
		using glm::isCompNull;
		using glm::isIdentity;
		using glm::isNormalized;
		using glm::isNull;
		using glm::isOrthogonal;
		using glm::isdenormal;
		using glm::isfinite;
		using glm::isinf;
		using glm::isnan;
		using glm::l1Norm;
		using glm::l2Norm;
		using glm::lMaxNorm;
		using glm::ldexp;
		using glm::leftHanded;
		using glm::length;
		using glm::length2;
		using glm::lerp;
		using glm::lessThan;
		using glm::lessThanEqual;
		using glm::linearGradient;
		using glm::linearInterpolation;
		using glm::ln_ln_two;
		using glm::ln_ten;
		using glm::ln_two;
		using glm::log;
		using glm::log2;
		using glm::lookAt;
		using glm::lookAtLH;
		using glm::lookAtRH;
		using glm::lowestBitValue;
		using glm::luminosity;
		using glm::lxNorm;
		using glm::make_mat2;
		using glm::make_mat2x2;
		using glm::make_mat2x3;
		using glm::make_mat2x4;
		using glm::make_mat3;
		using glm::make_mat3x2;
		using glm::make_mat3x3;
		using glm::make_mat3x4;
		using glm::make_mat4;
		using glm::make_mat4x2;
		using glm::make_mat4x3;
		using glm::make_mat4x4;
		using glm::make_quat;
		using glm::make_vec1;
		using glm::make_vec2;
		using glm::make_vec3;
		using glm::make_vec4;
		using glm::mask;
		using glm::mat2x4_cast;
		using glm::mat3_cast;
		using glm::mat3x4_cast;
		using glm::mat4_cast;
		using glm::matrixCompMult;
		using glm::matrixCross3;
		using glm::matrixCross4;
		using glm::max;
		using glm::min;
		using glm::mirrorClamp;
		using glm::mirrorRepeat;
		using glm::mix;
		using glm::mixedProduct;
		using glm::mod;
		using glm::modf;
		using glm::nlz;
		using glm::normalize;
		using glm::normalizeDot;
		using glm::notEqual;
		using glm::not_;
		using glm::YCoCg2rgb;
		using glm::YCoCgR2rgb;
		using glm::one;
		using glm::one_over_pi;
		using glm::one_over_root_two;
		using glm::one_over_two_pi;
		using glm::openBounded;
		using glm::orientate2;
		using glm::orientate3;
		using glm::orientate4;
		using glm::orientation;
		using glm::orientedAngle;
		using glm::ortho;
		using glm::orthoLH;
		using glm::orthoLH_NO;
		using glm::orthoLH_ZO;
		using glm::orthoNO;
		using glm::orthoRH;
		using glm::orthoRH_NO;
		using glm::orthoRH_ZO;
		using glm::orthoZO;
		using glm::orthonormalize;
		using glm::outerProduct;
		using glm::packDouble2x32;
		using glm::packHalf2x16;
		using glm::packSnorm2x16;
		using glm::packSnorm4x8;
		using glm::packUnorm2x16;
		using glm::packUnorm4x8;
		using glm::perp;
		using glm::perspective;
		using glm::perspectiveFov;
		using glm::perspectiveFovLH;
		using glm::perspectiveFovLH_NO;
		using glm::perspectiveFovLH_ZO;
		using glm::perspectiveFovNO;
		using glm::perspectiveFovRH;
		using glm::perspectiveFovRH_NO;
		using glm::perspectiveFovRH_ZO;
		using glm::perspectiveFovZO;
		using glm::perspectiveLH;
		using glm::perspectiveLH_NO;
		using glm::perspectiveLH_ZO;
		using glm::perspectiveNO;
		using glm::perspectiveRH;
		using glm::perspectiveRH_NO;
		using glm::perspectiveRH_ZO;
		using glm::perspectiveZO;
		using glm::pi;
		using glm::pickMatrix;
		using glm::pitch;
		using glm::polar;
		using glm::pow;
		using glm::pow2;
		using glm::pow3;
		using glm::pow4;
		using glm::powerOfTwoAbove;
		using glm::powerOfTwoBelow;
		using glm::powerOfTwoNearest;
		using glm::proj;
		using glm::proj2D;
		using glm::proj3D;
		using glm::project;
		using glm::projectNO;
		using glm::projectZO;
		using glm::qr_decompose;
		using glm::quadraticEaseIn;
		using glm::quadraticEaseInOut;
		using glm::quadraticEaseOut;
		using glm::quarter_pi;
		using glm::quarticEaseIn;
		using glm::quarticEaseInOut;
		using glm::quarticEaseOut;
		using glm::quatLookAt;
		using glm::quatLookAtLH;
		using glm::quatLookAtRH;
		using glm::quat_cast;
		using glm::quat_identity;
		using glm::quinticEaseIn;
		using glm::quinticEaseInOut;
		using glm::quinticEaseOut;
		using glm::radialGradient;
		using glm::radians;
		using glm::recompose;
		using glm::reflect;
		using glm::refract;
		using glm::repeat;
		using glm::rgb2YCoCg;
		using glm::rgb2YCoCgR;
		using glm::rgbColor;
		using glm::rightHanded;
		using glm::roll;
		using glm::root_five;
		using glm::root_half_pi;
		using glm::root_ln_four;
		using glm::root_pi;
		using glm::root_three;
		using glm::root_two;
		using glm::root_two_pi;
		using glm::rotate;
		using glm::rotateNormalizedAxis;
		using glm::rotateX;
		using glm::rotateY;
		using glm::rotateZ;
		using glm::rotation;
		using glm::round;
		using glm::roundEven;
		using glm::rowMajor2;
		using glm::rowMajor3;
		using glm::rowMajor4;
		using glm::rq_decompose;
		using glm::saturation;
		using glm::scale;
		using glm::scaleBias;
		using glm::shearX2D;
		using glm::shearX3D;
		using glm::shearY2D;
		using glm::shearY3D;
		using glm::shearZ3D;
		using glm::shortMix;
		using glm::sign;
		using glm::sin;
		using glm::sineEaseIn;
		using glm::sineEaseInOut;
		using glm::sineEaseOut;
		using glm::sinh;
		using glm::slerp;
		using glm::smoothstep;
		using glm::sortEigenvalues;
		using glm::sqrt;
		using glm::squad;
		using glm::step;
		using glm::tan;
		using glm::tanh;
		using glm::third;
		using glm::three_over_two_pi;
		using glm::translate;
		using glm::transpose;
		using glm::triangleNormal;
		using glm::trunc;
		using glm::tweakedInfinitePerspective;
		using glm::two_over_pi;
		using glm::two_over_root_pi;
		using glm::two_pi;
		using glm::two_thirds;
		using glm::uaddCarry;
		using glm::uintBitsToFloat;
		using glm::umulExtended;
		using glm::unProject;
		using glm::unProjectNO;
		using glm::unProjectZO;
		using glm::unpackDouble2x32;
		using glm::unpackHalf2x16;
		using glm::unpackSnorm2x16;
		using glm::unpackSnorm4x8;
		using glm::unpackUnorm2x16;
		using glm::unpackUnorm4x8;
		using glm::uround;
		using glm::usubBorrow;
		using glm::value_ptr;
		using glm::wrapAngle;
		using glm::wxyz;
		using glm::yaw;
		using glm::yawPitchRoll;
		using glm::zero;
	}
#   endif
}

#if defined(_MSC_VER) // Workaround
// Partial template specialization doesn't need to be exported explicitly, but this may not work otherwise on MSVC.
export namespace std {
	using std::hash; // See GLM_GTX_hash
}
#endif
