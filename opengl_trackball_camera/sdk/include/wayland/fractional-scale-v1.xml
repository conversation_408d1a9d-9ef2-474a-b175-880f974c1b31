<?xml version="1.0" encoding="UTF-8"?>
<protocol name="fractional_scale_v1">
  <copyright>
    Copyright © 2022 Kenny <PERSON>

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice (including the next
    paragraph) shall be included in all copies or substantial portions of the
    Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
  </copyright>

  <description summary="Protocol for requesting fractional surface scales">
    This protocol allows a compositor to suggest for surfaces to render at
    fractional scales.

    A client can submit scaled content by utilizing wp_viewport. This is done by
    creating a wp_viewport object for the surface and setting the destination
    rectangle to the surface size before the scale factor is applied.

    The buffer size is calculated by multiplying the surface size by the
    intended scale.

    The wl_surface buffer scale should remain set to 1.

    If a surface has a surface-local size of 100 px by 50 px and wishes to
    submit buffers with a scale of 1.5, then a buffer of 150px by 75 px should
    be used and the wp_viewport destination rectangle should be 100 px by 50 px.

    For toplevel surfaces, the size is rounded halfway away from zero. The
    rounding algorithm for subsurface position and size is not defined.
  </description>

  <interface name="wp_fractional_scale_manager_v1" version="1">
    <description summary="fractional surface scale information">
      A global interface for requesting surfaces to use fractional scales.
    </description>

    <request name="destroy" type="destructor">
      <description summary="unbind the fractional surface scale interface">
        Informs the server that the client will not be using this protocol
        object anymore. This does not affect any other objects,
        wp_fractional_scale_v1 objects included.
      </description>
    </request>

    <enum name="error">
      <entry name="fractional_scale_exists" value="0"
        summary="the surface already has a fractional_scale object associated"/>
    </enum>

    <request name="get_fractional_scale">
      <description summary="extend surface interface for scale information">
        Create an add-on object for the the wl_surface to let the compositor
        request fractional scales. If the given wl_surface already has a
        wp_fractional_scale_v1 object associated, the fractional_scale_exists
        protocol error is raised.
      </description>
      <arg name="id" type="new_id" interface="wp_fractional_scale_v1"
           summary="the new surface scale info interface id"/>
      <arg name="surface" type="object" interface="wl_surface"
           summary="the surface"/>
    </request>
  </interface>

  <interface name="wp_fractional_scale_v1" version="1">
    <description summary="fractional scale interface to a wl_surface">
      An additional interface to a wl_surface object which allows the compositor
      to inform the client of the preferred scale.
    </description>

    <request name="destroy" type="destructor">
      <description summary="remove surface scale information for surface">
        Destroy the fractional scale object. When this object is destroyed,
        preferred_scale events will no longer be sent.
      </description>
    </request>

    <event name="preferred_scale">
      <description summary="notify of new preferred scale">
        Notification of a new preferred scale for this surface that the
        compositor suggests that the client should use.

        The sent scale is the numerator of a fraction with a denominator of 120.
      </description>
      <arg name="scale" type="uint" summary="the new preferred scale"/>
    </event>
  </interface>
</protocol>
