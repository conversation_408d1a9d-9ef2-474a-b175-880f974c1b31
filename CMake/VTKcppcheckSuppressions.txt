//=========================================================================
//
//  Program:   Visualization Toolkit
//
//  Copyright (c) <PERSON>, <PERSON>, <PERSON>
//  All rights reserved.
//  See Copyright.txt or http://www.kitware.com/Copyright.htm for details.
//
//     This software is distributed WITHOUT ANY WARRANTY; without even
//     the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
//     PURPOSE.  See the above copyright notice for more information.
//
//=========================================================================

// This file contains enough suppressions to result in no cppcheck warnings,
// at least on macOS with cppcheck 1.83.

// To run:
// cd /path/to/VTK/
// cppcheck --platform=unspecified --enable=style -q --suppressions-list=CMake/VTKcppcheckSuppressions.txt --template='{id},{file}:{line},{severity},{message}' -j8 .

// Suppress everything from ThirdParty.
*:ThirdParty/*

// Similarly, these aren't really part of VTK proper.
*:Utilities/KWSys/*
*:Utilities/MetaIO/*
*:Utilities/DICOMParser/*

// Can't fix in portable way for all VTK-supported platforms.
duplicateExpression:IO/PostgreSQL/vtkPostgreSQLQuery.cxx
unpreciseMathCall

// Using reallocf() would be a good solution, but it's not available on all platforms.
memleakOnRealloc

// VTK assumes IEEE 754 floats, so this warning isn't useful.
memsetClassFloat

// Gives too many false positives.
assertWithSideEffect
argumentSize

// Gives too many false positives with << stream operator.
shiftNegative

// There are *thousands* of these, too much to manage, so suppress all.
variableScope

// Many warnings
uninitMemberVar:Charts/Core/vtkChart.cxx
uninitMemberVar:Charts/Core/vtkChartBox.cxx
uninitMemberVar:Charts/Core/vtkChartParallelCoordinates.cxx
uninitMemberVar:Charts/Core/vtkInteractiveArea.cxx
uninitMemberVar:Charts/Core/vtkPlotStacked.cxx
uninitMemberVar:Charts/Core/vtkScatterPlotMatrix.cxx
uninitMemberVar:Common/ComputationalGeometry/vtkParametricEllipsoid.cxx
uninitMemberVar:Common/ComputationalGeometry/vtkParametricSpline.cxx
uninitMemberVar:Common/DataModel/Testing/Cxx/TestMappedGridDeepCopy.cxx
uninitMemberVar:Common/DataModel/vtkAbstractCellLocator.cxx
uninitMemberVar:Common/DataModel/vtkAttributesErrorMetric.cxx
uninitMemberVar:Common/DataModel/vtkBiQuadraticQuad.cxx
uninitMemberVar:Common/DataModel/vtkBox.cxx
uninitMemberVar:Common/DataModel/vtkBSPCuts.cxx
uninitMemberVar:Common/DataModel/vtkCellLocator.cxx
uninitMemberVar:Common/DataModel/vtkDataObjectTreeIterator.cxx
uninitMemberVar:Common/DataModel/vtkDistributedGraphHelper.cxx
uninitMemberVar:Common/DataModel/vtkEdgeTable.cxx
uninitMemberVar:Common/DataModel/vtkFieldData.cxx
uninitMemberVar:Common/DataModel/vtkGenericAttributeCollection.cxx
uninitMemberVar:Common/DataModel/vtkGenericEdgeTable.h
uninitMemberVar:Common/DataModel/vtkGenericInterpolatedVelocityField.cxx
uninitMemberVar:Common/DataModel/vtkHyperTreeGrid.cxx
uninitMemberVar:Common/DataModel/vtkHyperTreeGrid.h
uninitMemberVar:Common/DataModel/vtkImplicitFunction.cxx
uninitMemberVar:Common/DataModel/vtkImplicitSelectionLoop.cxx
uninitMemberVar:Common/DataModel/vtkOrderedTriangulator.cxx
uninitMemberVar:Common/DataModel/vtkPixelExtent.h
uninitMemberVar:Common/DataModel/vtkPointLocator.cxx
uninitMemberVar:Common/DataModel/vtkReebGraph.cxx
uninitMemberVar:Common/DataModel/vtkStaticCellLocator.cxx
uninitMemberVar:Common/DataModel/vtkStaticPointLocator.cxx
uninitMemberVar:Common/DataModel/vtkUniformGridAMRDataIterator.cxx
uninitMemberVar:Common/ExecutionModel/vtkProgressObserver.cxx
uninitMemberVar:Common/ExecutionModel/vtkSpanSpace.cxx
uninitMemberVar:Common/ExecutionModel/vtkSphereTree.cxx
uninitMemberVar:Common/ExecutionModel/vtkThreadedCompositeDataPipeline.cxx
uninitMemberVar:Common/Misc/vtkFunctionParser.cxx
uninitMemberVar:Common/Transforms/vtkAbstractTransform.cxx
uninitMemberVar:Common/Transforms/vtkAbstractTransform.h
uninitMemberVar:Common/Transforms/vtkTransform.cxx
uninitMemberVar:Domains/Chemistry/Testing/Cxx/TestMoleculeSelection.cxx
uninitMemberVar:Domains/Chemistry/vtkBlueObeliskDataParser.cxx
uninitMemberVar:Domains/Chemistry/vtkOpenQubeElectronicData.cxx
uninitMemberVar:Domains/Chemistry/vtkOpenQubeMoleculeSource.cxx
uninitMemberVar:Domains/Microscopy/vtkOpenSlideReader.h
uninitMemberVar:Examples/Charts/Cxx/vtkGraphItem.cxx
uninitMemberVar:Examples/GUI/Qt/FourPaneViewer/QtVTKRenderWindows.cxx
uninitMemberVar:Filters/AMR/vtkAMRResampleFilter.cxx
uninitMemberVar:Filters/Core/vtkConnectivityFilter.cxx
uninitMemberVar:Filters/Core/vtkDelaunay2D.cxx
uninitMemberVar:Filters/Core/vtkDelaunay3D.cxx
uninitMemberVar:Filters/Core/vtkFieldDataToAttributeDataFilter.cxx
uninitMemberVar:Filters/Core/vtkGlyph3D.cxx
uninitMemberVar:Filters/Core/vtkMergeFields.cxx
uninitMemberVar:Filters/Core/vtkMergeFields.h
uninitMemberVar:Filters/Core/vtkPlaneCutter.cxx
uninitMemberVar:Filters/Core/vtkPolyDataConnectivityFilter.cxx
uninitMemberVar:Filters/Core/vtkQuadricClustering.cxx
uninitMemberVar:Filters/Core/vtkQuadricClustering.h
uninitMemberVar:Filters/Core/vtkQuadricDecimation.cxx
uninitMemberVar:Filters/Core/vtkRearrangeFields.h
uninitMemberVar:Filters/Core/vtkTransposeTable.cxx
uninitMemberVar:Filters/Core/vtkTubeFilter.cxx
uninitMemberVar:Filters/Core/vtkUnstructuredGridQuadricDecimation.cxx
uninitMemberVar:Filters/Extraction/vtkExtractTimeSteps.cxx
uninitMemberVar:Filters/FlowPaths/vtkCachingInterpolatedVelocityField.cxx
uninitMemberVar:Filters/FlowPaths/vtkEvenlySpacedStreamlines2D.cxx
uninitMemberVar:Filters/FlowPaths/vtkParticlePathFilter.h
uninitMemberVar:Filters/FlowPaths/vtkTemporalStreamTracer.cxx
uninitMemberVar:Filters/General/vtkClipVolume.cxx
uninitMemberVar:Filters/General/vtkCoincidentPoints.cxx
uninitMemberVar:Filters/General/vtkHyperStreamline.cxx
uninitMemberVar:Filters/General/vtkImageMarchingCubes.cxx
uninitMemberVar:Filters/General/vtkIntersectionPolyDataFilter.cxx
uninitMemberVar:Filters/General/vtkLoopBooleanPolyDataFilter.cxx
uninitMemberVar:Filters/General/vtkMergeCells.cxx
uninitMemberVar:Filters/General/vtkMultiThreshold.h
uninitMemberVar:Filters/General/vtkOBBDicer.h
uninitMemberVar:Filters/General/vtkOBBTree.cxx
uninitMemberVar:Filters/General/vtkSplineFilter.cxx
uninitMemberVar:Filters/General/vtkSplitField.h
uninitMemberVar:Filters/General/vtkTableBasedClipDataSet.cxx
uninitMemberVar:Filters/General/vtkTemporalPathLineFilter.cxx
uninitMemberVar:Filters/General/vtkUncertaintyTubeFilter.cxx
uninitMemberVar:Filters/General/vtkVoxelContoursToSurfaceFilter.cxx
uninitMemberVar:Filters/General/vtkWarpScalar.cxx
uninitMemberVar:Filters/General/vtkYoungsMaterialInterface.cxx
uninitMemberVar:Filters/Generic/vtkGenericGlyph3DFilter.cxx
uninitMemberVar:Filters/Geometry/vtkDataSetSurfaceFilter.cxx
uninitMemberVar:Filters/Geometry/vtkStructuredNeighbor.cxx
uninitMemberVar:Filters/Hybrid/vtkAdaptiveDataSetSurfaceFilter.cxx
uninitMemberVar:Filters/Hybrid/vtkBSplineTransform.cxx
uninitMemberVar:Filters/Hybrid/vtkGreedyTerrainDecimation.cxx
uninitMemberVar:Filters/Hybrid/vtkGridTransform.cxx
uninitMemberVar:Filters/Hybrid/vtkImageToPolyDataFilter.cxx
uninitMemberVar:Filters/Hybrid/vtkProjectedTerrainPath.cxx
uninitMemberVar:Filters/Hybrid/vtkTemporalFractal.cxx
uninitMemberVar:Filters/Hybrid/vtkTemporalShiftScale.cxx
uninitMemberVar:Filters/HyperTree/vtkHyperTreeGridCellCenters.cxx
uninitMemberVar:Filters/Modeling/vtkBandedPolyDataContourFilter.cxx
uninitMemberVar:Filters/Modeling/vtkLinearExtrusionFilter.cxx
uninitMemberVar:Filters/Modeling/vtkRibbonFilter.cxx
uninitMemberVar:Filters/Modeling/vtkRuledSurfaceFilter.cxx
uninitMemberVar:Filters/Modeling/vtkSelectEnclosedPoints.cxx
uninitMemberVar:Filters/Modeling/vtkSelectPolyData.cxx
uninitMemberVar:Filters/MomentInvariants/vtkComputeMoments.cxx
uninitMemberVar:Filters/MomentInvariants/vtkMomentInvariants.cxx
uninitMemberVar:Filters/MomentInvariants/vtkReconstructFromMoments.cxx
uninitMemberVar:Filters/MomentInvariants/vtkSimilarityBalls.cxx
uninitMemberVar:Filters/OpenTurns/vtkOTFilter.cxx
uninitMemberVar:Filters/Parallel/vtkPKdTree.cxx
uninitMemberVar:Filters/ParallelFlowPaths/Testing/Cxx/TestPParticleTracers.cxx
uninitMemberVar:Filters/ParallelFlowPaths/Testing/Cxx/TestPStreamAMR.cxx
uninitMemberVar:Filters/ParallelFlowPaths/Testing/Cxx/TestVectorFieldSource.cxx
uninitMemberVar:Filters/ParallelFlowPaths/vtkPStreamTracer.cxx
uninitMemberVar:Filters/ParallelGeometry/vtkPDataSetGhostGenerator.cxx
uninitMemberVar:Filters/ParallelGeometry/vtkPStructuredGridConnectivity.cxx
uninitMemberVar:Filters/ParallelGeometry/vtkPUnstructuredGridGhostCellsGenerator.cxx
uninitMemberVar:Filters/Points/vtkEuclideanClusterExtraction.cxx
uninitMemberVar:Filters/Points/vtkExtractEnclosedPoints.cxx
uninitMemberVar:Filters/Points/vtkSPHInterpolator.cxx
uninitMemberVar:Filters/Points/vtkSPHKernel.cxx
uninitMemberVar:Filters/SMP/Testing/Cxx/TestSMPWarp.cxx
uninitMemberVar:Filters/SMP/vtkSMPContourGrid.cxx
uninitMemberVar:Filters/Sources/vtkCellTypeSource.cxx
uninitMemberVar:Filters/Sources/vtkEllipticalButtonSource.cxx
uninitMemberVar:Filters/Sources/vtkGlyphSource2D.cxx
uninitMemberVar:Filters/Sources/vtkGraphToPolyData.cxx
uninitMemberVar:Filters/Sources/vtkSelectionSource.cxx
uninitMemberVar:Filters/Statistics/vtkExtractFunctionalBagPlot.cxx
uninitMemberVar:Filters/Statistics/vtkKMeansAssessFunctor.h
uninitMemberVar:Filters/Statistics/vtkKMeansDistanceFunctor.cxx
uninitMemberVar:Filters/Statistics/vtkMultiCorrelativeStatisticsAssessFunctor.h
uninitMemberVar:Filters/Verdict/vtkMeshQuality.cxx
uninitMemberVar:Imaging/Color/vtkImageQuantizeRGBToIndex.cxx
uninitMemberVar:Imaging/Core/vtkImageInterpolatorInternals.h
uninitMemberVar:Imaging/Hybrid/vtkCheckerboardSplatter.cxx
uninitMemberVar:Imaging/Hybrid/vtkFastSplatter.cxx
uninitMemberVar:Imaging/Hybrid/vtkGaussianSplatter.cxx
uninitMemberVar:Imaging/Hybrid/vtkShepardMethod.cxx
uninitMemberVar:Imaging/Hybrid/vtkSurfaceReconstructionFilter.cxx
uninitMemberVar:Imaging/OpenGL2/vtkOpenGLImageGradient.cxx
uninitMemberVar:Imaging/Statistics/vtkImageHistogram.cxx
uninitMemberVar:Imaging/Stencil/vtkImageToImageStencil.cxx
uninitMemberVar:Infovis/BoostGraphAlgorithms/vtkBoostDividedEdgeBundling.cxx
uninitMemberVar:Infovis/Core/vtkKCoreDecomposition.cxx
uninitMemberVar:Infovis/Core/vtkStringToNumeric.cxx
uninitMemberVar:Infovis/Core/vtkTableToGraph.cxx
uninitMemberVar:Infovis/Layout/vtkAttributeClustering2DLayoutStrategy.cxx
uninitMemberVar:Infovis/Layout/vtkCirclePackFrontChainLayoutStrategy.cxx
uninitMemberVar:Infovis/Layout/vtkClustering2DLayoutStrategy.cxx
uninitMemberVar:Infovis/Layout/vtkCommunity2DLayoutStrategy.cxx
uninitMemberVar:Infovis/Layout/vtkConeLayoutStrategy.cxx
uninitMemberVar:Infovis/Layout/vtkConstrained2DLayoutStrategy.cxx
uninitMemberVar:Infovis/Layout/vtkFast2DLayoutStrategy.cxx
uninitMemberVar:Infovis/Layout/vtkForceDirectedLayoutStrategy.cxx
uninitMemberVar:Infovis/Layout/vtkIncrementalForceLayout.cxx
uninitMemberVar:Infovis/Layout/vtkSimple2DLayoutStrategy.cxx
uninitMemberVar:Interaction/Style/vtkInteractorStyleFlight.cxx
uninitMemberVar:Interaction/Style/vtkInteractorStyleUnicam.cxx
uninitMemberVar:Interaction/Widgets/Testing/Cxx/TestResliceCursorWidget2.cxx
uninitMemberVar:Interaction/Widgets/Testing/Cxx/TestResliceCursorWidget3.cxx
uninitMemberVar:Interaction/Widgets/vtk3DWidget.cxx
uninitMemberVar:Interaction/Widgets/vtkBoundedPlanePointPlacer.cxx
uninitMemberVar:Interaction/Widgets/vtkClosedSurfacePointPlacer.cxx
uninitMemberVar:Interaction/Widgets/vtkResliceCursorLineRepresentation.cxx
uninitMemberVar:Interaction/Widgets/vtkResliceCursorPicker.cxx
uninitMemberVar:Interaction/Widgets/vtkXYPlotWidget.cxx
uninitMemberVar:IO/ADIOS/Testing/Cxx/TestADIOSSphereWR.cxx
uninitMemberVar:IO/AMR/vtkAMRBaseParticlesReader.cxx
uninitMemberVar:IO/AMR/vtkAMRBaseReader.cxx
uninitMemberVar:IO/AMR/vtkAMRFlashReaderInternal.h
uninitMemberVar:IO/Core/vtkAbstractPolyDataReader.cxx
uninitMemberVar:IO/Core/vtkBase64InputStream.cxx
uninitMemberVar:IO/Core/vtkBase64OutputStream.cxx
uninitMemberVar:IO/EnSight/vtkEnSightReader.cxx
uninitMemberVar:IO/Exodus/vtkExodusIIReaderVariableCheck.cxx
uninitMemberVar:IO/Exodus/vtkExodusIIWriter.cxx
uninitMemberVar:IO/Export/vtkSingleVTPExporter.cxx
uninitMemberVar:IO/Export/vtkX3DExporterFIWriter.cxx
uninitMemberVar:IO/FFMPEG/vtkFFMPEGWriter.cxx
uninitMemberVar:IO/GDAL/vtkGDALRasterReader.cxx
uninitMemberVar:IO/GeoJSON/vtkGeoJSONWriter.cxx
uninitMemberVar:IO/Geometry/vtkAVSucdReader.cxx
uninitMemberVar:IO/Geometry/vtkGAMBITReader.cxx
uninitMemberVar:IO/Geometry/vtkMFIXReader.cxx
uninitMemberVar:IO/Geometry/vtkOpenFOAMReader.cxx
uninitMemberVar:IO/Geometry/vtkTecplotReader.cxx
uninitMemberVar:IO/Infovis/vtkPhyloXMLTreeWriter.cxx
uninitMemberVar:IO/LSDyna/LSDynaMetaData.cxx
uninitMemberVar:IO/LSDyna/vtkLSDynaPartCollection.cxx
uninitMemberVar:IO/MINC/vtkMINCImageWriter.cxx
uninitMemberVar:IO/MINC/vtkMNITagPointWriter.cxx
uninitMemberVar:IO/Movie/vtkOggTheoraWriter.cxx
uninitMemberVar:IO/MySQL/vtkTableToMySQLWriter.cxx
uninitMemberVar:IO/NetCDF/vtkNetCDFCFReader.h
uninitMemberVar:IO/ParallelNetCDF/vtkPNetCDFPOPReader.cxx
uninitMemberVar:IO/ParallelXdmf3/Testing/Cxx/TestXdmf3Parallel.cxx
uninitMemberVar:IO/ParallelXML/vtkXMLPMultiBlockDataWriter.cxx
uninitMemberVar:IO/PostgreSQL/vtkPostgreSQLQuery.cxx
uninitMemberVar:IO/PostgreSQL/vtkTableToPostgreSQLWriter.cxx
uninitMemberVar:IO/SegY/vtkSegY3DReader.cxx
uninitMemberVar:IO/SegY/vtkSegYReader.cxx
uninitMemberVar:IO/SQL/vtkTableToDatabaseWriter.cxx
uninitMemberVar:IO/Video/vtkMILVideoSource.cxx
uninitMemberVar:IO/Xdmf2/vtkXdmfWriter.cxx
uninitMemberVar:IO/Xdmf3/vtkXdmf3HeavyDataHandler.cxx
uninitMemberVar:IO/Xdmf3/vtkXdmf3Reader.cxx
uninitMemberVar:IO/Xdmf3/vtkXdmf3Writer.cxx
uninitMemberVar:IO/XML/Testing/Cxx/TestXMLMappedUnstructuredGridIO.cxx
uninitMemberVar:IO/XML/vtkXMLCompositeDataReader.cxx
uninitMemberVar:IO/XML/vtkXMLFileReadTester.cxx
uninitMemberVar:IO/XML/vtkXMLImageDataReader.cxx
uninitMemberVar:IO/XML/vtkXMLPDataReader.cxx
uninitMemberVar:IO/XML/vtkXMLPImageDataReader.cxx
uninitMemberVar:IO/XML/vtkXMLPolyDataReader.cxx
uninitMemberVar:IO/XML/vtkXMLPolyDataWriter.cxx
uninitMemberVar:IO/XML/vtkXMLPRectilinearGridReader.cxx
uninitMemberVar:IO/XML/vtkXMLPStructuredGridReader.cxx
uninitMemberVar:IO/XML/vtkXMLPUnstructuredDataReader.cxx
uninitMemberVar:IO/XML/vtkXMLStructuredDataReader.cxx
uninitMemberVar:IO/XML/vtkXMLStructuredDataWriter.cxx
uninitMemberVar:IO/XML/vtkXMLTableReader.cxx
uninitMemberVar:IO/XML/vtkXMLTableWriter.cxx
uninitMemberVar:IO/XML/vtkXMLUnstructuredDataReader.cxx
uninitMemberVar:IO/XML/vtkXMLUnstructuredDataWriter.cxx
uninitMemberVar:IO/XML/vtkXMLUnstructuredGridWriter.cxx
uninitMemberVar:IO/XMLParser/vtkXMLDataParser.cxx
uninitMemberVar:Parallel/Core/vtkSubGroup.cxx
uninitMemberVar:Parallel/MPI/vtkMPIEventLog.cxx
uninitMemberVar:Rendering/Annotation/vtkArcPlotter.cxx
uninitMemberVar:Rendering/Annotation/vtkScalarBarActorInternal.h
uninitMemberVar:Rendering/Context2D/vtkContextMouseEvent.h
uninitMemberVar:Rendering/Core/Testing/Cxx/TestCompositePolyDataMapper2Picking.cxx
uninitMemberVar:Rendering/Core/Testing/Cxx/TestPickingManager.cxx
uninitMemberVar:Rendering/Core/Testing/Cxx/TestPointSelection.cxx
uninitMemberVar:Rendering/Core/vtkCoordinate.cxx
uninitMemberVar:Rendering/Core/vtkHardwareSelector.cxx
uninitMemberVar:Rendering/Core/vtkMapArrayValues.cxx
uninitMemberVar:Rendering/Core/vtkProp.cxx
uninitMemberVar:Rendering/Core/vtkRenderWindowInteractor3D.cxx
uninitMemberVar:Rendering/Core/vtkScenePicker.cxx
uninitMemberVar:Rendering/Core/vtkVolume.cxx
uninitMemberVar:Rendering/Label/vtkLabelHierarchy.cxx
uninitMemberVar:Rendering/Label/vtkLabelHierarchyPrivate.h
uninitMemberVar:Rendering/LICOpenGL2/vtkLineIntegralConvolution2D.cxx
uninitMemberVar:Rendering/LICOpenGL2/vtkSurfaceLICComposite.cxx
uninitMemberVar:Rendering/LICOpenGL2/vtkSurfaceLICInterface.cxx
uninitMemberVar:Rendering/OpenGL2/vtkAndroidRenderWindowInteractor.cxx
uninitMemberVar:Rendering/OpenGL2/vtkCompositePolyDataMapper2.cxx
uninitMemberVar:Rendering/OpenGL2/vtkCompositePolyDataMapper2Internal.h
uninitMemberVar:Rendering/OpenGL2/vtkDepthPeelingPass.cxx
uninitMemberVar:Rendering/OpenGL2/vtkEGLRenderWindow.cxx
uninitMemberVar:Rendering/OpenGL2/vtkFramebufferPass.cxx
uninitMemberVar:Rendering/OpenGL2/vtkOpenGLHardwareSelector.cxx
uninitMemberVar:Rendering/OpenGL2/vtkOpenGLHelper.cxx
uninitMemberVar:Rendering/OpenGL2/vtkOpenGLState.h
uninitMemberVar:Rendering/OpenGL2/vtkOpenGLTexture.cxx
uninitMemberVar:Rendering/OpenGL2/vtkRenderbuffer.cxx
uninitMemberVar:Rendering/OpenGL2/vtkSimpleMotionBlurPass.cxx
uninitMemberVar:Rendering/OpenGL2/vtkValuePass.cxx
uninitMemberVar:Rendering/OpenGL2/vtkWin32OpenGLRenderWindow.cxx
uninitMemberVar:Rendering/OpenGL2/vtkXRenderWindowInteractor.cxx
uninitMemberVar:Rendering/OpenVR/vtkOpenVRCamera.cxx
uninitMemberVar:Rendering/OpenVR/vtkOpenVRFollower.cxx
uninitMemberVar:Rendering/OpenVR/vtkOpenVRMenuRepresentation.cxx
uninitMemberVar:Rendering/OpenVR/vtkOpenVRMenuWidget.cxx
uninitMemberVar:Rendering/OpenVR/vtkOpenVRModel.cxx
uninitMemberVar:Rendering/OpenVR/vtkOpenVROverlayInternal.h
uninitMemberVar:Rendering/OpenVR/vtkOpenVRRenderWindow.cxx
uninitMemberVar:Rendering/OptiX/vtkOptiXPass.cxx
uninitMemberVar:Rendering/OptiX/vtkOptiXPtxLoader.cxx
uninitMemberVar:Rendering/OptiX/vtkOptiXRendererNode.cxx
uninitMemberVar:Rendering/OSPRay/vtkOSPRayPass.cxx
uninitMemberVar:Rendering/OSPRay/vtkOSPRayRendererNode.cxx
uninitMemberVar:Rendering/Parallel/Testing/Cxx/TestClientServerRendering.cxx
uninitMemberVar:Rendering/Parallel/vtkParallelRenderManager.h
uninitMemberVar:Rendering/ParallelLIC/vtkPPainterCommunicator.h
uninitMemberVar:Rendering/ParallelLIC/vtkPPixelTransfer.h
uninitMemberVar:Rendering/Volume/Testing/Cxx/TestGPURayCastClippingUserTransform.cxx
uninitMemberVar:Rendering/Volume/vtkEncodedGradientEstimator.cxx
uninitMemberVar:Rendering/Volume/vtkUnstructuredGridBunykRayCastFunction.cxx
uninitMemberVar:Rendering/Volume/vtkUnstructuredGridHomogeneousRayIntegrator.cxx
uninitMemberVar:Rendering/Volume/vtkUnstructuredGridPreIntegration.cxx
uninitMemberVar:Rendering/Volume/vtkUnstructuredGridVolumeRayCastMapper.cxx
uninitMemberVar:Rendering/Volume/vtkUnstructuredGridVolumeZSweepMapper.cxx
uninitMemberVar:Rendering/Volume/vtkVolumeOutlineSource.cxx
uninitMemberVar:Rendering/VolumeOpenGL2/vtkOpenGLGPUVolumeRayCastMapper.cxx
uninitMemberVar:Rendering/VolumeOpenGL2/vtkOpenGLProjectedTetrahedraMapper.cxx
uninitMemberVar:Rendering/VolumeOpenGL2/vtkVolumeMask.h
uninitMemberVar:Rendering/VolumeOpenGL2/vtkVolumeTexture.cxx
uninitMemberVar:Rendering/VolumeOpenGL2/vtkVolumeTexture.h
uninitMemberVar:Testing/GenericBridge/vtkBridgeCellIterator.cxx
uninitMemberVar:Testing/GenericBridge/vtkBridgeCellIteratorOnCellBoundaries.cxx
uninitMemberVar:Testing/GenericBridge/vtkBridgeCellIteratorOnDataSet.cxx
uninitMemberVar:Utilities/Benchmarks/vtkRenderTimings.h
uninitMemberVar:Views/Qt/vtkQtRecordView.cxx
uninitMemberVar:Web/WebGLExporter/vtkWebGLDataSet.cxx
uninitMemberVar:Web/WebGLExporter/vtkWebGLExporter.cxx
uninitMemberVar:Web/WebGLExporter/vtkWebGLObject.cxx
uninitMemberVar:Web/WebGLExporter/vtkWebGLWidget.cxx
uninitMemberVar:Wrapping/PythonCore/vtkPythonOverload.cxx

// Many warnings
cstyleCast:Common/Core/vtkTypeFloat32ArrayJava.cxx
cstyleCast:Common/Core/vtkTypeFloat64ArrayJava.cxx
cstyleCast:Common/Core/vtkTypeInt16ArrayJava.cxx
cstyleCast:Common/Core/vtkTypeInt32ArrayJava.cxx
cstyleCast:Common/Core/vtkTypeInt64ArrayJava.cxx
cstyleCast:Common/Core/vtkTypeInt8ArrayJava.cxx
cstyleCast:Common/Core/vtkTypeUInt16ArrayJava.cxx
cstyleCast:Common/Core/vtkTypeUInt32ArrayJava.cxx
cstyleCast:Common/Core/vtkTypeUInt64ArrayJava.cxx
cstyleCast:Common/Core/vtkTypeUInt8ArrayJava.cxx
cstyleCast:Common/DataModel/vtkIterativeClosestPointTransform.cxx
cstyleCast:Common/DataModel/vtkReebGraph.cxx
cstyleCast:Common/Transforms/vtkLandmarkTransform.cxx
cstyleCast:Common/Transforms/vtkThinPlateSplineTransform.cxx
cstyleCast:Domains/ChemistryOpenGL2/vtkDomainsChemistryOpenGL2ObjectFactoryJava.cxx
cstyleCast:Examples/Android/JavaVTK/jni/main.cxx
cstyleCast:Examples/Android/VolumeRender/jni/main.cxx
cstyleCast:Examples/GUI/Win32/SampleMFC/SampleView.h
cstyleCast:Examples/GUI/Win32/SampleMFC/vtkMFCView.h
cstyleCast:Examples/GUI/Win32/vtkMFC/vtkSDI/vtkSDIDoc.cpp
cstyleCast:Examples/ParallelProcessing/Generic/Cxx/ParallelIso.cxx
cstyleCast:Filters/General/vtkMarchingContourFilter.cxx
cstyleCast:Filters/General/vtkTessellatorFilter.cxx
cstyleCast:Filters/General/vtkYoungsMaterialInterface.cxx
cstyleCast:Filters/Hybrid/vtkBSplineTransform.cxx
cstyleCast:Filters/Hybrid/vtkGridTransform.cxx
cstyleCast:Filters/Parallel/vtkPMaskPoints.cxx
cstyleCast:Filters/ParallelImaging/vtkExtractPiece.cxx
cstyleCast:Interaction/Style/vtkInteractionStyleObjectFactoryJava.cxx
cstyleCast:IO/Exodus/vtkExodusIIReader.cxx
cstyleCast:IO/Export/vtkRIBExporter.cxx
cstyleCast:IO/ExportOpenGL2/vtkIOExportOpenGL2ObjectFactoryJava.cxx
cstyleCast:IO/ExportPDF/vtkIOExportPDFObjectFactoryJava.cxx
cstyleCast:IO/Legacy/vtkDataReader.cxx
cstyleCast:IO/MINC/vtkMINCImageWriter.cxx
cstyleCast:IO/MINC/vtkMNITransformReader.cxx
cstyleCast:IO/MINC/vtkMNITransformWriter.cxx
cstyleCast:IO/MPIImage/Testing/Cxx/ParallelIso.cxx
cstyleCast:IO/MPIImage/Testing/Cxx/ParallelIso2.cxx
cstyleCast:IO/Parallel/vtkEnSightWriter.cxx
cstyleCast:IO/Parallel/vtkPDataSetWriter.cxx
cstyleCast:IO/Video/vtkMILVideoSource.cxx
cstyleCast:IO/Video/vtkVideoSource.cxx
cstyleCast:IO/Video/vtkWin32VideoSource.cxx
cstyleCast:IO/Xdmf2/vtkXdmfWriter.cxx
cstyleCast:Parallel/Core/vtkMultiProcessController.cxx
cstyleCast:Parallel/MPI/vtkMPIController.cxx
cstyleCast:Parallel/MPI/vtkMPIController.h
cstyleCast:Rendering/ContextOpenGL2/vtkRenderingContextOpenGL2ObjectFactoryJava.cxx
cstyleCast:Rendering/FreeType/vtkRenderingFreeTypeObjectFactoryJava.cxx
cstyleCast:Rendering/GL2PSOpenGL2/vtkRenderingGL2PSOpenGL2ObjectFactoryJava.cxx
cstyleCast:Rendering/OpenGL2/vtkAndroidRenderWindowInteractor.cxx
cstyleCast:Rendering/OpenGL2/vtkOpenGLGlyph3DMapper.cxx
cstyleCast:Rendering/OpenGL2/vtkOpenGLLabeledContourMapper.cxx
cstyleCast:Rendering/OpenGL2/vtkOpenGLPointGaussianMapper.cxx
cstyleCast:Rendering/OpenGL2/vtkOpenGLPolyDataMapper.cxx
cstyleCast:Rendering/OpenGL2/vtkOpenGLSphereMapper.cxx
cstyleCast:Rendering/OpenGL2/vtkOpenGLStickMapper.cxx
cstyleCast:Rendering/OpenGL2/vtkRenderingOpenGL2ObjectFactoryJava.cxx
cstyleCast:Rendering/OpenGL2/vtkWin32OpenGLRenderWindow.cxx
cstyleCast:Rendering/OpenGL2/vtkWin32RenderWindowInteractor.cxx
cstyleCast:Rendering/OptiX/vtkOptiXActorNode.cxx
cstyleCast:Rendering/OptiX/vtkOptiXPolyDataMapperNode.cxx
cstyleCast:Rendering/OSPRay/vtkOSPRayActorNode.cxx
cstyleCast:Rendering/OSPRay/vtkOSPRayPolyDataMapperNode.cxx
cstyleCast:Rendering/OSPRay/vtkOSPRayVolumeNode.cxx
cstyleCast:Rendering/Parallel/vtkParallelRenderManager.cxx
cstyleCast:Rendering/Volume/vtkUnstructuredGridBunykRayCastFunction.cxx
cstyleCast:Rendering/VolumeOpenGL2/vtkRenderingVolumeOpenGL2ObjectFactoryJava.cxx
cstyleCast:Web/WebGLExporter/vtkWebGLExporter.cxx
cstyleCast:Wrapping/Java/vtkJavaUtil.cxx
cstyleCast:Wrapping/PythonCore/PyVTKObject.cxx
cstyleCast:Wrapping/PythonCore/PyVTKReference.cxx
cstyleCast:Wrapping/PythonCore/PyVTKSpecialObject.cxx
cstyleCast:Wrapping/PythonCore/vtkPythonArgs.h
cstyleCast:Wrapping/PythonCore/vtkPythonUtil.cxx

// 182 warnings
noExplicitConstructor

// 169 warnings
invalidscanf

// Many warnings
unreadVariable:Charts/Core/Testing/Cxx/TestFreeTypeRender.cxx
unreadVariable:CMake/vtkTestvfw32Capture.cxx
unreadVariable:Common/Core/Testing/Cxx/TestSparseArrayValidation.cxx
unreadVariable:Common/DataModel/Testing/Cxx/TestAMRBox.cxx
unreadVariable:Common/DataModel/Testing/Cxx/TestComputeBoundingSphere.cxx
unreadVariable:Common/DataModel/vtkGenericEdgeTable.cxx
unreadVariable:Common/DataModel/vtkIncrementalOctreePointLocator.cxx
unreadVariable:Common/DataModel/vtkPolyhedron.cxx
unreadVariable:Common/DataModel/vtkReebGraph.cxx
unreadVariable:Common/DataModel/vtkSimpleCellTessellator.cxx
unreadVariable:Common/ExecutionModel/vtkSphereTree.cxx
unreadVariable:Common/System/vtkSocket.cxx
unreadVariable:Examples/Android/NativeVTK/jni/main.cxx
unreadVariable:Examples/GUI/Win32/vtkBorland/ProjectDemo/Form_Test.cpp
unreadVariable:Examples/Widgets/Cxx/SplineWidget.cxx
unreadVariable:Filters/AMR/vtkAMRResampleFilter.cxx
unreadVariable:Filters/Core/Testing/Cxx/TestAssignAttribute.cxx
unreadVariable:Filters/Core/Testing/Cxx/TestGlyph3D.cxx
unreadVariable:Filters/Core/vtkCompositeCutter.cxx
unreadVariable:Filters/Core/vtkUnstructuredGridQuadricDecimation.cxx
unreadVariable:Filters/FlowPaths/vtkEvenlySpacedStreamlines2D.cxx
unreadVariable:Filters/General/Testing/Cxx/TestContourTriangulator.cxx
unreadVariable:Filters/General/Testing/Cxx/TestContourTriangulatorCutter.cxx
unreadVariable:Filters/General/Testing/Cxx/TestContourTriangulatorMarching.cxx
unreadVariable:Filters/General/Testing/Cxx/TestQuadraturePoints.cxx
unreadVariable:Filters/General/vtkClipClosedSurface.cxx
unreadVariable:Filters/General/vtkContourTriangulator.cxx
unreadVariable:Filters/General/vtkOBBTree.cxx
unreadVariable:Filters/Geometry/Testing/Cxx/TestUnstructuredGridGeometryFilter.cxx
unreadVariable:Filters/Hybrid/vtkTemporalFractal.cxx
unreadVariable:Filters/MomentInvariants/vtkMomentsHelper.cxx
unreadVariable:Filters/MomentInvariants/vtkReconstructFromMoments.cxx
unreadVariable:Filters/ParallelFlowPaths/vtkPStreamTracer.cxx
unreadVariable:Filters/Points/vtkStatisticalOutlierRemoval.cxx
unreadVariable:Filters/ReebGraph/Testing/Cxx/TestReebGraph.cxx
unreadVariable:Filters/Sources/Testing/Cxx/TestPolyLineSource.cxx
unreadVariable:Filters/Statistics/vtkKMeansDistanceFunctor.cxx
unreadVariable:Imaging/Stencil/vtkLassoStencilSource.cxx
unreadVariable:Imaging/Stencil/vtkPolyDataToImageStencil.cxx
unreadVariable:Interaction/Widgets/Testing/Cxx/ScaledBoxWidget2.cxx
unreadVariable:Interaction/Widgets/Testing/Cxx/TestDijkstraImageGeodesicPath.cxx
unreadVariable:Interaction/Widgets/Testing/Cxx/vtkBiDimensionalRepresentation2DTest1.cxx
unreadVariable:Interaction/Widgets/Testing/Cxx/vtkSeedRepresentationTest1.cxx
unreadVariable:Interaction/Widgets/vtkImageOrthoPlanes.cxx
unreadVariable:IO/ADIOS/Testing/Cxx/TestADIOSSphereWR.cxx
unreadVariable:IO/Exodus/vtkExodusIIReader.cxx
unreadVariable:IO/Export/vtkVRMLExporter.cxx
unreadVariable:IO/Export/vtkX3DExporter.cxx
unreadVariable:IO/Export/vtkX3DExporterFIWriter.cxx
unreadVariable:IO/Geometry/vtkChacoReader.cxx
unreadVariable:IO/Geometry/vtkFacetWriter.cxx
unreadVariable:IO/Geometry/vtkMFIXReader.cxx
unreadVariable:IO/Geometry/vtkParticleReader.cxx
unreadVariable:IO/Infovis/Testing/Cxx/TestFixedWidthTextReader.cxx
unreadVariable:IO/LSDyna/vtkLSDynaReader.cxx
unreadVariable:IO/MySQL/Testing/Cxx/TestMySQLDatabase.cxx
unreadVariable:IO/SQL/Testing/Cxx/TestSQLiteDatabase.cxx
unreadVariable:IO/Video/vtkMILVideoSource.cxx
unreadVariable:Rendering/Annotation/vtkAxisActor.cxx
unreadVariable:Rendering/Annotation/vtkCaptionActor2D.cxx
unreadVariable:Rendering/External/vtkExternalOpenGLRenderWindow.cxx
unreadVariable:Rendering/Label/vtkLabelPlacementMapper.cxx
unreadVariable:Rendering/Label/vtkLabelPlacer.cxx
unreadVariable:Rendering/LOD/vtkQuadricLODActor.cxx
unreadVariable:Rendering/OpenGL2/vtkOpenGLFramebufferObject.cxx
unreadVariable:Rendering/OpenGL2/vtkOpenGLGlyph3DHelper.cxx
unreadVariable:Rendering/OpenGL2/vtkOpenGLPolyDataMapper.cxx
unreadVariable:Rendering/OpenGL2/vtkOpenGLRenderWindow.cxx
unreadVariable:Rendering/OpenGL2/vtkPixelBufferObject.cxx
unreadVariable:Rendering/OpenGL2/vtkWin32OpenGLRenderWindow.cxx
unreadVariable:Rendering/OpenVR/vtkOpenVRFollower.cxx
unreadVariable:Rendering/OpenVR/vtkOpenVRRenderWindow.cxx
unreadVariable:Rendering/OSPRay/vtkOSPRayMaterialLibrary.cxx
unreadVariable:Rendering/Tk/vtkTkImageViewerWidget.cxx
unreadVariable:Rendering/VolumeOpenGL2/vtkVolumeShaderComposer.h
unreadVariable:Utilities/ParseOGLExt/ParseOGLExt.cxx
unreadVariable:Web/WebGLExporter/vtkWebGLExporter.cxx
unreadVariable:Web/WebGLExporter/vtkWebGLWidget.cxx
unreadVariable:Wrapping/PythonCore/PyVTKObject.cxx
unreadVariable:Wrapping/PythonCore/vtkPythonOverload.cxx

// 48 warnings
noOperatorEq

// Many warnings
noCopyConstructor:Common/Core/Testing/Cxx/TestCxxFeatures.cxx
noCopyConstructor:Common/DataModel/vtkDataObjectTreeIterator.cxx
noCopyConstructor:Common/DataModel/vtkPolygon.cxx
noCopyConstructor:Common/DataModel/vtkStaticCellLocator.cxx
noCopyConstructor:Common/DataModel/vtkStaticPointLocator.cxx
noCopyConstructor:Common/DataModel/vtkStaticPointLocator.cxx
noCopyConstructor:Common/DataModel/vtkStaticPointLocator.cxx
noCopyConstructor:Common/DataModel/vtkStaticPointLocator2D.cxx
noCopyConstructor:Common/DataModel/vtkStaticPointLocator2D.cxx
noCopyConstructor:Common/DataModel/vtkStaticPointLocator2D.cxx
noCopyConstructor:Common/ExecutionModel/vtkSpanSpace.cxx
noCopyConstructor:Common/ExecutionModel/vtkSphereTree.cxx
noCopyConstructor:Common/Misc/vtkHeap.cxx
noCopyConstructor:Filters/Core/vtkDecimatePolylineFilter.cxx
noCopyConstructor:Filters/Core/vtkDecimatePro.h
noCopyConstructor:Filters/Core/vtkDelaunay3D.cxx
noCopyConstructor:Filters/Core/vtkPlaneCutter.cxx
noCopyConstructor:Filters/Core/vtkSmoothPolyDataFilter.cxx
noCopyConstructor:Filters/FlowPaths/vtkLagrangianBasicIntegrationModel.cxx
noCopyConstructor:Filters/General/vtkHyperStreamline.cxx
noCopyConstructor:Filters/General/vtkIntersectionPolyDataFilter.cxx
noCopyConstructor:Filters/General/vtkTableBasedClipDataSet.cxx
noCopyConstructor:Filters/General/vtkUncertaintyTubeFilter.cxx
noCopyConstructor:Filters/ParallelFlowPaths/vtkPLagrangianParticleTracker.cxx
noCopyConstructor:Filters/Points/vtkHierarchicalBinningFilter.cxx
noCopyConstructor:Filters/Points/vtkHierarchicalBinningFilter.cxx
noCopyConstructor:Filters/Points/vtkHierarchicalBinningFilter.cxx
noCopyConstructor:GUISupport/Qt/Testing/Cxx/QTestApp.cxx
noCopyConstructor:GUISupport/Qt/Testing/Cxx/QTestApp.h
noCopyConstructor:IO/ADIOS/ADIOSReader.cxx
noCopyConstructor:IO/ADIOS/ADIOSReader.h
noCopyConstructor:IO/ADIOS/ADIOSWriter.cxx
noCopyConstructor:IO/ADIOS/ADIOSWriter.h
noCopyConstructor:IO/Export/vtkPOVExporter.cxx
noCopyConstructor:IO/GeoJSON/vtkGeoJSONWriter.cxx
noCopyConstructor:IO/Geometry/vtkCGMWriter.cxx
noCopyConstructor:IO/LSDyna/LSDynaFamily.cxx
noCopyConstructor:IO/LSDyna/LSDynaFamily.h
noCopyConstructor:IO/LSDyna/vtkLSDynaPart.cxx
noCopyConstructor:IO/LSDyna/vtkLSDynaPartCollection.cxx
noCopyConstructor:IO/SegY/vtkSegYReader.h
noCopyConstructor:IO/Xdmf2/vtkXdmfWriter.cxx
noCopyConstructor:IO/Xdmf3/vtkXdmf3Reader.cxx
noCopyConstructor:Rendering/LICOpenGL2/vtkSurfaceLICHelper.cxx
noCopyConstructor:Rendering/LICOpenGL2/vtkSurfaceLICHelper.h
noCopyConstructor:Rendering/Volume/vtkUnstructuredGridVolumeZSweepMapper.cxx

// 18 warnings
redundantAssignment:Filters/General/vtkBoxClipDataSet.cxx
redundantAssignment:*/Testing/*

// 13 warnings
useInitializationList:Filters/MomentInvariants/vtkMomentsTensor.h
useInitializationList:Interaction/Widgets/vtkBalloonWidget.cxx
useInitializationList:IO/Exodus/vtkExodusIIWriter.h
useInitializationList:IO/Import/vtkOBJImporter.cxx
useInitializationList:IO/Import/vtkOBJImporterInternals.cxx
useInitializationList:IO/TRUCHAS/vtkTRUCHASReader.cxx
useInitializationList:IO/XML/vtkXMLReader.cxx
useInitializationList:Rendering/Core/vtkTextActor3D.cxx
useInitializationList:Rendering/OpenGL2/vtkValuePass.cxx
useInitializationList:Rendering/OpenVR/vtkOpenVROverlay.cxx
useInitializationList:Utilities/Benchmarks/vtkRenderTimings.h
useInitializationList:Utilities/ParseOGLExt/ParseOGLExt.cxx
useInitializationList:Filters/Core/Testing/Cxx/TestAppendFilter.cxx

// False positives.
AssignmentAddressToInteger:IO/Exodus/vtkExodusIIReader.cxx
CastIntegerToAddressAtReturn:Geovis/Core/vtkGeoTreeNode.h
copyCtorPointerCopying:IO/NetCDF/vtkSLACReader.cxx
duplicateExpression:Rendering/Annotation/vtkScalarBarActor.cxx
knownConditionTrueFalse:IO/Exodus/vtkExodusIIReader.cxx
knownConditionTrueFalse:IO/Geometry/Testing/Cxx/TestIncrementalOctreePointLocator.cxx
memleak:Examples/Infovis/Cxx/MultiView.cxx
memleak:Imaging/Hybrid/vtkSurfaceReconstructionFilter.cxx
memleak:Rendering/OpenGL2/vtkOpenGLRenderTimerLog.cxx
operatorEqVarError:Filters/General/vtkHyperStreamline.cxx
redundantAssignment:Wrapping/Tools/vtkParsePreprocess.c
stlIfStrFind:Infovis/Core/vtkPipelineGraphSource.cxx
stlIfStrFind:Rendering/OpenGL2/vtkOpenGLRenderer.cxx
unsafeClassCanLeak:Filters/General/vtkDensifyPolyData.cxx
unsafeClassCanLeak:GUISupport/Qt/QVTKInteractorInternal.h
unsafeClassCanLeak:Interaction/Widgets/vtkBiDimensionalRepresentation.h
unusedStructMember:Common/Misc/vtkHeap.cxx
unusedStructMember:IO/Infovis/vtkTulipReader.cxx
uselessAssignmentArg:Common/DataModel/vtkReebGraph.cxx
uselessAssignmentPtrArg:Wrapping/Tools/vtkParse.tab.c
syntaxError:Rendering/OpenGL2/vtkOpenGLGlyph3DMapper.cxx
arrayIndexOutOfBoundsCond:IO/MINC/vtkMINCImageAttributes.cxx
uninitMemberVar:GUISupport/Qt/QVTKOpenGLWidget.cxx
unusedVariable:IO/SegY/vtkSegYReaderInternal.cxx

// false positive: <https://trac.cppcheck.net/ticket/8346>
syntaxError:Accelerators/Vtkm/vtkmlib/Portals.hxx
syntaxError:Common/Math/vtkQuaternion.txx
syntaxError:Rendering/OpenGL2/vtkOpenGLVertexBufferObject.cxx

// false positive: <https://trac.cppcheck.net/ticket/8367>
CastIntegerToAddressAtReturn:Common/ExecutionModel/vtkAlgorithm.h

// false positive: "syntax error"
syntaxError:Accelerators/Vtkm/vtkmCellSetExplicit.cxx
syntaxError:Accelerators/Vtkm/vtkmCellSetSingleType.cxx

// false positive: struct member is never used.
unusedStructMember:Common/Core/Testing/Cxx/TestArrayFreeFunctions.cxx

// Not worth fixing, but not actually false positives.
knownConditionTrueFalse:Rendering/Label/vtkLabeledTreeMapDataMapper.cxx
duplicateExpression:Common/Math/Testing/Cxx/TestPolynomialSolversUnivariate.cxx
knownConditionTrueFalse:Common/Core/Testing/Cxx/UnitTestMath.cxx
knownConditionTrueFalse:GUISupport/Qt/QVTKWidget.cxx
knownConditionTrueFalse:Rendering/OpenGL/vtkXGPUInfoList.cxx
knownConditionTrueFalse:Rendering/OpenGL2/vtkOpenGLRenderWindow.cxx
knownConditionTrueFalse:Rendering/OpenGL2/vtkOpenGLShaderCache.cxx
knownConditionTrueFalse:Filters/Parallel/Testing/Cxx/UnitTestPMaskPoints.cxx
knownConditionTrueFalse:Rendering/OSPRay/vtkOSPRayRendererNode.cxx
nullPointer:Wrapping/Tools/lex.yy.c
passedByValue:Filters/General/vtkYoungsMaterialInterface.cxx
uninitMemberVar:Common/DataModel/vtkStaticPointLocator2D.cxx

// Style, not really problematic:
duplicateAssignExpression:Rendering/Context2D/vtkMarkerUtilities.cxx
duplicateAssignExpression:Common/Misc/vtkFunctionParser.cxx
duplicateAssignExpression:Common/Math/vtkQuaternionInterpolator.cxx
duplicateAssignExpression:Wrapping/Tools/vtkWrapText.c

// 4 warnings, should fix!
invalidPointerCast:Rendering/GL2PSOpenGL2/vtkOpenGLGL2PSHelperImpl.cxx
invalidPointerCast:Rendering/OSPRay/vtkOSPRayPolyDataMapperNode.cxx

// 16 warnings
copyCtorAndEqOperator:Filters/General/vtkCoincidentPoints.cxx
copyCtorAndEqOperator:Filters/General/vtkHyperStreamline.cxx
copyCtorAndEqOperator:Filters/General/vtkUncertaintyTubeFilter.cxx
copyCtorAndEqOperator:Filters/ParallelFlowPaths/vtkPLagrangianParticleTracker.cxx
copyCtorAndEqOperator:Filters/ParallelFlowPaths/vtkPStreamTracer.cxx
copyCtorAndEqOperator:Filters/ParallelGeometry/vtkPUnstructuredGridGhostCellsGenerator.cxx
copyCtorAndEqOperator:Imaging/Morphological/vtkImageConnectivityFilter.cxx
copyCtorAndEqOperator:Interaction/Widgets/vtkWidgetEventTranslator.cxx
copyCtorAndEqOperator:IO/Exodus/vtkExodusIICache.h
copyCtorAndEqOperator:IO/Export/vtkSVGContextDevice2D.cxx
copyCtorAndEqOperator:IO/Geometry/vtkOpenFOAMReader.cxx
copyCtorAndEqOperator:Rendering/Context2D/vtkContextActor.cxx

// 6 warnings
noConstructor:Common/Core/Testing/Cxx/TestCxxFeatures.cxx
noConstructor:Common/Core/Testing/Cxx/TestDataArrayIterators.cxx
noConstructor:Common/Core/vtkTypeList.txx
noConstructor:Examples/GUI/Win32/vtkMFC/vtkSDI/vtkSDIView.h
noConstructor:Filters/ParallelDIY2/vtkPResampleToImage.cxx
noConstructor:Filters/ParallelDIY2/vtkPResampleWithDataSet.cxx
noConstructor:Rendering/Core/Testing/Cxx/TestInteractorTimers.cxx

// Need someone that knows this code...
memleak:Wrapping/Tools/vtkParsePreprocess.c
redundantAssignment:IO/Image/vtkJPEGReader.cxx
knownConditionTrueFalse:IO/Exodus/vtkExodusIIWriter.cxx
knownConditionTrueFalse:Filters/ParallelFlowPaths/Testing/Cxx/TestPStreamGeometry.cxx
knownConditionTrueFalse:Rendering/Label/vtkLabelSizeCalculator.cxx
redundantAssignment:Rendering/OSPRay/vtkOSPRayRendererNode.cxx
