/*=========================================================================

  Program:   Visualization Toolkit
  Module:    TestLabelPlacer2D.cxx

  Copyright (c) <PERSON>, <PERSON>, <PERSON>
  All rights reserved.
  See Copyright.txt or http://www.kitware.com/Copyright.htm for details.

     This software is distributed WITHOUT ANY WARRANTY; without even
     the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
     PURPOSE.  See the above copyright notice for more information.

=========================================================================*/

#include "vtkActor.h"
#include "vtkActor2D.h"
#include "vtkCamera.h"
#include "vtkIdTypeArray.h"
#include "vtkLabeledDataMapper.h"
#include "vtkLabelPlacer.h"
#include "vtkLabelSizeCalculator.h"
#include "vtkMath.h"
#include "vtkPoints.h"
#include "vtkPointData.h"
#include "vtkPointSetToLabelHierarchy.h"
#include "vtkPolyData.h"
#include "vtkPolyDataMapper.h"
#include "vtkRegressionTestImage.h"
#include "vtkRenderer.h"
#include "vtkRenderWindow.h"
#include "vtkRenderWindowInteractor.h"
#include "vtkSmartPointer.h"
#include "vtkStringArray.h"
#include "vtkTestUtilities.h"
#include "vtkTextProperty.h"

#define PTSMULT 10
#define TXTMULT ( PTSMULT * PTSMULT )
//#define GENERATE_TEST_POINTS

static const char* vtkTextLabelList[] =
{
  "Barbey",
  "Ozark",
  "Pennsylvania",
  "Gear",
  "Firm",
  "Beacon",
  "Hayes",
  "Harder",
  "Fortify",
  "Gudgeon",
  "Threadfin",
  "Surprise",
  "Cape May",
  "Tolovana",
  "Wilkes",
  "Billfish",
  "Plaice",
  "Minotaur",
  "Fitzgerald",
  "Specter",
  "Maloy",
  "Curb",
  "Whitehall",
  "Longview",
  "Hull",
  "Phoenix",
  "Abbot",
  "Newport",
  "Shannon",
  "Charrette",
  "Yarnall",
  "Muliphen",
  "Herndon",
  "Talbot",
  "Helios",
  "Lake Erie",
  "Carbonero",
  "Begor",
  "Reeves",
  "Potomac",
  "Miller",
  "Chilton",
  "Florida",
  "McInerney",
  "Dupont",
  "Laub",
  "Desplaines River",
  "Rincon",
  "Audacious",
  "Timber Hitch",
  "Heron",
  "Birmingham",
  "Cofer",
  "Koelsch",
  "Fremont",
  "Cape Douglas",
  "Cape Isabel",
  "Champion",
  "Des Moines",
  "Herald",
  "Crommelin",
  "Waccamaw",
  "Whale",
  "Marchand",
  "Aylwin",
  "Markab",
  "McLanahan",
  "Susquehanna",
  "Mobile",
  "Bulwark",
  "Ashland",
  "Bronx",
  "Mark",
  "Northern Light",
  "Melville",
  "Schuylkill",
  "Tippecanoe",
  "Yellowstone",
  "Gypsy",
  "Crosley",
  "Dash",
  "Rhode Island",
  "Memphis",
  "Khirirat",
  "Kearsarge",
  "Okinawa",
  "Bondia",
  "Sabine",
  "Acme",
  "Cushing",
  "Twin Falls",
  "Mendonca",
  "Lamoille River",
  "Benner",
  "Cape Trinity",
  "St. Louis",
  "Cronin",
  "Ozbourn",
  "Cape Gloucester",
  "Puffer",
  "Arcadia",
  "Gordon",
  "Ardent",
  "Yorktown",
  "Latona",
  "Kalamazoo",
  "Klondike",
  "Passumpsic",
  "Scorpion",
  "Carpellotti",
  "Hooper Island",
  "Higgins",
  "Chukawan",
  "Napa",
  "Lucid",
  "Bold",
  "Assertive",
  "Bronstein",
  "Kittiwake",
  "Allagash",
  "San Bernardino",
  "Pogy",
  "Leyte Gulf",
  "Inflict",
  "Gilmer",
  "Norwalk",
  "Satyr",
  "Dyess",
  "Gregory",
  "Avenger",
  "Wren",
  "Higbee",
  "Blandy",
  "Inchon",
  "Tattnall",
  "Spadefish",
  "Herkimer",
  "Williamson",
  "Tillamook",
  "Cheyenne",
  "Macon",
  "Gherardi",
  "Ward",
  "Bang",
  "Earheart",
  "Skate",
  "Cape John",
  "Union",
  "Atule",
  "Sailfish",
  "Nebraska",
  "Warbler",
  "Booth",
  "Deperm",
  "Fitch",
  "Grasp",
  "Laramie River",
  "Sea Leopard",
  "Gunason",
  "Preble",
  "Texas",
  "Sealift Antarctic",
  "Mauna Kea",
  "Ticonderoga",
  "Aldebaran",
  "Capricornus",
  "Hidatsa",
  "Grayling",
  "Myers",
  "Chauncey",
  "Monterey",
  "Misn Santa Clara",
  "Cape Cod",
  "Sylvania",
  "Caloosahatchee",
  "Ranger",
  "Guide",
  "Aeolus",
  "Atlas",
  "Shangri-la",
  "Petersburg",
  "Chewaucan",
  "Queenfish",
  "Curlew",
  "Mount Whitney",
  "Robinson",
  "Haraden",
  "Pyro",
  "Hartford",
  "Hepburn",
  "Albuquerque",
  "Hammerhead",
  "Starling",
  "Augusta",
  "Overton",
  "Worden",
  "McFaul",
  "Idaho",
  "Safeguard",
  "Umpqua",
  "Stanton",
  "Mispillion",
  "O'Kane",
  "Indra",
  "Manley",
  "Chatterer",
  "Engage",
  "Gettysburg",
  "Invincible",
  "Saipan",
  "Cape Henry",
  "Intrepid",
  "Furse",
  "Denver",
  "Dubuque",
  "Guest",
  "Accokeek",
  "Counsel",
  "Knapp",
  "Mount Baker",
  "Ingersoll",
  "Hancock",
  "Boyd",
  "Pinnacle",
  "Perkins",
  "Zeus",
  "Cape Vincent",
  "Chepachet",
  "Prairie",
  "Leftwich",
  "Chafee",
  "Observation Island",
  "Roanoke",
  "Hanson",
  "Brunswick",
  "Pinckney",
  "Swordfish",
  "Affray",
  "Rednour",
  "Cone",
  "Picking",
  "Hobby",
  "Sealift Mediteranean",
  "Yukon",
  "Whirlwind",
  "Shamal",
  "Firecrest",
  "Salinan",
  "Pope",
  "Scamp",
  "Owyhee",
  "Watertown",
  "Mahopac",
  "Philippine Sea",
  "Sequoia",
  "Wabash",
  "Atlantis",
  "Raton",
  "Peacock",
  "Askari",
  "Warrington",
  "Susanville",
  "Bridget",
  "Stringham",
  "Knudson",
  "Rodman",
  "Colhoun",
  "Tappahannock",
  "Alamo",
  "Omaha",
  "Bauer",
  "Shasta",
  "Soderman",
  "Loyal",
  "Vella Gulf",
  "Sentry",
  "Southerland",
  "Wyoming",
  "Louisiana",
  "Albacore",
  "Cavallaro",
  "Oldendorf",
  "Price",
  "Prevail",
  "Widgeon",
  "Register",
  "Vicksburg",
  "Scott",
  "Tortuga",
  "Knox",
  "Wilkinson",
  "Boulder",
  "Halibut",
  "Nimitz",
  "Neches",
  "Wagner",
  "Forster",
  "Rockingham",
  "Carr",
  "Short Splice",
  "Rockwall",
  "Gemini",
  "Noa",
  "Roosevelt",
  "Constitution",
  "Foote",
  "Lynch",
  "Kleinsmith",
  "Herbert",
  "Pegasus",
  "Salute",
  "Relentless",
  "Capps",
  "Norton Sound",
  "Limpkin",
  "Galveston",
  "Mountrail",
  "Douglas",
  "Copeland",
  "Harwood",
  "Rowe",
  "Grebe",
  "Frigate Bird",
  "Varian",
  "Kane",
  "Kilty",
  "San Marcos",
  "Ford",
  "Pioneer Valley",
  "Hawkins",
  "Dufilho",
  "Waldron",
  "Defender",
  "New York",
  "Chung-hoon",
  "Quick",
  "Escape",
  "Asterion",
  "Gary",
  "Sevier",
  "Antelope",
  "Seahorse",
  "Lang",
  "Betelgeuse",
  "San Juan",
  "Point Defiance",
  "Tallulah",
  "Yokes",
  "Saugatuck",
  "Effective",
  "Yosemite",
  "Sunfish",
  "Adventurous",
  "Bunker Hill",
  "Los Angeles",
  "Kiowa",
  "Sargo",
  "Jallao",
  "Mackenzie",
  "Nenville",
  "Marine Fiddler",
  "Arlington",
  "Caperton",
  "Navajo",
  "Brownson",
  "Tollberg",
  "Fort Marion",
  "Rushmore",
  "Salish",
  "Valcour",
  "Telamon",
  "Dallas",
  "Providence",
  "Frament",
  "Ottersetter",
  "Mack",
  "Irwin",
  "Plainview",
  "Nautilus",
  "Quillback",
  "Vega",
  "Loyalty",
  "Tidewater",
  "Sturgeon",
  "Sealift Arctic",
  "Atherton",
  "Fife",
  "Raven",
  "Atlanta",
  "Implicit",
  "Sunnyvale",
  "Rival",
  "Cape Edmont",
  "Trout",
  "Trumpetfish",
  "Algol",
  "Gendreau",
  "Bellerophon",
  "Coral Sea",
  "Menhaden",
  "Utina",
  "Cobia",
  "Vital",
  "Amick",
  "Kilo Moana",
  "Salem",
  "Ray",
  "French",
  "Perseus",
  "Eichenberger",
  "Swasey",
  "Corporal",
  "Muskingum",
  "Minnesota",
  "Zelima",
  "Mason",
  "Quirinus",
  "Nashville",
  "Mission Capistrano",
  "Chicago",
  "Wheatear",
  "Stethem",
  "Stockham",
  "Canon",
  "Pueblo",
  "Severn",
  "Somerset",
  "Haddo",
  "Dolphin",
  "Sims",
  "Lawrence",
  "Dickerson",
  "Samoset",
  "Alacrity",
  "Muir",
  "Conner",
  "Elkhorn",
  "Marsh",
  "Cape Intrepid",
  "Pivot",
  "Penobscot",
  "Triton",
  "Peconic",
  "Shadwell",
  "Clamp",
  "Skill",
  "Valley Forge",
  "Pollack",
  "Buchanan",
  "Coffman",
  "Snook",
  "Portsmouth",
  "Halyburton",
  "Benavidez",
  "Boise",
  "Vesuvius",
  "Sirago",
  "Pampanito",
  "Tabberer",
  "Pigeon",
  "McCandless",
  "Jeffers",
  "Fisher",
  "Runels",
  "South Carolina",
  "Endurance",
  "Finback",
  "Pinnebog",
  "Sigourney",
  "Pargo",
  "Towers",
  "O'Bannon",
  "Guadalupe",
  "Kansas",
  "La Jolla",
  "Bartlett",
  "Hunley",
  "Tolman",
  "Hoist",
  "Pearl Harbor",
  "Sealift China Sea",
  "Collett",
  "Nespelen",
  "Anchorage",
  "Columbia",
  "Saginaw",
  "Scranton",
  "Sutton",
  "Illusive",
  "Tench",
  "Cape Victory",
  "Trippe",
  "Fiske",
  "New Jersey",
  "Kline",
  "Georgia",
  "Oregon",
  "Piscataqua",
  "Henson",
  "Gallery",
  "Meteor",
  "Chandler",
  "Putnam",
  "Pollux",
  "Patuxent",
  "Hazelwood",
  "Apollo",
  "Fletcher",
  "Huse",
  "Moinester",
  "Wiltsie",
  "Gato",
  "Ouellet",
  "Red River",
  "Gearing",
  "Glennon",
  "Houston",
  "Flasher",
  "Bunch",
  "Barb",
  "Simpson",
  "Sterett",
  "Cahokia",
  "Gridley",
  "Tunxis",
  "Laning",
  "Stallion",
  "Cincinnati",
  "Miami",
  "O'Brien",
  "Sage",
  "Wheeling",
  "Hoel",
  "Tunny",
  "Acadia",
  "Mero",
  "San Francisco",
  "Vulcan",
  "Nimble",
  "Moana Wave",
  "Papago",
  "Glynn",
  "Holton",
  "Gravely",
  "Sanctuary",
  "Cape Rise",
  "Lander",
  "Bowers",
  "Makin Island",
  "Chanticleer",
  "Helena",
  "Conolly",
  "Shughart",
  "Arikara",
  "Nicholas",
  "Magnet",
  "Chickadee",
  "Tuscaloosa",
  "Skylark",
  "Esteem",
  "White Sands",
  "Nicholson",
  "Mount Vernon",
  "Francovich",
  "Peterson",
  "Hawes",
  "Cowpens",
  "Kankakee",
  "Falgout",
  "Chimariko",
  "Denebola",
  "Chancellorsville",
  "Willamette",
  "Supply",
  "Illinois",
  "Plymouth Rock",
  "Deyo",
  "Harkness",
  "Henley",
  "Sealion",
  "Pickerel",
  "Toledo",
  "Talledega",
  "Wiseman",
  "Coastal Crusader",
  "Tunica",
  "McNair",
  "Massachusetts",
  "Gilliland",
  "Southern Cross",
  "Marshfield",
  "Mataco",
  "Grand Canyon",
  "Aries",
  "Ponchatoula",
  "Cape Taylor",
  "Fahrion",
  "Mullinnix",
  "Hill",
  "Tigrone",
  "Tautog",
  "Fort Mandan",
  "Grenadier",
  "Caliente",
  "McMorris",
  "Henrico",
  "Gosselin",
  "Prime",
  "Hilbert",
  "Fabius",
  "Indiana",
  "Reid",
  "Hickox",
  "Guitarro",
  "Kretchmer",
  "Leary",
  "Iwo Jima",
  "Carney",
  "Daniel",
  "Shakamayon",
  "Chain",
  "Besugo",
  "Wright",
  "Sealift Indian Ocean",
  "Greenfish",
  "Whitemarsh",
  "Velocity",
  "Alexandria",
  "Anzio",
  "De Steiguer",
  "Biddle",
  "Colorado",
  "Libra",
  "Gillespie",
  "Truckee",
  "Kirkpatrick",
  "Pluck",
  "Blueback",
  "Roselle",
  "Hyades",
  "Evans",
  "Cape Ray",
  "Sharps",
  "Parche",
  "Swenning",
  "Bellatrix",
  "Gantner",
  "Wickes",
  "Chatelain",
  "Darter",
  "Upshur",
  "Cread",
  "Niagara Falls",
  "Lamar",
  "Berkeley",
  "Rutland",
  "Lapon",
  "Canberra",
  "South Dakota",
  "Durant",
  "Persistent",
  "Pasadena",
  "Gallup",
  "Acree",
  "Tatum",
  "Burdo",
  "Canopus",
  "Edwards",
  "Laboon",
  "Silversides",
  "Wisconsin",
  "Regulus",
  "Connecticut",
  "Trepang",
  "Tonawanda",
  "Consolation",
  "Conquest",
  "Tinsman",
  "McKean",
  "Hoquiam",
  "Bradford",
  "McConnell",
  "Condor",
  "Tiru",
  "Sprig",
  "Cape Lambert",
  "Aggressive",
  "Tawakoni",
  "Ampere",
  "Dextrous",
  "McNulty",
  "Yano",
  "Enright",
  "Alatna",
  "Brewton",
  "America",
  "Opportune",
  "Concord",
  "Agile",
  "Point Loma",
  "Volador",
  "Waxsaw",
  "White Plains",
  "Contender",
  "Wyman",
  "Cape Bover",
  "Cascade",
  "Kennebec",
  "Sierra",
  "Provo",
  "Dorchester",
  "Roper",
  "Portland",
  "Coghlan",
  "Cape Knox",
  "Briscoe",
  "Kellar",
  "Cook",
  "Davidson",
  "Recovery",
  "Hayter",
  "Rupertus",
  "Mississinewa",
  "Hector",
  "Big Black River",
  "Threat",
  "Zephyr",
  "Cromwell",
  "Suribachi",
  "Clamagore",
  "Thornhill",
  "Barry",
  "Coolbaugh",
  "Smoky Hill River",
  "Maury",
  "Cleveland",
  "Chattahoochee",
  "Hudson",
  "Sunbird",
  "Bremerton",
  "Riley",
  "Mahan",
  "Rochester",
  "Edenton",
  "Boxer",
  "Bexar",
  "Worthy",
  "Michigan",
  "Pilot",
  "O'Hare",
  "Hercules",
  "Cape Hudson",
  "Whippoorwill",
  "Yancey",
  "Cape Diamond",
  "Pride",
  "Batfish",
  "Ross",
  "Monticello",
  "Collingsworth",
  "Laffey",
  "Rigel",
  "Doyle",
  "Goldsborough",
  "Norfolk",
  "Barney",
  "Bryce Canyon",
  "Flint",
  "Cotten",
  "Bray",
  "Sarpedon",
  "Maine",
  "Ramsey",
  "Pavlic",
  "Comstock",
  "Seadragon",
  "Uhlmann",
  "Bridge",
  "Meade",
  "Bowen",
  "Fresno",
  "Abatan",
  "Venture",
  "Pledge",
  "Sea Devil",
  "Dennis",
  "New Mexico",
  "Little Rock",
  "Vireo",
  "Cape Washington",
  "Shawnee Trail",
  "Balduck",
  "Cole",
  "Kephart",
  "Mitscher",
  "Moore",
  "Advance",
  "Midas",
  "Washington",
  "McCampbell",
  "Gansevoort",
  "Peoria",
  "Reaper",
  "Bushnell",
  "Chipola",
  "Mount McKinley",
  "Camden",
  "Hollis",
  "Cabildo",
  "Stoddard",
  "Estocin",
  "Quincy",
  "Coucal",
  "Wahoo",
  "Ashtabula",
  "Lexington",
  "Scout",
  "Dace",
  "Tercel",
  "Catamount",
  "Tripoli",
  "Aludra",
  "Comet",
  "Isle Royale",
  "Marias",
  "Shrike",
  "Magoffin",
  "Baltimore",
  "Thomaston",
  "New Orleans",
  "Cadmus",
  "Fearless",
  "Redpoll",
  "Mercy",
  "Rappahannock",
  "Hemminger",
  "Colonial",
  "Lewis And Clark",
  "Savage",
  "Rankin",
  "Kyne",
  "Sumner",
  "Tomich",
  "Sam Houston",
  "Owen",
  "Exultant",
  "Hammann",
  "Winston",
  "Devastator",
  "Flyer",
  "Namakagon",
  "Surfbird",
  "Oklahoma",
  "Clark",
  "Newman",
  "Vancouver",
  "Davison",
  "Barber",
  "Dixon",
  "Tuscarora",
  "Cape Borda",
  "Jacksonville",
  "Brittin",
  "Welch",
  "Kitty Hawk",
  "Truett",
  "Greeneville",
  "Compass Island",
  "Ainsworth",
  "Impervious",
  "Mount Hood",
  "Freedom.",
  "Arneb",
  "Bataan",
  "Gallatin",
  "Preserver",
  "Tempest",
  "Capella",
  "Taussig",
  "Zumwalt",
  "Mapiro",
  "Black Hawk",
  "Vesole",
  "Cape Orlando",
  "Hart",
  "Witter",
  "Stafford",
  "Gonzalez",
  "Franks",
  "Spokane",
  "Kingman",
  "Indomitable",
  "Tekesta",
  "Cape Florida",
  "Brule",
  "Rogers",
  "Scribner",
  "Gallant",
  "Swerve",
  "Ajax",
  "Nitro",
  "Tullibee",
  "Jason",
  "Shakori",
  "Kauffman",
  "Cape Flattery",
  "Rooks",
  "Blair",
  "Tarawa",
  "Decatur",
  "Princeton",
  "Arizona",
  "Trathen",
  "Hale",
  "Misn Buenaventura",
  "Patriot",
  "Sea Dog",
  "Mender",
  "Quapaw",
  "Semmes",
  "Pickaway",
  "Navasota",
  "Parker",
  "Jicarilla",
  "Chikaskia",
  "Sam Rayburn",
  "Noxubee",
  "Dale",
  "Carpenter",
  "Abraham Lincoln",
  "Okanogan",
  "Salt Lake City",
  "Charleston",
  "Eldorado",
  "Reasoner",
  "Rentz",
  "Washburn",
  "Patterson",
  "Cape Race",
  "Mirfak",
  "Sculpin",
  "Connole",
  "Donner",
  "Baton Rouge",
  "Durham",
  "Gurke",
  "Rhodes",
  "Picuda",
  "Briareus",
  "Germantown",
  "Hitchiti",
  "Cape Johnson",
  "Bollinger",
  "Cape Gibson",
  "Schenectady",
  "Austin",
  "Liddle",
  "Genesee",
  "Flatley",
  "Luiseno",
  "Bates",
  "Louisville",
  "Shea",
  "Tulare",
  "Spangler",
  "Deliver",
  "Aquila",
  "Permit",
  "Ready",
  "Stevens",
  "Vigor",
  "Kilauea",
  "Guardian",
  "Cape Ann",
  "Corbesier",
  "Utah",
  "Hamner",
  "Frederick",
  "North Dakota",
  "Olympia",
  "Schmitt",
  "Bluefish",
  "Gage",
  "Monsoon",
  "Agerholm",
  "Boyle",
  "Duncan",
  "Red Cloud",
  "Pittsburgh",
  "Fort Fisher",
  "Blackfin",
  "Woolsey",
  "Stark",
  "Klakring",
  "Dominant",
  "Hailey",
  "De Haven",
  "Thrush",
  "Shark",
  "Seminole",
  "Paiute",
  "Annapolis",
  "Clemson",
  "Schley",
  "Maumee",
  "Cape Farewell",
  "Hampton",
  "Capitaine",
  "El Paso",
  "Gyre",
  "Jacana",
  "Pathfinder",
  "Sides",
  "Shoup",
  "Nipmuc",
  "Eunice",
  "Snyder",
  "Parks",
  "Merrick",
  "Navigator",
  "Embattle",
  "Sacagawea",
  "Farquhar",
  "Bannock",
  "Hurricane",
  "Roncador",
  "Nantahala",
  "Goodrich",
  "Waters",
  "Hulbert",
  "Lagarto",
  "Norman Scott",
  "Raymond",
  "Blessman",
  "Molala",
  "Sand Lance",
  "Marietta",
  "Alabama",
  "Meredith",
  "Sample",
  "Megara",
  "Claxton",
  "Porterfield",
  "Gurnard",
  "Hank",
  "Detroit",
  "Plunkett",
  "Vreeland",
  "Exploit",
  "Barrett",
  "Stalwart",
  "Moosbrugger",
  "Delong",
  "Thresher",
  "Mellette",
  "Comfort",
  "Trenton",
  "Grand Rapids",
  "Macdonough",
  "Delta",
  "Cockrill",
  "Clinton",
  "Spot",
  "Carronade",
  "Tornado",
  "Kingbird",
  "Squall",
  "Sisler",
  "Tutuila",
  "Brooks",
  "Hollister",
  "Burke",
  "Cabot",
  "Trumpeter",
  "Kirwin",
  "Corry",
  "Aspro",
  "Puget Sound",
  "Saint Paul",
  "Lake Champlain",
  "Springfield",
  "Portage",
  "Whipple",
  "Brown",
  "Pinto",
  "Luce",
  "Benicia",
  "Pecos",
  "Eltanin",
  "Cape Horn",
  "Fort Snelling",
  "Kinzer",
  "Converse",
  "Chambers",
  "Merrimack",
  "Thach",
  "Hanna",
  "Defiance",
  "Butte",
  "Little",
  "Wantuck",
  "Gray",
  "Juneau",
  "Jarrett",
  "Sigsbee",
  "Hurst",
  "Lassen",
  "Hunt",
  "Port Royal",
  "Walke",
  "Antares",
  "McCook",
  "Bullard",
  "Ringness",
  "Bennington",
  "Cossatot",
  "Benham",
  "Cape Kennedy",
  "Conserver",
  "Tirante",
  "Cooner",
  "Midway",
  "Razorback",
  "Horne",
  "Plunger",
  "Hall",
  "Mars",
  "Griffin",
  "Chara",
  "San Diego",
  "Ortolan",
  "Taconic",
  "Kinkaid",
  "Dutton",
  "Coates",
  "Superior",
  "Mizar",
  "Somers",
  "McKee",
  "McCloy",
  "Coronado",
  "Raleigh",
  "Seneca",
  "Benfold",
  "Terry",
  "Buck",
  "Bassett",
  "Charlton",
  "Texas",
  "Constant",
  "Kirk",
  "Mauna Loa",
  "Kraken",
  "Hermitage",
  "Sealift Pacific",
  "Capodanno",
  "Archerfish",
  "Morton",
  "Michelson",
  "Cape Jacob",
  "Laramie",
  "Vindicator",
  "Vandivier",
  "Cusabo",
  "Brooke",
  "Big Horn",
  "Leahy",
  "Remora",
  "Maine",
  "New Hampshire",
  "Gatling",
  "Callaghan",
  "McCaffery",
  "Integrity",
  "California",
  "Petrel",
  "Adria",
  "Mosopelea",
  "Lowry",
  "Warrior",
  "Mount Washington",
  "Tusk",
  "Grapple",
  "Repose",
  "Mosley",
  "Delaware",
  "Cocopa",
  "Pecatonica",
  "Cape Alexander",
  "Fulton",
  "Wyandot",
  "Parrot",
  "Glomar Explorer",
  "Typhoon",
  "Sausalito",
  "Adroit",
  "Thornback",
  "Oriole",
  "Forrestal",
  "Takelma",
  "Boston",
  "Saratoga",
  "Isherwood",
  "Blue Ridge",
  "Pintado",
  "Halsey",
  "Breton",
  "Growler",
  "Heermann",
  "Cape Texas",
  "Challenge",
  "Whidbey Island",
  "Sturtevant",
  "Moctobi",
  "Scabbard Fish",
  "Stewart",
  "Stockdale",
  "Sandoval",
  "Cowell",
  "Range Sentinel",
  "Tombigbee",
  "Burns",
  "Marathon",
  "Falcon",
  "Barbel",
  "Taylor",
  "Kamehameha",
  "Chowanoc",
  "Brumby",
  "Vogelgesang",
  "Mississippi",
  "Capable",
  "Kalmia",
  "Niobrara",
  "Keywadin",
  "Chesapeake",
  "Currituck",
  "Green Bay",
  "Amberjack",
  "Pililaau",
  "Lockwood",
  "Pomeroy",
  "Chloris",
  "Greenlet",
  "Lester",
  "Howard",
  "Key West",
  "West Virginia",
  "Hopping",
  "Thunderbolt",
  "Bradley",
  "Kershaw",
  "Montana",
  "Cape Mendocino",
  "Kite",
  "Shreveport",
  "Pensacola",
  "Kanawha",
  "Levy",
  "Strong",
  "Suamico",
  "Powhatan",
  "Hewitt",
  "Santa Fe",
  "La Salle",
  "Kiska",
  "Ordronaux",
  "Detector",
  "Petaluma",
  "Sarasota",
  "Oriskany",
  "Sphinx",
  "Woodpecker",
  "Pocono",
  "Chosin",
  "Hooper",
  "Racine",
  "Antietam",
  "Remey",
  "Tang",
  "Pelican",
  "Mount Katmai",
  "Mesa Verde",
  "Aulick",
  "Kingsbury",
  "Vance",
  "Humphreys",
  "Cape Ducato",
  "New",
  "Randolph",
  "Joyce",
  "Pascagoula",
  "Sway",
  "Van Valkenburgh",
  "Sarsfield",
  "Blenny",
  "Groton",
  "Tennessee",
  "Weber",
  "Watts",
  "Damato",
  "Mission Buenaventura",
  "Placerville",
  "Chauvenet",
  "Lavallette",
  "Arkansas",
  "Daly",
  "Dewey",
  "Procyon",
  "Chase",
  "Victoria",
  "Shenandoah",
  "Milwaukee",
  "Flying Fish",
  "Carib",
  "Palmyra",
  "Bainbridge",
  "Thuban",
  "Alaska",
  "Sealift Atlantic",
  "Charlotte",
  "Frontier",
  "Meyerkord",
  "Huntsville",
  "Topeka",
  "Narragansett",
  "Tatnuck",
  "Tanner",
  "Davis",
  "Mohawk",
  "Cavalla",
  "Guam",
  "Antrim",
  "Keith",
  "Altair",
  "Bergall",
  "Paul",
  "Seawolf",
  "Mockingbird",
  "Rendova",
  "San Antonio",
  "Current",
  "Robin",
  "Honolulu",
  "Kingsport",
  "Olmsted",
  "Haddock",
  "Oswald",
  "Vermont",
  "Thorn",
  "Ringgold",
  "North Carolina",
  "Mobile Bay",
  "Fanning",
  "McFarland",
  "Tacoma",
  "Dahlgren",
  "Bolster",
  "Narwhal",
  "Diachenko",
  "Enterprise",
  "Sioux",
  "Bennett",
  "Mansfield",
  "Tucson",
  "Dyson",
  "Seattle",
  "Broadbill",
  "American Cormorant",
  "Sea Poacher",
  "Pelias",
  "American Explorer",
  "Upham",
  "Ohio",
  "Cape Domingo",
  "Kishwaukee",
  "Ruddy",
  "Titan",
  "Norris",
  "Hopper",
  "Blue",
  "Rommel",
  "Neunzer",
  "Fort Mchenry",
  "Glover",
  "Cape Wrath",
  "Piedmont",
  "Rich",
  "Belknap",
  "Canisteo",
  "Renshaw",
  "Silverstein",
  "Van Voorhis",
  "Rowan",
  "Edson",
  "Tinosa",
  "Garcia",
  "O'Callahan",
  "Lodestone",
  "Sumter",
  "Walsh",
  "Major",
  "Ogden",
  "Merrill",
  "Wichita",
  "Swallow",
  "Force",
  "Endymion",
  "Ramsden",
  "Ramage",
  "Holder",
  "England",
  "Sands",
  "Rolf",
  "Sampson",
  "Haines",
  "Logan",
  "High Point",
  "Dashiell",
  "Drum",
  "Cape St. George",
  "Chief",
  "Hanford",
  "Epperson",
  "Lindsey",
  "Fidelity",
  "Ingraham",
  "Barr",
  "Robison",
  "Watkins",
  "Pictor",
  "Atakapa",
  "Lowndes",
  "Arcturus",
  "Sellers",
  "Greene",
  "New York City",
  "Lipan",
  "Wadleigh",
  "Kidd",
  "Cape Mohican",
  "Finch",
  "Edmonds",
  "Skipjack",
  "Littlehales",
  "Lizardfish",
  "Krishna",
  "Hayler",
  "Malabar",
  "Crockett",
  "Montpelier",
  "Blakely",
  "Bancroft",
  "Cormorant",
  "Northhampton",
  "Von Steuben",
  "Tawasa",
  "Spruance",
  "Camp",
  "Nassau",
  "Shoshone",
  "Ricketts",
  "Nereus",
  "Savannah",
  "Fentress",
  "Peleliu",
  "Neptune",
  "Cape Fear",
  "Choctaw",
  "Sabalo",
  "Jouett",
  "Laws",
  "Ault",
  "Salmon",
  "Arctic",
  "Elliot",
  "Cardinal",
  "McClelland",
  "Vanguard",
  "Oak Hill",
  "Willis",
  "Triumph",
  "Poole",
  "Sirius",
  "Manitowoc",
  "Bulkeley",
  "Schroeder",
  "Cutlass",
  "Seay",
  "Nevada",
  "Cape Lobos",
  "Everglades",
  "Iowa",
  "Power",
  "Fox",
  "Chickasaw",
  "Sagamore",
  "Pharris",
  "Bearss",
  "Redstone",
  "Bull",
  "Bittern",
  "Pitt",
  "Tenacious",
  "Lofberg",
  "Inch",
  "Jack",
  "Stein",
  "King",
  "Essex",
  "Florikan",
  "Mulberry",
  "Holland",
  "Cobbler",
  "Great Sitkin",
  "Hue City",
  "Rathburne",
  "Normandy",
  "Gillette",
  "Whitley",
  "Shoveler",
  "Voge",
  "Columbus",
  "Momsen",
  "Paricutin",
  "Indianapolis",
  "Straub",
  "Anthony",
  "Lavsan Island",
  "Conflict",
  "Phoebe",
  "Rose Knot",
  "Long Beach",
  "Egeria",
  "Frybarger",
  "Bagley",
  "Pettit",
  "Hummingbird",
  "Crosby",
  "Porter",
  "Amphion",
  "Maryland",
  "Duluth",
  "Rainier",
  "Henderson",
  "Cape Breton",
  "Calcaterra",
  "Cache",
  "Oflaherty",
  "Bingham",
  "Saturn",
  "Hawaii",
  "Tecumseh",
  "Observer",
  "Elrod",
  "Missouri",
  "Shelton",
  "Bon Homme Richard",
  "Osterhaus",
  "Asheboro",
  "Courtney",
  "Hodges",
  "Wadsworth",
  "Truxtun",
  "Independence",
  "Casa Grande",
  "Ponce",
  "Santa Barbara",
  "Haleakala",
  "Direct",
  "Downes",
  "Doran",
  "Marshall",
  "Braine",
  "Bigelow",
  "Cape Decision",
  "Purdy",
  "Sangay",
  "Nitze",
  "Ruchamkin",
  "Lloyd",
  "Cape Nome",
  "Shiloh",
  "Bausell",
  "Vandegrift",
  "Bonefish",
  "Mustin",
  "Valdez",
  "Newport News",
  "Sirocco",
  "Cohoes",
  "Aventinus",
  "Rizzi",
  "Gladiator",
  "Orleck",
  "Taluga",
  "St. Croix",
  "Thor",
  "Furman",
  "Victorious",
  "Strong Virginian",
  "Wandank",
  "Sealift Caribbean",
  "Colleton",
  "Coontz",
  "McCord",
  "Osprey",
  "Osmus",
  "Kawishiwi",
  "Beaufort",
  "Hyde",
  "Underwood",
  "Sperry",
  "Wrangell",
  "Maddox",
  "Kimberly",
  "Neosho",
  "Basilone",
  "Jarvis",
  "Catawba",
  "Williamsburg",
  "Curtiss",
  "Thrasher",
  "Kingfisher",
  "Dortch",
  "Erben",
  "Jenks",
  "Knorr",
  "Millicoma",
  "Entemedor",
  "Dynamic",
  "Schofield",
  "Virginia",
  "Cree",
  "Cimarron",
  "Cable",
  "Guadalcanal",
  "Asheville",
  "San Jose",
  "Haven",
  "Amesbury",
  "Pomfret",
  "Valor",
  "Blue Bird",
  "Sacramento",
  "Patapsgo",
  "Chevalier",
  "Sealift Arabian Sea",
  "Cape Blanco",
  "Eversole",
  "Greenville Victory",
  "Hassayampa",
  "Chinook",
  "Cape Avinof",
  "Hammerberg",
  "Watson",
  "Holt",
  "Spica",
  "Russell",
  "Lafayette",
  "Greenling",
  "Cochrane",
  "Woodrow Wilson",
  "Bordelon",
  "Cape Girardeau",
  "Becuna",
  "Misn Santa Ynez",
  "Bell",
  "Platte",
  "Strickland",
  "Stribling",
  "Massey",
  "Navarro",
  "Milius",
  "Firedrake",
  "Aucilla",
  "Stump",
  "Guardfish",
  "Excel",
  "Roark",
  "Borum",
  "Buffalo",
  "Lansing",
  "Stout",
  "Frankford",
  "Bottineau",
  "Energy",
  "Albany",
  "Waddell",
  "Cape Inscription",
  "Belet",
  "Grayback",
  "Trigger",
  "Steinaker",
  "Achelous",
  "Assurance",
  "Perry",
  "Cruise",
  "Vermilion",
  "San Jacinto",
  "Diamond Head",
  "Lardner",
  "Fanshaw Bay",
  "Pawcatuck",
  "Hawkbill",
  "Notable",
  "Constellation",
  "Estes",
  "Curts",
  "Kentucky",
  "Cape Juby",
  "Corpus Christi Bay",
  "Meadowlark",
  "Parsons",
  "Sturdy",
  "Swift",
  "Osberg",
  "Taurus",
  "Minneapolis-Saint Paul",
  "Healy",
  "Dahl",
  "Melvin",
  "Hornet",
  "Leader",
  "Salvor",
  "Soubarissen",
  "Monongahela",
  "Wainwright",
  "Black",
  "Johnston",
  "Monssen",
  "McClusky",
  "Dent",
  "Cayuga",
  "Conyngham",
  "Durkik",
  "Abnaki",
  "Tringa",
  "De Wert",
  "Cape Island",
  "Pioneer",
  "Weiss",
  "Odum",
  "Reclaimer",
  "Comte De Grasse",
  "Brock",
  "Wasp",
  "Geiger",
  "Vincennes",
  "Stembel",
  "McGowan",
  "Chehalis",
  "Janssen",
  "Impeccable",
  "Dixie",
  "Baya",
  "Stockton",
  "Ute",
  "Whetstone",
  "Muskogee",
  "Hissem",
  "Farragut",
  "Broad Kill River",
  "Boone",
  "Philadelphia",
  "Orion",
  "Nodaway",
  "Firebolt",
  "Bowditch",
  "Loy",
  "Benson",
  "Cape Archway",
  "Apache",
  "Enhance",
  "Mills",
  "Cyclone",
  "Able",
  "Moale",
  "Achomawi",
  "Caron",
  "Nervous Energy",
  "Prosthetic Conscience",
  "The Ends Of Invention",
  "Eschatologist",
  "Irregular Apocalyse",
  "No More Mr Nice Guy",
  "Determinist",
  "Bora Horza Gobuchul",
  "Profit Margin",
  "Trade Surplus",
  "Revisionist",
  "Screw Loose",
  "Flexible Demeanour",
  "Just Read The Instructions",
  "Of Course I Still Love You",
  "Limiting Factor",
  "Cargo Cult",
  "Little Rascal",
  "So Much For Subtlety",
  "Unfortunate Conflict Of Evidence",
  "Youthful Indiscretion",
  "Gunboat Diplomat",
  "Zealot",
  "Prime Mover",
  "Just Testing",
  "Xenophobe",
  "Very Little Gravitas Indeed",
  "Congenital Optimist",
  "Size Isn't Everything",
  "Sweet and Full of Grace",
  "Different Tan",
  "Fate Amenable To Change",
  "Grey Area",
  "It's Character Forming",
  "Jaundiced Outlook",
  "Problem Child",
  "Reasonable Excuse",
  "Recent Convert",
  "Tactical Grace",
  "Unacceptable Behaviour",
  "Steely Glint",
  "Highpoint",
  "Shoot Them Later",
  "Attitude Adjuster",
  "Killing Time",
  "Frank Exchange Of Views",
  "Death and Gravity",
  "Ethics Gradient",
  "Honest Mistake",
  "No Fixed Abode",
  "Quietly Confident",
  "Sleeper Service",
  "Uninvited Guest",
  "Use Psychology",
  "What Is The Answer and Why?",
  "Wisdom Like Silence",
  "Yawning Angel",
  "Zero Gravitas",
  "Misophist",
  "Serious Callers Only",
  "Not Invented Here",
  "Appeal To Reason",
  "Break Even",
  "Long View",
  "Peace Makes Plenty",
  "Sober Counsel",
  "Within Reason",
  "Full Refund",
  "Charitable View",
  "Just Passing Through",
  "Added Value",
  "I Blame Your Mother",
  "I Blame My Mother",
  "Heavy Messing",
  "Bad for Business",
  "Arbitrary",
  "Cantankerous",
  "Only Slightly Bent",
  "I Thought He Was With You",
  "Space Monster",
  "A Series Of Unlikely Explanations",
  "Never Talk To Strangers",
  "Funny, It Worked Last Time...",
  "Boo!",
  "It'll Be Over By Christmas",
  "A Ship With A View",
  "Ablation",
  "Arrested Development",
  "Credibility Problem",
  "Dramatic Exit",
  "Excuses And Accusations",
  "Halation Effect",
  "Happy Idiot Talk",
  "Helpless In The Face Of Your Beauty",
  "Just Another Victim Of The Ambient Morality",
  "Minority Report",
  "Not Wanted On Voyage",
  "Perfidy",
  "Sacrificial Victim",
  "Stranger Here Myself",
  "Synchronize Your Dogmas",
  "Thank you And Goodnight",
  "The Precise Nature Of The Catastrope",
  "Unwitting Accomplice",
  "Undesirable Alien",
  "Well I Was In The Neighbourhood",
  "You Would If You Really Loved Me",
  "You'll Thank Me Later",
  "Winter Storm",
  "Piety",
  "Nuisance Value",
  "Vulgarian",
  "Sanctioned Parts List",
  "Resistance Is Character-Forming",
  "Someone Else's Problem",
  "Lacking That Small Match Temperament",
  "Poke It With A Stick",
  "But Who's Counting?",
  "Germane Riposte",
  "We Haven't Met But You're A Great Fan Of Mine",
  "All The Same, I Saw It First",
  "Zero Credibility",
  "Charming But Irrational",
  "Demented But Determined",
  "You May Not Be The Coolest Person Here",
  "Lucid Nonsense",
  "Awkward Customer",
  "Thorough But ... Unreliable",
  "Advanced Case Of Chronic Patheticism",
  "Another Fine Product From The Nonsense Factory",
  "Conventional Wisdom",
  "In One Ear",
  "Fine Till You Came Along",
  "I Blame The Parents",
  "Inappropriate Response",
  "A Momentary Lapse Of Sanity",
  "Reformed Nice Guy",
  "Pride Comes Before A Fall",
  "Injury Time",
  "Now Look What You've Made Me Do",
  "Kiss This Then",
  "Experiencing A Significant Gravitas Shortfall",
  "Hairier Legs Than Mine"
};

static unsigned int vtkTextLabelListLength =
  sizeof(vtkTextLabelList) / sizeof(vtkTextLabelList[0]);

// This random array happens to be the same length as the
// vtkTextLabelList array. Make sure it remains at least
// that long!
static double vtkLabelPlacer2DTestPoints[] =
{
   0.00921164, 0.819977, 0.347464, 0.831205, 0.0670384, 0.714962,
   0.371513, 0.0237718, 0.53187, 0.133539, 0.381945, 0.35307,
   0.0462124, 0.691374, 0.924344, 0.457128, 0.942825, 0.0607652,
   0.280221, 0.673314, 0.380992, 0.331482, 0.225509, 0.123754,
   0.93843, 0.195171, 0.242676, 0.662619, 0.631243, 0.30133,
   0.447617, 0.107093, 0.907174, 0.865733, 0.368378, 0.331684,
   0.615594, 0.295142, 0.451003, 0.00361544, 0.764768, 0.461687,
   0.571292, 0.699818, 0.837362, 0.551284, 0.434063, 0.30029,
   0.979631, 0.656224, 0.164354, 0.290287, 0.860508, 0.565351,
   0.854881, 0.978975, 0.63096, 0.541449, 0.137936, 0.284214,
   0.77669, 0.823447, 0.675641, 0.501772, 0.277766, 0.410558,
   0.252085, 0.792617, 0.518522, 0.802662, 0.332251, 0.135308,
   0.125279, 0.563129, 0.512682, 0.652736, 0.533406, 0.948849,
   0.302287, 0.530752, 0.340962, 0.555047, 0.676284, 0.31189,
   0.926874, 0.972603, 0.536253, 0.809922, 0.361924, 0.849495,
   0.470372, 0.540795, 0.144947, 0.123148, 0.740687, 0.720296,
   0.0190669, 0.458023, 0.986429, 0.919463, 0.406779, 0.738743,
   0.0489612, 0.891708, 0.928413, 0.837712, 0.431541, 0.904269,
   0.047463, 0.7111, 0.459614, 0.74045, 0.745189, 0.398016,
   0.447616, 0.0806239, 0.046374, 0.408533, 0.213632, 0.512887,
   0.0956262, 0.19025, 0.53053, 0.623171, 0.633394, 0.457661,
   0.905121, 0.375017, 0.914485, 0.744035, 0.988009, 0.473073,
   0.944365, 0.944494, 0.103906, 0.352722, 0.195195, 0.646345,
   0.118655, 0.242551, 0.557834, 0.516189, 0.584005, 0.374389,
   0.350099, 0.118398, 0.921871, 0.885747, 0.75278, 0.981625,
   0.163932, 0.208981, 0.338259, 0.114224, 0.756866, 0.649797,
   0.140211, 0.525654, 0.663248, 0.216916, 0.70975, 0.760232,
   0.222778, 0.225644, 0.397588, 0.269708, 0.987455, 0.158755,
   0.19194, 0.938443, 0.408902, 0.408133, 0.488362, 0.907696,
   0.641816, 0.00403692, 0.848517, 0.0237265, 0.77128, 0.896673,
   0.389918, 0.357299, 0.127779, 0.580769, 0.987407, 0.347348,
   0.880217, 0.801059, 0.397824, 0.232027, 0.675761, 0.510065,
   0.66343, 0.269865, 0.627837, 0.0623407, 0.759489, 0.73345,
   0.0917873, 0.668824, 0.930535, 0.497226, 0.870253, 0.34634,
   0.9444, 0.525171, 0.550098, 0.489602, 0.741733, 0.311525,
   0.795485, 0.717851, 0.925502, 0.91302, 0.123419, 0.311028,
   0.447825, 0.592253, 0.999612, 0.471759, 0.858625, 0.910176,
   0.330858, 0.733188, 0.698071, 0.486099, 0.85826, 0.771814,
   0.875937, 0.878095, 0.143901, 0.551986, 0.227157, 0.832514,
   0.0589326, 0.479662, 0.681143, 0.969573, 0.61705, 0.754182,
   0.530348, 0.556987, 0.284907, 0.426953, 0.791037, 0.957813,
   0.960302, 0.792472, 0.0846935, 0.44438, 0.700365, 0.0345829,
   0.235323, 0.0732833, 0.672021, 0.660814, 0.295711, 0.0214549,
   0.593233, 0.466199, 0.399559, 0.389259, 0.283942, 0.205167,
   0.233713, 0.00605129, 0.704013, 0.344583, 0.408559, 0.644951,
   0.69193, 0.260538, 0.85767, 0.865751, 0.682889, 0.309238,
   0.371072, 0.609343, 0.235205, 0.0974772, 0.299716, 0.319199,
   0.776657, 0.268534, 0.251848, 0.817612, 0.607462, 0.615124,
   0.388897, 0.188194, 0.973502, 0.649598, 0.80185, 0.687508,
   0.94625, 0.627194, 0.24633, 0.0624264, 0.200598, 0.44407,
   0.47779, 0.210866, 0.0257956, 0.547, 0.43066, 0.100981,
   0.188088, 0.201565, 0.705136, 0.223273, 0.54574, 0.247097,
   0.953654, 0.062951, 0.0173391, 0.418538, 0.364271, 0.306433,
   0.227732, 0.485644, 0.213744, 0.395716, 0.799009, 0.940637,
   0.286018, 0.101311, 0.739645, 0.216207, 0.784382, 0.107851,
   0.657926, 0.756327, 0.58172, 0.961214, 0.129855, 0.477344,
   0.727918, 0.115796, 0.186343, 0.866525, 0.689947, 0.930856,
   0.90311, 0.572044, 0.343724, 0.974073, 0.240767, 0.571992,
   0.476226, 0.931022, 0.680039, 0.41542, 0.95733, 0.837931,
   0.113644, 0.0136078, 0.705817, 0.673267, 0.592869, 0.344679,
   0.0139583, 0.596819, 0.744855, 0.786149, 0.802779, 0.306765,
   0.803879, 0.792016, 0.404912, 0.359009, 0.865855, 0.41963,
   0.728268, 0.992515, 0.201468, 0.0800923, 0.111556, 0.923455,
   0.509654, 0.762779, 0.0343794, 0.814362, 0.985321, 0.296453,
   0.477599, 0.00686788, 0.428397, 0.0621733, 0.946402, 0.185493,
   0.581593, 0.841356, 0.673021, 0.464169, 0.294535, 0.249103,
   0.682175, 0.314733, 0.713871, 0.0270751, 0.0506227, 0.815833,
   0.70577, 0.869459, 0.00433748, 0.900063, 0.352159, 0.737701,
   0.541624, 0.0688405, 0.00203305, 0.169404, 0.170001, 0.211147,
   0.746419, 0.0631366, 0.137243, 0.642403, 0.869911, 0.595023,
   0.559254, 0.375682, 0.0795518, 0.0264484, 0.518231, 0.916675,
   0.554188, 0.243873, 0.765219, 0.0423473, 0.730576, 0.792922,
   0.648394, 0.561278, 0.396737, 0.950779, 0.738713, 0.5514,
   0.376826, 0.320054, 0.155575, 0.754422, 0.573998, 0.185987,
   0.886725, 0.190613, 0.639591, 0.600613, 0.502161, 0.825737,
   0.158697, 0.222926, 0.718742, 0.891539, 0.100699, 0.445629,
   0.695, 0.859704, 0.0479826, 0.443325, 0.957508, 0.845057,
   0.877982, 0.246555, 0.846006, 0.828898, 0.281032, 0.297567,
   0.204121, 0.654834, 0.796263, 0.789383, 0.155419, 0.121702,
   0.445907, 0.356049, 0.110197, 0.0868531, 0.739943, 0.21512,
   0.521189, 0.628481, 0.880739, 0.575577, 0.725925, 0.627795,
   0.353866, 0.432755, 0.317263, 0.247245, 0.447425, 0.869152,
   0.830387, 0.320623, 0.718499, 0.804688, 0.383108, 0.902432,
   0.182611, 0.136, 0.750474, 0.215184, 0.591007, 0.0548935,
   0.594651, 0.29671, 0.810676, 0.0348111, 0.0706352, 0.166363,
   0.0601629, 0.157963, 0.880405, 0.970715, 0.807409, 0.1207,
   0.603239, 0.642915, 0.477015, 0.198298, 0.801575, 0.0688153,
   0.578266, 0.924327, 0.164226, 0.141104, 0.531582, 0.299034,
   0.859453, 0.833444, 0.686721, 0.71348, 0.455766, 0.0567995,
   0.629921, 0.0849518, 0.78454, 0.770007, 0.499638, 0.417882,
   0.341394, 0.802555, 0.535003, 0.797639, 0.91909, 0.146834,
   0.844283, 0.865188, 0.207738, 0.455267, 0.67565, 0.654128,
   0.923888, 0.779365, 0.794203, 0.172428, 0.997002, 0.620205,
   0.791694, 0.998441, 0.791884, 0.198072, 0.991197, 0.0551287,
   0.548681, 0.686498, 0.97595, 0.795811, 0.201553, 0.50752,
   0.894122, 0.511354, 0.331266, 0.580909, 0.343496, 0.14078,
   0.0844368, 0.130024, 0.310811, 0.800275, 0.222249, 0.338755,
   0.449925, 0.892724, 0.0107553, 0.76407, 0.72211, 0.494464,
   0.449709, 0.263527, 0.0968643, 0.997694, 0.234835, 0.867734,
   0.00958032, 0.0163636, 0.0235464, 0.744157, 0.0532865, 0.585373,
   0.366023, 0.751856, 0.451885, 0.835524, 0.657834, 0.219885,
   0.609302, 0.546782, 0.770557, 0.744365, 0.536295, 0.504919,
   0.180586, 0.113317, 0.519387, 0.337012, 0.158611, 0.779157,
   0.285274, 0.599727, 0.617612, 0.209508, 0.199102, 0.304128,
   0.479338, 0.238858, 0.48553, 0.299312, 0.542745, 0.914331,
   0.155917, 0.493773, 0.837866, 0.0149391, 0.0812193, 0.0526611,
   0.0744966, 0.0642446, 0.759306, 0.655511, 0.173542, 0.718922,
   0.923952, 0.863631, 0.0393725, 0.733765, 0.386279, 0.193276,
   0.397368, 0.559571, 0.709759, 0.922136, 0.345001, 0.432944,
   0.492868, 0.625549, 0.596143, 0.37073, 0.866338, 0.54319,
   0.40084, 0.91561, 0.654851, 0.0878107, 0.835067, 0.964881,
   0.756416, 0.0910349, 0.0236578, 0.616241, 0.155286, 0.889653,
   0.39488, 0.74058, 0.927173, 0.00181209, 0.455839, 0.278754,
   0.0193973, 0.0107221, 0.205588, 0.317085, 0.254277, 0.634945,
   0.526232, 0.388287, 0.940925, 0.127504, 0.95551, 0.259219,
   0.691616, 0.990652, 0.888024, 0.0220513, 0.616558, 0.489927,
   0.202958, 0.117015, 0.678102, 0.862607, 0.838234, 0.195321,
   0.762525, 0.752729, 0.121225, 0.425655, 0.98228, 0.177356,
   0.815212, 0.259758, 0.752869, 0.468697, 0.395982, 0.26504,
   0.532525, 0.15147, 0.753942, 0.503235, 0.867334, 0.277134,
   0.79102, 0.679656, 0.975773, 0.823716, 0.200355, 0.363995,
   0.671924, 0.0218781, 0.704421, 0.199723, 0.751807, 0.613478,
   0.718432, 0.682089, 0.865853, 0.386227, 0.324988, 0.069,
   0.682705, 0.229523, 0.593749, 0.137033, 0.115043, 0.525719,
   0.7651, 0.0274955, 0.116245, 0.736355, 0.923831, 0.83261,
   0.667971, 0.596602, 0.0905437, 0.767496, 0.298924, 0.0112172,
   0.527085, 0.711858, 0.191575, 0.808815, 0.750152, 0.808579,
   0.78774, 0.54182, 0.369166, 0.566529, 0.644536, 0.720082,
   0.425793, 0.302579, 0.448482, 0.636293, 0.18167, 0.321279,
   0.736586, 0.80744, 0.64538, 0.896556, 0.423715, 0.38186,
   0.913895, 0.825426, 0.939886, 0.668885, 0.955374, 0.97597,
   0.120379, 0.205165, 0.215837, 0.573743, 0.901457, 0.787682,
   0.568188, 0.542169, 0.242026, 0.733524, 0.344679, 0.027177,
   0.764144, 0.962245, 0.446222, 0.646643, 0.128831, 0.262236,
   0.394522, 0.736542, 0.0553103, 0.600864, 0.713883, 0.228737,
   0.38481, 0.49817, 0.743719, 0.682866, 0.925692, 0.0986886,
   0.659319, 0.16819, 0.773473, 0.766871, 0.803692, 0.653477,
   0.989573, 0.757097, 0.531301, 0.569316, 0.496407, 0.113476,
   0.193845, 0.956127, 0.631733, 0.536838, 0.636711, 0.208716,
   0.892044, 0.576015, 0.0840899, 0.2993, 0.343266, 0.279186,
   0.287465, 0.41921, 0.666519, 0.183198, 0.00804122, 0.148758,
   0.167705, 0.611713, 0.0583549, 0.77102, 0.5288, 0.544969,
   0.293798, 0.865103, 0.783871, 0.522395, 0.888173, 0.518327,
   0.514851, 0.0966784, 0.873086, 0.956759, 0.243296, 0.0722611,
   0.491732, 0.539038, 0.604535, 0.424859, 0.609011, 0.639555,
   0.998937, 0.140084, 0.399777, 0.0538551, 0.143248, 0.574415,
   0.195534, 0.338995, 0.496542, 0.374546, 0.986991, 0.356131,
   0.489446, 0.122795, 0.821478, 0.586519, 0.624206, 0.0248468,
   0.600499, 0.583496, 0.810822, 0.49154, 0.308485, 0.699421,
   0.171174, 0.924855, 0.0448304, 0.463923, 0.157493, 0.979678,
   0.442047, 0.479048, 0.353873, 0.544304, 0.125358, 0.889902,
   0.587755, 0.400586, 0.65567, 0.847538, 0.577308, 0.81636,
   0.560954, 0.957543, 0.424162, 0.885078, 0.506765, 0.203868,
   0.407863, 0.947065, 0.316899, 0.119981, 0.513088, 0.462405,
   0.642105, 0.850453, 0.571522, 0.574653, 0.196272, 0.746303,
   0.114379, 0.359533, 0.672782, 0.445712, 0.0856751, 0.940817,
   0.313289, 0.443916, 0.900691, 0.921835, 0.282882, 0.39696,
   0.703656, 0.34427, 0.148683, 0.912136, 0.267437, 0.807726,
   0.447656, 0.746437, 0.360625, 0.0201081, 0.956305, 0.620076,
   0.610485, 0.416885, 0.588588, 0.406305, 0.762131, 0.129899,
   0.214142, 0.089907, 0.0676436, 0.885713, 0.172557, 0.164625,
   0.852161, 0.26415, 0.573579, 0.137027, 0.0101539, 0.656283,
   0.142571, 0.198427, 0.96973, 0.24534, 0.431885, 0.687674,
   0.73754, 0.842411, 0.395701, 0.549277, 0.701355, 0.675792,
   0.0342518, 0.669867, 0.44753, 0.637723, 0.213601, 0.988912,
   0.64071, 0.413929, 0.904198, 0.856626, 0.31587, 0.820605,
   0.912327, 0.480646, 0.225101, 0.279947, 0.0618994, 0.343151,
   0.341565, 0.688027, 0.675962, 0.901458, 0.801628, 0.968527,
   0.0308536, 0.556561, 0.124741, 0.529949, 0.860683, 0.501069,
   0.46844, 0.0689622, 0.0473451, 0.728806, 0.0424824, 0.00202327,
   0.00517529, 0.981055, 0.589066, 0.432051, 0.475427, 0.497677,
   0.458177, 0.572692, 0.239243, 0.963525, 0.968087, 0.6464,
   0.0496256, 0.0570642, 0.0777251, 0.325331, 0.845197, 0.22648,
   0.448332, 0.113064, 0.267234, 0.409798, 0.475404, 0.117331,
   0.984748, 0.665244, 0.760132, 0.541321, 0.98955, 0.372141,
   0.582115, 0.610333, 0.872368, 0.89137, 0.248347, 0.97259,
   0.312904, 0.984524, 0.897239, 0.892031, 0.358951, 0.883536,
   0.585689, 0.681858, 0.979857, 0.454432, 0.63678, 0.353597,
   0.897382, 0.300572, 0.718638, 0.154625, 0.787838, 0.200869,
   0.0125718, 0.293938, 0.216802, 0.784604, 0.832029, 0.914414,
   0.548893, 0.246995, 0.251137, 0.858669, 0.65797, 0.50344,
   0.310514, 0.800876, 0.330099, 0.970595, 0.790804, 0.0419046,
   0.29108, 0.180723, 0.408486, 0.422676, 0.909404, 0.354949,
   0.631967, 0.475216, 0.950923, 0.163346, 0.35278, 0.172458,
   0.506875, 0.0440273, 0.967224, 0.137235, 0.511867, 0.951143,
   0.853855, 0.733703, 0.350929, 0.059283, 0.369295, 0.733619,
   0.931922, 0.811377, 0.812722, 0.41955, 0.371339, 0.102345,
   0.106312, 0.77887, 0.460701, 0.998689, 0.96114, 0.877034,
   0.302832, 0.694757, 0.778628, 0.40528, 0.536855, 0.928917,
   0.303574, 0.172353, 0.73734, 0.481666, 0.359481, 0.801057,
   0.372255, 0.498073, 0.113173, 0.100084, 0.112865, 0.930204,
   0.933076, 0.211311, 0.498131, 0.0917588, 0.1905, 0.735537,
   0.166749, 0.55014, 0.211092, 0.815809, 0.299362, 0.375197,
   0.931167, 0.116012, 0.815055, 0.625073, 0.608793, 0.981823,
   0.498271, 0.436703, 0.674121, 0.957778, 0.377926, 0.802581,
   0.981458, 0.370764, 0.424695, 0.84361, 0.551341, 0.391972,
   0.86787, 0.293041, 0.139121, 0.210377, 0.799898, 0.88631,
   0.212372, 0.338473, 0.715974, 0.367734, 0.508309, 0.141473,
   0.737682, 0.213511, 0.47861, 0.00450534, 0.721273, 0.43634,
   0.566219, 0.447929, 0.338747, 0.314725, 0.577434, 0.928814,
   0.583041, 0.168083, 0.97452, 0.753964, 0.875543, 0.251453,
   0.177916, 0.236446, 0.949907, 0.0832262, 0.783154, 0.473801,
   0.173318, 0.947677, 0.599206, 0.860656, 0.0522033, 0.381676,
   0.836918, 0.0863289, 0.929439, 0.0762689, 0.85215, 0.0770687,
   0.294291, 0.147405, 0.4374, 0.38824, 0.142181, 0.643921,
   0.387538, 0.344055, 0.5329, 0.454014, 0.608583, 0.446777,
   0.983971, 0.602353, 0.747934, 0.520915, 0.0168086, 0.502151,
   0.650684, 0.0529897, 0.598685, 0.091241, 0.48674, 0.640578,
   0.198699, 0.527877, 0.0369948, 0.771979, 0.652357, 0.171514,
   0.643161, 0.603586, 0.470164, 0.041782, 0.230624, 0.0957006,
   0.439706, 0.143794, 0.746043, 0.751005, 0.148812, 0.0823411,
   0.906862, 0.629939, 0.377337, 0.904768, 0.429238, 0.209917,
   0.0691044, 0.437636, 0.342372, 0.241961, 0.632065, 0.11099,
   0.400947, 0.720527, 0.899553, 0.787215, 0.728491, 0.745173,
   0.123967, 0.520426, 0.804662, 0.953355, 0.0309477, 0.138764,
   0.198606, 0.9687, 0.947303, 0.324086, 0.914426, 0.74971,
   0.375101, 0.328099, 0.362405, 0.932515, 0.777202, 0.428321,
   0.790647, 0.396576, 0.255612, 0.0706103, 0.746596, 0.0308958,
   0.266507, 0.18058, 0.999936, 0.93145, 0.882709, 0.69415,
   0.580188, 0.223542, 0.0643723, 0.904459, 0.242477, 0.303118,
   0.509531, 0.694356, 0.0438888, 0.639365, 0.800605, 0.776044,
   0.973204, 0.632293, 0.95124, 0.494371, 0.895627, 0.804935,
   0.548036, 0.840997, 0.644502, 0.142105, 0.360139, 0.849535,
   0.136239, 0.77581, 0.0457593, 0.0763716, 0.577246, 0.767869,
   0.575014, 0.268134, 0.524638, 0.591049, 0.763532, 0.683678,
   0.580608, 0.286871, 0.444323, 0.730161, 0.821138, 0.865952,
   0.0510293, 0.648668, 0.166321, 0.362818, 0.877964, 0.934987,
   0.32666, 0.181021, 0.41928, 0.833845, 0.438913, 0.808663,
   0.191884, 0.999466, 0.0312313, 0.903997, 0.480135, 0.62562,
   0.794477, 0.779162, 0.375246, 0.75814, 0.0563962, 0.851424,
   0.891263, 0.458934, 0.304246, 0.467643, 0.677011, 0.528844,
   0.282628, 0.124944, 0.933536, 0.934755, 0.432823, 0.448362,
   0.614539, 0.55101, 0.820818, 0.482607, 0.178068, 0.794612,
   0.0490282, 0.0168655, 0.458525, 0.436858, 0.267757, 0.186155,
   0.714885, 0.0803502, 0.445635, 0.788235, 0.869374, 0.574186,
   0.342346, 0.813608, 0.305844, 0.320495, 0.561927, 0.304089,
   0.825367, 0.941281, 0.104807, 0.494289, 0.512387, 0.684721,
   0.107786, 0.558031, 0.830194, 0.0765543, 0.647624, 0.620611,
   0.610175, 0.218689, 0.505082, 0.907356, 0.931008, 0.458482,
   0.712175, 0.526264, 0.911347, 0.00592003, 0.498002, 0.919777,
   0.691997, 0.390916, 0.123487, 0.453647, 0.438743, 0.946872,
   0.0842719, 0.357353, 0.0239921, 0.235965, 0.864062, 0.291255,
   0.122107, 0.255404, 0.570916, 0.388485, 0.265786, 0.062965,
   0.253328, 0.677464, 0.131551, 0.983861, 0.758241, 0.761989,
   0.750057, 0.203524, 0.623534, 0.735528, 0.0241604, 0.0631783,
   0.837726, 0.669055, 0.813063, 0.157781, 0.825304, 0.879735,
   0.713837, 0.455202, 0.584443, 0.73494, 0.14063, 0.567422,
   0.656592, 0.349956, 0.707298, 0.549327, 0.531717, 0.572038,
   0.247726, 0.531563, 0.985885, 0.769474, 0.553292, 0.177642,
   0.630777, 0.460802, 0.707616, 0.89487, 0.0771614, 0.852388,
   0.084882, 0.611759, 0.832453, 0.0421072, 0.695613, 0.170867,
   0.768483, 0.899263, 0.919816, 0.352675, 0.416378, 0.0579712,
   0.321833, 0.0506232, 0.824505, 0.449639, 0.0801564, 0.188684,
   0.206162, 0.958938, 0.863136, 0.72373, 0.729258, 0.641762,
   0.0897057, 0.683342, 0.92075, 0.0523174, 0.298628, 0.0450572,
   0.275921, 0.399501, 0.419564, 0.620453, 0.950141, 0.0237171,
   0.612569, 0.441868, 0.46959, 0.391274, 0.140971, 0.30743,
   0.969635, 0.649755, 0.428403, 0.163822, 0.359195, 0.987418,
   0.529507, 0.422236, 0.514933, 0.479645, 0.389478, 0.950822,
   0.473507, 0.239715, 0.893711, 0.60499, 0.0731942, 0.175549,
   0.451881, 0.75766, 0.98838, 0.702535, 0.49914, 0.0455373,
   0.345612, 0.697478, 0.511227, 0.184978, 0.920965, 0.651148,
   0.845901, 0.0627834, 0.200684, 0.88935, 0.29805, 0.334505,
   0.0283577, 0.607199, 0.185681, 0.736168, 0.777623, 0.510238,
   0.572844, 0.784919, 0.128337, 0.955633, 0.316161, 0.716171,
   0.692642, 0.232997, 0.972922, 0.907827, 0.85005, 0.785575,
   0.15699, 0.534399, 0.645617, 0.887973, 0.169517, 0.0737578,
   0.646575, 0.985438, 0.249077, 0.234976, 0.245518, 0.425761,
   0.758537, 0.730677, 0.487926, 0.576399, 0.539538, 0.00770798,
   0.547989, 0.0434513, 0.286201, 0.181747, 0.618445, 0.204207,
   0.100357, 0.694195, 0.336787, 0.376135, 0.697685, 0.988662,
   0.449619, 0.744538, 0.453715, 0.58313, 0.66533, 0.205694,
   0.0969426, 0.314981, 0.886108, 0.821934, 0.247885, 0.201161,
   0.920034, 0.0116909, 0.489241, 0.680625, 0.266111, 0.520527,
   0.499134, 0.93679, 0.628046, 0.561526, 0.567643, 0.384214,
   0.483071, 0.966206, 0.0206604, 0.239224, 0.642895, 0.127983,
   0.0075606, 0.0710209, 0.648882, 0.75778, 0.00149435, 0.115476,
   0.811326, 0.949771, 0.799953, 0.817304, 0.421498, 0.12518,
   0.901067, 0.227228, 0.0172774, 0.380839, 0.768941, 0.596764,
   0.80674, 0.877635, 0.415229, 0.750052, 0.126199, 0.0301871,
   0.354945, 0.560857, 0.322873, 0.520966, 0.868247, 0.629661,
   0.709291, 0.0594918, 0.87921, 0.880081, 0.517583, 0.0119911,
   0.533986, 0.70272, 0.611507, 0.598513, 0.203326, 0.305991,
   0.787899, 0.217319, 0.482807, 0.535158, 0.402685, 0.929562,
   0.156354, 0.833944, 0.103996, 0.867703, 0.490997, 0.190983,
   0.859292, 0.127766, 0.363368, 0.130728, 0.142708, 0.496852,
   0.592738, 0.149546, 0.426678, 0.171656, 0.0145037, 0.764167,
   0.349157, 0.279854, 0.506956, 0.415403, 0.676152, 0.0798168,
   0.481233, 0.0903182, 0.977279, 0.126579, 0.420942, 0.771996,
   0.931986, 0.885147, 0.665718, 0.71415, 0.710789, 0.23455,
   0.0877166, 0.25317, 0.0209904, 0.786273, 0.89177, 0.978563,
   0.709663, 0.309278, 0.0376205, 0.288146, 0.873681, 0.953263,
   0.487024, 0.416221, 0.425486, 0.139876, 0.888844, 0.807481,
   0.328896, 0.749284, 0.214529, 0.591131, 0.140574, 0.625964,
   0.581443, 0.318933, 0.306978, 0.380037, 0.274316, 0.43402,
   0.57434, 0.929016, 0.973354, 0.154067, 0.399647, 0.863262,
   0.847885, 0.400338, 0.482259, 0.322011, 0.0392475, 0.633512,
   0.431604, 0.971433, 0.882565, 0.274808, 0.698353, 0.217107,
   0.925041, 0.166659, 0.0460845, 0.542042, 0.0962001, 0.834309,
   0.233045, 0.792656, 0.175757, 0.951496, 0.800393, 0.197437,
   0.319575, 0.0987195, 0.178418, 0.675064, 0.807679, 0.654171,
   0.656248, 0.559429, 0.317833, 0.817173, 0.224011, 0.945025,
   0.0387007, 0.443186, 0.632357, 0.0205023, 0.581601, 0.961561,
   0.961909, 0.806313, 0.701911, 0.0211029, 0.676318, 0.872993,
   0.397057, 0.340572, 0.998811, 0.0242412, 0.421828, 0.66621,
   0.997383, 0.0173153, 0.0174285, 0.921091, 0.778066, 0.94982,
   0.631959, 0.336486, 0.31209, 0.303405, 0.320541, 0.340408,
   0.236338, 0.13116, 0.411253, 0.929145, 0.145594, 0.993317,
   0.679243, 0.0419781, 0.525698, 0.39834, 0.903667, 0.939637,
   0.473873, 0.385552, 0.96722, 0.0631664, 0.63824, 0.902373,
   0.189006, 0.624568, 0.121189, 0.829035, 0.593614, 0.872434,
   0.998355, 0.347716, 0.0561768, 0.163514, 0.180885, 0.141431,
   0.0236819, 0.0210987, 0.605067, 0.35297, 0.366204, 0.789771,
   0.685882, 0.619809, 0.127642, 0.281828, 0.682904, 0.573191,
   0.614515, 0.15444, 0.669555, 0.205326, 0.917234, 0.945925,
   0.168098, 0.219117, 0.704457, 0.808678, 0.444026, 0.752387,
   0.367896, 0.235297, 0.642223, 0.833726, 0.430878, 0.763745,
   0.267553, 0.759168, 0.342827, 0.888683, 0.101819, 0.268619,
   0.677743, 0.818772, 0.0967181, 0.541309, 0.784648, 0.58131,
   0.0791748, 0.691229, 0.493325, 0.320912, 0.57325, 0.615437,
   0.653703, 0.789816, 0.429348, 0.0519935, 0.855224, 0.75111,
   0.906622, 0.601096, 0.617832, 0.908219, 0.431627, 0.356976,
   0.697085, 0.90419, 0.721152, 0.396757, 0.292478, 0.678543,
   0.279767, 0.0415861, 0.93764, 0.91365, 0.722366, 0.807537,
   0.267066, 0.578562, 0.891317, 0.358395, 0.547624, 0.920507,
   0.960723, 0.869138, 0.605035, 0.821552, 0.818851, 0.432789,
   0.883274, 0.191621, 0.579931, 0.905342, 0.0897643, 0.667938,
   0.0321878, 0.979876, 0.778475, 0.82736, 0.436357, 0.847444,
   0.99527, 0.504789, 0.989915, 0.502576, 0.789257, 0.0372353,
   0.813954, 0.132271, 0.073794, 0.255693, 0.439951, 0.255698,
   0.516094, 0.986839, 0.800273, 0.193972, 0.0927261, 0.447896,
   0.789797, 0.122727, 0.66453, 0.749911, 0.756242, 0.151795,
   0.218094, 0.503858, 0.346341, 0.957039, 0.958699, 0.852693,
   0.209565, 0.160822, 0.930611, 0.77254, 0.084369, 0.989197,
   0.442047, 0.479275, 0.168469, 0.456859, 0.426212, 0.340152,
   0.937572, 0.76488, 0.342747, 0.547134, 0.683402, 0.93792,
   0.624029, 0.055538, 0.426753, 0.435465, 0.868071, 0.6672,
   0.63111, 0.0661186, 0.255324, 0.227074, 0.429496, 0.542728,
   0.624763, 0.389293, 0.852457, 0.241635, 0.159922, 0.810284,
   0.448906, 0.758837, 0.776525, 0.0568491, 0.462146, 0.287192,
   0.832081, 0.790435, 0.836596, 0.661919, 0.877703, 0.561088,
   0.197614, 0.301422, 0.996002, 0.806175, 0.375327, 0.125133,
   0.104162, 0.655026, 0.0165555, 0.248457, 0.810526, 0.515534,
   0.582783, 0.83557, 0.417297, 0.515309, 0.794163, 0.496934,
   0.970508, 0.324847, 0.698808, 0.85818, 0.424512, 0.777246,
   0.177485, 0.982341, 0.204833, 0.62219, 0.148877, 0.176788,
   0.282694, 0.2338, 0.470435, 0.608015, 0.900814, 0.976093,
   0.189976, 0.926324, 0.731633, 0.555979, 0.336951, 0.135955,
   0.999456, 0.849033, 0.704289, 0.988234, 0.250549, 0.97319,
   0.409304, 0.17658, 0.787531, 0.0360684, 0.200807, 0.962899,
   0.445792, 0.430989, 0.62891, 0.0901475, 0.108343, 0.924966,
   0.911169, 0.0171861, 0.847466, 0.359337, 0.379618, 0.238293,
   0.989412, 0.0556066, 0.580665, 0.235412, 0.56117, 0.584001,
   0.299827, 0.189592, 0.466494, 0.362064, 0.212276, 0.728612,
   0.787566, 0.619775, 0.552122, 0.519039, 0.489931, 0.277089,
   0.0429447, 0.771714, 0.196657, 0.209507, 0.176734, 0.369129,
   0.959327, 0.416781, 0.836765, 0.513958, 0.0892318, 0.71897,
   0.726789, 0.141476, 0.788929, 0.522948, 0.18226, 0.245569,
   0.281144, 0.181092, 0.614366, 0.654095, 0.371612, 0.683807,
   0.749037, 0.0672187, 0.744127, 0.546263, 0.0413061, 0.23083,
   0.56171, 0.659177, 0.783597, 0.919417, 0.648068, 0.0709468,
   0.402212, 0.971968, 0.874117, 0.277312, 0.785311, 0.722022,
   0.015992, 0.777689, 0.617716, 0.959093, 0.471641, 0.873905,
   0.71633, 0.354781, 0.811633, 0.123267, 0.750201, 0.625855,
   0.752804, 0.383196, 0.367842, 0.318063, 0.676528, 0.401175,
   0.552717, 0.509055, 0.688833, 0.224237, 0.755739, 0.708512,
   0.9638, 0.592141, 0.117769, 0.342138, 0.314509, 0.944605,
   0.98139, 0.215795, 0.867147, 0.143792, 0.716773, 0.799678,
   0.184907, 0.730848, 0.357394, 0.722582, 0.430472, 0.950727,
   0.860681, 0.458486, 0.771335, 0.820404, 0.531977, 0.942267,
   0.68555, 0.0322665, 0.302251, 0.929311, 0.925019, 0.797247,
   0.330946, 0.215229, 0.351323, 0.687936, 0.140491, 0.239726,
   0.0822563, 0.481845, 0.361011, 0.515877, 0.347397, 0.703418,
   0.340229, 0.232429, 0.428853, 0.732093, 0.294161, 0.957297,
   0.29738, 0.0616925, 0.865214, 0.658506, 0.505713, 0.519787,
   0.0671259, 0.185694, 0.952661, 0.375919, 0.0646607, 0.752793,
   0.19224, 0.977349, 0.303806, 0.0719589, 0.41335, 0.167529,
   0.667464, 0.0686066, 0.0712313, 0.183771, 0.641323, 0.723738,
   0.868661, 0.592844, 0.931705, 0.159491, 0.570783, 0.153344,
   0.257332, 0.983394, 0.903501, 0.149364, 0.358569, 0.464872,
   0.104886, 0.82215, 0.871212, 0.461711, 0.982419, 0.511367,
   0.540543, 0.901364, 0.229812, 0.445307, 0.280365, 0.0986663,
   0.283849, 0.642804, 0.608141, 0.0267921, 0.295477, 0.086513,
   0.0241103, 0.222305, 0.286231, 0.677633, 0.984239, 0.113026,
   0.631365, 0.359125, 0.809792, 0.170063, 0.24501, 0.888233,
   0.526199, 0.81967, 0.194465, 0.378425, 0.188825, 0.588352,
   0.430893, 0.0111055, 0.650885, 0.420982, 0.438896, 0.529989,
   0.525862, 0.160759, 0.875984, 0.660975, 0.000387218, 0.50797,
   0.458504, 0.0720393, 0.7653, 0.399667, 0.197636, 0.668345,
   0.882124, 0.850495, 0.269462, 0.851524, 0.567695, 0.247768,
   0.231076, 0.701851, 0.0175667, 0.243433, 0.380555, 0.983891,
   0.261555, 0.957145, 0.728327, 0.993508, 0.884572, 0.000801061,
   0.463429, 0.85312, 0.389586, 0.769193, 0.831667, 0.824669,
   0.206712, 0.212163, 0.831587, 0.474636, 0.201333, 0.805295,
   0.60113, 0.191717, 0.17961, 0.703088, 0.7952, 0.920303,
   0.524446, 0.361725, 0.509851, 0.0600288, 0.904524, 0.336603,
   0.285901, 0.133574, 0.982799, 0.903679, 0.133613, 0.637952,
   0.0634623, 0.61074, 0.699146, 0.541673, 0.903186, 0.851014,
   0.985987, 0.484615, 0.928026, 0.328396, 0.348537, 0.868114,
   0.39434, 0.675469, 0.600998, 0.980431, 0.0983345, 0.708539,
   0.410976, 0.266742, 0.139063, 0.234726, 0.0361525, 0.615635,
   0.969626, 0.509123, 0.825888, 0.691788, 0.874974, 0.692929,
   0.0518878, 0.0775269, 0.994836, 0.202528, 0.89283, 0.801992,
   0.0807178, 0.623411, 0.667457, 0.95287, 0.89042, 0.292267,
   0.123173, 0.161831, 0.900214, 0.89706, 0.880272, 0.724242,
   0.332238, 0.924622, 0.128022, 0.667856, 0.651131, 0.558705,
   0.15211, 0.50778, 0.264108, 0.871506, 0.408204, 0.679121,
   0.987548, 0.724772, 0.245659, 0.785387, 0.00704876, 0.468503,
   0.126751, 0.300177, 0.0773379, 0.818363, 0.22353, 0.871922,
   0.390249, 0.907604, 0.0963743, 0.763163, 0.487964, 0.216776,
   0.355977, 0.901986, 0.677733, 0.657118, 0.183704, 0.513379,
   0.366173, 0.274021, 0.477181, 0.984682, 0.542599, 0.45365,
   0.491485, 0.380027, 0.108538, 0.200592, 0.342405, 0.801922,
   0.897731, 0.160582, 0.900586, 0.15083, 0.991416, 0.735416,
   0.141253, 0.0444179, 0.531286, 0.327067, 0.00725067, 0.861938,
   0.596312, 0.211828, 0.188039, 0.364832, 0.73713, 0.947066,
   0.342835, 0.0362105, 0.589125, 0.428942, 0.232729, 0.481197,
   0.47738, 0.332325, 0.384561, 0.31856, 0.0402633, 0.70539,
   0.491939, 0.0176865, 0.257534, 0.367258, 0.502348, 0.955705,
   0.540158, 0.43701, 0.831401, 0.361729, 0.57225, 0.810814,
   0.345758, 0.154105, 0.0390846, 0.895167, 0.0695241, 0.492067,
   0.163857, 0.944412, 0.725526, 0.917474, 0.98585, 0.185111,
   0.152802, 0.150655, 0.0600263, 0.862254, 0.901009, 0.261556,
   0.964212, 0.50695, 0.305114, 0.0456411, 0.0896164, 0.183574,
   0.334409, 0.404425, 0.173848, 0.859352, 0.127045, 0.239696,
   0.573871, 0.0502023, 0.750492, 0.520608, 0.853189, 0.552592,
   0.416049, 0.53815, 0.692923, 0.950959, 0.766211, 0.708601,
   0.450427, 0.326537, 0.104325, 0.393347, 0.984022, 0.461008,
   0.153284, 0.247629, 0.898732, 0.994069, 0.319894, 0.458968,
   0.883366, 0.737815, 0.448432, 0.804006, 0.927127, 0.228896,
   0.0469571, 0.208345, 0.649669, 0.98389, 0.234265, 0.284877,
   0.932569, 0.680498, 0.125215, 0.490118, 0.418852, 0.651852,
   0.683019, 0.495065, 0.559923, 0.619779, 0.63055, 0.659214,
   0.410865, 0.410061, 0.903431, 0.96172, 0.630756, 0.112834,
   0.406017, 0.92664, 0.033646, 0.488245, 0.940499, 0.961077,
   0.828277, 0.844607, 0.314767, 0.285, 0.997165, 0.34901,
   0.80921, 0.39492, 0.416082, 0.0852057, 0.0527761, 0.00737501,
   0.951782, 0.605728, 0.478592, 0.698166, 0.0691696, 0.532819,
   0.0827556, 0.872722, 0.838275, 0.894357, 0.454686, 0.908897,
   0.833708, 0.129766, 0.978884, 0.108387, 0.653993, 0.656135,
   0.652949, 0.107255, 0.627502, 0.427824, 0.444293, 0.237789,
   0.51581, 0.223922, 0.45393, 0.196228, 0.00599107, 0.691922,
   0.140896, 0.039503, 0.927191, 0.298322, 0.89648, 0.141991,
   0.438295, 0.430279, 0.693321, 0.649415, 0.72294, 0.458577,
   0.301288, 0.747995, 0.549597, 0.0806066, 0.75449, 0.706724,
   0.913141, 0.156217, 0.541507, 0.107396, 0.00156861, 0.363555,
   0.273939, 0.0861067, 0.195141, 0.741421, 0.0619014, 0.377172,
   0.13633, 0.304363, 0.428086, 0.840639, 0.617211, 0.471474,
   0.0557778, 0.458037, 0.23476, 0.61348, 0.763652, 0.707429,
   0.753404, 0.46669, 0.654086, 0.215179, 0.506498, 0.709857,
   0.568382, 0.792586, 0.990047, 0.713517, 0.0828693, 0.78515,
   0.0175727, 0.344072, 0.824972, 0.312729, 0.0436015, 0.81105,
   0.324504, 0.940228, 0.405966, 0.0732837, 0.679378, 0.306335,
   0.564714, 0.152579, 0.394436, 0.287834, 0.621498, 0.522093,
   0.822563, 0.816489, 0.725665, 0.257441, 0.811438, 0.842367,
   0.662496, 0.563133, 0.572351, 0.496833, 0.268382, 0.697455,
   0.119705, 0.875588, 0.00911642, 0.219648, 0.632082, 0.410528,
   0.746713, 0.00302351, 0.816052, 0.387693, 0.953429, 0.283769,
   0.313276, 0.231767, 0.305868, 0.728366, 0.653756, 0.675455,
   0.376992, 0.105858, 0.162803, 0.224463, 0.547106, 0.216911,
   0.628058, 0.7688, 0.214425, 0.835567, 0.373164, 0.773721,
   0.935751, 0.16884, 0.696056, 0.613995, 0.414217, 0.740041,
   0.87208, 0.044006, 0.60927, 0.993419, 0.391246, 0.670059,
   0.678717, 0.197398, 0.671317, 0.823728, 0.389581, 0.684637,
   0.697404, 0.264768, 0.950687, 0.19493, 0.196537, 0.204602,
   0.746932, 0.686927, 0.179082, 0.835271, 0.405126, 0.953488,
   0.278117, 0.320797, 0.643557, 0.263779, 0.338647, 0.637338,
   0.74099, 0.818135, 0.393516, 0.829925, 0.54691, 0.916594,
   0.1951, 0.0492725, 0.122994, 0.153688, 0.0359633, 0.435421,
   0.115565, 0.30887, 0.179381, 0.855934, 0.685447, 0.301308,
   0.0896472, 0.700201, 0.275342, 0.667349, 0.127706, 0.357461,
   0.841893, 0.701857, 0.112696, 0.085248, 0.763246, 0.876245,
   0.0473259, 0.406054, 0.555637, 0.594141, 0.732385, 0.192951,
   0.931181, 0.359123, 0.773368, 0.993197, 0.657841, 0.330519,
   0.0274757, 0.783617, 0.248821, 0.942033, 0.743353, 0.527614,
   0.612224, 0.64731, 0.338164, 0.522379, 0.628447, 0.305289,
   0.996628, 0.3334, 0.459545, 0.577201, 0.0206621, 0.267767,
   0.361208, 0.820593, 0.704592, 0.0731358, 0.193034, 0.329495,
   0.821117, 0.509468, 0.636317, 0.581848, 0.111462, 0.347921,
   0.505986, 0.0997631, 0.717672, 0.909032, 0.0925625, 0.697718,
   0.542582, 0.168113, 0.466822, 0.875452, 0.717125, 0.720702,
   0.835451, 0.426144, 0.197401, 0.710511, 0.564971, 0.461034,
   0.604498, 0.795729, 0.812373, 0.550125, 0.949433, 0.112938,
   0.146625, 0.325336, 0.924118, 0.653536, 0.972401, 0.140994,
   0.692057, 0.394233, 0.873314, 0.789136, 0.00970479, 0.108407,
   0.995082, 0.336017, 0.444101, 0.997304, 0.689243, 0.107083,
   0.742757, 0.519879, 0.611769, 0.00971271, 0.241471, 0.400556,
   0.137705, 0.413643, 0.105875, 0.433329, 0.959648, 0.800673,
   0.917371, 0.260386, 0.312034, 0.358629, 0.47337, 0.932729,
   0.368363, 0.0685616, 0.315157, 0.84421, 0.633568, 0.369094,
   0.356924, 0.82169, 0.139511, 0.754654, 0.462201, 0.211439,
   0.655634, 0.245354, 0.665275, 0.276054, 0.643854, 0.247995,
   0.047488, 0.131173, 0.618855, 0.100514, 0.33237, 0.146716,
   0.862983, 0.153355, 0.431187, 0.96382, 0.920129, 0.603394,
   0.249981, 0.424237, 0.153991, 0.12297, 0.761571, 0.729702,
   0.0975681, 0.826656, 0.604741, 0.886228, 0.826573, 0.205402,
   0.192783, 0.106175, 0.479114, 0.476268, 0.641578, 0.995825,
   0.822622, 0.802807, 0.775416, 0.418171, 0.194314, 0.833462,
   0.993953, 0.360891, 0.498294, 0.826378, 0.940593, 0.54677,
   0.567994, 0.283332, 0.959346, 0.722205, 0.0989476, 0.0116565,
   0.91063, 0.96041, 0.609035, 0.0576239, 0.484504, 0.0651855,
   0.572524, 0.414143, 0.505064, 0.603724, 0.79098, 0.994402,
   0.921657, 0.288521, 0.170754, 0.862989, 0.262446, 0.936416,
   0.346406, 0.0443176, 0.845917, 0.322079, 0.179143, 0.849279,
   0.835571, 0.445848, 0.36516, 0.242378, 0.652615, 0.50539,
   0.0879584, 0.317567, 0.356667, 0.497801, 0.536931, 0.193063,
   0.813266, 0.566415, 0.741249, 0.176922, 0.523094, 0.635647,
   0.316783, 0.170729, 0.448497, 0.889649, 0.322947, 0.776769,
   0.158657, 0.54845, 0.79216, 0.824873, 0.63233, 0.565109,
   0.792322, 0.554679, 0.486683, 0.67907, 0.123736, 0.636019,
   0.575037, 0.653027, 0.421521, 0.505189, 0.71837, 0.643647,
   0.774433, 0.894052, 0.332233, 0.848104, 0.0830674, 0.114621,
   0.428791, 0.693425, 0.388145, 0.552568, 0.00377168, 0.390547,
   0.91576, 0.179309, 0.653679, 0.376583, 0.234134, 0.0909816,
   0.128259, 0.640975, 0.861584, 0.64536, 0.560782, 0.0579905,
   0.646721, 0.447285, 0.51264, 0.933915, 0.313716, 0.622427,
   0.12227, 0.991064, 0.815535, 0.695226, 0.667499, 0.654199,
   0.123153, 0.840794, 0.220144, 0.966743, 0.0518601, 0.612163,
   0.626415, 0.154465, 0.0973362, 0.929288, 0.54854, 0.303486,
   0.692527, 0.309283, 0.116135, 0.875237, 0.106273, 0.131386,
   0.20795, 0.0147782, 0.377076, 0.510715, 0.590388, 0.646613,
   0.621737, 0.534328, 0.458881, 0.418544, 0.467697, 0.578304,
   0.552064, 0.547137, 0.732786, 0.941363, 0.493612, 0.13787,
   0.188021, 0.0679312, 0.719599, 0.303563, 0.991431, 0.978292,
   0.153433, 0.744382, 0.82083, 0.692745, 0.96768, 0.797513,
   0.80507, 0.81719, 0.512678, 0.584803, 0.785405, 0.294652,
   0.219141, 0.111131, 0.775225, 0.207778, 0.130542, 0.0190868,
   0.792537, 0.17457, 0.00214379, 0.0307179, 0.276004, 0.802503,
   0.672694, 0.964141, 0.324691, 0.075299, 0.550065, 0.942038,
   0.831924, 0.140763, 0.797655, 0.190853, 0.66499, 0.483491,
   0.0323685, 0.0172901, 0.594053, 0.252904, 0.552556, 0.812284,
   0.055484, 0.520342, 0.386532, 0.437681, 0.102171, 0.193509,
   0.307954, 0.783097, 0.50989, 0.716254, 0.0849843, 0.331658,
   0.182162, 0.600897, 0.272654, 0.500659, 0.582393, 0.282989,
   0.193401, 0.492933, 0.723111, 0.324014, 0.70551, 0.502189,
   0.287798, 0.0181713, 0.405543, 0.96388, 0.936469, 0.2321,
   0.912726, 0.189019, 0.840208, 0.381866, 0.0224776, 0.780699,
   0.208126, 0.980816, 0.568026, 0.815721, 0.817384, 0.774247,
   0.774172, 0.502981, 0.60928, 0.170428, 0.3814, 0.195631,
   0.968751, 0.794007, 0.871234, 0.831813, 0.272882, 0.331448,
   0.646631, 0.931254, 0.590774, 0.138164, 0.113961, 0.346402,
   0.976205, 0.0725465, 0.28896, 0.547044, 0.176012, 0.233773,
   0.016553, 0.205529, 0.325758, 0.0203419, 0.88681, 0.607272,
   0.42616, 0.475589, 0.221191, 0.556148, 0.179974, 0.815952,
   0.700007, 0.0132562, 0.796486, 0.540178, 0.771924, 0.727969,
   0.980281, 0.587346, 0.519204, 0.266219, 0.334366, 0.696914,
   0.0262618, 0.38262, 0.696469, 0.548667, 0.444468, 0.169852,
   0.701222, 0.446224, 0.680703, 0.581285, 0.656598, 0.439896,
   0.339559, 0.965689, 0.339033, 0.133131, 0.526695, 0.168293,
   0.499211, 0.24377, 0.0440521, 0.383517, 0.763724, 0.91456,
   0.0103098, 0.27598, 0.391783, 0.70111, 0.552438, 0.824417,
   0.983447, 0.790926, 0.0876236, 0.690447, 0.344681, 0.0491691,
   0.385328, 0.2132, 0.253092, 0.72258, 0.404145, 0.457607,
   0.00437671, 0.559442, 0.542348, 0.241701, 0.273888, 0.24136,
   0.53244, 0.725365, 0.201262, 0.612635, 0.557595, 0.496667,
   0.486006, 0.303778, 0.604795, 0.791238, 0.330242, 0.382439,
   0.659438, 0.17844, 0.0435847, 0.527509, 0.841977, 0.11501,
   0.970337, 0.44904, 0.00701654, 0.926934, 0.974887, 0.923092,
   0.405509, 0.391068, 0.685755, 0.478569, 0.309571, 0.954913,
   0.217361, 0.186405, 0.91385, 0.0767558, 0.0350725, 0.463764,
   0.483755, 0.462039, 0.484615, 0.917178, 0.0170215, 0.0795608,
   0.179031, 0.978949, 0.198036, 0.39716, 0.0739331, 0.593516,
   0.224185, 0.877096, 0.355651, 0.421093, 0.317781, 0.938326,
   0.451167, 0.75658, 0.839437, 0.411796, 0.0484464, 0.239414,
   0.825949, 0.730261, 0.492545, 0.207525, 0.879243, 0.429492,
   0.47031, 0.505609, 0.773247, 0.962786, 0.546387, 0.130571,
   0.506451, 0.913681, 0.229194, 0.0631589, 0.512126, 0.302455,
   0.364844, 0.936343, 0.109926, 0.520707, 0.516668, 0.646386,
   0.816071, 0.70639, 0.3037, 0.288095, 0.00529818, 0.0464415,
   0.542895, 0.432183, 0.703891, 0.304397, 0.00640768, 0.693956,
   0.31689, 0.964322, 0.358019, 0.228152, 0.549631, 0.642869,
   0.692265, 0.904281, 0.255918, 0.220087, 0.00737114, 0.886714,
   0.000557859, 0.375943, 0.480969, 0.642358, 0.106144, 0.953816,
   0.791466, 0.167957, 0.85762, 0.0184138, 0.48118, 0.18782,
   0.697239, 0.498851, 0.180979, 0.718014, 0.653414, 0.935228,
   0.380892, 0.64911, 0.599494, 0.692485, 0.588772, 0.484283,
   0.338065, 0.857828, 0.513976, 0.387338, 0.99332, 0.733511,
   0.125001, 0.893002, 0.678355, 0.111692, 0.202635, 0.681242,
   0.633498, 0.208498, 0.223707, 0.841645, 0.535497, 0.102716,
   0.347583, 0.824167, 0.770176, 0.343443, 0.251657, 0.602477,
   0.831791, 0.903924, 0.245502, 0.144927, 0.788085, 0.344901,
   0.745242, 0.289619, 0.627055, 0.905744, 0.845353, 0.850521,
   0.701016, 0.9767, 0.400766, 0.674692, 0.549315, 0.330345,
   0.104749, 0.522786, 0.463669, 0.884866, 0.94961, 0.0953167,
   0.988477, 0.334254, 0.81259, 0.199162, 0.311617, 0.338806,
   0.311984, 0.517012, 0.425784, 0.158801, 0.967776, 0.410919,
   0.320096, 0.854361, 0.237374, 0.550796, 0.221666, 0.542285,
   0.180524, 0.065194, 0.715723, 0.161053, 0.815644, 0.525188,
   0.833123, 0.290635, 0.697, 0.476245, 0.257092, 0.938906,
   0.199958, 0.697776, 0.519344, 0.610224, 0.0334531, 0.246516,
   0.199423, 0.702992, 0.191733, 0.458268, 0.106035, 0.122696,
   0.15825, 0.701934, 0.403554, 0.533411, 0.0382729, 0.253054,
   0.0771629, 0.87737, 0.953502, 0.503947, 0.843674, 0.635129,
   0.613336, 0.337282, 0.70466, 0.213391, 0.45954, 0.491307,
   0.393437, 0.499356, 0.67601, 0.700162, 0.627522, 0.758473,
   0.652767, 0.0514409, 0.566584, 0.574168, 0.0416337, 0.737174,
   0.682549, 0.606476, 0.0463637, 0.235273, 0.230078, 0.922264,
   0.499006, 0.789148, 0.212063, 0.149457, 0.926292, 0.193828,
   0.673791, 0.406026, 0.0748398, 0.833202, 0.630626, 0.939397,
   0.441485, 0.0394289, 0.68146, 0.297045, 0.441741, 0.341842,
   0.344349, 0.477632, 0.554714, 0.0735667, 0.434727, 0.455637,
   0.892112, 0.724636, 0.964491, 0.203202, 0.212578, 0.797251,
   0.394528, 0.82822, 0.888961, 0.761608, 0.341528, 0.0641562,
   0.272718, 0.578804, 0.965012, 0.960964, 0.918275, 0.442024,
   0.102701, 0.0962546, 0.751176, 0.0164641, 0.711757, 0.497362,
   0.166126, 0.0880301, 0.521271, 2.09413e-05, 0.35196, 0.385871,
   0.326944, 0.955083, 0.0754337, 0.813568, 0.643436, 0.225182,
   0.638667, 0.0742234, 0.471927, 0.673668, 0.34258, 0.742383,
   0.22319, 0.147394, 0.258951, 0.195132, 0.58523, 0.962263,
   0.761757, 0.855648, 0.868606, 0.659604, 0.971233, 0.514057,
   0.753102, 0.389654, 0.907077, 0.242223, 0.038068, 0.808043,
   0.773578, 0.526646, 0.338432, 0.0213833, 0.389036, 0.535964,
   0.942265, 0.645361, 0.585724, 0.268921, 0.760883, 0.165255,
   0.441825, 0.756842, 0.244908, 0.164279, 0.0399154, 0.857324,
   0.0366559, 0.075204, 0.954057, 0.835515, 0.505384, 0.99531,
   0.173231, 0.500863, 0.997539, 0.638374, 0.154215, 0.8991,
   0.165897, 0.230397, 0.28242, 0.6255, 0.784634, 0.349016,
   0.906085, 0.569661, 0.29876, 0.256539, 0.645641, 0.284183,
   0.270042, 0.593194, 0.809504, 0.326832, 0.0578847, 0.868672,
   0.77181, 0.806306, 0.586394, 0.52335, 0.941762, 0.197046,
   0.752633, 0.502982, 0.620276, 0.98102, 0.00687803, 0.599066,
   0.506719, 0.431664, 0.96892, 0.63674, 0.684332, 0.574967,
   0.46705, 0.711257, 0.0970853, 0.711979, 0.227558, 0.575174,
   0.941089, 0.888498, 0.987949, 0.460388, 0.743806, 0.149433,
   0.528736, 0.468804, 0.190239, 0.355274, 0.097481, 0.363414,
   0.905257, 0.648374, 0.221958, 0.444394, 0.92643, 0.503593,
   0.895055, 0.182344, 0.653674, 0.296911, 0.179451, 0.0288017,
   0.0699155, 0.0704978, 0.857067, 0.731167, 0.717964, 0.820579,
   0.473041, 0.396136, 0.853972, 0.706692, 0.369631, 0.382982,
   0.774302, 0.690334, 0.447999, 0.523902, 0.213521, 0.646979,
   0.776942, 0.0585694, 0.376628, 0.987798, 0.916904, 0.409575,
   0.731379, 0.281012, 0.977085, 0.863234, 0.372496, 0.532011,
   0.503778, 0.995698, 0.691713, 0.614616, 0.847526, 0.370801,
   0.060282, 0.158923, 0.015162, 0.827835, 0.427988, 0.187522,
   0.679449, 0.492898, 0.140312, 0.231082, 0.799587, 0.661465,
   0.242697, 0.00340222, 0.181152, 0.623874, 0.450312, 0.390911,
   0.0470238, 0.329456, 0.174745, 0.935083, 0.948362, 0.126102,
   0.398092, 0.727681, 0.138177, 0.341662, 0.30681, 0.557449,
   0.0394847, 0.618965, 0.945611, 0.892076, 0.123845, 0.466184,
   0.160643, 0.935201, 0.920271, 0.992589, 0.448256, 0.83897,
   0.573014, 0.641371, 0.527178, 0.274917, 0.52401, 0.0369476
};

int TestLabelPlacer2D( int argc, char* argv[] )
{
  vtkIdType i;

  vtkRenderer* rr = vtkRenderer::New();
  vtkRenderWindow* rw = vtkRenderWindow::New();
  vtkRenderWindowInteractor* ri = vtkRenderWindowInteractor::New();
  //rw->SetSize( 1800, 1200 );
  rw->SetSize( 600, 600 );

  vtkPoints* pts = vtkPoints::New();
  vtkPolyData* polyData = vtkPolyData::New();
  vtkStringArray* labelText = vtkStringArray::New();
  labelText->SetName( "LabelText" );
  labelText->SetNumberOfValues( TXTMULT * vtkTextLabelListLength );
  vtkIdTypeArray* priorities = vtkIdTypeArray::New();
  priorities->SetName( "Priorities" );
  priorities->SetNumberOfComponents( 1 );
  priorities->SetNumberOfTuples( TXTMULT * vtkTextLabelListLength );
  pts->SetNumberOfPoints( TXTMULT * vtkTextLabelListLength );
#ifdef GENERATE_TEST_POINTS
  cout << "static double vtkLabelPlacer2DTestPoints[] =\n{\n";
#else
  double* ptsrc = vtkLabelPlacer2DTestPoints;
#endif
  for ( i = 0; i < static_cast<int>( TXTMULT * vtkTextLabelListLength); ++ i )
  {
    labelText->SetValue( i, vtkTextLabelList[i % vtkTextLabelListLength] );
    priorities->SetValue( i, i );
#ifdef GENERATE_TEST_POINTS
    double x = vtkMath::Random(), y = vtkMath::Random();
    cout << "   " << x << ", " << y << ",\n";
    pts->SetPoint( i, x, y, -1. ); // 2-D
    //pts->SetPoint( i, vtkMath::Random(), vtkMath::Random(), vtkMath::Random() ); // 3-D
#else // GENERATE_TEST_POINTS
    if ( i % vtkTextLabelListLength == 0 )
    {
      ptsrc = vtkLabelPlacer2DTestPoints;
    }
    pts->SetPoint( i,
      ptsrc[0] + double( ( i / vtkTextLabelListLength ) % PTSMULT ),
      ptsrc[1] + double( ( i / vtkTextLabelListLength ) / PTSMULT ),
      -1. ); // 2-D
    ptsrc += 2;
#endif // GENERATE_TEST_POINTS
  }
#ifdef GENERATE_TEST_POINTS
  cout << "};\n";
#endif
  polyData->SetPoints( pts );
  polyData->GetPointData()->AddArray( labelText );
  polyData->GetPointData()->AddArray( priorities );

  vtkLabelSizeCalculator* sizeCalc = vtkLabelSizeCalculator::New();
  sizeCalc->SetInputData( polyData );
  sizeCalc->GetFontProperty()->SetFontSize( 12 );
  sizeCalc->GetFontProperty()->SetFontFamily( vtkTextProperty::GetFontFamilyFromString( "Arial" ) );
  sizeCalc->SetInputArrayToProcess( 0, 0, 0, vtkDataObject::FIELD_ASSOCIATION_POINTS, "LabelText" );

  vtkPointSetToLabelHierarchy* labelHierarchy = vtkPointSetToLabelHierarchy::New();
  labelHierarchy->SetInputConnection( sizeCalc->GetOutputPort() );
  labelHierarchy->SetInputArrayToProcess( 0, 0, 0, vtkDataObject::FIELD_ASSOCIATION_POINTS, "Priorities" );
  labelHierarchy->SetInputArrayToProcess( 1, 0, 0, vtkDataObject::FIELD_ASSOCIATION_POINTS, "LabelSize" );
  labelHierarchy->SetInputArrayToProcess( 2, 0, 0, vtkDataObject::FIELD_ASSOCIATION_POINTS, "LabelText" );

  vtkLabelPlacer* labelPlacer = vtkLabelPlacer::New();
  labelPlacer->SetInputConnection( labelHierarchy->GetOutputPort() );
  labelPlacer->GeneratePerturbedLabelSpokesOn();
  labelPlacer->OutputTraversedBoundsOn();
  labelPlacer->SetRenderer( rr );

  vtkActor2D* a1 = vtkActor2D::New();
  vtkActor* a2 = vtkActor::New();
  vtkActor* a3 = vtkActor::New();
  vtkLabeledDataMapper* m1 = vtkLabeledDataMapper::New();
  vtkPolyDataMapper* m2 = vtkPolyDataMapper::New();
  vtkPolyDataMapper* m3 = vtkPolyDataMapper::New();

  m1->SetInputConnection( labelPlacer->GetOutputPort( 0 ) );
  m2->SetInputConnection( labelPlacer->GetOutputPort( 2 ) );
  m3->SetInputConnection( labelPlacer->GetOutputPort( 3 ) );
  a1->SetMapper( m1 );
  a2->SetMapper( m2 );
  a3->SetMapper( m3 );

  m1->SetLabelTextProperty( sizeCalc->GetFontProperty() );
  m1->SetFieldDataName( "LabelText" );
  m1->SetLabelModeToLabelFieldData();
  //m1->GetLabelTextProperty()->SetColor(0.0, 0.8, 0.2);

  rr->AddActor( a1 );
  rr->AddActor( a2 );
  rr->AddActor( a3 );
  rw->AddRenderer( rr );
  rw->SetInteractor( ri );

  rw->Render();

  //labelPlacer->Update();

  cout << "Set of " << pts->GetNumberOfPoints() << " labels\n";

  //rr->ResetCamera();
  vtkCamera* cam = rr->GetActiveCamera();
  cam->SetClippingRange( 0.0106829, 10.6829 );
  cam->SetFocalPoint( 5.00016, 4.99974, -1. );
  cam->SetPosition( 4.91977, 4.45127, -0.859406 );
  cam->SetViewUp( -0.0373979, 0.253276, 0.966671 );
  //cam->SetDirectionOfProjection( 0.140573, 0.959062, -0.245844 );

  //rw->Render();

  int retval = vtkRegressionTestImageThreshold( rw, 60.0 );
  if ( retval == vtkRegressionTester::DO_INTERACTOR)
  {
    ri->Start();
#ifdef GENERATE_TEST_POINTS
    vtkIndent indent;
    cam->PrintSelf( cout, indent );
#endif // GENERATE_TEST_POINTS
  }

  pts->Delete();
  polyData->Delete();
  labelText->Delete();
  priorities->Delete();
  sizeCalc->Delete();
  labelHierarchy->Delete();
  labelPlacer->Delete();
  rr->Delete();
  rw->Delete();
  ri->Delete();
  a1->Delete();
  a2->Delete();
  a3->Delete();
  m1->Delete();
  m2->Delete();
  m3->Delete();

  return !retval;
}
