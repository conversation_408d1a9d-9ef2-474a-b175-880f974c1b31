vtk_module(vtkUtilitiesBenchmarks
  EXCLUDE_FROM_WRAPPING
  DEPENDS
    vtkCommonComputationalGeometry
    vtkCommonCore
    vtkCommonDataModel
    vtkCommonSystem
    vtkCommonTransforms
    vtkDomainsChemistry
    vtkFiltersCore
    vtkFiltersSources
    vtkImagingCore
    vtkRenderingOpenGL2
    vtkRenderingContextOpenGL2
    vtkRenderingCore
    vtkRenderingVolume
    vtkRenderingVolumeOpenGL2
    vtksys
  PRIVATE_DEPENDS
    vtkChartsCore
    vtkIOCore
    vtkRenderingContext2D
    vtkViewsContext2D
)
