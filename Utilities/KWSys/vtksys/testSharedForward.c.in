/* Distributed under the OSI-approved BSD 3-Clause License.  See accompanying
   file Copyright.txt or https://cmake.org/licensing#kwsys for details.  */
#if defined(CMAKE_INTDIR)
#define CONFIG_DIR_PRE CMAKE_INTDIR "/"
#define CONFIG_DIR_POST "/" CMAKE_INTDIR
#else
#define CONFIG_DIR_PRE ""
#define CONFIG_DIR_POST ""
#endif
#define @KWSYS_NAMESPACE@_SHARED_FORWARD_DIR_BUILD "@EXEC_DIR@"
#define @KWSYS_NAMESPACE@_SHARED_FORWARD_PATH_BUILD "." CONFIG_DIR_POST
#define @KWSYS_NAMESPACE@_SHARED_FORWARD_PATH_INSTALL 0
#define @KWSYS_NAMESPACE@_SHARED_FORWARD_EXE_BUILD                            \
  CONFIG_DIR_PRE "@KWSYS_NAMESPACE@TestProcess"
#define @KWSYS_NAMESPACE@_SHARED_FORWARD_EXE_INSTALL                          \
  "@KWSYS_NAMESPACE@TestProcess"
#define @KWSYS_NAMESPACE@_SHARED_FORWARD_OPTION_COMMAND "--command"
#define @KWSYS_NAMESPACE@_SHARED_FORWARD_OPTION_PRINT "--print"
#define @KWSYS_NAMESPACE@_SHARED_FORWARD_OPTION_LDD "--ldd"
#if defined(CMAKE_INTDIR)
#define @KWSYS_NAMESPACE@_SHARED_FORWARD_CONFIG_NAME CMAKE_INTDIR
#endif
#include <@KWSYS_NAMESPACE@/SharedForward.h>
int main(int argc, char** argv)
{
  return @KWSYS_NAMESPACE@_shared_forward_to_real(argc, argv);
}
