KWSys
*****

Introduction
============

KWSys is the Kitware System Library.  It provides platform-independent
APIs to many common system features that are implemented differently on
every platform.  This library is intended to be shared among many
projects at the source level, so it has a configurable namespace.
Each project should configure KWSys to use a namespace unique to itself.
See comments in `CMakeLists.txt`_ for details.

.. _`CMakeLists.txt`: CMakeLists.txt

License
=======

KWSys is distributed under the OSI-approved BSD 3-clause License.
See `Copyright.txt`_ for details.

.. _`Copyright.txt`: Copyright.txt

Reporting Bugs
==============

KWSys has no independent issue tracker.  After encountering an issue
(bug) please submit a patch using the instructions for `Contributing`_.
Otherwise please report the issue to the tracker for the project that
hosts the copy of KWSys in which the problem was found.

Contributing
============

See `CONTRIBUTING.rst`_ for instructions to contribute.

.. _`CONTRIBUTING.rst`: CONTRIBUTING.rst
