set(vtksys_LIBRARIES vtksys)
vtk_module_export_info()

#-----------------------------------------------------------------------------
# Configure KWSys to be named "vtksys".
SET(KWSYS_NAMESPACE vtksys)
SET(KWSYS_USE_Base64 1)
SET(KWSYS_USE_CommandLineArguments 1)
SET(KWSYS_USE_DynamicLoader 1)
SET(KWSYS_USE_Process 1)
SET(KWSYS_USE_RegularExpression 1)
SET(KWSYS_USE_SystemTools 1)
SET(KWSYS_USE_SystemInformation 1)
SET(KWSYS_USE_MD5 1)
SET(KWSYS_USE_Glob 1)
set(KWSYS_HEADER_ROOT ${CMAKE_CURRENT_BINARY_DIR})
SET(KWSYS_PROPERTIES_CXX ${VTK_LIBRARY_PROPERTIES})
SET(KWSYS_INSTALL_EXPORT_NAME ${VTK_INSTALL_EXPORT_NAME})
set(KWSYS_SYSTEMTOOLS_SUPPORT_WINDOWS_SLASHES 1)

if(NOT VTK_INSTALL_NO_LIBRARIES)
  set(KWSYS_INSTALL_BIN_DIR ${VTK_INSTALL_RUNTIME_DIR})
  set(KWSYS_INSTALL_LIB_DIR ${VTK_INSTALL_LIBRARY_DIR})
  set(KWSYS_INSTALL_COMPONENT_NAME_RUNTIME RuntimeLibraries)
endif()
if(NOT VTK_INSTALL_NO_DEVELOPMENT)
  set(KWSYS_INSTALL_INCLUDE_DIR ${VTK_INSTALL_INCLUDE_DIR})
  set(KWSYS_INSTALL_COMPONENT_NAME_DEVELOPMENT Development)
endif()

add_subdirectory(vtksys)
vtk_target(${KWSYS_NAMESPACE} NO_INSTALL)
