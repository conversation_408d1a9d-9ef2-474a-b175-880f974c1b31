# -------------------------------------------------------------------------
# doxyfile for VTK
# modified by <PERSON><PERSON> (Time-stamp: <2005-12-05 12:59:07 barre>
# -------------------------------------------------------------------------

PROJECT_NAME         = VTK

FULL_PATH_NAMES      = YES
WARN_IF_UNDOCUMENTED = NO

GENERATE_TREEVIEW    = NO
GENERATE_TODOLIST    = YES
GENERATE_BUGLIST     = YES
GENERATE_HTML        = YES
GENERATE_HTMLHELP    = @DOXYGEN_GENERATE_HTMLHELP@
GENERATE_LATEX       = NO
GENERATE_MAN         = NO
GENERATE_RTF         = NO
GENERATE_TAGFILE     = "@VTK_BINARY_DIR@/Utilities/Doxygen/vtk@VTK_MAJOR_VERSION@.@VTK_MINOR_VERSION@.tag"

PDF_HYPERLINKS       = YES

HAVE_DOT             = @HAVE_DOT_YESNO@
DOT_PATH             = "@DOT_PATH@"
DOT_IMAGE_FORMAT     = svg
DOT_GRAPH_MAX_NODES  = 75
CLASS_GRAPH          = YES
COLLABORATION_GRAPH  = YES
TEMPLATE_RELATIONS   = YES
INCLUDE_GRAPH        = YES
INCLUDED_BY_GRAPH    = YES
CLASS_DIAGRAMS       = YES
GENERATE_LEGEND      = YES
GRAPHICAL_HIERARCHY  = NO        # would be too large for vtkObject
MAX_DOT_GRAPH_DEPTH  = 3

ALLEXTERNALS         = NO

IMAGE_PATH           = "@VTK_SOURCE_DIR@/Utilities/Doxygen" "@VTK_BINARY_DIR@/Utilities/Doxygen/contrib" \
                       "@VTK_SOURCE_DIR@/Filters" \
                       "@VTK_SOURCE_DIR@/Geovis" \
                       "@VTK_SOURCE_DIR@/Rendering"

OUTPUT_DIRECTORY     = "@VTK_BINARY_DIR@/Utilities/Doxygen/doc"

INPUT                = \
          "@VTK_SOURCE_DIR@/Documentation/Doxygen" \
          "@VTK_BINARY_DIR@/Utilities/Doxygen/dox" \
          "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/doc_VTK_version.dox" \
          "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/doc_VTK_class2examples.dox" \
          "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/doc_VTK_class2tests.dox" \
          "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/doc_VTK_events2.dox" \
          "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/doc_VTK_events.dox" \
          "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/doc_VTK_index.dox"

EXCLUDE_PATTERNS     =
EXCLUDE              = \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Common/Core/vtkSetGet.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Common/DataModel/vtkMarchingSquaresCases.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Common/DataModel/vtkMarchingCubesCases.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Common/Core/vtkWindows.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Common/vtkTk.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Common/vtkPython.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Common/vtkPythonUtil.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Common/vtkJavaAwt.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Common/vtkJavaUtil.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Common/vtkPythonUtil.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Parallel/MPI/vtkMPI.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/IO/Import/vtk3DS.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/IO/Import/vtkVRML.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/IO/PLY/vtkPLY.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Rendering/OpenGL/vtkOpenGL.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Rendering/FreeType/fonts/vtkEmbeddedFonts.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Testing/Rendering/Testing/vtkRegressionTestImage.h" \
    "@VTK_BINARY_DIR@/Utilities/Doxygen/dox/Testing/Core/Testing/vtkTestDriver.h" \

# TAGFILES           = "@VTK_BINARY_DIR@/Utilities/Doxygen/vtk@<EMAIL>"=http://www.vtk.org/doc/nightly/html

EXTRACT_ALL          = YES
EXTRACT_PRIVATE      = NO
EXTRACT_STATIC       = YES
EXTRACT_LOCAL_CLASSES = NO
HIDE_UNDOC_MEMBERS   = NO
HIDE_UNDOC_CLASSES   = YES
ALWAYS_DETAILED_SEC  = NO
SOURCE_BROWSER       = YES
INLINE_SOURCES       = NO
CASE_SENSE_NAMES     = YES
VERBATIM_HEADERS     = NO
SHOW_INCLUDE_FILES   = YES
JAVADOC_AUTOBRIEF    = YES
SORT_MEMBER_DOCS     = NO
DISTRIBUTE_GROUP_DOC = YES
TAB_SIZE             = 3
SHORT_NAMES          = @DOXYGEN_SHORT_NAMES@
# PAPER_TYPE           = letter

FILE_PATTERNS        = *.h *.md
RECURSIVE            = YES

ALPHABETICAL_INDEX   = YES
COLS_IN_ALPHA_INDEX  = 3
IGNORE_PREFIX        = vtk

ENABLE_PREPROCESSING = YES
MACRO_EXPANSION      = YES
SEARCH_INCLUDES      = YES
INCLUDE_PATH         =
EXPAND_ONLY_PREDEF   = YES
EXPAND_AS_DEFINED = vtkDataArray \
                    vtkAllEventsMacro \
                    vtkEventDeclarationMacro \
                    _vtk_add_event
PREDEFINED  = "vtkSetMacro(name,type)= \
                 virtual void Set##name (type);" \
              "vtkGetMacro(name,type)= \
                 virtual type Get##name ();" \
              "vtkSetStringMacro(name)= \
                 virtual void Set##name (const char*);" \
              "vtkGetStringMacro(name)= \
                 virtual char* Get##name ();" \
              "vtkSetClampMacro(name,type,min,max)= \
                 virtual void Set##name (type);" \
              "vtkSetObjectMacro(name,type)= \
                 virtual void Set##name (type*);" \
              "vtkGetObjectMacro(name,type)= \
                 virtual type *Get##name ();" \
              "vtkBooleanMacro(name,type)= \
                 virtual void name##On (); \
                 virtual void name##Off ();" \
              "vtkSetVector2Macro(name,type)= \
                 virtual void Set##name (type, type); \
                 void Set##name (type [2]);" \
              "vtkGetVector2Macro(name,type)= \
                 virtual type *Get##name (); \
                 virtual void Get##name (type &, type &); \
                 virtual void Get##name (type [2]);" \
              "vtkSetVector3Macro(name,type)= \
                 virtual void Set##name (type, type, type); \
                 virtual void Set##name (type [3]);" \
              "vtkGetVector3Macro(name,type)= \
                 virtual type *Get##name (); \
                 virtual void Get##name (type &, type &, type &); \
                 virtual void Get##name (type [3]);" \
              "vtkSetVector4Macro(name,type)= \
                 virtual void Set##name (type, type, type, type); \
                 virtual void Set##name (type [4]);" \
              "vtkGetVector4Macro(name,type)= \
                 virtual type *Get##name (); \
                 virtual void Get##name (type &, type &, type &, type &); \
                 virtual void Get##name (type [4]);" \
               "vtkSetVector6Macro(name,type)= \
                 virtual void Set##name (type, type, type, type, \
                                         type, type); \
                 virtual void Set##name (type [6]);" \
               "vtkGetVector6Macro(name,type)= \
                  virtual type *Get##name (); \
                  virtual void Get##name (type &, type &, type &, \
                                          type &, type &, type &); \
                  virtual void Get##name (type [6]);" \
               "vtkSetVectorMacro(name,type,count)= \
                  virtual void Set##name(type data[]);" \
               "vtkGetVectorMacro(name,type,count)= \
                   virtual type *Get##name (); \
                   virtual void Get##name(type data[##count]);" \
               "vtkWorldCoordinateMacro(name)= \
                   virtual vtkCoordinate *Get##name##Coordinate (); \
                   virtual void Set##name(float x[3]); \
                   virtual void Set##name(float x, float y, float z); \
                   virtual float *Get##name();" \
               "vtkViewportCoordinateMacro(name)= \
                   virtual vtkCoordinate *Get##name##Coordinate (); \
                   virtual void Set##name(float x[2]); \
                   virtual void Set##name(float x, float y); \
                   virtual float *Get##name();" \
               "vtkTypeMacro(thisClass,superclass)= \
                   typedef superclass Superclass; \
                   private: \
                   virtual const char* GetClassNameInternal() const; \
                   public: \
                   static vtkTypeBool IsTypeOf(const char *type); \
                   virtual vtkTypeBool IsA(const char *type); \
                   static thisClass* SafeDownCast(vtkObjectBase *o); \
                   protected: \
                   virtual vtkObjectBase *NewInstanceInternal() const; \
                   public: \
                   thisClass *NewInstance() const;" \
               "VTK_LEGACY(x)= x" \
               "VTK_NEWINSTANCE=" \
               "VTK_ZEROCOPY=" \
               "VTK_EXPECTS(x)=" \
               "VTK_SIZEHINT(...)=" \
               "DOXYGEN_SHOULD_SKIP_THIS"
