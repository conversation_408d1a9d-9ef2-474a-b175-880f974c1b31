#!/usr/bin/env bash

egrep_q() {
  egrep "$@" >/dev/null 2>/dev/null
}

# First argument is file containing commit message.
commit_msg="$1"

# Check for our extra instructions.
egrep_q "^# Start the commit message" -- "$commit_msg" && return 0

# Insert our extra instructions.
commit_msg_tmp="$commit_msg.$$"
sed 's/^# \(On\|Not currently on any\) branch.*/'\
'# Start the commit message in "WIP: " to indicate Work In Progress\
# that is not yet ready to merge.\
#\
&/' "$commit_msg" > "$commit_msg_tmp" &&
mv "$commit_msg_tmp" "$commit_msg"
