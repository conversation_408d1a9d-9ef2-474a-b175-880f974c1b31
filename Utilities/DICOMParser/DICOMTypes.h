
/*=========================================================================

  Program:   DICOMParser
  Module:    DICOMTypes.h
  Language:  C++

  Copyright (c) 2003 Matt <PERSON>
  All rights reserved.
  See Copyright.txt for details.

     This software is distributed WITHOUT ANY WARRANTY; without even
     the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
     PURPOSE.  See the above copyright notice for more information.

=========================================================================*/


#ifndef __DICOM_TYPES_H_
#define __DICOM_TYPES_H_

typedef unsigned short doublebyte;
typedef int quadbyte;
typedef unsigned short ushort;
typedef unsigned long  ulong;
typedef unsigned int  uint;


#endif
