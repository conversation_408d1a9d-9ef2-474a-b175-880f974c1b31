vtk_module(vtkIOXML
  GROUPS
    StandAlone
  TEST_DEPENDS
    vtkFiltersAMR
    vtkFiltersCore
    vtkFiltersHyperTree
    vtkFiltersSources
    vtkImagingSources
    vtkInfovisCore
    vtkIOLegacy
    vtkRenderingOpenGL2
    vtkTestingCore
    vtkTestingRendering
    vtkInteractionStyle
    vtkIOParallelXML
  KIT
    vtkIO
  DEPENDS
    vtkCommonCore
    vtkCommonExecutionModel
    vtkIOXMLParser
  PRIVATE_DEPENDS
    vtkCommonDataModel
    vtkCommonMisc
    vtkCommonSystem
    vtkIOCore
    vtksys
  )
