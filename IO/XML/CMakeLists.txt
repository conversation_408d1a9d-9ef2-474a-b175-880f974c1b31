set(Module_SRCS
  vtkRTXMLPolyDataReader.cxx
  vtkXMLCompositeDataReader.cxx
  vtkXMLCompositeDataWriter.cxx
  vtkXMLDataReader.cxx
  vtkXMLDataSetWriter.cxx
  vtkXMLDataObjectWriter.cxx
  vtkXMLFileReadTester.cxx
  vtkXMLGenericDataObjectReader.cxx
  vtkXMLHierarchicalBoxDataFileConverter.cxx
  vtkXMLHierarchicalBoxDataReader.cxx
  vtkXMLHierarchicalBoxDataWriter.cxx
  vtkXMLHierarchicalDataReader.cxx
  vtkXMLHyperTreeGridReader.cxx
  vtkXMLHyperTreeGridWriter.cxx
  vtkXMLImageDataReader.cxx
  vtkXMLImageDataWriter.cxx
  vtkXMLMultiBlockDataReader.cxx
  vtkXMLMultiBlockDataWriter.cxx
  vtkXMLMultiGroupDataReader.cxx
  vtkXMLPDataReader.cxx
  vtkXMLPDataObjectReader.cxx
  vtkXMLPImageDataReader.cxx
  vtkXMLPolyDataReader.cxx
  vtkXMLPolyDataWriter.cxx
  vtkXMLPPolyDataReader.cxx
  vtkXMLPRectilinearGridReader.cxx
  vtkXMLPStructuredDataReader.cxx
  vtkXMLPStructuredGridReader.cxx
  vtkXMLPTableReader.cxx
  vtkXMLPUnstructuredDataReader.cxx
  vtkXMLPUnstructuredGridReader.cxx
  vtkXMLPartitionedDataSetReader.cxx
  vtkXMLPartitionedDataSetWriter.cxx
  vtkXMLPartitionedDataSetCollectionReader.cxx
  vtkXMLPartitionedDataSetCollectionWriter.cxx
  vtkXMLReader.cxx
  vtkXMLRectilinearGridReader.cxx
  vtkXMLRectilinearGridWriter.cxx
  vtkXMLStructuredDataReader.cxx
  vtkXMLStructuredDataWriter.cxx
  vtkXMLStructuredGridReader.cxx
  vtkXMLStructuredGridWriter.cxx
  vtkXMLTableReader.cxx
  vtkXMLTableWriter.cxx
  vtkXMLUniformGridAMRReader.cxx
  vtkXMLUniformGridAMRWriter.cxx
  vtkXMLUnstructuredDataReader.cxx
  vtkXMLUnstructuredDataWriter.cxx
  vtkXMLUnstructuredGridReader.cxx
  vtkXMLUnstructuredGridWriter.cxx
  vtkXMLWriterC.cxx
  vtkXMLWriter.cxx
  )

vtk_module_library(vtkIOXML ${Module_SRCS})
