/*=========================================================================

  Program:   Visualization Toolkit
  Module:    vtkIOMovieConfigure.h.in

  Copyright (c) <PERSON>, <PERSON>, <PERSON>
  All rights reserved.
  See Copyright.txt or http://www.kitware.com/Copyright.htm for details.

     This software is distributed WITHOUT ANY WARRANTY; without even
     the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
     PURPOSE.  See the above copyright notice for more information.

=========================================================================*/
#ifndef vtkIOMovieConfigure_h
#define vtkIOMovieConfigure_h

/* This header contains build settings for the vtkIOMovie module */

// If vtkOggTheoraWriter is enabled.
#cmakedefine VTK_HAS_OGGTHEORA_SUPPORT

#endif
