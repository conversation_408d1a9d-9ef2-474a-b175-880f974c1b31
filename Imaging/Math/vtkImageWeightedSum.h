/*=========================================================================

  Program:   Visualization Toolkit
  Module:    vtkImageWeightedSum.h

  Copyright (c) <PERSON>, <PERSON>, <PERSON>
  All rights reserved.
  See Copyright.txt or http://www.kitware.com/Copyright.htm for details.

     This software is distributed WITHOUT ANY WARRANTY; without even
     the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
     PURPOSE.  See the above copyright notice for more information.

=========================================================================*/
/**
 * @class   vtkImageWeightedSum
 * @brief    adds any number of images, weighting
 * each according to the weight set using this->SetWeights(i,w).
 *
 *
 * All weights are normalized so they will sum to 1.
 * Images must have the same extents. Output is
 *
 * @par Thanks:
 * The original author of this class is <PERSON> (MIT) for Slicer
*/

#ifndef vtkImageWeightedSum_h
#define vtkImageWeightedSum_h

#include "vtkImagingMathModule.h" // For export macro
#include "vtkThreadedImageAlgorithm.h"

class vtkDoubleArray;
class VTKIMAGINGMATH_EXPORT vtkImageWeightedSum : public vtkThreadedImageAlgorithm
{
public:
  static vtkImageWeightedSum *New();
  vtkTypeMacro(vtkImageWeightedSum,vtkThreadedImageAlgorithm);
  void PrintSelf(ostream& os, vtkIndent indent) override;

  //@{
  /**
   * The weights control the contribution of each input to the sum.
   * They will be normalized to sum to 1 before filter execution.
   */
  virtual void SetWeights(vtkDoubleArray*);
  vtkGetObjectMacro(Weights, vtkDoubleArray);
  //@}

  /**
   * Change a specific weight. Reallocation is done
   */
  virtual void SetWeight(vtkIdType id, double weight);

  //@{
  /**
   * Setting NormalizeByWeight on will divide the
   * final result by the total weight of the component functions.
   * This process does not otherwise normalize the weighted sum
   * By default, NormalizeByWeight is on.
   */
  vtkGetMacro(NormalizeByWeight, vtkTypeBool);
  vtkSetClampMacro(NormalizeByWeight, vtkTypeBool, 0, 1);
  vtkBooleanMacro(NormalizeByWeight, vtkTypeBool);
  //@}

  /**
   * Compute the total value of all the weight
   */
  double CalculateTotalWeight();

protected:
  vtkImageWeightedSum();
  ~vtkImageWeightedSum() override;

  // Array to hold all the weights
  vtkDoubleArray *Weights;

  // Boolean flag to divide by sum or not
  vtkTypeBool NormalizeByWeight;

  int RequestInformation (vtkInformation * vtkNotUsed(request),
    vtkInformationVector** vtkNotUsed( inputVector ),
    vtkInformationVector *outputVector) override;

  void ThreadedRequestData (vtkInformation* request,
                            vtkInformationVector** inputVector,
                            vtkInformationVector* outputVector,
                            vtkImageData ***inData, vtkImageData **outData,
                            int ext[6], int id) override;
  int FillInputPortInformation(int i, vtkInformation* info) override;

private:
  vtkImageWeightedSum(const vtkImageWeightedSum&) = delete;
  void operator=(const vtkImageWeightedSum&) = delete;
};

#endif

