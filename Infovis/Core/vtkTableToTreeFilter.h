/*=========================================================================

  Program:   Visualization Toolkit
  Module:    vtkTableToTreeFilter.h

  Copyright (c) <PERSON>, <PERSON>, <PERSON>
  All rights reserved.
  See Copyright.txt or http://www.kitware.com/Copyright.htm for details.

     This software is distributed WITHOUT ANY WARRANTY; without even
     the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
     PURPOSE.  See the above copyright notice for more information.

=========================================================================*/
/*-------------------------------------------------------------------------
  Copyright 2008 Sandia Corporation.
  Under the terms of Contract DE-AC04-94AL85000 with Sandia Corporation,
  the U.S. Government retains certain rights in this software.
-------------------------------------------------------------------------*/
/**
 * @class   vtkTableToTreeFilter
 * @brief   Filter that converts a vtkTable to a vtkTree
 *
 *
 *
 * vtkTableToTreeFilter is a filter for converting a vtkTable data structure
 * into a vtkTree datastructure.  Currently, this will convert the table into
 * a star, with each row of the table as a child of a new root node.
 * The columns of the table are passed as node fields of the tree.
*/

#ifndef vtkTableToTreeFilter_h
#define vtkTableToTreeFilter_h

#include "vtkInfovisCoreModule.h" // For export macro
#include "vtkTreeAlgorithm.h"

class VTKINFOVISCORE_EXPORT vtkTableToTreeFilter : public vtkTreeAlgorithm
{
public:
  static vtkTableToTreeFilter* New();
  vtkTypeMacro(vtkTableToTreeFilter,vtkTreeAlgorithm);
  void PrintSelf(ostream& os, vtkIndent indent) override;

protected:
  vtkTableToTreeFilter();
  ~vtkTableToTreeFilter() override;

  int RequestData(
    vtkInformation*,
    vtkInformationVector**,
    vtkInformationVector*) override;

  int FillOutputPortInformation(
    int vtkNotUsed(port), vtkInformation* info) override;
  int FillInputPortInformation(
    int vtkNotUsed(port), vtkInformation* info) override;

private:
  vtkTableToTreeFilter(const vtkTableToTreeFilter&) = delete;
  void operator=(const vtkTableToTreeFilter&) = delete;
};

#endif

