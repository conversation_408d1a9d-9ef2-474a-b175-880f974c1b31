//=============================================================================
//
//  Copyright (c) Kitware, Inc.
//  All rights reserved.
//  See LICENSE.txt for details.
//
//  This software is distributed WITHOUT ANY WARRANTY; without even
//  the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
//  PURPOSE.  See the above copyright notice for more information.
//
//  Copyright 2012 Sandia Corporation.
//  Under the terms of Contract DE-AC04-94AL85000 with Sandia Corporation,
//  the U.S. Government retains certain rights in this software.
//
//=============================================================================
/**
 * @class   vtkmPointElevation
 * @brief   generate a scalar field along a specified direction
 *
 * vtkmPointElevation is a filter that generates a scalar field along a specified
 * direction. The scalar field values lie within a user specified range, and are
 * generated by computing a projection of each dataset point onto a line. The line
 * can be oriented arbitrarily. A typical example is to generate scalars based
 * on elevation or height above a plane.
 *
*/

#ifndef vtkmPointElevation_h
#define vtkmPointElevation_h

#include "vtkElevationFilter.h"
#include "vtkAcceleratorsVTKmModule.h" // required for correct export

class VTKACCELERATORSVTKM_EXPORT vtkmPointElevation : public vtkElevationFilter
{
public:
  vtkTypeMacro(vtkmPointElevation, vtkElevationFilter)
  void PrintSelf(ostream& os, vtkIndent indent) override;

  static vtkmPointElevation* New();

protected:
  vtkmPointElevation();
  ~vtkmPointElevation();

  virtual int RequestData(vtkInformation* , vtkInformationVector**,
                          vtkInformationVector*) override;

private:
  vtkmPointElevation(const vtkmPointElevation&) = delete;
  void operator=(const vtkmPointElevation&) = delete;

};

#endif // vtkmPointElevation_h

// VTK-HeaderTest-Exclude: vtkmPointElevation.h
