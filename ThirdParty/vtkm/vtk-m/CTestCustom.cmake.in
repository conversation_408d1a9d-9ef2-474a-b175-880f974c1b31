##============================================================================
##  Copyright (c) Kitware, Inc.
##  All rights reserved.
##  See LICENSE.txt for details.
##  This software is distributed WITHOUT ANY WARRANTY; without even
##  the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
##  PURPOSE.  See the above copyright notice for more information.
##
##  Copyright 2014 National Technology & Engineering Solutions of Sandia, LLC (NTESS).
##  Copyright 2014 UT-Battelle, LLC.
##  Copyright 2014 Los Alamos National Security.
##
##  Under the terms of Contract DE-********* with NTESS,
##  the U.S. Government retains certain rights in this software.
##
##  Under the terms of Contract DE-AC52-06NA25396 with Los Alamos National
##  Laboratory (LANL), the U.S. Government retains certain rights in
##  this software.
##============================================================================

list(APPEND CTEST_CUSTOM_WARNING_EXCEPTION
  ".*warning: ignoring loop annotation.*"
  ".*warning: Included by graph for.*not generated, too many nodes. Consider increasing DOT_GRAPH_MAX_NODES."

  # disable doxygen warning about potential recursion.
  ".*warning: Detected potential recursive class relation between class vtkm::exec::internal::ArrayPortalTransform"

  # disable doxygen warning about not generating graph
  ".*warning: Included by graph for"

  # Doxygen warns when creating output directory:
  "Notice: Output directory.*does not exist. I have created it for you."

  # disable doxygen warnings from CONTRIBUTING.md, CodingConventions.md.
  # these files are really intended for Gitlab, hence we don't want to use
  # doxygen tags in them.
  "CONTRIBUTING.md.*warning"
  "CodingConventions.md.*warning"

  # disable PTX warning about recursive functions. These look like they can't be silenced
  # without disabling all PTX warnings, show hide them on the dashboard.
  # We explicitly only suppress specific worklets so we can see when new recursive
  # worklets are added
  "ptxas warning : Stack size for entry function.*NearestNeighborSearch3DWorklet.*"
  "ptxas warning : Stack size for entry function.*BoundingIntervalHierarchy.*"
  "ptxas warning : Stack size for entry function.*CoordinatesPortal.*"
  "ptxas warning : Stack size for entry function.*CellRangesExtracter.*"
)
