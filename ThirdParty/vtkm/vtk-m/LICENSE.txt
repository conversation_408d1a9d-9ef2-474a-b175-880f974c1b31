VTKm License Version 1.0
========================================================================

Copyright (c) 2014,
National Technology & Engineering Solutions of Sandia, LLC (NTESS),
Los Alamos National Security, LLC., UT-Battelle, LLC., Kitware Inc.,
University of California Davis
All rights reserved.

Sandia National Laboratories, New Mexico
PO Box 5800
Albuquerque, NM 87185
USA

UT-Battelle
1 Bethel Valley Rd
Oak Ridge, TN 37830

Los Alamos National Security, LLC
105 Central Park Square
Los Alamos, NM 87544

Kitware Inc.
28 Corporate Drive
Clifton Park, NY 12065
USA

University of California, Davis
One Shields Avenue
Davis, CA 95616
USA

Under the terms of Contract DE-********* with NTESS, the U.S. Government
retains certain rights in this software.

Under the terms of Contract DE-AC52-06NA25396 with Los Alamos National
Laboratory (LANL), the U.S. Government retains certain rights in
this software.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:

 * Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

 * Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the
   distribution.

 * Neither the name of Kitware nor the names of any contributors may
   be used to endorse or promote products derived from this software
   without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE AUTHORS OR
CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
========================================================================

The following files and directories come from third parties. Check the
contents of these for details on the specifics of their respective
licenses.
- - - - - - - - - - - - - - - - - - - - - - - - do not remove this line
CMake/FindTBB.cmake
CMake/FindGLEW.cmake
Utilities
vtkm/cont/tbb/internal/parallel_sort.h
vtkm/cont/tbb/internal/parallel_radix_sort_tbb.h
vtkm/cont/tbb/internal/kxsort.h
vtkm/testing/OptionParser.h
vtkm/internal/brigand.hpp
version.txt

