#ifndef vtk_lz4_mangle_h
#define vtk_lz4_mangle_h

#define LZ4_compress vtklz4_LZ4_compress
#define LZ4_compressBound vtklz4_LZ4_compressBound
#define LZ4_compress_continue vtklz4_LZ4_compress_continue
#define LZ4_compress_default vtklz4_LZ4_compress_default
#define LZ4_compress_destSize vtklz4_LZ4_compress_destSize
#define LZ4_compress_fast vtklz4_LZ4_compress_fast
#define LZ4_compress_fast_continue vtklz4_LZ4_compress_fast_continue
#define LZ4_compress_fast_extState vtklz4_LZ4_compress_fast_extState
#define LZ4_compress_fast_force vtklz4_LZ4_compress_fast_force
#define LZ4_compress_forceExtDict vtklz4_LZ4_compress_forceExtDict
#define LZ4_compressHC vtklz4_LZ4_compressHC
#define LZ4_compress_HC vtklz4_LZ4_compress_HC
#define LZ4_compressHC2 vtklz4_LZ4_compressHC2
#define LZ4_compressHC2_continue vtklz4_LZ4_compressHC2_continue
#define LZ4_compressHC2_limitedOutput vtklz4_LZ4_compressHC2_limitedOutput
#define LZ4_compressHC2_limitedOutput_continue vtklz4_LZ4_compressHC2_limitedOutput_continue
#define LZ4_compressHC2_limitedOutput_withStateHC vtklz4_LZ4_compressHC2_limitedOutput_withStateHC
#define LZ4_compressHC2_withStateHC vtklz4_LZ4_compressHC2_withStateHC
#define LZ4_compress_HC_continue vtklz4_LZ4_compress_HC_continue
#define LZ4_compressHC_continue vtklz4_LZ4_compressHC_continue
#define LZ4_compress_HC_continue_destSize vtklz4_LZ4_compress_HC_continue_destSize
#define LZ4_compress_HC_destSize vtklz4_LZ4_compress_HC_destSize
#define LZ4_compress_HC_extStateHC vtklz4_LZ4_compress_HC_extStateHC
#define LZ4_compressHC_limitedOutput vtklz4_LZ4_compressHC_limitedOutput
#define LZ4_compressHC_limitedOutput_continue vtklz4_LZ4_compressHC_limitedOutput_continue
#define LZ4_compressHC_limitedOutput_withStateHC vtklz4_LZ4_compressHC_limitedOutput_withStateHC
#define LZ4_compressHC_withStateHC vtklz4_LZ4_compressHC_withStateHC
#define LZ4_compress_limitedOutput vtklz4_LZ4_compress_limitedOutput
#define LZ4_compress_limitedOutput_continue vtklz4_LZ4_compress_limitedOutput_continue
#define LZ4_compress_limitedOutput_withState vtklz4_LZ4_compress_limitedOutput_withState
#define LZ4_compress_withState vtklz4_LZ4_compress_withState
#define LZ4_create vtklz4_LZ4_create
#define LZ4_createHC vtklz4_LZ4_createHC
#define LZ4_createStream vtklz4_LZ4_createStream
#define LZ4_createStreamDecode vtklz4_LZ4_createStreamDecode
#define LZ4_createStreamHC vtklz4_LZ4_createStreamHC
#define LZ4_decompress_fast vtklz4_LZ4_decompress_fast
#define LZ4_decompress_fast_continue vtklz4_LZ4_decompress_fast_continue
#define LZ4_decompress_fast_usingDict vtklz4_LZ4_decompress_fast_usingDict
#define LZ4_decompress_fast_withPrefix64k vtklz4_LZ4_decompress_fast_withPrefix64k
#define LZ4_decompress_safe vtklz4_LZ4_decompress_safe
#define LZ4_decompress_safe_continue vtklz4_LZ4_decompress_safe_continue
#define LZ4_decompress_safe_forceExtDict vtklz4_LZ4_decompress_safe_forceExtDict
#define LZ4_decompress_safe_partial vtklz4_LZ4_decompress_safe_partial
#define LZ4_decompress_safe_usingDict vtklz4_LZ4_decompress_safe_usingDict
#define LZ4_decompress_safe_withPrefix64k vtklz4_LZ4_decompress_safe_withPrefix64k
#define LZ4F_compressBegin vtklz4_LZ4F_compressBegin
#define LZ4F_compressBegin_usingCDict vtklz4_LZ4F_compressBegin_usingCDict
#define LZ4F_compressBound vtklz4_LZ4F_compressBound
#define LZ4F_compressEnd vtklz4_LZ4F_compressEnd
#define LZ4F_compressFrame vtklz4_LZ4F_compressFrame
#define LZ4F_compressFrameBound vtklz4_LZ4F_compressFrameBound
#define LZ4F_compressFrame_usingCDict vtklz4_LZ4F_compressFrame_usingCDict
#define LZ4F_compressionLevel_max vtklz4_LZ4F_compressionLevel_max
#define LZ4F_compressUpdate vtklz4_LZ4F_compressUpdate
#define LZ4F_createCDict vtklz4_LZ4F_createCDict
#define LZ4F_createCompressionContext vtklz4_LZ4F_createCompressionContext
#define LZ4F_createDecompressionContext vtklz4_LZ4F_createDecompressionContext
#define LZ4F_decompress vtklz4_LZ4F_decompress
#define LZ4F_decompress_usingDict vtklz4_LZ4F_decompress_usingDict
#define LZ4F_flush vtklz4_LZ4F_flush
#define LZ4F_freeCDict vtklz4_LZ4F_freeCDict
#define LZ4F_freeCompressionContext vtklz4_LZ4F_freeCompressionContext
#define LZ4F_freeDecompressionContext vtklz4_LZ4F_freeDecompressionContext
#define LZ4F_getErrorCode vtklz4_LZ4F_getErrorCode
#define LZ4F_getErrorName vtklz4_LZ4F_getErrorName
#define LZ4F_getFrameInfo vtklz4_LZ4F_getFrameInfo
#define LZ4F_getVersion vtklz4_LZ4F_getVersion
#define LZ4F_isError vtklz4_LZ4F_isError
#define LZ4_freeHC vtklz4_LZ4_freeHC
#define LZ4_freeStream vtklz4_LZ4_freeStream
#define LZ4_freeStreamDecode vtklz4_LZ4_freeStreamDecode
#define LZ4_freeStreamHC vtklz4_LZ4_freeStreamHC
#define LZ4F_resetDecompressionContext vtklz4_LZ4F_resetDecompressionContext
#define LZ4_loadDict vtklz4_LZ4_loadDict
#define LZ4_loadDictHC vtklz4_LZ4_loadDictHC
#define LZ4_resetStream vtklz4_LZ4_resetStream
#define LZ4_resetStreamHC vtklz4_LZ4_resetStreamHC
#define LZ4_resetStreamState vtklz4_LZ4_resetStreamState
#define LZ4_resetStreamStateHC vtklz4_LZ4_resetStreamStateHC
#define LZ4_saveDict vtklz4_LZ4_saveDict
#define LZ4_saveDictHC vtklz4_LZ4_saveDictHC
#define LZ4_setCompressionLevel vtklz4_LZ4_setCompressionLevel
#define LZ4_setStreamDecode vtklz4_LZ4_setStreamDecode
#define LZ4_sizeofState vtklz4_LZ4_sizeofState
#define LZ4_sizeofStateHC vtklz4_LZ4_sizeofStateHC
#define LZ4_sizeofStreamState vtklz4_LZ4_sizeofStreamState
#define LZ4_sizeofStreamStateHC vtklz4_LZ4_sizeofStreamStateHC
#define LZ4_slideInputBuffer vtklz4_LZ4_slideInputBuffer
#define LZ4_slideInputBufferHC vtklz4_LZ4_slideInputBufferHC
#define LZ4_uncompress vtklz4_LZ4_uncompress
#define LZ4_uncompress_unknownOutputSize vtklz4_LZ4_uncompress_unknownOutputSize
#define LZ4_versionNumber vtklz4_LZ4_versionNumber
#define LZ4_versionString vtklz4_LZ4_versionString
#define XXH32 vtklz4_XXH32
#define XXH32_canonicalFromHash vtklz4_XXH32_canonicalFromHash
#define XXH32_copyState vtklz4_XXH32_copyState
#define XXH32_createState vtklz4_XXH32_createState
#define XXH32_digest vtklz4_XXH32_digest
#define XXH32_freeState vtklz4_XXH32_freeState
#define XXH32_hashFromCanonical vtklz4_XXH32_hashFromCanonical
#define XXH32_reset vtklz4_XXH32_reset
#define XXH32_update vtklz4_XXH32_update
#define XXH64 vtklz4_XXH64
#define XXH64_canonicalFromHash vtklz4_XXH64_canonicalFromHash
#define XXH64_copyState vtklz4_XXH64_copyState
#define XXH64_createState vtklz4_XXH64_createState
#define XXH64_digest vtklz4_XXH64_digest
#define XXH64_freeState vtklz4_XXH64_freeState
#define XXH64_hashFromCanonical vtklz4_XXH64_hashFromCanonical
#define XXH64_reset vtklz4_XXH64_reset
#define XXH64_update vtklz4_XXH64_update
#define XXH_versionNumber vtklz4_XXH_versionNumber

#endif
