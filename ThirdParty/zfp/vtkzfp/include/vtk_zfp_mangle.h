#ifndef ZFP_MANGLE_H
#define ZFP_MANGLE_H

#define stream_align vtkzfp_stream_align
#define stream_capacity vtkzfp_stream_capacity
#define stream_close vtkzfp_stream_close
#define stream_data vtkzfp_stream_data
#define stream_flush vtkzfp_stream_flush
#define stream_open vtkzfp_stream_open
#define stream_pad vtkzfp_stream_pad
#define stream_read_bit vtkzfp_stream_read_bit
#define stream_read_bits vtkzfp_stream_read_bits
#define stream_rewind vtkzfp_stream_rewind
#define stream_rseek vtkzfp_stream_rseek
#define stream_rtell vtkzfp_stream_rtell
#define stream_size vtkzfp_stream_size
#define stream_skip vtkzfp_stream_skip
#define stream_stride_block vtkzfp_stream_stride_block
#define stream_stride_delta vtkzfp_stream_stride_delta
#define stream_word_bits vtkzfp_stream_word_bits
#define stream_write_bit vtkzfp_stream_write_bit
#define stream_write_bits vtkzfp_stream_write_bits
#define stream_wseek vtkzfp_stream_wseek
#define stream_wtell vtkzfp_stream_wtell
#define zfp_codec_version vtkzfp_zfp_codec_version
#define zfp_compress vtkzfp_zfp_compress
#define zfp_decode_block_double_1 vtkzfp_zfp_decode_block_double_1
#define zfp_decode_block_double_2 vtkzfp_zfp_decode_block_double_2
#define zfp_decode_block_double_3 vtkzfp_zfp_decode_block_double_3
#define zfp_decode_block_float_1 vtkzfp_zfp_decode_block_float_1
#define zfp_decode_block_float_2 vtkzfp_zfp_decode_block_float_2
#define zfp_decode_block_float_3 vtkzfp_zfp_decode_block_float_3
#define zfp_decode_block_int32_1 vtkzfp_zfp_decode_block_int32_1
#define zfp_decode_block_int32_2 vtkzfp_zfp_decode_block_int32_2
#define zfp_decode_block_int32_3 vtkzfp_zfp_decode_block_int32_3
#define zfp_decode_block_int64_1 vtkzfp_zfp_decode_block_int64_1
#define zfp_decode_block_int64_2 vtkzfp_zfp_decode_block_int64_2
#define zfp_decode_block_int64_3 vtkzfp_zfp_decode_block_int64_3
#define zfp_decode_block_strided_double_1 vtkzfp_zfp_decode_block_strided_double_1
#define zfp_decode_block_strided_double_2 vtkzfp_zfp_decode_block_strided_double_2
#define zfp_decode_block_strided_double_3 vtkzfp_zfp_decode_block_strided_double_3
#define zfp_decode_block_strided_float_1 vtkzfp_zfp_decode_block_strided_float_1
#define zfp_decode_block_strided_float_2 vtkzfp_zfp_decode_block_strided_float_2
#define zfp_decode_block_strided_float_3 vtkzfp_zfp_decode_block_strided_float_3
#define zfp_decode_block_strided_int32_1 vtkzfp_zfp_decode_block_strided_int32_1
#define zfp_decode_block_strided_int32_2 vtkzfp_zfp_decode_block_strided_int32_2
#define zfp_decode_block_strided_int32_3 vtkzfp_zfp_decode_block_strided_int32_3
#define zfp_decode_block_strided_int64_1 vtkzfp_zfp_decode_block_strided_int64_1
#define zfp_decode_block_strided_int64_2 vtkzfp_zfp_decode_block_strided_int64_2
#define zfp_decode_block_strided_int64_3 vtkzfp_zfp_decode_block_strided_int64_3
#define zfp_decode_partial_block_strided_double_1 vtkzfp_zfp_decode_partial_block_strided_double_1
#define zfp_decode_partial_block_strided_double_2 vtkzfp_zfp_decode_partial_block_strided_double_2
#define zfp_decode_partial_block_strided_double_3 vtkzfp_zfp_decode_partial_block_strided_double_3
#define zfp_decode_partial_block_strided_float_1 vtkzfp_zfp_decode_partial_block_strided_float_1
#define zfp_decode_partial_block_strided_float_2 vtkzfp_zfp_decode_partial_block_strided_float_2
#define zfp_decode_partial_block_strided_float_3 vtkzfp_zfp_decode_partial_block_strided_float_3
#define zfp_decode_partial_block_strided_int32_1 vtkzfp_zfp_decode_partial_block_strided_int32_1
#define zfp_decode_partial_block_strided_int32_2 vtkzfp_zfp_decode_partial_block_strided_int32_2
#define zfp_decode_partial_block_strided_int32_3 vtkzfp_zfp_decode_partial_block_strided_int32_3
#define zfp_decode_partial_block_strided_int64_1 vtkzfp_zfp_decode_partial_block_strided_int64_1
#define zfp_decode_partial_block_strided_int64_2 vtkzfp_zfp_decode_partial_block_strided_int64_2
#define zfp_decode_partial_block_strided_int64_3 vtkzfp_zfp_decode_partial_block_strided_int64_3
#define zfp_decompress vtkzfp_zfp_decompress
#define zfp_demote_int32_to_int16 vtkzfp_zfp_demote_int32_to_int16
#define zfp_demote_int32_to_int8 vtkzfp_zfp_demote_int32_to_int8
#define zfp_demote_int32_to_uint16 vtkzfp_zfp_demote_int32_to_uint16
#define zfp_demote_int32_to_uint8 vtkzfp_zfp_demote_int32_to_uint8
#define zfp_encode_block_double_1 vtkzfp_zfp_encode_block_double_1
#define zfp_encode_block_double_2 vtkzfp_zfp_encode_block_double_2
#define zfp_encode_block_double_3 vtkzfp_zfp_encode_block_double_3
#define zfp_encode_block_float_1 vtkzfp_zfp_encode_block_float_1
#define zfp_encode_block_float_2 vtkzfp_zfp_encode_block_float_2
#define zfp_encode_block_float_3 vtkzfp_zfp_encode_block_float_3
#define zfp_encode_block_int32_1 vtkzfp_zfp_encode_block_int32_1
#define zfp_encode_block_int32_2 vtkzfp_zfp_encode_block_int32_2
#define zfp_encode_block_int32_3 vtkzfp_zfp_encode_block_int32_3
#define zfp_encode_block_int64_1 vtkzfp_zfp_encode_block_int64_1
#define zfp_encode_block_int64_2 vtkzfp_zfp_encode_block_int64_2
#define zfp_encode_block_int64_3 vtkzfp_zfp_encode_block_int64_3
#define zfp_encode_block_strided_double_1 vtkzfp_zfp_encode_block_strided_double_1
#define zfp_encode_block_strided_double_2 vtkzfp_zfp_encode_block_strided_double_2
#define zfp_encode_block_strided_double_3 vtkzfp_zfp_encode_block_strided_double_3
#define zfp_encode_block_strided_float_1 vtkzfp_zfp_encode_block_strided_float_1
#define zfp_encode_block_strided_float_2 vtkzfp_zfp_encode_block_strided_float_2
#define zfp_encode_block_strided_float_3 vtkzfp_zfp_encode_block_strided_float_3
#define zfp_encode_block_strided_int32_1 vtkzfp_zfp_encode_block_strided_int32_1
#define zfp_encode_block_strided_int32_2 vtkzfp_zfp_encode_block_strided_int32_2
#define zfp_encode_block_strided_int32_3 vtkzfp_zfp_encode_block_strided_int32_3
#define zfp_encode_block_strided_int64_1 vtkzfp_zfp_encode_block_strided_int64_1
#define zfp_encode_block_strided_int64_2 vtkzfp_zfp_encode_block_strided_int64_2
#define zfp_encode_block_strided_int64_3 vtkzfp_zfp_encode_block_strided_int64_3
#define zfp_encode_partial_block_strided_double_1 vtkzfp_zfp_encode_partial_block_strided_double_1
#define zfp_encode_partial_block_strided_double_2 vtkzfp_zfp_encode_partial_block_strided_double_2
#define zfp_encode_partial_block_strided_double_3 vtkzfp_zfp_encode_partial_block_strided_double_3
#define zfp_encode_partial_block_strided_float_1 vtkzfp_zfp_encode_partial_block_strided_float_1
#define zfp_encode_partial_block_strided_float_2 vtkzfp_zfp_encode_partial_block_strided_float_2
#define zfp_encode_partial_block_strided_float_3 vtkzfp_zfp_encode_partial_block_strided_float_3
#define zfp_encode_partial_block_strided_int32_1 vtkzfp_zfp_encode_partial_block_strided_int32_1
#define zfp_encode_partial_block_strided_int32_2 vtkzfp_zfp_encode_partial_block_strided_int32_2
#define zfp_encode_partial_block_strided_int32_3 vtkzfp_zfp_encode_partial_block_strided_int32_3
#define zfp_encode_partial_block_strided_int64_1 vtkzfp_zfp_encode_partial_block_strided_int64_1
#define zfp_encode_partial_block_strided_int64_2 vtkzfp_zfp_encode_partial_block_strided_int64_2
#define zfp_encode_partial_block_strided_int64_3 vtkzfp_zfp_encode_partial_block_strided_int64_3
#define zfp_field_1d vtkzfp_zfp_field_1d
#define zfp_field_2d vtkzfp_zfp_field_2d
#define zfp_field_3d vtkzfp_zfp_field_3d
#define zfp_field_alloc vtkzfp_zfp_field_alloc
#define zfp_field_dimensionality vtkzfp_zfp_field_dimensionality
#define zfp_field_free vtkzfp_zfp_field_free
#define zfp_field_metadata vtkzfp_zfp_field_metadata
#define zfp_field_pointer vtkzfp_zfp_field_pointer
#define zfp_field_precision vtkzfp_zfp_field_precision
#define zfp_field_set_metadata vtkzfp_zfp_field_set_metadata
#define zfp_field_set_pointer vtkzfp_zfp_field_set_pointer
#define zfp_field_set_size_1d vtkzfp_zfp_field_set_size_1d
#define zfp_field_set_size_2d vtkzfp_zfp_field_set_size_2d
#define zfp_field_set_size_3d vtkzfp_zfp_field_set_size_3d
#define zfp_field_set_stride_1d vtkzfp_zfp_field_set_stride_1d
#define zfp_field_set_stride_2d vtkzfp_zfp_field_set_stride_2d
#define zfp_field_set_stride_3d vtkzfp_zfp_field_set_stride_3d
#define zfp_field_set_type vtkzfp_zfp_field_set_type
#define zfp_field_size vtkzfp_zfp_field_size
#define zfp_field_stride vtkzfp_zfp_field_stride
#define zfp_field_type vtkzfp_zfp_field_type
#define zfp_library_version vtkzfp_zfp_library_version
#define zfp_promote_int16_to_int32 vtkzfp_zfp_promote_int16_to_int32
#define zfp_promote_int8_to_int32 vtkzfp_zfp_promote_int8_to_int32
#define zfp_promote_uint16_to_int32 vtkzfp_zfp_promote_uint16_to_int32
#define zfp_promote_uint8_to_int32 vtkzfp_zfp_promote_uint8_to_int32
#define zfp_read_header vtkzfp_zfp_read_header
#define zfp_stream_align vtkzfp_zfp_stream_align
#define zfp_stream_bit_stream vtkzfp_zfp_stream_bit_stream
#define zfp_stream_close vtkzfp_zfp_stream_close
#define zfp_stream_compressed_size vtkzfp_zfp_stream_compressed_size
#define zfp_stream_flush vtkzfp_zfp_stream_flush
#define zfp_stream_maximum_size vtkzfp_zfp_stream_maximum_size
#define zfp_stream_mode vtkzfp_zfp_stream_mode
#define zfp_stream_open vtkzfp_zfp_stream_open
#define zfp_stream_params vtkzfp_zfp_stream_params
#define zfp_stream_rewind vtkzfp_zfp_stream_rewind
#define zfp_stream_set_accuracy vtkzfp_zfp_stream_set_accuracy
#define zfp_stream_set_bit_stream vtkzfp_zfp_stream_set_bit_stream
#define zfp_stream_set_mode vtkzfp_zfp_stream_set_mode
#define zfp_stream_set_params vtkzfp_zfp_stream_set_params
#define zfp_stream_set_precision vtkzfp_zfp_stream_set_precision
#define zfp_stream_set_rate vtkzfp_zfp_stream_set_rate
#define zfp_type_size vtkzfp_zfp_type_size
#define zfp_version_string vtkzfp_zfp_version_string
#define zfp_write_header vtkzfp_zfp_write_header

#endif
