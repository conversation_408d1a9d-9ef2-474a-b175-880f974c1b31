/*****************************************************************************/
/*                                    XDMF                                   */
/*                       eXtensible Data Model and Format                    */
/*                                                                           */
/*  Id : XdmfRectilinearGrid.hpp                                             */
/*                                                                           */
/*  Author:                                                                  */
/*     <PERSON>                                                        */
/*     <EMAIL>                                           */
/*     US Army Research Laboratory                                           */
/*     Aberdeen Proving Ground, MD                                           */
/*                                                                           */
/*     Copyright @ 2011 US Army Research Laboratory                          */
/*     All Rights Reserved                                                   */
/*     See Copyright.txt for details                                         */
/*                                                                           */
/*     This software is distributed WITHOUT ANY WARRANTY; without            */
/*     even the implied warranty of MERCHANTABILITY or FITNESS               */
/*     FOR A PARTICULAR PURPOSE.  See the above copyright notice             */
/*     for more information.                                                 */
/*                                                                           */
/*****************************************************************************/

#ifndef XDMFRECTILINEARGRID_HPP_
#define XDMFRECTILINEARGRID_HPP_

// C Compatible Includes
#include "Xdmf.hpp"
#include "XdmfGrid.hpp"

#ifdef __cplusplus

// Forward Declarations
class XdmfArray;

/**
 * @brief A rectilinear grid consists of cells and points arranged on
 * a regular lattice in space.
 *
 * XdmfRectilinearGrid represents a mesh of cells and points arranged
 * on a regular lattice in space. Points are arranged along coordinate
 * axes, but the spacing between points may vary.
 *
 * In order to define a rectilinear grid, the coordinates along each
 * axis direction must be specified.
 *
 */
class XDMF_EXPORT XdmfRectilinearGrid : public XdmfGrid {

public:

  /**
   * Create a new rectilinear grid (Two dimensional).
   *
   * Example of use:
   *
   * C++
   *
   * @dontinclude ExampleXdmfRectilinearGrid.cpp
   * @skipline //#initvalues
   * @until //#initvalues
   * @skipline //#initialization2
   * @until //#initialization2
   *
   * Python
   *
   * @dontinclude XdmfExampleRectilinearGrid.py
   * @skipline #//initvalues
   * @until #//initvalues
   * @skipline #//initialization2
   * @until #//initialization2
   *
   * @param     xCoordinates    The coordinates of points along the x axis
   * @param     yCoordinates    The coordinates of points along the y axis.
   *
   * @return                    Constructed rectilinear grid.
   */
  static shared_ptr<XdmfRectilinearGrid>
  New(const shared_ptr<XdmfArray> xCoordinates,
      const shared_ptr<XdmfArray> yCoordinates);

  /**
   * Create a new rectilinear grid (Three dimensional).
   *
   * Example of use:
   *
   * C++
   *
   * @dontinclude ExampleXdmfRectilinearGrid.cpp
   * @skipline //#initvalues
   * @until //#initvalues
   * @skipline //#initialization3
   * @until //#initialization3
   *
   * Python
   *
   * @dontinclude XdmfExampleRectilinearGrid.py
   * @skipline #//initvalues
   * @until #//initvalues
   * @skipline #//initialization3
   * @until #//initialization3
   *
   * @param     xCoordinates    The coordinates of points along the x axis
   * @param     yCoordinates    The coordinates of points along the y axis.
   * @param     zCoordinates    The coordinates of points along the z axis.
   *
   * @return                    Constructed rectilinear grid.
   */
  static shared_ptr<XdmfRectilinearGrid>
  New(const shared_ptr<XdmfArray> xCoordinates,
      const shared_ptr<XdmfArray> yCoordinates,
      const shared_ptr<XdmfArray> zCoordinates);

  /**
   * Create a new rectilinear grid (N dimensional).
   *
   * Example of use:
   *
   * C++
   *
   * @dontinclude ExampleXdmfRectilinearGrid.cpp
   * @skipline //#initvalues
   * @until //#initvalues
   * @skipline //#initializationvector
   * @until //#initializationvector
   *
   * Python
   *
   * @dontinclude XdmfExampleRectilinearGrid.py
   * @skipline #//initvalues
   * @until #//initvalues
   * @skipline #//initializationvector
   * @until #//initializationvector
   *
   * @param     axesCoordinates         The coordinates of points along each axis.
   *
   * @return                            Constructed rectilinear grid.
   */
  static shared_ptr<XdmfRectilinearGrid>
  New(const std::vector<shared_ptr<XdmfArray> > & axesCoordinates);

  virtual ~XdmfRectilinearGrid();

  LOKI_DEFINE_VISITABLE(XdmfRectilinearGrid, XdmfGrid)
  static const std::string ItemTag;

  /**
   * Get the coordinates of the grid along a single axis.
   *
   * Example of use:
   *
   * C++
   *
   * @dontinclude ExampleXdmfRectilinearGrid.cpp
   * @skipline //#initvalues
   * @until //#initvalues
   * @skipline //#initialization2
   * @until //#initialization2
   * @skipline //#getCoodinatessingle
   * @until //#getCoodinatessingle
   *
   * Python
   *
   * @dontinclude XdmfExampleRectilinearGrid.py
   * @skipline #//initvalues
   * @until #//initvalues
   * @skipline #//initialization2
   * @until #//initialization2
   * @skipline #//getCoodinatessingle
   * @until #//getCoodinatessingle
   *
   * @param     axisIndex       The index of the axis to retrieve, (i.e. 0 for
   *                            x-axis). If no array exists at the index,
   *                            return NULL.
   *
   * @return                    Array of coordinates along requested axis
   */
  shared_ptr<XdmfArray> getCoordinates(const unsigned int axisIndex);

  /**
   * Get the coordinates of the grid along a single axis (const
   * version).
   *
   * Example of use:
   *
   * C++
   *
   * @dontinclude ExampleXdmfRectilinearGrid.cpp
   * @skipline //#initvalues
   * @until //#initvalues
   * @skipline //#initialization2
   * @until //#initialization2
   * @skipline //#getCoodinatessingleconst
   * @until //#getCoodinatessingleconst
   *
   * Python: does not support a constant version of this function
   *
   * @param     axisIndex       The index of the axis to retrieve (i.e. 0 for
   *                            x-axis). If no array exists at the index,
   *                            return NULL.
   *
   * @return                    Array of coordinates along requeste axis
   */
  shared_ptr<const XdmfArray>
  getCoordinates(const unsigned int axisIndex) const;

  /**
   * Get the coordinates of the grid along all axes.
   *
   * Example of use:
   *
   * C++
   *
   * @dontinclude ExampleXdmfRectilinearGrid.cpp
   * @skipline //#initvalues
   * @until //#initvalues
   * @skipline //#initializationvector
   * @until //#initializationvector
   * @skipline //#getCoodinatesvector
   * @until //#getCoodinatesvector
   *
   * Python
   *
   * @dontinclude XdmfExampleRectilinearGrid.py
   * @skipline #//initvalues
   * @until #//initvalues
   * @skipline #//initializationvector
   * @until #//initializationvector
   * @skipline #//getCoodinatesvector
   * @until #//getCoodinatesvector
   *
   * @return    Vector containing an array of coordinates along each
   *            direction.
   */
  std::vector<shared_ptr<XdmfArray> > getCoordinates();

  /**
   * Get the coordinates of the grid along all axes (const version).
   *
   * Example of use:
   *
   * C++
   *
   * @dontinclude ExampleXdmfRectilinearGrid.cpp
   * @skipline //#initvalues
   * @until //#initvalues
   * @skipline //#initializationvector
   * @until //#initializationvector
   * @skipline //#getCoodinatesvectorconst
   * @until //#getCoodinatesvectorconst
   *
   * Python: does not support a constant version of this function
   *
   * @return    Vector containing an array of coordinates along each
   *            direction.
   */
  const std::vector<shared_ptr<XdmfArray> > getCoordinates() const;

  /**
   * Get the dimensions of the grid, the number of points in each
   * direction.
   *
   * Example of use:
   *
   * C++
   *
   * @dontinclude ExampleXdmfRectilinearGrid.cpp
   * @skipline //#initvalues
   * @until //#initvalues
   * @skipline //#initializationvector
   * @until //#initializationvector
   * @skipline //#getDimensions
   * @until //#getDimensions
   *
   * Python
   *
   * @dontinclude XdmfExampleRectilinearGrid.py
   * @skipline #//initvalues
   * @until #//initvalues
   * @skipline #//initializationvector
   * @until #//initializationvector
   * @skipline #//getDimensions
   * @until #//getDimensions
   *
   * @return    XdmfArray containing dimensions of this grid.
   */
  shared_ptr<XdmfArray> getDimensions();

  /**
   * Get the dimensions of the grid, the number of points in each
   * direction (const version).
   *
   * Example of use:
   *
   * C++
   *
   * @dontinclude ExampleXdmfRectilinearGrid.cpp
   * @skipline //#initvalues
   * @until //#initvalues
   * @skipline //#initializationvector
   * @until //#initializationvector
   * @skipline //#getDimensionsconst
   * @until //#getDimensionsconst
   *
   * Python: Doesn't support a constant version of this function
   *
   * @return    XdmfArray containing the dimensions of this grid.
   */
  shared_ptr<const XdmfArray> getDimensions() const;

  virtual void read();

  virtual void release();

  /**
   * Set the coordinates of the grid along a single axis.
   *
   * Example of use:
   *
   * C++
   *
   * @dontinclude ExampleXdmfRectilinearGrid.cpp
   * @skipline //#initvalues
   * @until //#initvalues
   * @skipline //#initialization3
   * @until //#initialization3
   * @skipline //#setCoordinatessingle
   * @until //#setCoordinatessingle
   *
   * Python
   *
   * @dontinclude XdmfExampleRectilinearGrid.py
   * @skipline #//initvalues
   * @until #//initvalues
   * @skipline #//initialization3
   * @until #//initialization3
   * @skipline #//setCoordinatessingle
   * @until #//setCoordinatessingle
   *
   * @param     axisIndex               The index of the axis to set
   *                                    (i.e. 0 for x-axis).
   * @param     axisCoordinates         The coordinates of points along
   *                                    a single axis to set.
   */
  void setCoordinates(const unsigned int axisIndex,
                      const shared_ptr<XdmfArray> axisCoordinates);

  /**
   * Set the coordinates of the grid along all axes.
   *
   * Example of use:
   *
   * C++
   *
   * @dontinclude ExampleXdmfRectilinearGrid.cpp
   * @skipline //#initvalues
   * @until //#initvalues
   * @skipline //#initializationvector
   * @until //#initializationvector
   * @skipline //#setCoordinatesvector
   * @until //#setCoordinatesvector
   *
   * Python
   *
   * @dontinclude XdmfExampleRectilinearGrid.py
   * @skipline #//initvalues
   * @until #//initvalues
   * @skipline #//initializationvector
   * @until #//initializationvector
   * @skipline #//setCoordinatesvector
   * @until #//setCoordinatesvector
   *
   * @param     axesCoordinates         The coordinates of points
   *                                    along each axis.
   */
  void
  setCoordinates(const std::vector<shared_ptr<XdmfArray> > axesCoordinates);

  XdmfRectilinearGrid(XdmfRectilinearGrid &);

protected:

  XdmfRectilinearGrid(const std::vector<shared_ptr<XdmfArray> > & axesCoordinates);

  void copyGrid(shared_ptr<XdmfGrid> sourceGrid);

  void populateItem(const std::map<std::string, std::string> & itemProperties,
                    const std::vector<shared_ptr<XdmfItem> > & childItems,
                    const XdmfCoreReader * const reader);

private:

  /**
   * PIMPL
   */
  class XdmfRectilinearGridImpl;

  XdmfRectilinearGrid(const XdmfRectilinearGrid &);  // Not implemented.
  void operator=(const XdmfRectilinearGrid &);  // Not implemented.

};

#endif

#ifdef __cplusplus
extern "C" {
#endif

// C wrappers go here

struct XDMFRECTILINEARGRID; // Simply as a typedef to ensure correct typing
typedef struct XDMFRECTILINEARGRID XDMFRECTILINEARGRID;

XDMF_EXPORT XDMFRECTILINEARGRID * XdmfRectilinearGridNew(XDMFARRAY ** axesCoordinates, unsigned int numCoordinates, int passControl);

XDMF_EXPORT XDMFRECTILINEARGRID * XdmfRectilinearGridNew2D(XDMFARRAY * xCoordinates, XDMFARRAY * yCoordinates, int passControl);

XDMF_EXPORT XDMFRECTILINEARGRID * XdmfRectilinearGridNew3D(XDMFARRAY * xCoordinates, XDMFARRAY * yCoordinates, XDMFARRAY * zCoordinates, int passControl);

XDMF_EXPORT XDMFARRAY * XdmfRectilinearGridGetCoordinatesByIndex(XDMFRECTILINEARGRID * grid, unsigned int axisIndex, int * status);

XDMF_EXPORT XDMFARRAY ** XdmfRectilinearGridGetCoordinates(XDMFRECTILINEARGRID * grid, int * status);

XDMF_EXPORT int XdmfRectilinearGridGetNumberCoordinates(XDMFRECTILINEARGRID * grid, int * status);

XDMF_EXPORT XDMFARRAY * XdmfRectilinearGridGetDimensions(XDMFRECTILINEARGRID * grid, int * status);

XDMF_EXPORT void XdmfRectilinearGridSetCoordinates(XDMFRECTILINEARGRID * grid, XDMFARRAY ** axesCoordinates, unsigned int numCoordinates, int passControl, int * status);

XDMF_EXPORT void XdmfRectilinearGridSetCoordinatesByIndex(XDMFRECTILINEARGRID * grid, unsigned int index, XDMFARRAY * coordinates, int passControl, int * status);

XDMF_ITEM_C_CHILD_DECLARE(XdmfRectilinearGrid, XDMFRECTILINEARGRID, XDMF)
XDMF_GRID_C_CHILD_DECLARE(XdmfRectilinearGrid, XDMFRECTILINEARGRID, XDMF)

#ifdef __cplusplus
}
#endif

#endif /* XDMFRECTILINEARGRID_HPP_ */
