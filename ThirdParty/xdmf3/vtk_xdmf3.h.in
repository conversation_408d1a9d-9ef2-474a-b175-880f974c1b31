/*=========================================================================

  Program:   Visualization Toolkit
  Module:    vtk_xdmf2.h

  Copyright (c) <PERSON>, <PERSON>, <PERSON>
  All rights reserved.
  See Copyright.txt or http://www.kitware.com/Copyright.htm for details.

     This software is distributed WITHOUT ANY WARRANTY; without even
     the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
     PURPOSE.  See the above copyright notice for more information.

=========================================================================*/
#ifndef vtk_xdmf3_h
#define vtk_xdmf3_h

/* Use the xdmf library configured for VTK.  */
#cmakedefine VTK_USE_SYSTEM_XDMF3

#ifdef VTK_USE_SYSTEM_XDMF3
//TODO:
//# include <xdmf.h>
#else
//#include <vtkxdmf3/xdmf3.h>
#endif

#endif
