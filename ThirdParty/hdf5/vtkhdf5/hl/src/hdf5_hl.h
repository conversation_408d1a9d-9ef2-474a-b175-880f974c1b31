/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * Copyright by The HDF Group.                                               *
 * Copyright by the Board of Trustees of the University of Illinois.         *
 * All rights reserved.                                                      *
 *                                                                           *
 * This file is part of HDF5.  The full HDF5 copyright notice, including     *
 * terms governing use, modification, and redistribution, is contained in    *
 * the COPYING file, which can be found at the root of the source code       *
 * distribution tree, or in https://support.hdfgroup.org/ftp/HDF5/releases.  *
 * If you do not have access to either file, you may request a copy from     *
 * <EMAIL>.                                                        *
 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */

/*
 * This is the main public HDF5 High Level include file.  Put further
 * information in a particular header file and include that here, don't
 * fill this file with lots of gunk...
 */

#ifndef _HDF5_HL_H
#define _HDF5_HL_H
/* XXX(kitware): Mangle all HDF5 HL symbols */
#include "vtk_hdf5_hl_mangle.h"

#include "hdf5.h"       /* hdf5 main library */
#include "H5DOpublic.h" /* dataset optimization */
#include "H5DSpublic.h" /* dimension scales */
#include "H5LTpublic.h" /* lite */
#include "H5IMpublic.h" /* image */
#include "H5TBpublic.h" /* table */
#include "H5PTpublic.h" /* packet table */
#include "H5LDpublic.h" /* lite dataset */

#endif /*H5_INCLUDE_HL*/

