#ifndef VTK_HDF5_HL_MANGLE_H
#define VTK_HDF5_HL_MANGLE_H

#define asindex vtkhdf5_hl_asindex
#define cmpd_stack vtkhdf5_hl_cmpd_stack
#define csindex vtkhdf5_hl_csindex
#define first_quote vtkhdf5_hl_first_quote
#define H5DOappend vtkhdf5_hl_H5DOappend
#define H5DOwrite_chunk vtkhdf5_hl_H5DOwrite_chunk
#define H5DSattach_scale vtkhdf5_hl_H5DSattach_scale
#define H5DSdetach_scale vtkhdf5_hl_H5DSdetach_scale
#define H5DSget_label vtkhdf5_hl_H5DSget_label
#define H5DSget_num_scales vtkhdf5_hl_H5DSget_num_scales
#define H5DSget_scale_name vtkhdf5_hl_H5DSget_scale_name
#define H5DSis_attached vtkhdf5_hl_H5DSis_attached
#define H5DSis_scale vtkhdf5_hl_H5DSis_scale
#define H5DSiterate_scales vtkhdf5_hl_H5DSiterate_scales
#define H5DSset_label vtkhdf5_hl_H5DSset_label
#define H5DSset_scale vtkhdf5_hl_H5DSset_scale
#define H5IM_find_palette vtkhdf5_hl_H5IM_find_palette
#define H5IMget_image_info vtkhdf5_hl_H5IMget_image_info
#define H5IMget_npalettes vtkhdf5_hl_H5IMget_npalettes
#define H5IMget_palette vtkhdf5_hl_H5IMget_palette
#define H5IMget_palette_info vtkhdf5_hl_H5IMget_palette_info
#define H5IMis_image vtkhdf5_hl_H5IMis_image
#define H5IMis_palette vtkhdf5_hl_H5IMis_palette
#define H5IMlink_palette vtkhdf5_hl_H5IMlink_palette
#define H5IMmake_image_24bit vtkhdf5_hl_H5IMmake_image_24bit
#define H5IMmake_image_8bit vtkhdf5_hl_H5IMmake_image_8bit
#define H5IMmake_palette vtkhdf5_hl_H5IMmake_palette
#define H5IMread_image vtkhdf5_hl_H5IMread_image
#define H5IMunlink_palette vtkhdf5_hl_H5IMunlink_palette
#define H5LD_clean_vector vtkhdf5_hl_H5LD_clean_vector
#define H5LD_construct_vector vtkhdf5_hl_H5LD_construct_vector
#define H5LDget_dset_dims vtkhdf5_hl_H5LDget_dset_dims
#define H5LDget_dset_elmts vtkhdf5_hl_H5LDget_dset_elmts
#define H5LDget_dset_type_size vtkhdf5_hl_H5LDget_dset_type_size
#define H5LT_dtype_to_text vtkhdf5_hl_H5LT_dtype_to_text
#define H5LTdtype_to_text vtkhdf5_hl_H5LTdtype_to_text
#define H5LT_find_attribute vtkhdf5_hl_H5LT_find_attribute
#define H5LTfind_attribute vtkhdf5_hl_H5LTfind_attribute
#define H5LTfind_dataset vtkhdf5_hl_H5LTfind_dataset
#define H5LTget_attribute vtkhdf5_hl_H5LTget_attribute
#define H5LTget_attribute_char vtkhdf5_hl_H5LTget_attribute_char
#define H5LT_get_attribute_disk vtkhdf5_hl_H5LT_get_attribute_disk
#define H5LTget_attribute_double vtkhdf5_hl_H5LTget_attribute_double
#define H5LTget_attribute_float vtkhdf5_hl_H5LTget_attribute_float
#define H5LTget_attribute_info vtkhdf5_hl_H5LTget_attribute_info
#define H5LTget_attribute_int vtkhdf5_hl_H5LTget_attribute_int
#define H5LTget_attribute_long vtkhdf5_hl_H5LTget_attribute_long
#define H5LTget_attribute_long_long vtkhdf5_hl_H5LTget_attribute_long_long
#define H5LTget_attribute_ndims vtkhdf5_hl_H5LTget_attribute_ndims
#define H5LTget_attribute_short vtkhdf5_hl_H5LTget_attribute_short
#define H5LTget_attribute_string vtkhdf5_hl_H5LTget_attribute_string
#define H5LTget_attribute_uchar vtkhdf5_hl_H5LTget_attribute_uchar
#define H5LTget_attribute_uint vtkhdf5_hl_H5LTget_attribute_uint
#define H5LTget_attribute_ulong vtkhdf5_hl_H5LTget_attribute_ulong
#define H5LTget_attribute_ushort vtkhdf5_hl_H5LTget_attribute_ushort
#define H5LTget_dataset_info vtkhdf5_hl_H5LTget_dataset_info
#define H5LTget_dataset_ndims vtkhdf5_hl_H5LTget_dataset_ndims
#define H5LTmake_dataset vtkhdf5_hl_H5LTmake_dataset
#define H5LTmake_dataset_char vtkhdf5_hl_H5LTmake_dataset_char
#define H5LTmake_dataset_double vtkhdf5_hl_H5LTmake_dataset_double
#define H5LTmake_dataset_float vtkhdf5_hl_H5LTmake_dataset_float
#define H5LTmake_dataset_int vtkhdf5_hl_H5LTmake_dataset_int
#define H5LTmake_dataset_long vtkhdf5_hl_H5LTmake_dataset_long
#define H5LTmake_dataset_short vtkhdf5_hl_H5LTmake_dataset_short
#define H5LTmake_dataset_string vtkhdf5_hl_H5LTmake_dataset_string
#define H5LTopen_file_image vtkhdf5_hl_H5LTopen_file_image
#define H5LTpath_valid vtkhdf5_hl_H5LTpath_valid
#define H5LTread_dataset vtkhdf5_hl_H5LTread_dataset
#define H5LTread_dataset_char vtkhdf5_hl_H5LTread_dataset_char
#define H5LTread_dataset_double vtkhdf5_hl_H5LTread_dataset_double
#define H5LTread_dataset_float vtkhdf5_hl_H5LTread_dataset_float
#define H5LTread_dataset_int vtkhdf5_hl_H5LTread_dataset_int
#define H5LTread_dataset_long vtkhdf5_hl_H5LTread_dataset_long
#define H5LTread_dataset_short vtkhdf5_hl_H5LTread_dataset_short
#define H5LTread_dataset_string vtkhdf5_hl_H5LTread_dataset_string
#define H5LTset_attribute_char vtkhdf5_hl_H5LTset_attribute_char
#define H5LTset_attribute_double vtkhdf5_hl_H5LTset_attribute_double
#define H5LTset_attribute_float vtkhdf5_hl_H5LTset_attribute_float
#define H5LTset_attribute_int vtkhdf5_hl_H5LTset_attribute_int
#define H5LTset_attribute_long vtkhdf5_hl_H5LTset_attribute_long
#define H5LTset_attribute_long_long vtkhdf5_hl_H5LTset_attribute_long_long
#define H5LT_set_attribute_numerical vtkhdf5_hl_H5LT_set_attribute_numerical
#define H5LTset_attribute_short vtkhdf5_hl_H5LTset_attribute_short
#define H5LT_set_attribute_string vtkhdf5_hl_H5LT_set_attribute_string
#define H5LTset_attribute_string vtkhdf5_hl_H5LTset_attribute_string
#define H5LTset_attribute_uchar vtkhdf5_hl_H5LTset_attribute_uchar
#define H5LTset_attribute_uint vtkhdf5_hl_H5LTset_attribute_uint
#define H5LTset_attribute_ulong vtkhdf5_hl_H5LTset_attribute_ulong
#define H5LTset_attribute_ushort vtkhdf5_hl_H5LTset_attribute_ushort
#define H5LTtext_to_dtype vtkhdf5_hl_H5LTtext_to_dtype
#define H5LTyyalloc vtkhdf5_hl_H5LTyyalloc
#define H5LTyy_create_buffer vtkhdf5_hl_H5LTyy_create_buffer
#define H5LTyy_delete_buffer vtkhdf5_hl_H5LTyy_delete_buffer
#define H5LTyyerror vtkhdf5_hl_H5LTyyerror
#define H5LTyy_flush_buffer vtkhdf5_hl_H5LTyy_flush_buffer
#define H5LTyyfree vtkhdf5_hl_H5LTyyfree
#define H5LTyyget_debug vtkhdf5_hl_H5LTyyget_debug
#define H5LTyyget_in vtkhdf5_hl_H5LTyyget_in
#define H5LTyyget_leng vtkhdf5_hl_H5LTyyget_leng
#define H5LTyyget_lineno vtkhdf5_hl_H5LTyyget_lineno
#define H5LTyyget_out vtkhdf5_hl_H5LTyyget_out
#define H5LTyyget_text vtkhdf5_hl_H5LTyyget_text
#define H5LTyylex vtkhdf5_hl_H5LTyylex
#define H5LTyylex_destroy vtkhdf5_hl_H5LTyylex_destroy
#define H5LTyylineno vtkhdf5_hl_H5LTyylineno
#define H5LTyyparse vtkhdf5_hl_H5LTyyparse
#define H5LTyypop_buffer_state vtkhdf5_hl_H5LTyypop_buffer_state
#define H5LTyypush_buffer_state vtkhdf5_hl_H5LTyypush_buffer_state
#define H5LTyyrealloc vtkhdf5_hl_H5LTyyrealloc
#define H5LTyyrestart vtkhdf5_hl_H5LTyyrestart
#define H5LTyy_scan_buffer vtkhdf5_hl_H5LTyy_scan_buffer
#define H5LTyy_scan_bytes vtkhdf5_hl_H5LTyy_scan_bytes
#define H5LTyy_scan_string vtkhdf5_hl_H5LTyy_scan_string
#define H5LTyyset_debug vtkhdf5_hl_H5LTyyset_debug
#define H5LTyyset_in vtkhdf5_hl_H5LTyyset_in
#define H5LTyyset_lineno vtkhdf5_hl_H5LTyyset_lineno
#define H5LTyyset_out vtkhdf5_hl_H5LTyyset_out
#define H5LTyy_switch_to_buffer vtkhdf5_hl_H5LTyy_switch_to_buffer
#define H5LTyywrap vtkhdf5_hl_H5LTyywrap
#define H5PTappend vtkhdf5_hl_H5PTappend
#define H5PTclose vtkhdf5_hl_H5PTclose
#define H5PTcreate vtkhdf5_hl_H5PTcreate
#define H5PTcreate_fl vtkhdf5_hl_H5PTcreate_fl
#define H5PTcreate_index vtkhdf5_hl_H5PTcreate_index
#define H5PTfree_vlen_buff vtkhdf5_hl_H5PTfree_vlen_buff
#define H5PTget_dataset vtkhdf5_hl_H5PTget_dataset
#define H5PTget_index vtkhdf5_hl_H5PTget_index
#define H5PTget_next vtkhdf5_hl_H5PTget_next
#define H5PTget_num_packets vtkhdf5_hl_H5PTget_num_packets
#define H5PTget_type vtkhdf5_hl_H5PTget_type
#define H5PTis_valid vtkhdf5_hl_H5PTis_valid
#define H5PTis_varlen vtkhdf5_hl_H5PTis_varlen
#define H5PTopen vtkhdf5_hl_H5PTopen
#define H5PTread_packets vtkhdf5_hl_H5PTread_packets
#define H5PTset_index vtkhdf5_hl_H5PTset_index
#define H5TBadd_records_from vtkhdf5_hl_H5TBadd_records_from
#define H5TBAget_fill vtkhdf5_hl_H5TBAget_fill
#define H5TBAget_title vtkhdf5_hl_H5TBAget_title
#define H5TBappend_records vtkhdf5_hl_H5TBappend_records
#define H5TBcombine_tables vtkhdf5_hl_H5TBcombine_tables
#define H5TB_common_append_records vtkhdf5_hl_H5TB_common_append_records
#define H5TB_common_read_records vtkhdf5_hl_H5TB_common_read_records
#define H5TBdelete_field vtkhdf5_hl_H5TBdelete_field
#define H5TBdelete_record vtkhdf5_hl_H5TBdelete_record
#define H5TBget_field_info vtkhdf5_hl_H5TBget_field_info
#define H5TBget_table_info vtkhdf5_hl_H5TBget_table_info
#define H5TBinsert_field vtkhdf5_hl_H5TBinsert_field
#define H5TBinsert_record vtkhdf5_hl_H5TBinsert_record
#define H5TBmake_table vtkhdf5_hl_H5TBmake_table
#define H5TBread_fields_index vtkhdf5_hl_H5TBread_fields_index
#define H5TBread_fields_name vtkhdf5_hl_H5TBread_fields_name
#define H5TBread_records vtkhdf5_hl_H5TBread_records
#define H5TBread_table vtkhdf5_hl_H5TBread_table
#define H5TBwrite_fields_index vtkhdf5_hl_H5TBwrite_fields_index
#define H5TBwrite_fields_name vtkhdf5_hl_H5TBwrite_fields_name
#define H5TBwrite_records vtkhdf5_hl_H5TBwrite_records
#define my_yyinput vtkhdf5_hl_my_yyinput

#endif
