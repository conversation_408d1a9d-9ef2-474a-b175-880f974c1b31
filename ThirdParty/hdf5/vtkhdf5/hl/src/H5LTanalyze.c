#if __GNUC__ >= 4 && __GNUC_MINOR__ >=2                           
#pragma GCC diagnostic ignored "-Wconversion"                     
#pragma GCC diagnostic ignored "-Wimplicit-function-declaration"  
#pragma GCC diagnostic ignored "-Wlarger-than="                   
#pragma GCC diagnostic ignored "-Wmissing-prototypes"             
#pragma GCC diagnostic ignored "-Wnested-externs"                 
#pragma GCC diagnostic ignored "-Wold-style-definition"           
#pragma GCC diagnostic ignored "-Wredundant-decls"                
#pragma GCC diagnostic ignored "-Wsign-compare"                   
#pragma GCC diagnostic ignored "-Wsign-conversion"                
#pragma GCC diagnostic ignored "-Wstrict-overflow"                
#pragma GCC diagnostic ignored "-Wstrict-prototypes"              
#pragma GCC diagnostic ignored "-Wsuggest-attribute=pure"         
#pragma GCC diagnostic ignored "-Wswitch-default"                 
#pragma GCC diagnostic ignored "-Wunused-function"                
#pragma GCC diagnostic ignored "-Wunused-macros"                  
#pragma GCC diagnostic ignored "-Wunused-parameter"               
#elif defined __SUNPRO_CC                                         
#pragma disable_warn                                              
#elif defined _MSC_VER                                            
#pragma warning(push, 1)                                          
#endif                                                            
#line 2 "hl/src/H5LTanalyze.c"

#line 4 "hl/src/H5LTanalyze.c"

/* XXX(kitware): Mangle all HDF5 HL symbols */
#include "vtk_hdf5_hl_mangle.h"

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

#define yy_create_buffer H5LTyy_create_buffer
#define yy_delete_buffer H5LTyy_delete_buffer
#define yy_flex_debug H5LTyy_flex_debug
#define yy_init_buffer H5LTyy_init_buffer
#define yy_flush_buffer H5LTyy_flush_buffer
#define yy_load_buffer_state H5LTyy_load_buffer_state
#define yy_switch_to_buffer H5LTyy_switch_to_buffer
#define yyin H5LTyyin
#define yyleng H5LTyyleng
#define yylex H5LTyylex
#define yylineno H5LTyylineno
#define yyout H5LTyyout
#define yyrestart H5LTyyrestart
#define yytext H5LTyytext
#define yywrap H5LTyywrap
#define yyalloc H5LTyyalloc
#define yyrealloc H5LTyyrealloc
#define yyfree H5LTyyfree

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 5
#define YY_FLEX_SUBMINOR_VERSION 39
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>
#include <stdint.h>

/* end standard C headers. */

/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* C99 says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types. 
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

#ifdef __cplusplus

/* The "const" storage-class-modifier is valid. */
#define YY_USE_CONST

#else	/* ! __cplusplus */

/* C99 requires __STDC__ to be defined as 1. */
#if defined (__STDC__)

#define YY_USE_CONST

#endif	/* defined (__STDC__) */
#endif	/* ! __cplusplus */

#ifdef YY_USE_CONST
#define yyconst const
#else
#define yyconst
#endif

/* Returned upon end-of-file. */
#define YY_NULL 0

/* Promotes a possibly negative, possibly signed char to an unsigned
 * integer for use as an array index.  If the signed char is negative,
 * we want to instead treat it as an 8-bit unsigned char, hence the
 * double cast.
 */
#define YY_SC_TO_UI(c) ((unsigned int) (unsigned char) c)

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN (yy_start) = 1 + 2 *

/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START (((yy_start) - 1) / 2)
#define YYSTATE YY_START

/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)

/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE H5LTyyrestart(H5LTyyin  )

#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

extern yy_size_t H5LTyyleng;

extern FILE *H5LTyyin, *H5LTyyout;

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2

    #define YY_LESS_LINENO(n)
    #define YY_LINENO_REWIND_TO(ptr)
    
/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up H5LTyytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		*yy_cp = (yy_hold_char); \
		YY_RESTORE_YY_MORE_OFFSET \
		(yy_c_buf_p) = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
		YY_DO_BEFORE_ACTION; /* set up H5LTyytext again */ \
		} \
	while ( 0 )

#define unput(c) yyunput( c, (yytext_ptr)  )

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
	{
	FILE *yy_input_file;

	char *yy_ch_buf;		/* input buffer */
	char *yy_buf_pos;		/* current position in input buffer */

	/* Size of input buffer in bytes, not including room for EOB
	 * characters.
	 */
	yy_size_t yy_buf_size;

	/* Number of characters read into yy_ch_buf, not including EOB
	 * characters.
	 */
	yy_size_t yy_n_chars;

	/* Whether we "own" the buffer - i.e., we know we created it,
	 * and can realloc() it to grow it, and should free() it to
	 * delete it.
	 */
	int yy_is_our_buffer;

	/* Whether this is an "interactive" input source; if so, and
	 * if we're using stdio for input, then we want to use getc()
	 * instead of fread(), to make sure we stop fetching input after
	 * each newline.
	 */
	int yy_is_interactive;

	/* Whether we're considered to be at the beginning of a line.
	 * If so, '^' rules will be active on the next match, otherwise
	 * not.
	 */
	int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */
    
	/* Whether to try to fill the input buffer when we reach the
	 * end of it.
	 */
	int yy_fill_buffer;

	int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
	/* When an EOF's been seen but there's still some text to process
	 * then we mark the buffer as YY_EOF_PENDING, to indicate that we
	 * shouldn't try reading from the input source any more.  We might
	 * still have a bunch of tokens to match, though, because of
	 * possible backing-up.
	 *
	 * When we actually see the EOF, we change the status to "new"
	 * (via H5LTyyrestart()), so that the user can continue scanning by
	 * just pointing H5LTyyin at a new input file.
	 */
#define YY_BUFFER_EOF_PENDING 2

	};
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* Stack of input buffers. */
static size_t yy_buffer_stack_top = 0; /**< index of top of stack. */
static size_t yy_buffer_stack_max = 0; /**< capacity of stack. */
static YY_BUFFER_STATE * yy_buffer_stack = 0; /**< Stack as an array. */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ( (yy_buffer_stack) \
                          ? (yy_buffer_stack)[(yy_buffer_stack_top)] \
                          : NULL)

/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE (yy_buffer_stack)[(yy_buffer_stack_top)]

/* yy_hold_char holds the character lost when H5LTyytext is formed. */
static char yy_hold_char;
static yy_size_t yy_n_chars;		/* number of characters read into yy_ch_buf */
yy_size_t H5LTyyleng;

/* Points to current character in buffer. */
static char *yy_c_buf_p = (char *) 0;
static int yy_init = 0;		/* whether we need to initialize */
static int yy_start = 0;	/* start state number */

/* Flag which is used to allow H5LTyywrap()'s to do buffer switches
 * instead of setting up a fresh H5LTyyin.  A bit of a hack ...
 */
static int yy_did_buffer_switch_on_eof;

void H5LTyyrestart (FILE *input_file  );
void H5LTyy_switch_to_buffer (YY_BUFFER_STATE new_buffer  );
YY_BUFFER_STATE H5LTyy_create_buffer (FILE *file,int size  );
void H5LTyy_delete_buffer (YY_BUFFER_STATE b  );
void H5LTyy_flush_buffer (YY_BUFFER_STATE b  );
void H5LTyypush_buffer_state (YY_BUFFER_STATE new_buffer  );
void H5LTyypop_buffer_state (void );

static void H5LTyyensure_buffer_stack (void );
static void H5LTyy_load_buffer_state (void );
static void H5LTyy_init_buffer (YY_BUFFER_STATE b,FILE *file  );

#define YY_FLUSH_BUFFER H5LTyy_flush_buffer(YY_CURRENT_BUFFER )

YY_BUFFER_STATE H5LTyy_scan_buffer (char *base,yy_size_t size  );
YY_BUFFER_STATE H5LTyy_scan_string (yyconst char *yy_str  );
YY_BUFFER_STATE H5LTyy_scan_bytes (yyconst char *bytes,yy_size_t len  );

void *H5LTyyalloc (yy_size_t  );
void *H5LTyyrealloc (void *,yy_size_t  );
void H5LTyyfree (void *  );

#define yy_new_buffer H5LTyy_create_buffer

#define yy_set_interactive(is_interactive) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){ \
        H5LTyyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            H5LTyy_create_buffer(H5LTyyin,YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
	}

#define yy_set_bol(at_bol) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){\
        H5LTyyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            H5LTyy_create_buffer(H5LTyyin,YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
	}

#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* Begin user sect3 */

typedef unsigned char YY_CHAR;

FILE *H5LTyyin = (FILE *) 0, *H5LTyyout = (FILE *) 0;

typedef int yy_state_type;

extern int H5LTyylineno;

int H5LTyylineno = 1;

extern char *H5LTyytext;
#define yytext_ptr H5LTyytext

static yy_state_type yy_get_previous_state (void );
static yy_state_type yy_try_NUL_trans (yy_state_type current_state  );
static int yy_get_next_buffer (void );
static void yy_fatal_error (yyconst char msg[]  );

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up H5LTyytext.
 */
#define YY_DO_BEFORE_ACTION \
	(yytext_ptr) = yy_bp; \
	H5LTyyleng = (size_t) (yy_cp - yy_bp); \
	(yy_hold_char) = *yy_cp; \
	*yy_cp = '\0'; \
	(yy_c_buf_p) = yy_cp;

#define YY_NUM_RULES 66
#define YY_END_OF_BUFFER 67
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
	{
	flex_int32_t yy_verify;
	flex_int32_t yy_nxt;
	};
static yyconst flex_int16_t yy_acclist[437] =
    {   0,
       64,   64,   64,   64,   67,   66,   64,   66,   64,   65,
       66,   56,   66,   55,   66,   62,   66,   63,   66,   66,
       66,   66,   66,   60,   66,   61,   66,   58,   66,   59,
       66,   57,   66,   57,   64,   66,   57,   64,   65,   66,
       55,   57,   66,   57,   62,   66,   57,   63,   66,   57,
       66,   57,   66,   57,   66,   57,   66,   57,   60,   66,
       57,   61,   66,   57,   58,   66,   57,   59,   66,   64,
       55,   57,   57,   64,   55,   57,   57,   57,   57,   57,
       57,   57,   57,   57,   57,   57,   38,   38,   57,   57,
       57,   57,   57,   57,   39,   39,   57,   57,   57,   57,

       57,   57,   57,   57,   57,   57,   57,   57,   57,   57,
       37,   57,   57,   57,   57,   57,   57,   57,   57,   57,
       57,   57,   57,   57,   57,   37,   57,   57,   54,   36,
       57,   57,   57,   57,   57,   57,   57,   57,   57,   57,
       57,   57,   57,   57,   54,   57,   36,   57,   45,   49,
       51,   53,   57,   57,   57,   45,   57,   49,   57,   57,
       57,   57,   57,   57,   57,   57,   57,   51,   57,   53,
       57,   50,   50,   57,   57,   57,   57,   57,   57,   57,
       57,   57,   57,   57,   57,   57,   52,   35,   57,   57,
       57,   57,   57,   57,   52,   57,   57,   57,   57,   57,

       57,   57,   57,   57,   35,   57,   57,   57,   57,   57,
       57,   57,   57,   57,   57,   57,   57,   57,   57,   57,
       57,   57,   57,   57,   57,   57,   57,   57,   57,   48,
        1,    2,    9,   10,   47,   48,   57,   57,   57,   57,
       57,   57,   57,   57,   57,   57,   57,   57,   57,   57,
       57,   57,   57,   57,   57,    1,   57,    2,   57,   57,
       57,   57,   57,   57,   57,    9,   57,   10,   57,   57,
       57,   47,   57,   44,    3,    4,    5,    6,    7,    8,
       11,   12,   13,   14,   15,   16,   57,   44,   57,   57,
       57,   57,   57,   57,   57,   57,   57,   57,   57,   57,

       57,   57,   57,   57,   57,   57,   57,    3,   57,    4,
       57,    5,   57,    6,   57,    7,   57,    8,   57,   11,
       57,   12,   57,   13,   57,   14,   57,   15,   57,   16,
       57,   57,   57,   57,   43,   46,   28,   29,   30,   31,
       22,   43,   57,   46,   57,   28,   57,   29,   57,   30,
       57,   31,   57,   57,   57,   57,   22,   57,   57,   57,
       57,   57,   57,   57,   57,   57,   57,   57,   57,   57,
       57,   17,   24,   23,   41,   17,   57,   57,   57,   57,
       57,   24,   57,   57,   57,   57,   23,   57,   57,   57,
       57,   41,   57,   57,   57,   32,   26,   18,   20,   19,

       25,   40,   42,   57,   32,   57,   57,   26,   57,   18,
       57,   20,   57,   19,   57,   57,   25,   57,   57,   40,
       57,   42,   57,   33,   27,   21,   33,   57,   57,   27,
       57,   21,   57,   34,   34,   57
    } ;

static yyconst flex_int16_t yy_accept[546] =
    {   0,
        1,    2,    3,    4,    5,    6,    7,    9,   12,   14,
       16,   18,   20,   21,   22,   23,   24,   26,   28,   30,
       32,   34,   37,   41,   44,   47,   50,   52,   54,   56,
       58,   61,   64,   67,   70,   71,   72,   72,   72,   72,
       72,   72,   73,   75,   77,   78,   79,   80,   81,   82,
       82,   82,   82,   82,   82,   83,   84,   85,   86,   87,
       88,   88,   88,   88,   88,   88,   90,   91,   92,   93,
       94,   95,   96,   96,   96,   96,   96,   96,   96,   96,
       96,   96,   96,   96,   96,   96,   98,   99,  100,  101,
      102,  103,  104,  105,  106,  107,  108,  109,  110,  111,

      111,  111,  111,  111,  111,  111,  111,  111,  111,  111,
      111,  111,  111,  111,  112,  112,  113,  114,  115,  116,
      117,  118,  119,  120,  121,  122,  123,  124,  125,  126,
      128,  129,  129,  129,  129,  129,  129,  129,  129,  129,
      129,  129,  129,  129,  129,  129,  130,  131,  132,  133,
      134,  135,  136,  137,  138,  139,  140,  141,  142,  143,
      144,  145,  147,  149,  149,  149,  149,  150,  151,  151,
      151,  151,  151,  151,  151,  151,  151,  152,  153,  154,
      155,  156,  158,  160,  161,  162,  163,  164,  165,  166,
      167,  168,  170,  172,  173,  173,  173,  173,  173,  173,

      173,  173,  173,  173,  173,  173,  173,  175,  176,  177,
      178,  179,  180,  181,  182,  183,  184,  185,  186,  187,
      187,  187,  187,  187,  187,  187,  188,  188,  188,  188,
      188,  188,  188,  188,  188,  189,  189,  189,  189,  190,
      191,  192,  193,  194,  195,  197,  198,  199,  200,  201,
      202,  203,  204,  205,  207,  208,  209,  210,  210,  210,
      210,  210,  210,  210,  210,  210,  210,  210,  210,  210,
      210,  210,  210,  210,  210,  210,  210,  210,  211,  212,
      213,  214,  215,  216,  217,  218,  219,  220,  221,  222,
      223,  224,  225,  226,  227,  228,  229,  230,  231,  231,

      231,  231,  231,  231,  231,  231,  231,  231,  231,  231,
      231,  231,  231,  231,  231,  231,  231,  232,  233,  233,
      233,  233,  233,  233,  233,  234,  235,  235,  235,  236,
      238,  239,  240,  241,  242,  243,  244,  245,  246,  247,
      248,  249,  250,  251,  252,  253,  254,  255,  256,  258,
      260,  261,  262,  263,  264,  265,  266,  268,  270,  271,
      272,  274,  274,  275,  275,  275,  275,  275,  275,  275,
      275,  275,  275,  275,  275,  275,  275,  275,  275,  275,
      275,  275,  276,  277,  278,  279,  280,  281,  282,  283,
      284,  285,  286,  287,  287,  287,  287,  288,  290,  291,

      292,  293,  294,  295,  296,  297,  298,  299,  300,  301,
      302,  303,  304,  305,  306,  307,  308,  310,  312,  314,
      316,  318,  320,  322,  324,  326,  328,  330,  332,  333,
      334,  335,  336,  337,  338,  339,  340,  341,  341,  341,
      341,  342,  342,  342,  342,  342,  342,  342,  342,  342,
      342,  342,  342,  342,  342,  344,  346,  348,  350,  352,
      354,  355,  356,  357,  359,  360,  361,  362,  363,  364,
      365,  366,  367,  368,  369,  370,  371,  372,  373,  373,
      373,  373,  373,  374,  374,  374,  374,  375,  375,  375,
      375,  376,  376,  376,  378,  379,  380,  381,  382,  384,

      385,  386,  387,  389,  390,  391,  392,  394,  395,  396,
      396,  397,  397,  398,  399,  400,  401,  401,  402,  402,
      403,  404,  405,  407,  408,  410,  412,  414,  416,  417,
      419,  420,  422,  424,  425,  425,  426,  427,  429,  430,
      432,  434,  435,  437,  437
    } ;

static yyconst flex_int32_t yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    2,    3,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    2,    1,    4,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    5,    6,    7,
        8,    9,   10,   11,    5,   12,    5,   13,   14,    1,
        1,    1,    1,    1,   15,   16,   17,   18,   19,   20,
       21,   22,   23,    1,    1,   24,   25,   26,   27,   28,
       29,   30,   31,   32,   33,   34,    1,    1,   35,   36,
       37,    1,   38,    1,   39,    1,    1,    1,    1,    1,

        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,   40,    1,   41,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,

        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

static yyconst flex_int32_t yy_meta[42] =
    {   0,
        1,    1,    1,    2,    3,    3,    3,    3,    3,    3,
        3,    3,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1
    } ;

static yyconst flex_int16_t yy_base[547] =
    {   0,
        0,    0,   41,    0,  610,  611,   81,   83,  611,    0,
      611,  611,   56,  599,  580,  575,  611,  611,  611,  611,
        0,   87,   89,   88,    0,    0,   70,  596,  577,  572,
        0,    0,    0,    0,  101,    0,  584,  567,  569,  571,
      569,    0,  103,    0,  579,  562,  564,  566,  564,  561,
      564,  552,  551,   79,  557,  560,  548,  547,   80,  611,
      566,   97,   87,  569,  560,    0,  563,  117,   90,  566,
      557,  611,  549,   99,  552,  550,  557,  560,  546,  541,
      118,  549,  556,  552,  533,    0,  538,  108,  541,  539,
      546,  549,  535,  530,  126,  538,  545,  541,  522,  527,

      531,  536,  523,  520,  522,  532,  518,  534,   95,  518,
      528,  510,  524,  611,  525,  513,  517,  522,  509,  506,
      508,  518,  504,  520,   97,  504,  514,  496,  510,    0,
      511,  514,  500,  495,  520,  500,  492,  504,  499,  492,
      481,  106,  496,  492,  498,  611,  611,  501,  487,  482,
      507,  487,  479,  491,  486,  479,  468,  123,  483,  479,
      485,    0,    0,  468,  475,  462,  611,  611,  470,  460,
      464,  464,  126,  470,  126,  480,  611,  611,  459,  466,
      453,    0,    0,  461,  451,  455,  455,  130,  461,  129,
      471,    0,    0,  611,  452,  139,  469,  463,  463,  462,

      158,  165,  459,  446,  450,  461,    0,  443,  141,  460,
      454,  454,  453,  172,  179,  450,  437,  441,  452,  441,
      435,  433,  438,  150,  424,  611,  451,  454,  451,  151,
      448,  451,  448,  165,  611,  432,  440,  430,  427,  421,
      419,  424,  157,  410,    0,  437,  440,  437,  170,  434,
      437,  434,  172,    0,  418,  426,  416,  421,  421,  417,
      397,  428,  425,  180,  177,  183,  186,  414,  413,  190,
      192,  193,  412,  411,  405,  411,  408,  408,  408,  404,
      384,  415,  412,  201,  196,  199,  206,  401,  400,  211,
      212,  213,  399,  398,  392,  398,  395,  611,  390,  400,

      380,  215,  217,  388,  382,  384,  381,  220,  223,  225,
      387,  386,  385,  384,  383,  382,  611,  611,  381,  380,
      379,  378,  377,  376,  611,  611,  218,  375,  611,    0,
      370,  380,  360,  227,  236,  368,  362,  364,  361,  235,
      241,  244,  367,  366,  365,  364,  363,  362,    0,    0,
      361,  360,  359,  358,  357,  356,    0,    0,  237,  355,
        0,  350,  611,  366,  352,  351,  350,  349,  352,  333,
      338,  332,  336,  335,  335,  338,  332,  336,  331,  155,
      334,  611,  611,  611,  611,  611,  611,  611,  611,  611,
      611,  611,  611,  340,  335,  325,  329,    0,  345,  331,

      330,  329,  328,  331,  312,  317,  311,  315,  314,  314,
      317,  311,  315,  310,  168,  313,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,  319,  314,
      304,  611,  611,  611,  611,  611,  611,  301,  314,  314,
      611,  295,  301,  305,  310,  294,  308,  290,  294,  294,
      292,  300,  287,  301,    0,    0,    0,    0,    0,    0,
      285,  298,  298,    0,  279,  285,  289,  294,  278,  292,
      274,  278,  278,  276,  284,  271,  285,  611,  275,  266,
      281,  275,  611,  265,  262,  263,  611,  266,  270,  260,
      611,  264,  270,    0,  263,  254,  269,  263,    0,  253,

      250,  251,    0,  254,  258,  247,    0,  249,  255,  253,
      611,  247,  611,  611,  611,  611,  249,  611,  234,  611,
      611,  245,    0,  233,    0,    0,    0,    0,  234,    0,
      222,    0,    0,  611,  207,  611,  611,    0,  186,    0,
        0,  611,    0,  611,  106,  275
    } ;

static yyconst flex_int16_t yy_def[547] =
    {   0,
      544,    1,  544,    3,  544,  544,  544,  544,  544,  545,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      546,  546,  546,  546,  546,  546,  546,  546,  546,  546,
      546,  546,  546,  546,  544,  545,  544,  544,  544,  544,
      544,  546,  546,   24,  546,  546,  546,  546,  546,  544,
      544,  544,  544,  544,  546,  546,  546,  546,  546,  544,
      544,  544,  544,  544,  544,  546,  546,  546,  546,  546,
      546,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  546,  546,  546,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  546,  546,  544,

      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  546,  546,  546,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  546,  546,  546,
      546,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  546,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  546,  546,  546,
      546,  546,  546,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  546,  546,  546,
      546,  546,  546,  544,  544,  544,  544,  544,  544,  544,

      544,  544,  544,  544,  544,  544,  546,  546,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  546,  546,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  546,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  546,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  546,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  544,  544,  544,

      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  546,
      546,  546,  546,  546,  546,  546,  546,  546,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  546,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  546,  546,  546,
      546,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  546,  546,  546,  546,

      546,  546,  546,  546,  546,  546,  546,  546,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  546,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  546,  546,  546,
      546,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  546,  546,  546,  546,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  546,  546,  546,
      546,  546,  546,  546,  546,  546,  546,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  546,  546,  546,  546,  546,  546,  546,

      546,  546,  546,  546,  546,  546,  546,  546,  546,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  546,  546,  546,  546,  546,  546,  546,  546,  546,
      546,  546,  546,  544,  544,  544,  544,  546,  546,  546,
      546,  544,  546,    0,  544,  544
    } ;

static yyconst flex_int16_t yy_nxt[653] =
    {   0,
        6,    7,    8,    9,   10,   10,   10,   10,   10,   10,
       10,   10,   11,   12,    6,    6,   13,    6,    6,    6,
        6,   14,    6,    6,    6,    6,   15,    6,    6,    6,
       16,    6,    6,    6,    6,    6,   17,   18,    6,   19,
       20,   21,   22,   23,    9,   24,   24,   24,   24,   24,
       24,   24,   24,   25,   26,   21,   21,   27,   21,   21,
       21,   21,   28,   21,   21,   21,   21,   29,   21,   21,
       21,   30,   21,   21,   21,   21,   21,   31,   32,   21,
       33,   34,   35,   35,   35,   35,   37,   38,   43,   43,
       43,   43,   44,   44,   44,   44,   44,   44,   44,   44,

       45,   46,   35,   35,   43,   43,   64,   70,   36,   65,
       71,   73,  141,   74,  157,   75,   76,   82,   83,   77,
       96,   97,   78,   79,  142,  101,  158,   80,  174,  102,
       81,   87,  110,   88,  117,   89,   90,  103,  118,   91,
      126,  111,   92,   93,  175,  189,  119,   94,  201,  127,
       95,  204,  214,  221,  217,  240,  205,  262,  202,  218,
      263,  190,  215,  227,  282,  228,  268,  283,  229,  230,
      231,  222,  232,  241,  269,  233,  234,  246,  449,  247,
      273,  450,  248,  249,  250,  288,  251,  293,  274,  252,
      253,  472,  311,  289,  473,  294,  304,  305,  313,  306,

      312,  315,  307,  308,  543,  319,  314,  321,  323,  316,
      309,  343,  310,  320,  345,  322,  324,  336,  337,  344,
      338,  347,  346,  339,  340,  542,  351,  353,  355,  348,
      365,  341,  367,  342,  352,  354,  356,  373,  366,  376,
      368,  378,  400,  374,  377,  394,  375,  379,  380,  395,
      401,  402,  408,  541,  540,  381,  539,  411,  409,  403,
      413,  410,  412,  538,  429,  537,  414,  415,  430,  536,
      535,  534,  533,  532,  416,   42,  531,   42,  530,  529,
      528,  527,  526,  525,  524,  523,  522,  521,  520,  519,
      518,  517,  516,  515,  514,  513,  512,  511,  510,  509,

      508,  507,  506,  505,  504,  503,  502,  501,  500,  499,
      498,  497,  496,  495,  494,  493,  492,  491,  490,  489,
      488,  487,  486,  485,  484,  483,  482,  481,  480,  479,
      478,  477,  476,  475,  474,  471,  470,  469,  468,  467,
      466,  465,  464,  463,  462,  461,  460,  459,  458,  457,
      456,  455,  454,  453,  452,  451,  448,  447,  446,  445,
      444,  443,  442,  441,  440,  439,  438,  437,  436,  435,
      434,  433,  432,  431,  428,  427,  426,  425,  424,  423,
      422,  421,  420,  419,  418,  417,  407,  406,  405,  404,
      399,  398,  397,  396,  393,  392,  391,  390,  389,  388,

      387,  386,  385,  384,  383,  382,  372,  371,  370,  369,
      364,  363,  362,  361,  360,  359,  358,  357,  350,  349,
      335,  334,  333,  332,  331,  330,  329,  328,  327,  326,
      325,  318,  317,  303,  302,  301,  300,  299,  298,  297,
      296,  295,  292,  291,  290,  287,  286,  285,  284,  281,
      280,  279,  278,  277,  276,  275,  272,  271,  270,  267,
      266,  265,  264,  261,  260,  259,  258,  257,  256,  255,
      254,  245,  244,  243,  242,  239,  238,  237,  236,  235,
      226,  225,  224,  223,  220,  219,  216,  213,  212,  211,
      210,  209,  208,  207,  206,  203,  200,  199,  198,  197,

      196,  195,  194,  193,  192,  191,  188,  187,  186,  185,
      184,  183,  182,  181,  180,  179,  178,  177,  176,  173,
      172,  171,  170,  169,  168,  167,  166,  165,  164,  163,
      162,  161,  160,  159,  156,  155,  154,  153,  152,  151,
      150,  149,  148,  147,  146,  145,  144,  143,  140,  139,
      138,  137,  136,  135,  134,  133,  132,  131,  130,  129,
      128,  125,  124,  123,  122,  121,  120,  116,  115,  114,
      113,  112,  109,  108,  107,  106,  105,  104,  100,   99,
       98,   86,   85,   84,   72,   69,   68,   67,   66,   63,
       62,   61,   60,   59,   58,   57,   56,   55,   54,   53,

       52,   51,   50,   49,   48,   47,   41,   40,   39,  544,
        5,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544
    } ;

static yyconst flex_int16_t yy_chk[653] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    3,    3,    3,    3,    3,    3,    3,    3,    3,
        3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
        3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
        3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
        3,    3,    7,    7,    8,    8,   13,   13,   22,   22,
       23,   23,   24,   24,   24,   24,   24,   24,   24,   24,

       27,   27,   35,   35,   43,   43,   54,   59,  545,   54,
       59,   62,  109,   62,  125,   62,   62,   63,   63,   62,
       69,   69,   62,   62,  109,   74,  125,   62,  142,   74,
       62,   68,   81,   68,   88,   68,   68,   74,   88,   68,
       95,   81,   68,   68,  142,  158,   88,   68,  173,   95,
       68,  175,  188,  196,  190,  209,  175,  224,  173,  190,
      224,  158,  188,  201,  243,  201,  230,  243,  201,  201,
      202,  196,  202,  209,  230,  202,  202,  214,  380,  214,
      234,  380,  214,  214,  215,  249,  215,  253,  234,  215,
      215,  415,  265,  249,  415,  253,  264,  264,  266,  264,

      265,  267,  264,  264,  539,  270,  266,  271,  272,  267,
      264,  285,  264,  270,  286,  271,  272,  284,  284,  285,
      284,  287,  286,  284,  284,  535,  290,  291,  292,  287,
      302,  284,  303,  284,  290,  291,  292,  308,  302,  309,
      303,  310,  334,  308,  309,  327,  308,  310,  310,  327,
      334,  335,  340,  531,  529,  310,  524,  341,  340,  335,
      342,  340,  341,  522,  359,  519,  342,  342,  359,  517,
      512,  510,  509,  508,  342,  546,  506,  546,  505,  504,
      502,  501,  500,  498,  497,  496,  495,  493,  492,  490,
      489,  488,  486,  485,  484,  482,  481,  480,  479,  477,

      476,  475,  474,  473,  472,  471,  470,  469,  468,  467,
      466,  465,  463,  462,  461,  454,  453,  452,  451,  450,
      449,  448,  447,  446,  445,  444,  443,  442,  440,  439,
      438,  431,  430,  429,  416,  414,  413,  412,  411,  410,
      409,  408,  407,  406,  405,  404,  403,  402,  401,  400,
      399,  397,  396,  395,  394,  381,  379,  378,  377,  376,
      375,  374,  373,  372,  371,  370,  369,  368,  367,  366,
      365,  364,  362,  360,  356,  355,  354,  353,  352,  351,
      348,  347,  346,  345,  344,  343,  339,  338,  337,  336,
      333,  332,  331,  328,  324,  323,  322,  321,  320,  319,

      316,  315,  314,  313,  312,  311,  307,  306,  305,  304,
      301,  300,  299,  297,  296,  295,  294,  293,  289,  288,
      283,  282,  281,  280,  279,  278,  277,  276,  275,  274,
      273,  269,  268,  263,  262,  261,  260,  259,  258,  257,
      256,  255,  252,  251,  250,  248,  247,  246,  244,  242,
      241,  240,  239,  238,  237,  236,  233,  232,  231,  229,
      228,  227,  225,  223,  222,  221,  220,  219,  218,  217,
      216,  213,  212,  211,  210,  208,  206,  205,  204,  203,
      200,  199,  198,  197,  195,  191,  189,  187,  186,  185,
      184,  181,  180,  179,  176,  174,  172,  171,  170,  169,

      166,  165,  164,  161,  160,  159,  157,  156,  155,  154,
      153,  152,  151,  150,  149,  148,  145,  144,  143,  141,
      140,  139,  138,  137,  136,  135,  134,  133,  132,  131,
      129,  128,  127,  126,  124,  123,  122,  121,  120,  119,
      118,  117,  116,  115,  113,  112,  111,  110,  108,  107,
      106,  105,  104,  103,  102,  101,  100,   99,   98,   97,
       96,   94,   93,   92,   91,   90,   89,   87,   85,   84,
       83,   82,   80,   79,   78,   77,   76,   75,   73,   71,
       70,   67,   65,   64,   61,   58,   57,   56,   55,   53,
       52,   51,   50,   49,   48,   47,   46,   45,   41,   40,

       39,   38,   37,   30,   29,   28,   16,   15,   14,    5,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544,  544,  544,  544,  544,  544,  544,  544,  544,
      544,  544
    } ;

extern int H5LTyy_flex_debug;
int H5LTyy_flex_debug = 0;

static yy_state_type *yy_state_buf=0, *yy_state_ptr=0;
static char *yy_full_match;
static int yy_lp;
#define REJECT \
{ \
*yy_cp = (yy_hold_char); /* undo effects of setting up H5LTyytext */ \
yy_cp = (yy_full_match); /* restore poss. backed-over text */ \
++(yy_lp); \
goto find_rule; \
}

#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET
char *H5LTyytext;
#line 1 "hl/src/H5LTanalyze.l"
/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * Copyright by The HDF Group.                                               *
 * Copyright by the Board of Trustees of the University of Illinois.         *
 * All rights reserved.                                                      *
 *                                                                           *
 * This file is part of HDF5.  The full HDF5 copyright notice, including     *
 * terms governing use, modification, and redistribution, is contained in    *
 * the COPYING file, which can be found at the root of the source code       *
 * distribution tree, or in https://support.hdfgroup.org/ftp/HDF5/releases.  *
 * If you do not have access to either file, you may request a copy from     *
 * <EMAIL>.                                                        *
 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
/* NOTE!
 *
 * If you make any changes to H5LTanalyze.l, please run bin/genparser to
 * recreate the output files.
 */
#line 23 "hl/src/H5LTanalyze.l"
#include <stdlib.h>
#include <string.h>
#include <hdf5.h>
#include "H5LTparse.h"

/* Turn off suggest const attribute warning in gcc */
#if __GNUC__ >= 4 && __GNUC_MINOR__ >=2 
#pragma GCC diagnostic ignored "-Wsuggest-attribute=const"
#endif 

int my_yyinput(char *, int);
#undef YY_INPUT
#define YY_INPUT(b, r, ms) (r=my_yyinput(b, ms))
#define token(x)        (int)x
#define hid(x)          (hid_t)x

#ifdef  YY_BUF_SIZE
#undef  YY_BUF_SIZE
#endif
#define YY_BUF_SIZE        262144    /*Define read buffer to be 256K*/

extern char *myinput;
extern size_t input_len;

#define STACK_SIZE      16

/*variables for compound type*/
struct cmpd_info {
    hid_t       id;
    hbool_t         is_field;
    hbool_t         first_memb;
};
extern struct cmpd_info cmpd_stack[STACK_SIZE];
extern int csindex;

/*variables for array type*/
struct arr_info {
    hsize_t             dims[H5S_MAX_RANK];
    int                 ndim;
    hbool_t             is_dim;
};
extern struct arr_info arr_stack[STACK_SIZE];
extern int asindex;

/*variables for enumerate type*/
extern hbool_t     is_enum;
extern hbool_t     is_enum_memb;

/*variables for string type*/
extern hbool_t is_str_size;

/*variables for opaque type*/
extern hbool_t is_opq_size;
extern hbool_t is_opq_tag;

hbool_t        first_quote = 1;


#line 936 "hl/src/H5LTanalyze.c"

#define INITIAL 0
#define TAG_STRING 1

#ifndef YY_EXTRA_TYPE
#define YY_EXTRA_TYPE void *
#endif

static int yy_init_globals (void );

/* Accessor methods to globals.
   These are made visible to non-reentrant scanners for convenience. */

int H5LTyylex_destroy (void );

int H5LTyyget_debug (void );

void H5LTyyset_debug (int debug_flag  );

YY_EXTRA_TYPE H5LTyyget_extra (void );

void H5LTyyset_extra (YY_EXTRA_TYPE user_defined  );

FILE *H5LTyyget_in (void );

void H5LTyyset_in  (FILE * in_str  );

FILE *H5LTyyget_out (void );

void H5LTyyset_out  (FILE * out_str  );

yy_size_t H5LTyyget_leng (void );

char *H5LTyyget_text (void );

int H5LTyyget_lineno (void );

void H5LTyyset_lineno (int line_number  );

/* Macros after this point can all be overridden by user definitions in
 * section 1.
 */

#ifndef YY_SKIP_YYWRAP
#ifdef __cplusplus
extern "C" int H5LTyywrap (void );
#else
extern int H5LTyywrap (void );
#endif
#endif

    static void yyunput (int c,char *buf_ptr  );
    
#ifndef yytext_ptr
static void yy_flex_strncpy (char *,yyconst char *,int );
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (yyconst char * );
#endif

#ifndef YY_NO_INPUT

#ifdef __cplusplus
static int yyinput (void );
#else
static int input (void );
#endif

#endif

/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* This used to be an fputs(), but since the string might contain NUL's,
 * we now use fwrite().
 */
#define ECHO do { if (fwrite( H5LTyytext, H5LTyyleng, 1, H5LTyyout )) {} } while (0)
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
	if ( YY_CURRENT_BUFFER_LVALUE->yy_is_interactive ) \
		{ \
		int c = '*'; \
		size_t n; \
		for ( n = 0; n < max_size && \
			     (c = getc( H5LTyyin )) != EOF && c != '\n'; ++n ) \
			buf[n] = (char) c; \
		if ( c == '\n' ) \
			buf[n++] = (char) c; \
		if ( c == EOF && ferror( H5LTyyin ) ) \
			YY_FATAL_ERROR( "input in flex scanner failed" ); \
		result = n; \
		} \
	else \
		{ \
		errno=0; \
		while ( (result = fread(buf, 1, max_size, H5LTyyin))==0 && ferror(H5LTyyin)) \
			{ \
			if( errno != EINTR) \
				{ \
				YY_FATAL_ERROR( "input in flex scanner failed" ); \
				break; \
				} \
			errno=0; \
			clearerr(H5LTyyin); \
			} \
		}\
\

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
#define YY_FATAL_ERROR(msg) yy_fatal_error( msg )
#endif

/* end tables serialization structures and prototypes */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1

extern int H5LTyylex (void);

#define YY_DECL int H5LTyylex (void)
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after H5LTyytext and H5LTyyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK break;
#endif

#define YY_RULE_SETUP \
	YY_USER_ACTION

/** The main scanner function which does all the work.
 */
YY_DECL
{
	register yy_state_type yy_current_state;
	register char *yy_cp, *yy_bp;
	register int yy_act;
    
	if ( !(yy_init) )
		{
		(yy_init) = 1;

#ifdef YY_USER_INIT
		YY_USER_INIT;
#endif

        /* Create the reject buffer large enough to save one state per allowed character. */
        if ( ! (yy_state_buf) )
            (yy_state_buf) = (yy_state_type *)H5LTyyalloc(YY_STATE_BUF_SIZE  );
            if ( ! (yy_state_buf) )
                YY_FATAL_ERROR( "out of dynamic memory in H5LTyylex()" );

		if ( ! (yy_start) )
			(yy_start) = 1;	/* first start state */

		if ( ! H5LTyyin )
			H5LTyyin = stdin;

		if ( ! H5LTyyout )
			H5LTyyout = stdout;

		if ( ! YY_CURRENT_BUFFER ) {
			H5LTyyensure_buffer_stack ();
			YY_CURRENT_BUFFER_LVALUE =
				H5LTyy_create_buffer(H5LTyyin,YY_BUF_SIZE );
		}

		H5LTyy_load_buffer_state( );
		}

	{
#line 84 "hl/src/H5LTanalyze.l"


#line 1152 "hl/src/H5LTanalyze.c"

	while ( 1 )		/* loops until end-of-file is reached */
		{
		yy_cp = (yy_c_buf_p);

		/* Support of H5LTyytext. */
		*yy_cp = (yy_hold_char);

		/* yy_bp points to the position in yy_ch_buf of the start of
		 * the current run.
		 */
		yy_bp = yy_cp;

		yy_current_state = (yy_start);

		(yy_state_ptr) = (yy_state_buf);
		*(yy_state_ptr)++ = yy_current_state;

yy_match:
		do
			{
			register YY_CHAR yy_c = yy_ec[YY_SC_TO_UI(*yy_cp)] ;
			while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
				{
				yy_current_state = (int) yy_def[yy_current_state];
				if ( yy_current_state >= 545 )
					yy_c = yy_meta[(unsigned int) yy_c];
				}
			yy_current_state = yy_nxt[yy_base[yy_current_state] + (unsigned int) yy_c];
			*(yy_state_ptr)++ = yy_current_state;
			++yy_cp;
			}
		while ( yy_base[yy_current_state] != 611 );

yy_find_action:
		yy_current_state = *--(yy_state_ptr);
		(yy_lp) = yy_accept[yy_current_state];
find_rule: /* we branch to this label when backing up */
		for ( ; ; ) /* until we find what rule we matched */
			{
			if ( (yy_lp) && (yy_lp) < yy_accept[yy_current_state + 1] )
				{
				yy_act = yy_acclist[(yy_lp)];
					{
					(yy_full_match) = yy_cp;
					break;
					}
				}
			--yy_cp;
			yy_current_state = *--(yy_state_ptr);
			(yy_lp) = yy_accept[yy_current_state];
			}

		YY_DO_BEFORE_ACTION;

do_action:	/* This label is used only to access EOF actions. */

		switch ( yy_act )
	{ /* beginning of action switch */
case 1:
YY_RULE_SETUP
#line 86 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_I8BE_TOKEN);}
	YY_BREAK
case 2:
YY_RULE_SETUP
#line 87 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_I8LE_TOKEN);}
	YY_BREAK
case 3:
YY_RULE_SETUP
#line 88 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_I16BE_TOKEN);}
	YY_BREAK
case 4:
YY_RULE_SETUP
#line 89 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_I16LE_TOKEN);}
	YY_BREAK
case 5:
YY_RULE_SETUP
#line 90 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_I32BE_TOKEN);}
	YY_BREAK
case 6:
YY_RULE_SETUP
#line 91 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_I32LE_TOKEN);}
	YY_BREAK
case 7:
YY_RULE_SETUP
#line 92 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_I64BE_TOKEN);}
	YY_BREAK
case 8:
YY_RULE_SETUP
#line 93 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_I64LE_TOKEN);}
	YY_BREAK
case 9:
YY_RULE_SETUP
#line 95 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_U8BE_TOKEN);}
	YY_BREAK
case 10:
YY_RULE_SETUP
#line 96 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_U8LE_TOKEN);}
	YY_BREAK
case 11:
YY_RULE_SETUP
#line 97 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_U16BE_TOKEN);}
	YY_BREAK
case 12:
YY_RULE_SETUP
#line 98 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_U16LE_TOKEN);}
	YY_BREAK
case 13:
YY_RULE_SETUP
#line 99 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_U32BE_TOKEN);}
	YY_BREAK
case 14:
YY_RULE_SETUP
#line 100 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_U32LE_TOKEN);}
	YY_BREAK
case 15:
YY_RULE_SETUP
#line 101 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_U64BE_TOKEN);}
	YY_BREAK
case 16:
YY_RULE_SETUP
#line 102 "hl/src/H5LTanalyze.l"
{return hid(H5T_STD_U64LE_TOKEN);}
	YY_BREAK
case 17:
YY_RULE_SETUP
#line 104 "hl/src/H5LTanalyze.l"
{return hid(H5T_NATIVE_CHAR_TOKEN);}
	YY_BREAK
case 18:
YY_RULE_SETUP
#line 105 "hl/src/H5LTanalyze.l"
{return hid(H5T_NATIVE_SCHAR_TOKEN);}
	YY_BREAK
case 19:
YY_RULE_SETUP
#line 106 "hl/src/H5LTanalyze.l"
{return hid(H5T_NATIVE_UCHAR_TOKEN);}
	YY_BREAK
case 20:
YY_RULE_SETUP
#line 107 "hl/src/H5LTanalyze.l"
{return hid(H5T_NATIVE_SHORT_TOKEN);}
	YY_BREAK
case 21:
YY_RULE_SETUP
#line 108 "hl/src/H5LTanalyze.l"
{return hid(H5T_NATIVE_USHORT_TOKEN);}
	YY_BREAK
case 22:
YY_RULE_SETUP
#line 109 "hl/src/H5LTanalyze.l"
{return hid(H5T_NATIVE_INT_TOKEN);}
	YY_BREAK
case 23:
YY_RULE_SETUP
#line 110 "hl/src/H5LTanalyze.l"
{return hid(H5T_NATIVE_UINT_TOKEN);}
	YY_BREAK
case 24:
YY_RULE_SETUP
#line 111 "hl/src/H5LTanalyze.l"
{return hid(H5T_NATIVE_LONG_TOKEN);}
	YY_BREAK
case 25:
YY_RULE_SETUP
#line 112 "hl/src/H5LTanalyze.l"
{return hid(H5T_NATIVE_ULONG_TOKEN);}
	YY_BREAK
case 26:
YY_RULE_SETUP
#line 113 "hl/src/H5LTanalyze.l"
{return hid(H5T_NATIVE_LLONG_TOKEN);}
	YY_BREAK
case 27:
YY_RULE_SETUP
#line 114 "hl/src/H5LTanalyze.l"
{return hid(H5T_NATIVE_ULLONG_TOKEN);}
	YY_BREAK
case 28:
YY_RULE_SETUP
#line 116 "hl/src/H5LTanalyze.l"
{return hid(H5T_IEEE_F32BE_TOKEN);}
	YY_BREAK
case 29:
YY_RULE_SETUP
#line 117 "hl/src/H5LTanalyze.l"
{return hid(H5T_IEEE_F32LE_TOKEN);}
	YY_BREAK
case 30:
YY_RULE_SETUP
#line 118 "hl/src/H5LTanalyze.l"
{return hid(H5T_IEEE_F64BE_TOKEN);}
	YY_BREAK
case 31:
YY_RULE_SETUP
#line 119 "hl/src/H5LTanalyze.l"
{return hid(H5T_IEEE_F64LE_TOKEN);}
	YY_BREAK
case 32:
YY_RULE_SETUP
#line 120 "hl/src/H5LTanalyze.l"
{return hid(H5T_NATIVE_FLOAT_TOKEN);}
	YY_BREAK
case 33:
YY_RULE_SETUP
#line 121 "hl/src/H5LTanalyze.l"
{return hid(H5T_NATIVE_DOUBLE_TOKEN);}
	YY_BREAK
case 34:
YY_RULE_SETUP
#line 122 "hl/src/H5LTanalyze.l"
{return hid(H5T_NATIVE_LDOUBLE_TOKEN);}
	YY_BREAK
case 35:
YY_RULE_SETUP
#line 124 "hl/src/H5LTanalyze.l"
{return token(H5T_STRING_TOKEN);}
	YY_BREAK
case 36:
YY_RULE_SETUP
#line 125 "hl/src/H5LTanalyze.l"
{return token(STRSIZE_TOKEN);}
	YY_BREAK
case 37:
YY_RULE_SETUP
#line 126 "hl/src/H5LTanalyze.l"
{return token(STRPAD_TOKEN);}
	YY_BREAK
case 38:
YY_RULE_SETUP
#line 127 "hl/src/H5LTanalyze.l"
{return token(CSET_TOKEN);}
	YY_BREAK
case 39:
YY_RULE_SETUP
#line 128 "hl/src/H5LTanalyze.l"
{return token(CTYPE_TOKEN);}
	YY_BREAK
case 40:
YY_RULE_SETUP
#line 129 "hl/src/H5LTanalyze.l"
{return token(H5T_STR_NULLTERM_TOKEN);} 
	YY_BREAK
case 41:
YY_RULE_SETUP
#line 130 "hl/src/H5LTanalyze.l"
{return token(H5T_STR_NULLPAD_TOKEN);} 
	YY_BREAK
case 42:
YY_RULE_SETUP
#line 131 "hl/src/H5LTanalyze.l"
{return token(H5T_STR_SPACEPAD_TOKEN);} 
	YY_BREAK
case 43:
YY_RULE_SETUP
#line 132 "hl/src/H5LTanalyze.l"
{return token(H5T_CSET_ASCII_TOKEN);}
	YY_BREAK
case 44:
YY_RULE_SETUP
#line 133 "hl/src/H5LTanalyze.l"
{return token(H5T_CSET_UTF8_TOKEN);}
	YY_BREAK
case 45:
YY_RULE_SETUP
#line 134 "hl/src/H5LTanalyze.l"
{return token(H5T_C_S1_TOKEN);}
	YY_BREAK
case 46:
YY_RULE_SETUP
#line 135 "hl/src/H5LTanalyze.l"
{return token(H5T_FORTRAN_S1_TOKEN);}
	YY_BREAK
case 47:
YY_RULE_SETUP
#line 136 "hl/src/H5LTanalyze.l"
{return token(H5T_VARIABLE_TOKEN);}
	YY_BREAK
case 48:
YY_RULE_SETUP
#line 138 "hl/src/H5LTanalyze.l"
{return token(H5T_COMPOUND_TOKEN);}
	YY_BREAK
case 49:
YY_RULE_SETUP
#line 139 "hl/src/H5LTanalyze.l"
{return token(H5T_ENUM_TOKEN);}
	YY_BREAK
case 50:
YY_RULE_SETUP
#line 140 "hl/src/H5LTanalyze.l"
{return token(H5T_ARRAY_TOKEN);}
	YY_BREAK
case 51:
YY_RULE_SETUP
#line 141 "hl/src/H5LTanalyze.l"
{return token(H5T_VLEN_TOKEN);}
	YY_BREAK
case 52:
YY_RULE_SETUP
#line 143 "hl/src/H5LTanalyze.l"
{return token(H5T_OPAQUE_TOKEN);}
	YY_BREAK
case 53:
YY_RULE_SETUP
#line 144 "hl/src/H5LTanalyze.l"
{return token(OPQ_SIZE_TOKEN);}
	YY_BREAK
case 54:
YY_RULE_SETUP
#line 145 "hl/src/H5LTanalyze.l"
{return token(OPQ_TAG_TOKEN);}
	YY_BREAK
case 55:
YY_RULE_SETUP
#line 147 "hl/src/H5LTanalyze.l"
{    
                        if( is_str_size || (is_enum && is_enum_memb) || 
                            is_opq_size || (asindex>-1 && arr_stack[asindex].is_dim) ||
                            (csindex>-1 && cmpd_stack[csindex].is_field) ) {
                            H5LTyylval.ival = atoi(H5LTyytext);
                            return NUMBER; 
                        } else
                            REJECT;
                 }
	YY_BREAK
case 56:
YY_RULE_SETUP
#line 157 "hl/src/H5LTanalyze.l"
{
                    /*if it's first quote, and is a compound field name or an enum symbol*/
                    if((is_opq_tag || is_enum || (csindex>-1 && cmpd_stack[csindex].is_field)) 
                        && first_quote) {
                        first_quote = 0;
                        BEGIN TAG_STRING;
                    } else /*if it's second quote*/
                        first_quote = 1;
                    return token('"');
                 }
	YY_BREAK
case 57:
/* rule 57 can match eol */
YY_RULE_SETUP
#line 167 "hl/src/H5LTanalyze.l"
{
#ifdef H5_HAVE_WIN32_API
                    H5LTyylval.sval = _strdup(H5LTyytext);
#else /* H5_HAVE_WIN32_API */
                    H5LTyylval.sval = strdup(H5LTyytext);
#endif  /* H5_HAVE_WIN32_API */
                    BEGIN INITIAL;
                    return STRING;
                 }
	YY_BREAK
case 58:
YY_RULE_SETUP
#line 177 "hl/src/H5LTanalyze.l"
{return token('{');}
	YY_BREAK
case 59:
YY_RULE_SETUP
#line 178 "hl/src/H5LTanalyze.l"
{return token('}');}
	YY_BREAK
case 60:
YY_RULE_SETUP
#line 179 "hl/src/H5LTanalyze.l"
{return token('[');}
	YY_BREAK
case 61:
YY_RULE_SETUP
#line 180 "hl/src/H5LTanalyze.l"
{return token(']');}
	YY_BREAK
case 62:
YY_RULE_SETUP
#line 181 "hl/src/H5LTanalyze.l"
{return token(':');}
	YY_BREAK
case 63:
YY_RULE_SETUP
#line 182 "hl/src/H5LTanalyze.l"
{return token(';');}
	YY_BREAK
case 64:
/* rule 64 can match eol */
YY_RULE_SETUP
#line 183 "hl/src/H5LTanalyze.l"
;
	YY_BREAK
case 65:
/* rule 65 can match eol */
YY_RULE_SETUP
#line 184 "hl/src/H5LTanalyze.l"
{ return 0; }
	YY_BREAK
case 66:
YY_RULE_SETUP
#line 186 "hl/src/H5LTanalyze.l"
ECHO;
	YY_BREAK
#line 1570 "hl/src/H5LTanalyze.c"
			case YY_STATE_EOF(INITIAL):
			case YY_STATE_EOF(TAG_STRING):
				yyterminate();

	case YY_END_OF_BUFFER:
		{
		/* Amount of text matched not including the EOB char. */
		int yy_amount_of_matched_text = (int) (yy_cp - (yytext_ptr)) - 1;

		/* Undo the effects of YY_DO_BEFORE_ACTION. */
		*yy_cp = (yy_hold_char);
		YY_RESTORE_YY_MORE_OFFSET

		if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW )
			{
			/* We're scanning a new file or input source.  It's
			 * possible that this happened because the user
			 * just pointed H5LTyyin at a new source and called
			 * H5LTyylex().  If so, then we have to assure
			 * consistency between YY_CURRENT_BUFFER and our
			 * globals.  Here is the right place to do so, because
			 * this is the first action (other than possibly a
			 * back-up) that will match for the new input source.
			 */
			(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
			YY_CURRENT_BUFFER_LVALUE->yy_input_file = H5LTyyin;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
			}

		/* Note that here we test for yy_c_buf_p "<=" to the position
		 * of the first EOB in the buffer, since yy_c_buf_p will
		 * already have been incremented past the NUL character
		 * (since all states make transitions on EOB to the
		 * end-of-buffer state).  Contrast this with the test
		 * in input().
		 */
		if ( (yy_c_buf_p) <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			{ /* This was really a NUL. */
			yy_state_type yy_next_state;

			(yy_c_buf_p) = (yytext_ptr) + yy_amount_of_matched_text;

			yy_current_state = yy_get_previous_state(  );

			/* Okay, we're now positioned to make the NUL
			 * transition.  We couldn't have
			 * yy_get_previous_state() go ahead and do it
			 * for us because it doesn't know how to deal
			 * with the possibility of jamming (and we don't
			 * want to build jamming into it because then it
			 * will run more slowly).
			 */

			yy_next_state = yy_try_NUL_trans( yy_current_state );

			yy_bp = (yytext_ptr) + YY_MORE_ADJ;

			if ( yy_next_state )
				{
				/* Consume the NUL. */
				yy_cp = ++(yy_c_buf_p);
				yy_current_state = yy_next_state;
				goto yy_match;
				}

			else
				{
				yy_cp = (yy_c_buf_p);
				goto yy_find_action;
				}
			}

		else switch ( yy_get_next_buffer(  ) )
			{
			case EOB_ACT_END_OF_FILE:
				{
				(yy_did_buffer_switch_on_eof) = 0;

				if ( H5LTyywrap( ) )
					{
					/* Note: because we've taken care in
					 * yy_get_next_buffer() to have set up
					 * H5LTyytext, we can now set up
					 * yy_c_buf_p so that if some total
					 * hoser (like flex itself) wants to
					 * call the scanner after we return the
					 * YY_NULL, it'll still work - another
					 * YY_NULL will get returned.
					 */
					(yy_c_buf_p) = (yytext_ptr) + YY_MORE_ADJ;

					yy_act = YY_STATE_EOF(YY_START);
					goto do_action;
					}

				else
					{
					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
					}
				break;
				}

			case EOB_ACT_CONTINUE_SCAN:
				(yy_c_buf_p) =
					(yytext_ptr) + yy_amount_of_matched_text;

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_match;

			case EOB_ACT_LAST_MATCH:
				(yy_c_buf_p) =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)];

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_find_action;
			}
		break;
		}

	default:
		YY_FATAL_ERROR(
			"fatal flex scanner internal error--no action found" );
	} /* end of action switch */
		} /* end of scanning one token */
	} /* end of user's declarations */
} /* end of H5LTyylex */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *	EOB_ACT_LAST_MATCH -
 *	EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *	EOB_ACT_END_OF_FILE - end of file
 */
static int yy_get_next_buffer (void)
{
    	register char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
	register char *source = (yytext_ptr);
	register int number_to_move, i;
	int ret_val;

	if ( (yy_c_buf_p) > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] )
		YY_FATAL_ERROR(
		"fatal flex scanner internal error--end of buffer missed" );

	if ( YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0 )
		{ /* Don't try to fill the buffer, so this is an EOF. */
		if ( (yy_c_buf_p) - (yytext_ptr) - YY_MORE_ADJ == 1 )
			{
			/* We matched a single character, the EOB, so
			 * treat this as a final EOF.
			 */
			return EOB_ACT_END_OF_FILE;
			}

		else
			{
			/* We matched some text prior to the EOB, first
			 * process it.
			 */
			return EOB_ACT_LAST_MATCH;
			}
		}

	/* Try to read more data. */

	/* First move last chars to start of buffer. */
	number_to_move = (int) ((yy_c_buf_p) - (yytext_ptr)) - 1;

	for ( i = 0; i < number_to_move; ++i )
		*(dest++) = *(source++);

	if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING )
		/* don't do the read, it's not guaranteed to return an EOF,
		 * just force an EOF
		 */
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars) = 0;

	else
		{
			yy_size_t num_to_read =
			YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

		while ( num_to_read <= 0 )
			{ /* Not enough room in the buffer - grow it. */

			YY_FATAL_ERROR(
"input buffer overflow, can't enlarge buffer because scanner uses REJECT" );

			}

		if ( num_to_read > YY_READ_BUF_SIZE )
			num_to_read = YY_READ_BUF_SIZE;

		/* Read in more data. */
		YY_INPUT( (&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
			(yy_n_chars), num_to_read );

		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	if ( (yy_n_chars) == 0 )
		{
		if ( number_to_move == YY_MORE_ADJ )
			{
			ret_val = EOB_ACT_END_OF_FILE;
			H5LTyyrestart(H5LTyyin  );
			}

		else
			{
			ret_val = EOB_ACT_LAST_MATCH;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
				YY_BUFFER_EOF_PENDING;
			}
		}

	else
		ret_val = EOB_ACT_CONTINUE_SCAN;

	if ((yy_size_t) ((yy_n_chars) + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
		/* Extend the array by 50%, plus the number we really need. */
		yy_size_t new_size = (yy_n_chars) + number_to_move + ((yy_n_chars) >> 1);
		YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) H5LTyyrealloc((void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf,new_size  );
		if ( ! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			YY_FATAL_ERROR( "out of dynamic memory in yy_get_next_buffer()" );
	}

	(yy_n_chars) += number_to_move;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] = YY_END_OF_BUFFER_CHAR;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] = YY_END_OF_BUFFER_CHAR;

	(yytext_ptr) = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

	return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

    static yy_state_type yy_get_previous_state (void)
{
	register yy_state_type yy_current_state;
	register char *yy_cp;
    
	yy_current_state = (yy_start);

	(yy_state_ptr) = (yy_state_buf);
	*(yy_state_ptr)++ = yy_current_state;

	for ( yy_cp = (yytext_ptr) + YY_MORE_ADJ; yy_cp < (yy_c_buf_p); ++yy_cp )
		{
		register YY_CHAR yy_c = (*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1);
		while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
			{
			yy_current_state = (int) yy_def[yy_current_state];
			if ( yy_current_state >= 545 )
				yy_c = yy_meta[(unsigned int) yy_c];
			}
		yy_current_state = yy_nxt[yy_base[yy_current_state] + (unsigned int) yy_c];
		*(yy_state_ptr)++ = yy_current_state;
		}

	return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *	next_state = yy_try_NUL_trans( current_state );
 */
    static yy_state_type yy_try_NUL_trans  (yy_state_type yy_current_state )
{
	register int yy_is_jam;
    
	register YY_CHAR yy_c = 1;
	while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
		{
		yy_current_state = (int) yy_def[yy_current_state];
		if ( yy_current_state >= 545 )
			yy_c = yy_meta[(unsigned int) yy_c];
		}
	yy_current_state = yy_nxt[yy_base[yy_current_state] + (unsigned int) yy_c];
	yy_is_jam = (yy_current_state == 544);
	if ( ! yy_is_jam )
		*(yy_state_ptr)++ = yy_current_state;

		return yy_is_jam ? 0 : yy_current_state;
}

    static void yyunput (int c, register char * yy_bp )
{
	register char *yy_cp;
    
    yy_cp = (yy_c_buf_p);

	/* undo effects of setting up H5LTyytext */
	*yy_cp = (yy_hold_char);

	if ( yy_cp < YY_CURRENT_BUFFER_LVALUE->yy_ch_buf + 2 )
		{ /* need to shift things up to make room */
		/* +2 for EOB chars. */
		register yy_size_t number_to_move = (yy_n_chars) + 2;
		register char *dest = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[
					YY_CURRENT_BUFFER_LVALUE->yy_buf_size + 2];
		register char *source =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move];

		while ( source > YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			*--dest = *--source;

		yy_cp += (int) (dest - source);
		yy_bp += (int) (dest - source);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars =
			(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_buf_size;

		if ( yy_cp < YY_CURRENT_BUFFER_LVALUE->yy_ch_buf + 2 )
			YY_FATAL_ERROR( "flex scanner push-back overflow" );
		}

	*--yy_cp = (char) c;

	(yytext_ptr) = yy_bp;
	(yy_hold_char) = *yy_cp;
	(yy_c_buf_p) = yy_cp;
}

#ifndef YY_NO_INPUT
#ifdef __cplusplus
    static int yyinput (void)
#else
    static int input  (void)
#endif

{
	int c;
    
	*(yy_c_buf_p) = (yy_hold_char);

	if ( *(yy_c_buf_p) == YY_END_OF_BUFFER_CHAR )
		{
		/* yy_c_buf_p now points to the character we want to return.
		 * If this occurs *before* the EOB characters, then it's a
		 * valid NUL; if not, then we've hit the end of the buffer.
		 */
		if ( (yy_c_buf_p) < &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			/* This was really a NUL. */
			*(yy_c_buf_p) = '\0';

		else
			{ /* need more input */
			yy_size_t offset = (yy_c_buf_p) - (yytext_ptr);
			++(yy_c_buf_p);

			switch ( yy_get_next_buffer(  ) )
				{
				case EOB_ACT_LAST_MATCH:
					/* This happens because yy_g_n_b()
					 * sees that we've accumulated a
					 * token and flags that we need to
					 * try matching the token before
					 * proceeding.  But for input(),
					 * there's no matching to consider.
					 * So convert the EOB_ACT_LAST_MATCH
					 * to EOB_ACT_END_OF_FILE.
					 */

					/* Reset buffer status. */
					H5LTyyrestart(H5LTyyin );

					/*FALLTHROUGH*/

				case EOB_ACT_END_OF_FILE:
					{
					if ( H5LTyywrap( ) )
						return EOF;

					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
#ifdef __cplusplus
					return yyinput();
#else
					return input();
#endif
					}

				case EOB_ACT_CONTINUE_SCAN:
					(yy_c_buf_p) = (yytext_ptr) + offset;
					break;
				}
			}
		}

	c = *(unsigned char *) (yy_c_buf_p);	/* cast for 8-bit char's */
	*(yy_c_buf_p) = '\0';	/* preserve H5LTyytext */
	(yy_hold_char) = *++(yy_c_buf_p);

	return c;
}
#endif	/* ifndef YY_NO_INPUT */

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 * 
 * @note This function does not reset the start condition to @c INITIAL .
 */
    void H5LTyyrestart  (FILE * input_file )
{
    
	if ( ! YY_CURRENT_BUFFER ){
        H5LTyyensure_buffer_stack ();
		YY_CURRENT_BUFFER_LVALUE =
            H5LTyy_create_buffer(H5LTyyin,YY_BUF_SIZE );
	}

	H5LTyy_init_buffer(YY_CURRENT_BUFFER,input_file );
	H5LTyy_load_buffer_state( );
}

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 * 
 */
    void H5LTyy_switch_to_buffer  (YY_BUFFER_STATE  new_buffer )
{
    
	/* TODO. We should be able to replace this entire function body
	 * with
	 *		H5LTyypop_buffer_state();
	 *		H5LTyypush_buffer_state(new_buffer);
     */
	H5LTyyensure_buffer_stack ();
	if ( YY_CURRENT_BUFFER == new_buffer )
		return;

	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	YY_CURRENT_BUFFER_LVALUE = new_buffer;
	H5LTyy_load_buffer_state( );

	/* We don't actually know whether we did this switch during
	 * EOF (H5LTyywrap()) processing, but the only time this flag
	 * is looked at is after H5LTyywrap() is called, so it's safe
	 * to go ahead and always set it.
	 */
	(yy_did_buffer_switch_on_eof) = 1;
}

static void H5LTyy_load_buffer_state  (void)
{
    	(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
	(yytext_ptr) = (yy_c_buf_p) = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
	H5LTyyin = YY_CURRENT_BUFFER_LVALUE->yy_input_file;
	(yy_hold_char) = *(yy_c_buf_p);
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * 
 * @return the allocated buffer state.
 */
    YY_BUFFER_STATE H5LTyy_create_buffer  (FILE * file, int  size )
{
	YY_BUFFER_STATE b;
    
	b = (YY_BUFFER_STATE) H5LTyyalloc(sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in H5LTyy_create_buffer()" );

	b->yy_buf_size = size;

	/* yy_ch_buf has to be 2 characters longer than the size given because
	 * we need to put in 2 end-of-buffer characters.
	 */
	b->yy_ch_buf = (char *) H5LTyyalloc(b->yy_buf_size + 2  );
	if ( ! b->yy_ch_buf )
		YY_FATAL_ERROR( "out of dynamic memory in H5LTyy_create_buffer()" );

	b->yy_is_our_buffer = 1;

	H5LTyy_init_buffer(b,file );

	return b;
}

/** Destroy the buffer.
 * @param b a buffer created with H5LTyy_create_buffer()
 * 
 */
    void H5LTyy_delete_buffer (YY_BUFFER_STATE  b )
{
    
	if ( ! b )
		return;

	if ( b == YY_CURRENT_BUFFER ) /* Not sure if we should pop here. */
		YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) 0;

	if ( b->yy_is_our_buffer )
		H5LTyyfree((void *) b->yy_ch_buf  );

	H5LTyyfree((void *) b  );
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a H5LTyyrestart() or at EOF.
 */
    static void H5LTyy_init_buffer  (YY_BUFFER_STATE  b, FILE * file )

{
	int oerrno = errno;
    
	H5LTyy_flush_buffer(b );

	b->yy_input_file = file;
	b->yy_fill_buffer = 1;

    /* If b is the current buffer, then H5LTyy_init_buffer was _probably_
     * called from H5LTyyrestart() or through yy_get_next_buffer.
     * In that case, we don't want to reset the lineno or column.
     */
    if (b != YY_CURRENT_BUFFER){
        b->yy_bs_lineno = 1;
        b->yy_bs_column = 0;
    }

        b->yy_is_interactive = file ? (isatty( fileno(file) ) > 0) : 0;
    
	errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 * 
 */
    void H5LTyy_flush_buffer (YY_BUFFER_STATE  b )
{
    	if ( ! b )
		return;

	b->yy_n_chars = 0;

	/* We always need two end-of-buffer characters.  The first causes
	 * a transition to the end-of-buffer state.  The second causes
	 * a jam in that state.
	 */
	b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
	b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

	b->yy_buf_pos = &b->yy_ch_buf[0];

	b->yy_at_bol = 1;
	b->yy_buffer_status = YY_BUFFER_NEW;

	if ( b == YY_CURRENT_BUFFER )
		H5LTyy_load_buffer_state( );
}

/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *  
 */
void H5LTyypush_buffer_state (YY_BUFFER_STATE new_buffer )
{
    	if (new_buffer == NULL)
		return;

	H5LTyyensure_buffer_stack();

	/* This block is copied from H5LTyy_switch_to_buffer. */
	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	/* Only push if top exists. Otherwise, replace top. */
	if (YY_CURRENT_BUFFER)
		(yy_buffer_stack_top)++;
	YY_CURRENT_BUFFER_LVALUE = new_buffer;

	/* copied from H5LTyy_switch_to_buffer. */
	H5LTyy_load_buffer_state( );
	(yy_did_buffer_switch_on_eof) = 1;
}

/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *  
 */
void H5LTyypop_buffer_state (void)
{
    	if (!YY_CURRENT_BUFFER)
		return;

	H5LTyy_delete_buffer(YY_CURRENT_BUFFER );
	YY_CURRENT_BUFFER_LVALUE = NULL;
	if ((yy_buffer_stack_top) > 0)
		--(yy_buffer_stack_top);

	if (YY_CURRENT_BUFFER) {
		H5LTyy_load_buffer_state( );
		(yy_did_buffer_switch_on_eof) = 1;
	}
}

/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
static void H5LTyyensure_buffer_stack (void)
{
	yy_size_t num_to_alloc;
    
	if (!(yy_buffer_stack)) {

		/* First allocation is just for 2 elements, since we don't know if this
		 * scanner will even need a stack. We use 2 instead of 1 to avoid an
		 * immediate realloc on the next call.
         */
		num_to_alloc = 1;
		(yy_buffer_stack) = (struct yy_buffer_state**)H5LTyyalloc
								(num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in H5LTyyensure_buffer_stack()" );
								  
		memset((yy_buffer_stack), 0, num_to_alloc * sizeof(struct yy_buffer_state*));
				
		(yy_buffer_stack_max) = num_to_alloc;
		(yy_buffer_stack_top) = 0;
		return;
	}

	if ((yy_buffer_stack_top) >= ((yy_buffer_stack_max)) - 1){

		/* Increase the buffer to prepare for a possible push. */
		int grow_size = 8 /* arbitrary grow size */;

		num_to_alloc = (yy_buffer_stack_max) + grow_size;
		(yy_buffer_stack) = (struct yy_buffer_state**)H5LTyyrealloc
								((yy_buffer_stack),
								num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in H5LTyyensure_buffer_stack()" );

		/* zero only the new slots.*/
		memset((yy_buffer_stack) + (yy_buffer_stack_max), 0, grow_size * sizeof(struct yy_buffer_state*));
		(yy_buffer_stack_max) = num_to_alloc;
	}
}

/** Setup the input buffer state to scan directly from a user-specified character buffer.
 * @param base the character buffer
 * @param size the size in bytes of the character buffer
 * 
 * @return the newly allocated buffer state object. 
 */
YY_BUFFER_STATE H5LTyy_scan_buffer  (char * base, yy_size_t  size )
{
	YY_BUFFER_STATE b;
    
	if ( size < 2 ||
	     base[size-2] != YY_END_OF_BUFFER_CHAR ||
	     base[size-1] != YY_END_OF_BUFFER_CHAR )
		/* They forgot to leave room for the EOB's. */
		return 0;

	b = (YY_BUFFER_STATE) H5LTyyalloc(sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in H5LTyy_scan_buffer()" );

	b->yy_buf_size = size - 2;	/* "- 2" to take care of EOB's */
	b->yy_buf_pos = b->yy_ch_buf = base;
	b->yy_is_our_buffer = 0;
	b->yy_input_file = 0;
	b->yy_n_chars = b->yy_buf_size;
	b->yy_is_interactive = 0;
	b->yy_at_bol = 1;
	b->yy_fill_buffer = 0;
	b->yy_buffer_status = YY_BUFFER_NEW;

	H5LTyy_switch_to_buffer(b  );

	return b;
}

/** Setup the input buffer state to scan a string. The next call to H5LTyylex() will
 * scan from a @e copy of @a str.
 * @param yystr a NUL-terminated string to scan
 * 
 * @return the newly allocated buffer state object.
 * @note If you want to scan bytes that may contain NUL values, then use
 *       H5LTyy_scan_bytes() instead.
 */
YY_BUFFER_STATE H5LTyy_scan_string (yyconst char * yystr )
{
    
	return H5LTyy_scan_bytes(yystr,strlen(yystr) );
}

/** Setup the input buffer state to scan the given bytes. The next call to H5LTyylex() will
 * scan from a @e copy of @a bytes.
 * @param yybytes the byte buffer to scan
 * @param _yybytes_len the number of bytes in the buffer pointed to by @a bytes.
 * 
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE H5LTyy_scan_bytes  (yyconst char * yybytes, yy_size_t  _yybytes_len )
{
	YY_BUFFER_STATE b;
	char *buf;
	yy_size_t n;
	yy_size_t i;
    
	/* Get memory for full buffer, including space for trailing EOB's. */
	n = _yybytes_len + 2;
	buf = (char *) H5LTyyalloc(n  );
	if ( ! buf )
		YY_FATAL_ERROR( "out of dynamic memory in H5LTyy_scan_bytes()" );

	for ( i = 0; i < _yybytes_len; ++i )
		buf[i] = yybytes[i];

	buf[_yybytes_len] = buf[_yybytes_len+1] = YY_END_OF_BUFFER_CHAR;

	b = H5LTyy_scan_buffer(buf,n );
	if ( ! b )
		YY_FATAL_ERROR( "bad buffer in H5LTyy_scan_bytes()" );

	/* It's okay to grow etc. this buffer, and we should throw it
	 * away when we're done.
	 */
	b->yy_is_our_buffer = 1;

	return b;
}

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

static void yy_fatal_error (yyconst char* msg )
{
    	(void) fprintf( stderr, "%s\n", msg );
	exit( YY_EXIT_FAILURE );
}

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up H5LTyytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		H5LTyytext[H5LTyyleng] = (yy_hold_char); \
		(yy_c_buf_p) = H5LTyytext + yyless_macro_arg; \
		(yy_hold_char) = *(yy_c_buf_p); \
		*(yy_c_buf_p) = '\0'; \
		H5LTyyleng = yyless_macro_arg; \
		} \
	while ( 0 )

/* Accessor  methods (get/set functions) to struct members. */

/** Get the current line number.
 * 
 */
int H5LTyyget_lineno  (void)
{
        
    return H5LTyylineno;
}

/** Get the input stream.
 * 
 */
FILE *H5LTyyget_in  (void)
{
        return H5LTyyin;
}

/** Get the output stream.
 * 
 */
FILE *H5LTyyget_out  (void)
{
        return H5LTyyout;
}

/** Get the length of the current token.
 * 
 */
yy_size_t H5LTyyget_leng  (void)
{
        return H5LTyyleng;
}

/** Get the current token.
 * 
 */

char *H5LTyyget_text  (void)
{
        return H5LTyytext;
}

/** Set the current line number.
 * @param line_number
 * 
 */
void H5LTyyset_lineno (int  line_number )
{
    
    H5LTyylineno = line_number;
}

/** Set the input stream. This does not discard the current
 * input buffer.
 * @param in_str A readable stream.
 * 
 * @see H5LTyy_switch_to_buffer
 */
void H5LTyyset_in (FILE *  in_str )
{
        H5LTyyin = in_str ;
}

void H5LTyyset_out (FILE *  out_str )
{
        H5LTyyout = out_str ;
}

int H5LTyyget_debug  (void)
{
        return H5LTyy_flex_debug;
}

void H5LTyyset_debug (int  bdebug )
{
        H5LTyy_flex_debug = bdebug ;
}

static int yy_init_globals (void)
{
        /* Initialization is the same as for the non-reentrant scanner.
     * This function is called from H5LTyylex_destroy(), so don't allocate here.
     */

    (yy_buffer_stack) = 0;
    (yy_buffer_stack_top) = 0;
    (yy_buffer_stack_max) = 0;
    (yy_c_buf_p) = (char *) 0;
    (yy_init) = 0;
    (yy_start) = 0;

    (yy_state_buf) = 0;
    (yy_state_ptr) = 0;
    (yy_full_match) = 0;
    (yy_lp) = 0;

/* Defined in main.c */
#ifdef YY_STDINIT
    H5LTyyin = stdin;
    H5LTyyout = stdout;
#else
    H5LTyyin = (FILE *) 0;
    H5LTyyout = (FILE *) 0;
#endif

    /* For future reference: Set errno on error, since we are called by
     * H5LTyylex_init()
     */
    return 0;
}

/* H5LTyylex_destroy is for both reentrant and non-reentrant scanners. */
int H5LTyylex_destroy  (void)
{
    
    /* Pop the buffer stack, destroying each element. */
	while(YY_CURRENT_BUFFER){
		H5LTyy_delete_buffer(YY_CURRENT_BUFFER  );
		YY_CURRENT_BUFFER_LVALUE = NULL;
		H5LTyypop_buffer_state();
	}

	/* Destroy the stack itself. */
	H5LTyyfree((yy_buffer_stack) );
	(yy_buffer_stack) = NULL;

    H5LTyyfree ( (yy_state_buf) );
    (yy_state_buf)  = NULL;

    /* Reset the globals. This is important in a non-reentrant scanner so the next time
     * H5LTyylex() is called, initialization will occur. */
    yy_init_globals( );

    return 0;
}

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, yyconst char * s2, int n )
{
	register int i;
	for ( i = 0; i < n; ++i )
		s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (yyconst char * s )
{
	register int n;
	for ( n = 0; s[n]; ++n )
		;

	return n;
}
#endif

void *H5LTyyalloc (yy_size_t  size )
{
	return (void *) malloc( size );
}

void *H5LTyyrealloc  (void * ptr, yy_size_t  size )
{
	/* The cast to (char *) in the following accommodates both
	 * implementations that use char* generic pointers, and those
	 * that use void* generic pointers.  It works with the latter
	 * because both ANSI C and C++ allow castless assignment from
	 * any pointer type to void*, and deal with argument conversions
	 * as though doing an assignment.
	 */
	return (void *) realloc( (char *) ptr, size );
}

void H5LTyyfree (void * ptr )
{
	free( (char *) ptr );	/* see H5LTyyrealloc() for (char *) cast */
}

#define YYTABLES_NAME "yytables"

#line 185 "hl/src/H5LTanalyze.l"


int my_yyinput(char *buf, int max_size)
{
   int ret;
    
   memcpy(buf, myinput, input_len); 
   ret = (int)input_len;
   return ret;
}

int H5LTyyerror(const char *msg)
{
   printf("ERROR: %s before \"%s\".\n", msg, H5LTyytext);
   return 0;
}

int H5LTyywrap()
{
    return(1);
}

