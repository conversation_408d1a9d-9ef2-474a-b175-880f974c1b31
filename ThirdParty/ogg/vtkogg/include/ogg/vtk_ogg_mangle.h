#ifndef vtk_ogg_mangle_h
#define vtk_ogg_mangle_h

#define oggpack_adv vtkogg_oggpack_adv
#define oggpack_adv1 vtkogg_oggpack_adv1
#define oggpackB_adv vtkogg_oggpackB_adv
#define oggpackB_adv1 vtkogg_oggpackB_adv1
#define oggpackB_bits vtkogg_oggpackB_bits
#define oggpackB_bytes vtkogg_oggpackB_bytes
#define oggpackB_get_buffer vtkogg_oggpackB_get_buffer
#define oggpack_bits vtkogg_oggpack_bits
#define oggpackB_look vtkogg_oggpackB_look
#define oggpackB_look1 vtkogg_oggpackB_look1
#define oggpackB_read vtkogg_oggpackB_read
#define oggpackB_read1 vtkogg_oggpackB_read1
#define oggpackB_readinit vtkogg_oggpackB_readinit
#define oggpackB_reset vtkogg_oggpackB_reset
#define oggpackB_write vtkogg_oggpackB_write
#define oggpackB_writealign vtkogg_oggpackB_writealign
#define oggpackB_writecheck vtkogg_oggpackB_writecheck
#define oggpackB_writeclear vtkogg_oggpackB_writeclear
#define oggpackB_writecopy vtkogg_oggpackB_writecopy
#define oggpackB_writeinit vtkogg_oggpackB_writeinit
#define oggpackB_writetrunc vtkogg_oggpackB_writetrunc
#define oggpack_bytes vtkogg_oggpack_bytes
#define ogg_packet_clear vtkogg_ogg_packet_clear
#define oggpack_get_buffer vtkogg_oggpack_get_buffer
#define oggpack_look vtkogg_oggpack_look
#define oggpack_look1 vtkogg_oggpack_look1
#define oggpack_read vtkogg_oggpack_read
#define oggpack_read1 vtkogg_oggpack_read1
#define oggpack_readinit vtkogg_oggpack_readinit
#define oggpack_reset vtkogg_oggpack_reset
#define oggpack_write vtkogg_oggpack_write
#define oggpack_writealign vtkogg_oggpack_writealign
#define oggpack_writecheck vtkogg_oggpack_writecheck
#define oggpack_writeclear vtkogg_oggpack_writeclear
#define oggpack_writecopy vtkogg_oggpack_writecopy
#define oggpack_writeinit vtkogg_oggpack_writeinit
#define oggpack_writetrunc vtkogg_oggpack_writetrunc
#define ogg_page_bos vtkogg_ogg_page_bos
#define ogg_page_checksum_set vtkogg_ogg_page_checksum_set
#define ogg_page_continued vtkogg_ogg_page_continued
#define ogg_page_eos vtkogg_ogg_page_eos
#define ogg_page_granulepos vtkogg_ogg_page_granulepos
#define ogg_page_packets vtkogg_ogg_page_packets
#define ogg_page_pageno vtkogg_ogg_page_pageno
#define ogg_page_serialno vtkogg_ogg_page_serialno
#define ogg_page_version vtkogg_ogg_page_version
#define ogg_stream_check vtkogg_ogg_stream_check
#define ogg_stream_clear vtkogg_ogg_stream_clear
#define ogg_stream_destroy vtkogg_ogg_stream_destroy
#define ogg_stream_eos vtkogg_ogg_stream_eos
#define ogg_stream_flush vtkogg_ogg_stream_flush
#define ogg_stream_flush_fill vtkogg_ogg_stream_flush_fill
#define ogg_stream_init vtkogg_ogg_stream_init
#define ogg_stream_iovecin vtkogg_ogg_stream_iovecin
#define ogg_stream_packetin vtkogg_ogg_stream_packetin
#define ogg_stream_packetout vtkogg_ogg_stream_packetout
#define ogg_stream_packetpeek vtkogg_ogg_stream_packetpeek
#define ogg_stream_pagein vtkogg_ogg_stream_pagein
#define ogg_stream_pageout vtkogg_ogg_stream_pageout
#define ogg_stream_pageout_fill vtkogg_ogg_stream_pageout_fill
#define ogg_stream_reset vtkogg_ogg_stream_reset
#define ogg_stream_reset_serialno vtkogg_ogg_stream_reset_serialno
#define ogg_sync_buffer vtkogg_ogg_sync_buffer
#define ogg_sync_check vtkogg_ogg_sync_check
#define ogg_sync_clear vtkogg_ogg_sync_clear
#define ogg_sync_destroy vtkogg_ogg_sync_destroy
#define ogg_sync_init vtkogg_ogg_sync_init
#define ogg_sync_pageout vtkogg_ogg_sync_pageout
#define ogg_sync_pageseek vtkogg_ogg_sync_pageseek
#define ogg_sync_reset vtkogg_ogg_sync_reset
#define ogg_sync_wrote vtkogg_ogg_sync_wrote

#endif
