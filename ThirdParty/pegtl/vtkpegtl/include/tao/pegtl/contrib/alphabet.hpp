// Copyright (c) 2015-2018 Dr<PERSON> <PERSON> and <PERSON>
// Please see LICENSE for license or visit https://github.com/taocpp/PEGTL/

#ifndef TAO_PEGTL_CONTRIB_ALPHABET_HPP
#define TAO_PEGTL_CONTRIB_ALPHABET_HPP

#include "../config.hpp"

namespace tao
{
   namespace TAO_PEGTL_NAMESPACE
   {
      inline namespace alphabet
      {
         static const int a = 'a';
         static const int b = 'b';
         static const int c = 'c';
         static const int d = 'd';
         static const int e = 'e';
         static const int f = 'f';
         static const int g = 'g';
         static const int h = 'h';
         static const int i = 'i';
         static const int j = 'j';
         static const int k = 'k';
         static const int l = 'l';
         static const int m = 'm';
         static const int n = 'n';
         static const int o = 'o';
         static const int p = 'p';
         static const int q = 'q';
         static const int r = 'r';
         static const int s = 's';
         static const int t = 't';
         static const int u = 'u';
         static const int v = 'v';
         static const int w = 'w';
         static const int x = 'x';
         static const int y = 'y';
         static const int z = 'z';

         static const int A = 'A';
         static const int B = 'B';
         static const int C = 'C';
         static const int D = 'D';
         static const int E = 'E';
         static const int F = 'F';
         static const int G = 'G';
         static const int H = 'H';
         static const int I = 'I';
         static const int J = 'J';
         static const int K = 'K';
         static const int L = 'L';
         static const int M = 'M';
         static const int N = 'N';
         static const int O = 'O';
         static const int P = 'P';
         static const int Q = 'Q';
         static const int R = 'R';
         static const int S = 'S';
         static const int T = 'T';
         static const int U = 'U';
         static const int V = 'V';
         static const int W = 'W';
         static const int X = 'X';
         static const int Y = 'Y';
         static const int Z = 'Z';

      }  // namespace alphabet

   }  // namespace TAO_PEGTL_NAMESPACE

}  // namespace tao

#endif
