# 1 "pnglibconf.c"
# 1 "<built-in>"
# 1 "<command-line>"
# 31 "<command-line>"
# 1 "/usr/include/stdc-predef.h" 1 3 4
# 32 "<command-line>" 2
# 1 "pnglibconf.c"
# 1 "pnglibconf.dfn" 1



 PNG_DFN "/* pnglibconf.h - library build configuration */"
 PNG_DFN ""
 PNG_DFN "/* libpng version 1.6.29, March 16, 2017 */"
 PNG_DFN ""
 PNG_DFN "/* Copyright (c) 1998-2016 <PERSON> */"
 PNG_DFN ""
 PNG_DFN "/* This code is released under the libpng license. */"
 PNG_DFN "/* For conditions of distribution and use, see the disclaimer */"
 PNG_DFN "/* and license in png.h */"
 PNG_DFN ""
 PNG_DFN "/* pnglibconf.h */"
 PNG_DFN "/* Machine generated file: DO NOT EDIT */"
 PNG_DFN "/* Derived from: scripts/pnglibconf.dfa */"
 PNG_DFN "#ifndef PNGLCONF_H"
 PNG_DFN "#define PNGLCONF_H"
# 33 "pnglibconf.dfn"
# 1 "/usr/include/zlib.h" 1 3 4
# 34 "/usr/include/zlib.h" 3 4
# 1 "/usr/include/zconf.h" 1 3 4
# 247 "/usr/include/zconf.h" 3 4
# 1 "/usr/lib/gcc/x86_64-redhat-linux/7/include/stddef.h" 1 3 4
# 149 "/usr/lib/gcc/x86_64-redhat-linux/7/include/stddef.h" 3 4

# 149 "/usr/lib/gcc/x86_64-redhat-linux/7/include/stddef.h" 3 4
typedef long int ptrdiff_t;
# 216 "/usr/lib/gcc/x86_64-redhat-linux/7/include/stddef.h" 3 4
typedef long unsigned int size_t;
# 328 "/usr/lib/gcc/x86_64-redhat-linux/7/include/stddef.h" 3 4
typedef int wchar_t;
# 426 "/usr/lib/gcc/x86_64-redhat-linux/7/include/stddef.h" 3 4
typedef struct {
  long long __max_align_ll __attribute__((__aligned__(__alignof__(long long))));
  long double __max_align_ld __attribute__((__aligned__(__alignof__(long double))));
# 437 "/usr/lib/gcc/x86_64-redhat-linux/7/include/stddef.h" 3 4
} max_align_t;
# 248 "/usr/include/zconf.h" 2 3 4
     typedef size_t z_size_t;
# 391 "/usr/include/zconf.h" 3 4
typedef unsigned char Byte;

typedef unsigned int uInt;
typedef unsigned long uLong;





   typedef Byte Bytef;

typedef char charf;
typedef int intf;
typedef uInt uIntf;
typedef uLong uLongf;


   typedef void const *voidpc;
   typedef void *voidpf;
   typedef void *voidp;







# 1 "/usr/lib/gcc/x86_64-redhat-linux/7/include/limits.h" 1 3 4
# 34 "/usr/lib/gcc/x86_64-redhat-linux/7/include/limits.h" 3 4
# 1 "/usr/lib/gcc/x86_64-redhat-linux/7/include/syslimits.h" 1 3 4






# 1 "/usr/lib/gcc/x86_64-redhat-linux/7/include/limits.h" 1 3 4
# 194 "/usr/lib/gcc/x86_64-redhat-linux/7/include/limits.h" 3 4
# 1 "/usr/include/limits.h" 1 3 4
# 26 "/usr/include/limits.h" 3 4
# 1 "/usr/include/bits/libc-header-start.h" 1 3 4
# 33 "/usr/include/bits/libc-header-start.h" 3 4
# 1 "/usr/include/features.h" 1 3 4
# 410 "/usr/include/features.h" 3 4
# 1 "/usr/include/sys/cdefs.h" 1 3 4
# 441 "/usr/include/sys/cdefs.h" 3 4
# 1 "/usr/include/bits/wordsize.h" 1 3 4
# 442 "/usr/include/sys/cdefs.h" 2 3 4
# 1 "/usr/include/bits/long-double.h" 1 3 4
# 443 "/usr/include/sys/cdefs.h" 2 3 4
# 411 "/usr/include/features.h" 2 3 4
# 434 "/usr/include/features.h" 3 4
# 1 "/usr/include/gnu/stubs.h" 1 3 4
# 10 "/usr/include/gnu/stubs.h" 3 4
# 1 "/usr/include/gnu/stubs-64.h" 1 3 4
# 11 "/usr/include/gnu/stubs.h" 2 3 4
# 435 "/usr/include/features.h" 2 3 4
# 34 "/usr/include/bits/libc-header-start.h" 2 3 4
# 27 "/usr/include/limits.h" 2 3 4
# 183 "/usr/include/limits.h" 3 4
# 1 "/usr/include/bits/posix1_lim.h" 1 3 4
# 160 "/usr/include/bits/posix1_lim.h" 3 4
# 1 "/usr/include/bits/local_lim.h" 1 3 4
# 38 "/usr/include/bits/local_lim.h" 3 4
# 1 "/usr/include/linux/limits.h" 1 3 4
# 39 "/usr/include/bits/local_lim.h" 2 3 4
# 161 "/usr/include/bits/posix1_lim.h" 2 3 4
# 184 "/usr/include/limits.h" 2 3 4



# 1 "/usr/include/bits/posix2_lim.h" 1 3 4
# 188 "/usr/include/limits.h" 2 3 4
# 195 "/usr/lib/gcc/x86_64-redhat-linux/7/include/limits.h" 2 3 4
# 8 "/usr/lib/gcc/x86_64-redhat-linux/7/include/syslimits.h" 2 3 4
# 35 "/usr/lib/gcc/x86_64-redhat-linux/7/include/limits.h" 2 3 4
# 419 "/usr/include/zconf.h" 2 3 4
# 429 "/usr/include/zconf.h" 3 4
   typedef unsigned z_crc_t;
# 444 "/usr/include/zconf.h" 3 4
# 1 "/usr/include/sys/types.h" 1 3 4
# 27 "/usr/include/sys/types.h" 3 4


# 1 "/usr/include/bits/types.h" 1 3 4
# 27 "/usr/include/bits/types.h" 3 4
# 1 "/usr/include/bits/wordsize.h" 1 3 4
# 28 "/usr/include/bits/types.h" 2 3 4


typedef unsigned char __u_char;
typedef unsigned short int __u_short;
typedef unsigned int __u_int;
typedef unsigned long int __u_long;


typedef signed char __int8_t;
typedef unsigned char __uint8_t;
typedef signed short int __int16_t;
typedef unsigned short int __uint16_t;
typedef signed int __int32_t;
typedef unsigned int __uint32_t;

typedef signed long int __int64_t;
typedef unsigned long int __uint64_t;







typedef long int __quad_t;
typedef unsigned long int __u_quad_t;







typedef long int __intmax_t;
typedef unsigned long int __uintmax_t;
# 130 "/usr/include/bits/types.h" 3 4
# 1 "/usr/include/bits/typesizes.h" 1 3 4
# 131 "/usr/include/bits/types.h" 2 3 4


typedef unsigned long int __dev_t;
typedef unsigned int __uid_t;
typedef unsigned int __gid_t;
typedef unsigned long int __ino_t;
typedef unsigned long int __ino64_t;
typedef unsigned int __mode_t;
typedef unsigned long int __nlink_t;
typedef long int __off_t;
typedef long int __off64_t;
typedef int __pid_t;
typedef struct { int __val[2]; } __fsid_t;
typedef long int __clock_t;
typedef unsigned long int __rlim_t;
typedef unsigned long int __rlim64_t;
typedef unsigned int __id_t;
typedef long int __time_t;
typedef unsigned int __useconds_t;
typedef long int __suseconds_t;

typedef int __daddr_t;
typedef int __key_t;


typedef int __clockid_t;


typedef void * __timer_t;


typedef long int __blksize_t;




typedef long int __blkcnt_t;
typedef long int __blkcnt64_t;


typedef unsigned long int __fsblkcnt_t;
typedef unsigned long int __fsblkcnt64_t;


typedef unsigned long int __fsfilcnt_t;
typedef unsigned long int __fsfilcnt64_t;


typedef long int __fsword_t;

typedef long int __ssize_t;


typedef long int __syscall_slong_t;

typedef unsigned long int __syscall_ulong_t;



typedef __off64_t __loff_t;
typedef __quad_t *__qaddr_t;
typedef char *__caddr_t;


typedef long int __intptr_t;


typedef unsigned int __socklen_t;
# 30 "/usr/include/sys/types.h" 2 3 4



typedef __u_char u_char;
typedef __u_short u_short;
typedef __u_int u_int;
typedef __u_long u_long;
typedef __quad_t quad_t;
typedef __u_quad_t u_quad_t;
typedef __fsid_t fsid_t;




typedef __loff_t loff_t;



typedef __ino_t ino_t;
# 60 "/usr/include/sys/types.h" 3 4
typedef __dev_t dev_t;




typedef __gid_t gid_t;




typedef __mode_t mode_t;




typedef __nlink_t nlink_t;




typedef __uid_t uid_t;





typedef __off_t off_t;
# 98 "/usr/include/sys/types.h" 3 4
typedef __pid_t pid_t;





typedef __id_t id_t;




typedef __ssize_t ssize_t;





typedef __daddr_t daddr_t;
typedef __caddr_t caddr_t;





typedef __key_t key_t;




# 1 "/usr/include/bits/types/clock_t.h" 1 3 4







typedef __clock_t clock_t;




# 128 "/usr/include/sys/types.h" 2 3 4

# 1 "/usr/include/bits/types/clockid_t.h" 1 3 4






typedef __clockid_t clockid_t;
# 130 "/usr/include/sys/types.h" 2 3 4
# 1 "/usr/include/bits/types/time_t.h" 1 3 4







typedef __time_t time_t;



# 131 "/usr/include/sys/types.h" 2 3 4
# 1 "/usr/include/bits/types/timer_t.h" 1 3 4






typedef __timer_t timer_t;
# 132 "/usr/include/sys/types.h" 2 3 4
# 145 "/usr/include/sys/types.h" 3 4
# 1 "/usr/lib/gcc/x86_64-redhat-linux/7/include/stddef.h" 1 3 4
# 146 "/usr/include/sys/types.h" 2 3 4



typedef unsigned long int ulong;
typedef unsigned short int ushort;
typedef unsigned int uint;
# 193 "/usr/include/sys/types.h" 3 4
typedef int int8_t __attribute__ ((__mode__ (__QI__)));
typedef int int16_t __attribute__ ((__mode__ (__HI__)));
typedef int int32_t __attribute__ ((__mode__ (__SI__)));
typedef int int64_t __attribute__ ((__mode__ (__DI__)));


typedef unsigned int u_int8_t __attribute__ ((__mode__ (__QI__)));
typedef unsigned int u_int16_t __attribute__ ((__mode__ (__HI__)));
typedef unsigned int u_int32_t __attribute__ ((__mode__ (__SI__)));
typedef unsigned int u_int64_t __attribute__ ((__mode__ (__DI__)));

typedef int register_t __attribute__ ((__mode__ (__word__)));
# 215 "/usr/include/sys/types.h" 3 4
# 1 "/usr/include/endian.h" 1 3 4
# 36 "/usr/include/endian.h" 3 4
# 1 "/usr/include/bits/endian.h" 1 3 4
# 37 "/usr/include/endian.h" 2 3 4
# 60 "/usr/include/endian.h" 3 4
# 1 "/usr/include/bits/byteswap.h" 1 3 4
# 28 "/usr/include/bits/byteswap.h" 3 4
# 1 "/usr/include/bits/wordsize.h" 1 3 4
# 29 "/usr/include/bits/byteswap.h" 2 3 4






# 1 "/usr/include/bits/byteswap-16.h" 1 3 4
# 36 "/usr/include/bits/byteswap.h" 2 3 4
# 44 "/usr/include/bits/byteswap.h" 3 4
static __inline unsigned int
__bswap_32 (unsigned int __bsx)
{
  return __builtin_bswap32 (__bsx);
}
# 108 "/usr/include/bits/byteswap.h" 3 4
static __inline __uint64_t
__bswap_64 (__uint64_t __bsx)
{
  return __builtin_bswap64 (__bsx);
}
# 61 "/usr/include/endian.h" 2 3 4
# 1 "/usr/include/bits/uintn-identity.h" 1 3 4
# 32 "/usr/include/bits/uintn-identity.h" 3 4
static __inline __uint16_t
__uint16_identity (__uint16_t __x)
{
  return __x;
}

static __inline __uint32_t
__uint32_identity (__uint32_t __x)
{
  return __x;
}

static __inline __uint64_t
__uint64_identity (__uint64_t __x)
{
  return __x;
}
# 62 "/usr/include/endian.h" 2 3 4
# 216 "/usr/include/sys/types.h" 2 3 4


# 1 "/usr/include/sys/select.h" 1 3 4
# 30 "/usr/include/sys/select.h" 3 4
# 1 "/usr/include/bits/select.h" 1 3 4
# 22 "/usr/include/bits/select.h" 3 4
# 1 "/usr/include/bits/wordsize.h" 1 3 4
# 23 "/usr/include/bits/select.h" 2 3 4
# 31 "/usr/include/sys/select.h" 2 3 4


# 1 "/usr/include/bits/sigset.h" 1 3 4
# 22 "/usr/include/bits/sigset.h" 3 4
typedef int __sig_atomic_t;




typedef struct
  {
    unsigned long int __val[(1024 / (8 * sizeof (unsigned long int)))];
  } __sigset_t;
# 34 "/usr/include/sys/select.h" 2 3 4



typedef __sigset_t sigset_t;




# 1 "/usr/include/bits/types/struct_timeval.h" 1 3 4







struct timeval
{
  __time_t tv_sec;
  __suseconds_t tv_usec;
};
# 43 "/usr/include/sys/select.h" 2 3 4

# 1 "/usr/include/bits/types/struct_timespec.h" 1 3 4







struct timespec
{
  __time_t tv_sec;
  __syscall_slong_t tv_nsec;
};
# 45 "/usr/include/sys/select.h" 2 3 4



typedef __suseconds_t suseconds_t;





typedef long int __fd_mask;
# 64 "/usr/include/sys/select.h" 3 4
typedef struct
  {






    __fd_mask __fds_bits[1024 / (8 * (int) sizeof (__fd_mask))];


  } fd_set;






typedef __fd_mask fd_mask;
# 96 "/usr/include/sys/select.h" 3 4

# 106 "/usr/include/sys/select.h" 3 4
extern int select (int __nfds, fd_set *__restrict __readfds,
     fd_set *__restrict __writefds,
     fd_set *__restrict __exceptfds,
     struct timeval *__restrict __timeout);
# 118 "/usr/include/sys/select.h" 3 4
extern int pselect (int __nfds, fd_set *__restrict __readfds,
      fd_set *__restrict __writefds,
      fd_set *__restrict __exceptfds,
      const struct timespec *__restrict __timeout,
      const __sigset_t *__restrict __sigmask);
# 131 "/usr/include/sys/select.h" 3 4

# 219 "/usr/include/sys/types.h" 2 3 4







# 1 "/usr/include/sys/sysmacros.h" 1 3 4
# 41 "/usr/include/sys/sysmacros.h" 3 4
# 1 "/usr/include/bits/sysmacros.h" 1 3 4
# 42 "/usr/include/sys/sysmacros.h" 2 3 4
# 71 "/usr/include/sys/sysmacros.h" 3 4


extern unsigned int gnu_dev_major (__dev_t __dev) __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__const__));
extern unsigned int gnu_dev_minor (__dev_t __dev) __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__const__));
extern __dev_t gnu_dev_makedev (unsigned int __major, unsigned int __minor) __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__const__));
# 85 "/usr/include/sys/sysmacros.h" 3 4

# 227 "/usr/include/sys/types.h" 2 3 4






typedef __blksize_t blksize_t;






typedef __blkcnt_t blkcnt_t;



typedef __fsblkcnt_t fsblkcnt_t;



typedef __fsfilcnt_t fsfilcnt_t;
# 275 "/usr/include/sys/types.h" 3 4
# 1 "/usr/include/bits/pthreadtypes.h" 1 3 4
# 21 "/usr/include/bits/pthreadtypes.h" 3 4
# 1 "/usr/include/bits/wordsize.h" 1 3 4
# 22 "/usr/include/bits/pthreadtypes.h" 2 3 4
# 60 "/usr/include/bits/pthreadtypes.h" 3 4
typedef unsigned long int pthread_t;


union pthread_attr_t
{
  char __size[56];
  long int __align;
};

typedef union pthread_attr_t pthread_attr_t;





typedef struct __pthread_internal_list
{
  struct __pthread_internal_list *__prev;
  struct __pthread_internal_list *__next;
} __pthread_list_t;
# 90 "/usr/include/bits/pthreadtypes.h" 3 4
typedef union
{
  struct __pthread_mutex_s
  {
    int __lock;
    unsigned int __count;
    int __owner;

    unsigned int __nusers;



    int __kind;

    short __spins;
    short __elision;
    __pthread_list_t __list;
# 125 "/usr/include/bits/pthreadtypes.h" 3 4
  } __data;
  char __size[40];
  long int __align;
} pthread_mutex_t;

typedef union
{
  char __size[4];
  int __align;
} pthread_mutexattr_t;




typedef union
{
  struct
  {
    __extension__ union
    {
      __extension__ unsigned long long int __wseq;
      struct {
 unsigned int __low;
 unsigned int __high;
      } __wseq32;
    };
    __extension__ union
    {
      __extension__ unsigned long long int __g1_start;
      struct {
 unsigned int __low;
 unsigned int __high;
      } __g1_start32;
    };
    unsigned int __g_refs[2];
    unsigned int __g_size[2];
    unsigned int __g1_orig_size;
    unsigned int __wrefs;
    unsigned int __g_signals[2];
  } __data;
  char __size[48];
  __extension__ long long int __align;
} pthread_cond_t;

typedef union
{
  char __size[4];
  int __align;
} pthread_condattr_t;



typedef unsigned int pthread_key_t;



typedef int pthread_once_t;





typedef union
{

  struct
  {
    unsigned int __readers;
    unsigned int __writers;
    unsigned int __wrphase_futex;
    unsigned int __writers_futex;
    unsigned int __pad3;
    unsigned int __pad4;
    int __cur_writer;
    int __shared;
    signed char __rwelision;




    unsigned char __pad1[7];


    unsigned long int __pad2;


    unsigned int __flags;

  } __data;
# 233 "/usr/include/bits/pthreadtypes.h" 3 4
  char __size[56];
  long int __align;
} pthread_rwlock_t;

typedef union
{
  char __size[8];
  long int __align;
} pthread_rwlockattr_t;





typedef volatile int pthread_spinlock_t;




typedef union
{
  char __size[32];
  long int __align;
} pthread_barrier_t;

typedef union
{
  char __size[4];
  int __align;
} pthread_barrierattr_t;
# 276 "/usr/include/sys/types.h" 2 3 4



# 445 "/usr/include/zconf.h" 2 3 4





# 1 "/usr/lib/gcc/x86_64-redhat-linux/7/include/stdarg.h" 1 3 4
# 40 "/usr/lib/gcc/x86_64-redhat-linux/7/include/stdarg.h" 3 4
typedef __builtin_va_list __gnuc_va_list;
# 99 "/usr/lib/gcc/x86_64-redhat-linux/7/include/stdarg.h" 3 4
typedef __gnuc_va_list va_list;
# 451 "/usr/include/zconf.h" 2 3 4
# 475 "/usr/include/zconf.h" 3 4
# 1 "/usr/include/unistd.h" 1 3 4
# 27 "/usr/include/unistd.h" 3 4

# 205 "/usr/include/unistd.h" 3 4
# 1 "/usr/include/bits/posix_opt.h" 1 3 4
# 206 "/usr/include/unistd.h" 2 3 4



# 1 "/usr/include/bits/environments.h" 1 3 4
# 22 "/usr/include/bits/environments.h" 3 4
# 1 "/usr/include/bits/wordsize.h" 1 3 4
# 23 "/usr/include/bits/environments.h" 2 3 4
# 210 "/usr/include/unistd.h" 2 3 4
# 229 "/usr/include/unistd.h" 3 4
# 1 "/usr/lib/gcc/x86_64-redhat-linux/7/include/stddef.h" 1 3 4
# 230 "/usr/include/unistd.h" 2 3 4
# 258 "/usr/include/unistd.h" 3 4
typedef __useconds_t useconds_t;
# 270 "/usr/include/unistd.h" 3 4
typedef __intptr_t intptr_t;






typedef __socklen_t socklen_t;
# 290 "/usr/include/unistd.h" 3 4
extern int access (const char *__name, int __type) __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1)));
# 307 "/usr/include/unistd.h" 3 4
extern int faccessat (int __fd, const char *__file, int __type, int __flag)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (2))) ;
# 337 "/usr/include/unistd.h" 3 4
extern __off_t lseek (int __fd, __off_t __offset, int __whence) __attribute__ ((__nothrow__ , __leaf__));
# 356 "/usr/include/unistd.h" 3 4
extern int close (int __fd);






extern ssize_t read (int __fd, void *__buf, size_t __nbytes) ;





extern ssize_t write (int __fd, const void *__buf, size_t __n) ;
# 379 "/usr/include/unistd.h" 3 4
extern ssize_t pread (int __fd, void *__buf, size_t __nbytes,
        __off_t __offset) ;






extern ssize_t pwrite (int __fd, const void *__buf, size_t __n,
         __off_t __offset) ;
# 420 "/usr/include/unistd.h" 3 4
extern int pipe (int __pipedes[2]) __attribute__ ((__nothrow__ , __leaf__)) ;
# 435 "/usr/include/unistd.h" 3 4
extern unsigned int alarm (unsigned int __seconds) __attribute__ ((__nothrow__ , __leaf__));
# 447 "/usr/include/unistd.h" 3 4
extern unsigned int sleep (unsigned int __seconds);







extern __useconds_t ualarm (__useconds_t __value, __useconds_t __interval)
     __attribute__ ((__nothrow__ , __leaf__));






extern int usleep (__useconds_t __useconds);
# 472 "/usr/include/unistd.h" 3 4
extern int pause (void);



extern int chown (const char *__file, __uid_t __owner, __gid_t __group)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1))) ;



extern int fchown (int __fd, __uid_t __owner, __gid_t __group) __attribute__ ((__nothrow__ , __leaf__)) ;




extern int lchown (const char *__file, __uid_t __owner, __gid_t __group)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1))) ;






extern int fchownat (int __fd, const char *__file, __uid_t __owner,
       __gid_t __group, int __flag)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (2))) ;



extern int chdir (const char *__path) __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1))) ;



extern int fchdir (int __fd) __attribute__ ((__nothrow__ , __leaf__)) ;
# 514 "/usr/include/unistd.h" 3 4
extern char *getcwd (char *__buf, size_t __size) __attribute__ ((__nothrow__ , __leaf__)) ;
# 528 "/usr/include/unistd.h" 3 4
extern char *getwd (char *__buf)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1))) __attribute__ ((__deprecated__)) ;




extern int dup (int __fd) __attribute__ ((__nothrow__ , __leaf__)) ;


extern int dup2 (int __fd, int __fd2) __attribute__ ((__nothrow__ , __leaf__));
# 546 "/usr/include/unistd.h" 3 4
extern char **__environ;







extern int execve (const char *__path, char *const __argv[],
     char *const __envp[]) __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1, 2)));




extern int fexecve (int __fd, char *const __argv[], char *const __envp[])
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (2)));




extern int execv (const char *__path, char *const __argv[])
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1, 2)));



extern int execle (const char *__path, const char *__arg, ...)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1, 2)));



extern int execl (const char *__path, const char *__arg, ...)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1, 2)));



extern int execvp (const char *__file, char *const __argv[])
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1, 2)));




extern int execlp (const char *__file, const char *__arg, ...)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1, 2)));
# 601 "/usr/include/unistd.h" 3 4
extern int nice (int __inc) __attribute__ ((__nothrow__ , __leaf__)) ;




extern void _exit (int __status) __attribute__ ((__noreturn__));





# 1 "/usr/include/bits/confname.h" 1 3 4
# 24 "/usr/include/bits/confname.h" 3 4
enum
  {
    _PC_LINK_MAX,

    _PC_MAX_CANON,

    _PC_MAX_INPUT,

    _PC_NAME_MAX,

    _PC_PATH_MAX,

    _PC_PIPE_BUF,

    _PC_CHOWN_RESTRICTED,

    _PC_NO_TRUNC,

    _PC_VDISABLE,

    _PC_SYNC_IO,

    _PC_ASYNC_IO,

    _PC_PRIO_IO,

    _PC_SOCK_MAXBUF,

    _PC_FILESIZEBITS,

    _PC_REC_INCR_XFER_SIZE,

    _PC_REC_MAX_XFER_SIZE,

    _PC_REC_MIN_XFER_SIZE,

    _PC_REC_XFER_ALIGN,

    _PC_ALLOC_SIZE_MIN,

    _PC_SYMLINK_MAX,

    _PC_2_SYMLINKS

  };


enum
  {
    _SC_ARG_MAX,

    _SC_CHILD_MAX,

    _SC_CLK_TCK,

    _SC_NGROUPS_MAX,

    _SC_OPEN_MAX,

    _SC_STREAM_MAX,

    _SC_TZNAME_MAX,

    _SC_JOB_CONTROL,

    _SC_SAVED_IDS,

    _SC_REALTIME_SIGNALS,

    _SC_PRIORITY_SCHEDULING,

    _SC_TIMERS,

    _SC_ASYNCHRONOUS_IO,

    _SC_PRIORITIZED_IO,

    _SC_SYNCHRONIZED_IO,

    _SC_FSYNC,

    _SC_MAPPED_FILES,

    _SC_MEMLOCK,

    _SC_MEMLOCK_RANGE,

    _SC_MEMORY_PROTECTION,

    _SC_MESSAGE_PASSING,

    _SC_SEMAPHORES,

    _SC_SHARED_MEMORY_OBJECTS,

    _SC_AIO_LISTIO_MAX,

    _SC_AIO_MAX,

    _SC_AIO_PRIO_DELTA_MAX,

    _SC_DELAYTIMER_MAX,

    _SC_MQ_OPEN_MAX,

    _SC_MQ_PRIO_MAX,

    _SC_VERSION,

    _SC_PAGESIZE,


    _SC_RTSIG_MAX,

    _SC_SEM_NSEMS_MAX,

    _SC_SEM_VALUE_MAX,

    _SC_SIGQUEUE_MAX,

    _SC_TIMER_MAX,




    _SC_BC_BASE_MAX,

    _SC_BC_DIM_MAX,

    _SC_BC_SCALE_MAX,

    _SC_BC_STRING_MAX,

    _SC_COLL_WEIGHTS_MAX,

    _SC_EQUIV_CLASS_MAX,

    _SC_EXPR_NEST_MAX,

    _SC_LINE_MAX,

    _SC_RE_DUP_MAX,

    _SC_CHARCLASS_NAME_MAX,


    _SC_2_VERSION,

    _SC_2_C_BIND,

    _SC_2_C_DEV,

    _SC_2_FORT_DEV,

    _SC_2_FORT_RUN,

    _SC_2_SW_DEV,

    _SC_2_LOCALEDEF,


    _SC_PII,

    _SC_PII_XTI,

    _SC_PII_SOCKET,

    _SC_PII_INTERNET,

    _SC_PII_OSI,

    _SC_POLL,

    _SC_SELECT,

    _SC_UIO_MAXIOV,

    _SC_IOV_MAX = _SC_UIO_MAXIOV,

    _SC_PII_INTERNET_STREAM,

    _SC_PII_INTERNET_DGRAM,

    _SC_PII_OSI_COTS,

    _SC_PII_OSI_CLTS,

    _SC_PII_OSI_M,

    _SC_T_IOV_MAX,



    _SC_THREADS,

    _SC_THREAD_SAFE_FUNCTIONS,

    _SC_GETGR_R_SIZE_MAX,

    _SC_GETPW_R_SIZE_MAX,

    _SC_LOGIN_NAME_MAX,

    _SC_TTY_NAME_MAX,

    _SC_THREAD_DESTRUCTOR_ITERATIONS,

    _SC_THREAD_KEYS_MAX,

    _SC_THREAD_STACK_MIN,

    _SC_THREAD_THREADS_MAX,

    _SC_THREAD_ATTR_STACKADDR,

    _SC_THREAD_ATTR_STACKSIZE,

    _SC_THREAD_PRIORITY_SCHEDULING,

    _SC_THREAD_PRIO_INHERIT,

    _SC_THREAD_PRIO_PROTECT,

    _SC_THREAD_PROCESS_SHARED,


    _SC_NPROCESSORS_CONF,

    _SC_NPROCESSORS_ONLN,

    _SC_PHYS_PAGES,

    _SC_AVPHYS_PAGES,

    _SC_ATEXIT_MAX,

    _SC_PASS_MAX,


    _SC_XOPEN_VERSION,

    _SC_XOPEN_XCU_VERSION,

    _SC_XOPEN_UNIX,

    _SC_XOPEN_CRYPT,

    _SC_XOPEN_ENH_I18N,

    _SC_XOPEN_SHM,


    _SC_2_CHAR_TERM,

    _SC_2_C_VERSION,

    _SC_2_UPE,


    _SC_XOPEN_XPG2,

    _SC_XOPEN_XPG3,

    _SC_XOPEN_XPG4,


    _SC_CHAR_BIT,

    _SC_CHAR_MAX,

    _SC_CHAR_MIN,

    _SC_INT_MAX,

    _SC_INT_MIN,

    _SC_LONG_BIT,

    _SC_WORD_BIT,

    _SC_MB_LEN_MAX,

    _SC_NZERO,

    _SC_SSIZE_MAX,

    _SC_SCHAR_MAX,

    _SC_SCHAR_MIN,

    _SC_SHRT_MAX,

    _SC_SHRT_MIN,

    _SC_UCHAR_MAX,

    _SC_UINT_MAX,

    _SC_ULONG_MAX,

    _SC_USHRT_MAX,


    _SC_NL_ARGMAX,

    _SC_NL_LANGMAX,

    _SC_NL_MSGMAX,

    _SC_NL_NMAX,

    _SC_NL_SETMAX,

    _SC_NL_TEXTMAX,


    _SC_XBS5_ILP32_OFF32,

    _SC_XBS5_ILP32_OFFBIG,

    _SC_XBS5_LP64_OFF64,

    _SC_XBS5_LPBIG_OFFBIG,


    _SC_XOPEN_LEGACY,

    _SC_XOPEN_REALTIME,

    _SC_XOPEN_REALTIME_THREADS,


    _SC_ADVISORY_INFO,

    _SC_BARRIERS,

    _SC_BASE,

    _SC_C_LANG_SUPPORT,

    _SC_C_LANG_SUPPORT_R,

    _SC_CLOCK_SELECTION,

    _SC_CPUTIME,

    _SC_THREAD_CPUTIME,

    _SC_DEVICE_IO,

    _SC_DEVICE_SPECIFIC,

    _SC_DEVICE_SPECIFIC_R,

    _SC_FD_MGMT,

    _SC_FIFO,

    _SC_PIPE,

    _SC_FILE_ATTRIBUTES,

    _SC_FILE_LOCKING,

    _SC_FILE_SYSTEM,

    _SC_MONOTONIC_CLOCK,

    _SC_MULTI_PROCESS,

    _SC_SINGLE_PROCESS,

    _SC_NETWORKING,

    _SC_READER_WRITER_LOCKS,

    _SC_SPIN_LOCKS,

    _SC_REGEXP,

    _SC_REGEX_VERSION,

    _SC_SHELL,

    _SC_SIGNALS,

    _SC_SPAWN,

    _SC_SPORADIC_SERVER,

    _SC_THREAD_SPORADIC_SERVER,

    _SC_SYSTEM_DATABASE,

    _SC_SYSTEM_DATABASE_R,

    _SC_TIMEOUTS,

    _SC_TYPED_MEMORY_OBJECTS,

    _SC_USER_GROUPS,

    _SC_USER_GROUPS_R,

    _SC_2_PBS,

    _SC_2_PBS_ACCOUNTING,

    _SC_2_PBS_LOCATE,

    _SC_2_PBS_MESSAGE,

    _SC_2_PBS_TRACK,

    _SC_SYMLOOP_MAX,

    _SC_STREAMS,

    _SC_2_PBS_CHECKPOINT,


    _SC_V6_ILP32_OFF32,

    _SC_V6_ILP32_OFFBIG,

    _SC_V6_LP64_OFF64,

    _SC_V6_LPBIG_OFFBIG,


    _SC_HOST_NAME_MAX,

    _SC_TRACE,

    _SC_TRACE_EVENT_FILTER,

    _SC_TRACE_INHERIT,

    _SC_TRACE_LOG,


    _SC_LEVEL1_ICACHE_SIZE,

    _SC_LEVEL1_ICACHE_ASSOC,

    _SC_LEVEL1_ICACHE_LINESIZE,

    _SC_LEVEL1_DCACHE_SIZE,

    _SC_LEVEL1_DCACHE_ASSOC,

    _SC_LEVEL1_DCACHE_LINESIZE,

    _SC_LEVEL2_CACHE_SIZE,

    _SC_LEVEL2_CACHE_ASSOC,

    _SC_LEVEL2_CACHE_LINESIZE,

    _SC_LEVEL3_CACHE_SIZE,

    _SC_LEVEL3_CACHE_ASSOC,

    _SC_LEVEL3_CACHE_LINESIZE,

    _SC_LEVEL4_CACHE_SIZE,

    _SC_LEVEL4_CACHE_ASSOC,

    _SC_LEVEL4_CACHE_LINESIZE,



    _SC_IPV6 = _SC_LEVEL1_ICACHE_SIZE + 50,

    _SC_RAW_SOCKETS,


    _SC_V7_ILP32_OFF32,

    _SC_V7_ILP32_OFFBIG,

    _SC_V7_LP64_OFF64,

    _SC_V7_LPBIG_OFFBIG,


    _SC_SS_REPL_MAX,


    _SC_TRACE_EVENT_NAME_MAX,

    _SC_TRACE_NAME_MAX,

    _SC_TRACE_SYS_MAX,

    _SC_TRACE_USER_EVENT_MAX,


    _SC_XOPEN_STREAMS,


    _SC_THREAD_ROBUST_PRIO_INHERIT,

    _SC_THREAD_ROBUST_PRIO_PROTECT

  };


enum
  {
    _CS_PATH,


    _CS_V6_WIDTH_RESTRICTED_ENVS,



    _CS_GNU_LIBC_VERSION,

    _CS_GNU_LIBPTHREAD_VERSION,


    _CS_V5_WIDTH_RESTRICTED_ENVS,



    _CS_V7_WIDTH_RESTRICTED_ENVS,



    _CS_LFS_CFLAGS = 1000,

    _CS_LFS_LDFLAGS,

    _CS_LFS_LIBS,

    _CS_LFS_LINTFLAGS,

    _CS_LFS64_CFLAGS,

    _CS_LFS64_LDFLAGS,

    _CS_LFS64_LIBS,

    _CS_LFS64_LINTFLAGS,


    _CS_XBS5_ILP32_OFF32_CFLAGS = 1100,

    _CS_XBS5_ILP32_OFF32_LDFLAGS,

    _CS_XBS5_ILP32_OFF32_LIBS,

    _CS_XBS5_ILP32_OFF32_LINTFLAGS,

    _CS_XBS5_ILP32_OFFBIG_CFLAGS,

    _CS_XBS5_ILP32_OFFBIG_LDFLAGS,

    _CS_XBS5_ILP32_OFFBIG_LIBS,

    _CS_XBS5_ILP32_OFFBIG_LINTFLAGS,

    _CS_XBS5_LP64_OFF64_CFLAGS,

    _CS_XBS5_LP64_OFF64_LDFLAGS,

    _CS_XBS5_LP64_OFF64_LIBS,

    _CS_XBS5_LP64_OFF64_LINTFLAGS,

    _CS_XBS5_LPBIG_OFFBIG_CFLAGS,

    _CS_XBS5_LPBIG_OFFBIG_LDFLAGS,

    _CS_XBS5_LPBIG_OFFBIG_LIBS,

    _CS_XBS5_LPBIG_OFFBIG_LINTFLAGS,


    _CS_POSIX_V6_ILP32_OFF32_CFLAGS,

    _CS_POSIX_V6_ILP32_OFF32_LDFLAGS,

    _CS_POSIX_V6_ILP32_OFF32_LIBS,

    _CS_POSIX_V6_ILP32_OFF32_LINTFLAGS,

    _CS_POSIX_V6_ILP32_OFFBIG_CFLAGS,

    _CS_POSIX_V6_ILP32_OFFBIG_LDFLAGS,

    _CS_POSIX_V6_ILP32_OFFBIG_LIBS,

    _CS_POSIX_V6_ILP32_OFFBIG_LINTFLAGS,

    _CS_POSIX_V6_LP64_OFF64_CFLAGS,

    _CS_POSIX_V6_LP64_OFF64_LDFLAGS,

    _CS_POSIX_V6_LP64_OFF64_LIBS,

    _CS_POSIX_V6_LP64_OFF64_LINTFLAGS,

    _CS_POSIX_V6_LPBIG_OFFBIG_CFLAGS,

    _CS_POSIX_V6_LPBIG_OFFBIG_LDFLAGS,

    _CS_POSIX_V6_LPBIG_OFFBIG_LIBS,

    _CS_POSIX_V6_LPBIG_OFFBIG_LINTFLAGS,


    _CS_POSIX_V7_ILP32_OFF32_CFLAGS,

    _CS_POSIX_V7_ILP32_OFF32_LDFLAGS,

    _CS_POSIX_V7_ILP32_OFF32_LIBS,

    _CS_POSIX_V7_ILP32_OFF32_LINTFLAGS,

    _CS_POSIX_V7_ILP32_OFFBIG_CFLAGS,

    _CS_POSIX_V7_ILP32_OFFBIG_LDFLAGS,

    _CS_POSIX_V7_ILP32_OFFBIG_LIBS,

    _CS_POSIX_V7_ILP32_OFFBIG_LINTFLAGS,

    _CS_POSIX_V7_LP64_OFF64_CFLAGS,

    _CS_POSIX_V7_LP64_OFF64_LDFLAGS,

    _CS_POSIX_V7_LP64_OFF64_LIBS,

    _CS_POSIX_V7_LP64_OFF64_LINTFLAGS,

    _CS_POSIX_V7_LPBIG_OFFBIG_CFLAGS,

    _CS_POSIX_V7_LPBIG_OFFBIG_LDFLAGS,

    _CS_POSIX_V7_LPBIG_OFFBIG_LIBS,

    _CS_POSIX_V7_LPBIG_OFFBIG_LINTFLAGS,


    _CS_V6_ENV,

    _CS_V7_ENV

  };
# 613 "/usr/include/unistd.h" 2 3 4


extern long int pathconf (const char *__path, int __name)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1)));


extern long int fpathconf (int __fd, int __name) __attribute__ ((__nothrow__ , __leaf__));


extern long int sysconf (int __name) __attribute__ ((__nothrow__ , __leaf__));



extern size_t confstr (int __name, char *__buf, size_t __len) __attribute__ ((__nothrow__ , __leaf__));




extern __pid_t getpid (void) __attribute__ ((__nothrow__ , __leaf__));


extern __pid_t getppid (void) __attribute__ ((__nothrow__ , __leaf__));


extern __pid_t getpgrp (void) __attribute__ ((__nothrow__ , __leaf__));


extern __pid_t __getpgid (__pid_t __pid) __attribute__ ((__nothrow__ , __leaf__));

extern __pid_t getpgid (__pid_t __pid) __attribute__ ((__nothrow__ , __leaf__));






extern int setpgid (__pid_t __pid, __pid_t __pgid) __attribute__ ((__nothrow__ , __leaf__));
# 663 "/usr/include/unistd.h" 3 4
extern int setpgrp (void) __attribute__ ((__nothrow__ , __leaf__));






extern __pid_t setsid (void) __attribute__ ((__nothrow__ , __leaf__));



extern __pid_t getsid (__pid_t __pid) __attribute__ ((__nothrow__ , __leaf__));



extern __uid_t getuid (void) __attribute__ ((__nothrow__ , __leaf__));


extern __uid_t geteuid (void) __attribute__ ((__nothrow__ , __leaf__));


extern __gid_t getgid (void) __attribute__ ((__nothrow__ , __leaf__));


extern __gid_t getegid (void) __attribute__ ((__nothrow__ , __leaf__));




extern int getgroups (int __size, __gid_t __list[]) __attribute__ ((__nothrow__ , __leaf__)) ;
# 703 "/usr/include/unistd.h" 3 4
extern int setuid (__uid_t __uid) __attribute__ ((__nothrow__ , __leaf__)) ;




extern int setreuid (__uid_t __ruid, __uid_t __euid) __attribute__ ((__nothrow__ , __leaf__)) ;




extern int seteuid (__uid_t __uid) __attribute__ ((__nothrow__ , __leaf__)) ;






extern int setgid (__gid_t __gid) __attribute__ ((__nothrow__ , __leaf__)) ;




extern int setregid (__gid_t __rgid, __gid_t __egid) __attribute__ ((__nothrow__ , __leaf__)) ;




extern int setegid (__gid_t __gid) __attribute__ ((__nothrow__ , __leaf__)) ;
# 759 "/usr/include/unistd.h" 3 4
extern __pid_t fork (void) __attribute__ ((__nothrow__));







extern __pid_t vfork (void) __attribute__ ((__nothrow__ , __leaf__));





extern char *ttyname (int __fd) __attribute__ ((__nothrow__ , __leaf__));



extern int ttyname_r (int __fd, char *__buf, size_t __buflen)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (2))) ;



extern int isatty (int __fd) __attribute__ ((__nothrow__ , __leaf__));




extern int ttyslot (void) __attribute__ ((__nothrow__ , __leaf__));




extern int link (const char *__from, const char *__to)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1, 2))) ;




extern int linkat (int __fromfd, const char *__from, int __tofd,
     const char *__to, int __flags)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (2, 4))) ;




extern int symlink (const char *__from, const char *__to)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1, 2))) ;




extern ssize_t readlink (const char *__restrict __path,
    char *__restrict __buf, size_t __len)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1, 2))) ;




extern int symlinkat (const char *__from, int __tofd,
        const char *__to) __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1, 3))) ;


extern ssize_t readlinkat (int __fd, const char *__restrict __path,
      char *__restrict __buf, size_t __len)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (2, 3))) ;



extern int unlink (const char *__name) __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1)));



extern int unlinkat (int __fd, const char *__name, int __flag)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (2)));



extern int rmdir (const char *__path) __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1)));



extern __pid_t tcgetpgrp (int __fd) __attribute__ ((__nothrow__ , __leaf__));


extern int tcsetpgrp (int __fd, __pid_t __pgrp_id) __attribute__ ((__nothrow__ , __leaf__));






extern char *getlogin (void);







extern int getlogin_r (char *__name, size_t __name_len) __attribute__ ((__nonnull__ (1)));




extern int setlogin (const char *__name) __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1)));
# 873 "/usr/include/unistd.h" 3 4
# 1 "/usr/include/getopt.h" 1 3 4
# 57 "/usr/include/getopt.h" 3 4
extern char *optarg;
# 71 "/usr/include/getopt.h" 3 4
extern int optind;




extern int opterr;



extern int optopt;
# 150 "/usr/include/getopt.h" 3 4
extern int getopt (int ___argc, char *const *___argv, const char *__shortopts)
       __attribute__ ((__nothrow__ , __leaf__));
# 874 "/usr/include/unistd.h" 2 3 4







extern int gethostname (char *__name, size_t __len) __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1)));






extern int sethostname (const char *__name, size_t __len)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1))) ;



extern int sethostid (long int __id) __attribute__ ((__nothrow__ , __leaf__)) ;





extern int getdomainname (char *__name, size_t __len)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1))) ;
extern int setdomainname (const char *__name, size_t __len)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1))) ;





extern int vhangup (void) __attribute__ ((__nothrow__ , __leaf__));


extern int revoke (const char *__file) __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1))) ;







extern int profil (unsigned short int *__sample_buffer, size_t __size,
     size_t __offset, unsigned int __scale)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1)));





extern int acct (const char *__name) __attribute__ ((__nothrow__ , __leaf__));



extern char *getusershell (void) __attribute__ ((__nothrow__ , __leaf__));
extern void endusershell (void) __attribute__ ((__nothrow__ , __leaf__));
extern void setusershell (void) __attribute__ ((__nothrow__ , __leaf__));





extern int daemon (int __nochdir, int __noclose) __attribute__ ((__nothrow__ , __leaf__)) ;






extern int chroot (const char *__path) __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1))) ;



extern char *getpass (const char *__prompt) __attribute__ ((__nonnull__ (1)));







extern int fsync (int __fd);
# 971 "/usr/include/unistd.h" 3 4
extern long int gethostid (void);


extern void sync (void) __attribute__ ((__nothrow__ , __leaf__));





extern int getpagesize (void) __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__const__));




extern int getdtablesize (void) __attribute__ ((__nothrow__ , __leaf__));
# 995 "/usr/include/unistd.h" 3 4
extern int truncate (const char *__file, __off_t __length)
     __attribute__ ((__nothrow__ , __leaf__)) __attribute__ ((__nonnull__ (1))) ;
# 1018 "/usr/include/unistd.h" 3 4
extern int ftruncate (int __fd, __off_t __length) __attribute__ ((__nothrow__ , __leaf__)) ;
# 1039 "/usr/include/unistd.h" 3 4
extern int brk (void *__addr) __attribute__ ((__nothrow__ , __leaf__)) ;





extern void *sbrk (intptr_t __delta) __attribute__ ((__nothrow__ , __leaf__));
# 1060 "/usr/include/unistd.h" 3 4
extern long int syscall (long int __sysno, ...) __attribute__ ((__nothrow__ , __leaf__));
# 1083 "/usr/include/unistd.h" 3 4
extern int lockf (int __fd, int __cmd, __off_t __len) ;
# 1114 "/usr/include/unistd.h" 3 4
extern int fdatasync (int __fildes);
# 1163 "/usr/include/unistd.h" 3 4
int getentropy (void *__buffer, size_t __length) ;








# 476 "/usr/include/zconf.h" 2 3 4
# 35 "/usr/include/zlib.h" 2 3 4
# 81 "/usr/include/zlib.h" 3 4
typedef voidpf (*alloc_func) (voidpf opaque, uInt items, uInt size);
typedef void (*free_func) (voidpf opaque, voidpf address);

struct internal_state;

typedef struct z_stream_s {
    Bytef *next_in;
    uInt avail_in;
    uLong total_in;

    Bytef *next_out;
    uInt avail_out;
    uLong total_out;

    char *msg;
    struct internal_state *state;

    alloc_func zalloc;
    free_func zfree;
    voidpf opaque;

    int data_type;

    uLong adler;
    uLong reserved;
} z_stream;

typedef z_stream *z_streamp;





typedef struct gz_header_s {
    int text;
    uLong time;
    int xflags;
    int os;
    Bytef *extra;
    uInt extra_len;
    uInt extra_max;
    Bytef *name;
    uInt name_max;
    Bytef *comment;
    uInt comm_max;
    int hcrc;
    int done;

} gz_header;

typedef gz_header *gz_headerp;
# 220 "/usr/include/zlib.h" 3 4
extern const char * zlibVersion (void);
# 250 "/usr/include/zlib.h" 3 4
extern int deflate (z_streamp strm, int flush);
# 363 "/usr/include/zlib.h" 3 4
extern int deflateEnd (z_streamp strm);
# 400 "/usr/include/zlib.h" 3 4
extern int inflate (z_streamp strm, int flush);
# 520 "/usr/include/zlib.h" 3 4
extern int inflateEnd (z_streamp strm);
# 611 "/usr/include/zlib.h" 3 4
extern int deflateSetDictionary (z_streamp strm, const Bytef *dictionary, uInt dictLength)

                                                               ;
# 655 "/usr/include/zlib.h" 3 4
extern int deflateGetDictionary (z_streamp strm, Bytef *dictionary, uInt *dictLength)

                                                                ;
# 677 "/usr/include/zlib.h" 3 4
extern int deflateCopy (z_streamp dest, z_streamp source)
                                                      ;
# 695 "/usr/include/zlib.h" 3 4
extern int deflateReset (z_streamp strm);
# 706 "/usr/include/zlib.h" 3 4
extern int deflateParams (z_streamp strm, int level, int strategy)

                                                    ;
# 743 "/usr/include/zlib.h" 3 4
extern int deflateTune (z_streamp strm, int good_length, int max_lazy, int nice_length, int max_chain)



                                                   ;
# 760 "/usr/include/zlib.h" 3 4
extern uLong deflateBound (z_streamp strm, uLong sourceLen)
                                                        ;
# 775 "/usr/include/zlib.h" 3 4
extern int deflatePending (z_streamp strm, unsigned *pending, int *bits)

                                                  ;
# 790 "/usr/include/zlib.h" 3 4
extern int deflatePrime (z_streamp strm, int bits, int value)

                                                ;
# 807 "/usr/include/zlib.h" 3 4
extern int deflateSetHeader (z_streamp strm, gz_headerp head)
                                                          ;
# 884 "/usr/include/zlib.h" 3 4
extern int inflateSetDictionary (z_streamp strm, const Bytef *dictionary, uInt dictLength)

                                                               ;
# 907 "/usr/include/zlib.h" 3 4
extern int inflateGetDictionary (z_streamp strm, Bytef *dictionary, uInt *dictLength)

                                                                ;
# 922 "/usr/include/zlib.h" 3 4
extern int inflateSync (z_streamp strm);
# 941 "/usr/include/zlib.h" 3 4
extern int inflateCopy (z_streamp dest, z_streamp source)
                                                      ;
# 957 "/usr/include/zlib.h" 3 4
extern int inflateReset (z_streamp strm);
# 967 "/usr/include/zlib.h" 3 4
extern int inflateReset2 (z_streamp strm, int windowBits)
                                                      ;
# 981 "/usr/include/zlib.h" 3 4
extern int inflatePrime (z_streamp strm, int bits, int value)

                                                ;
# 1002 "/usr/include/zlib.h" 3 4
extern long inflateMark (z_streamp strm);
# 1030 "/usr/include/zlib.h" 3 4
extern int inflateGetHeader (z_streamp strm, gz_headerp head)
                                                          ;
# 1092 "/usr/include/zlib.h" 3 4
typedef unsigned (*in_func) (void *, unsigned char * *)
                                                                   ;
typedef int (*out_func) (void *, unsigned char *, unsigned);

extern int inflateBack (z_streamp strm, in_func in, void *in_desc, out_func out, void *out_desc)

                                                                      ;
# 1166 "/usr/include/zlib.h" 3 4
extern int inflateBackEnd (z_streamp strm);







extern uLong zlibCompileFlags (void);
# 1227 "/usr/include/zlib.h" 3 4
extern int compress (Bytef *dest, uLongf *destLen, const Bytef *source, uLong sourceLen)
                                                                       ;
# 1242 "/usr/include/zlib.h" 3 4
extern int compress2 (Bytef *dest, uLongf *destLen, const Bytef *source, uLong sourceLen, int level)

                                             ;
# 1258 "/usr/include/zlib.h" 3 4
extern uLong compressBound (uLong sourceLen);






extern int uncompress (Bytef *dest, uLongf *destLen, const Bytef *source, uLong sourceLen)
                                                                         ;
# 1283 "/usr/include/zlib.h" 3 4
extern int uncompress2 (Bytef *dest, uLongf *destLen, const Bytef *source, uLong *sourceLen)
                                                                           ;
# 1300 "/usr/include/zlib.h" 3 4
typedef struct gzFile_s *gzFile;
# 1340 "/usr/include/zlib.h" 3 4
extern gzFile gzdopen (int fd, const char *mode);
# 1363 "/usr/include/zlib.h" 3 4
extern int gzbuffer (gzFile file, unsigned size);
# 1379 "/usr/include/zlib.h" 3 4
extern int gzsetparams (gzFile file, int level, int strategy);
# 1390 "/usr/include/zlib.h" 3 4
extern int gzread (gzFile file, voidp buf, unsigned len);
# 1420 "/usr/include/zlib.h" 3 4
extern z_size_t gzfread (voidp buf, z_size_t size, z_size_t nitems, gzFile file)
                                                  ;
# 1446 "/usr/include/zlib.h" 3 4
extern int gzwrite (gzFile file, voidpc buf, unsigned len)
                                                          ;






extern z_size_t gzfwrite (voidpc buf, z_size_t size, z_size_t nitems, gzFile file)
                                                                    ;
# 1468 "/usr/include/zlib.h" 3 4
extern int gzprintf (gzFile file, const char *format, ...);
# 1483 "/usr/include/zlib.h" 3 4
extern int gzputs (gzFile file, const char *s);







extern char * gzgets (gzFile file, char *buf, int len);
# 1504 "/usr/include/zlib.h" 3 4
extern int gzputc (gzFile file, int c);





extern int gzgetc (gzFile file);
# 1519 "/usr/include/zlib.h" 3 4
extern int gzungetc (int c, gzFile file);
# 1531 "/usr/include/zlib.h" 3 4
extern int gzflush (gzFile file, int flush);
# 1566 "/usr/include/zlib.h" 3 4
extern int gzrewind (gzFile file);
# 1594 "/usr/include/zlib.h" 3 4
extern int gzeof (gzFile file);
# 1609 "/usr/include/zlib.h" 3 4
extern int gzdirect (gzFile file);
# 1630 "/usr/include/zlib.h" 3 4
extern int gzclose (gzFile file);
# 1643 "/usr/include/zlib.h" 3 4
extern int gzclose_r (gzFile file);
extern int gzclose_w (gzFile file);
# 1655 "/usr/include/zlib.h" 3 4
extern const char * gzerror (gzFile file, int *errnum);
# 1671 "/usr/include/zlib.h" 3 4
extern void gzclearerr (gzFile file);
# 1688 "/usr/include/zlib.h" 3 4
extern uLong adler32 (uLong adler, const Bytef *buf, uInt len);
# 1707 "/usr/include/zlib.h" 3 4
extern uLong adler32_z (uLong adler, const Bytef *buf, z_size_t len)
                                                  ;
# 1725 "/usr/include/zlib.h" 3 4
extern uLong crc32 (uLong crc, const Bytef *buf, uInt len);
# 1742 "/usr/include/zlib.h" 3 4
extern uLong crc32_z (uLong adler, const Bytef *buf, z_size_t len)
                                                ;
# 1764 "/usr/include/zlib.h" 3 4
extern int deflateInit_ (z_streamp strm, int level, const char *version, int stream_size)
                                                                           ;
extern int inflateInit_ (z_streamp strm, const char *version, int stream_size)
                                                                           ;
extern int deflateInit2_ (z_streamp strm, int level, int method, int windowBits, int memLevel, int strategy, const char *version, int stream_size)


                                                       ;
extern int inflateInit2_ (z_streamp strm, int windowBits, const char *version, int stream_size)
                                                                            ;
extern int inflateBackInit_ (z_streamp strm, int windowBits, unsigned char *window, const char *version, int stream_size)


                                                          ;
# 1817 "/usr/include/zlib.h" 3 4
struct gzFile_s {
    unsigned have;
    unsigned char *next;
    off_t pos;
};
extern int gzgetc_ (gzFile file);
# 1872 "/usr/include/zlib.h" 3 4
   extern gzFile gzopen (const char *, const char *);
   extern off_t gzseek (gzFile, off_t, int);
   extern off_t gztell (gzFile);
   extern off_t gzoffset (gzFile);
   extern uLong adler32_combine (uLong, uLong, off_t);
   extern uLong crc32_combine (uLong, uLong, off_t);
# 1888 "/usr/include/zlib.h" 3 4
extern const char * zError (int);
extern int inflateSyncPoint (z_streamp);
extern const z_crc_t * get_crc_table (void);
extern int inflateUndermine (z_streamp, int);
extern int inflateValidate (z_streamp, int);
extern unsigned long inflateCodesUsed (z_streamp);
extern int inflateResetKeep (z_streamp);
extern int deflateResetKeep (z_streamp);






extern int gzvprintf (gzFile file, const char *format, va_list va)

                                                              ;
# 34 "pnglibconf.dfn" 2
# 60 "pnglibconf.dfn"
 
# 60 "pnglibconf.dfn"
PNG_DFN "/* options */"
PNG_DFN_START_SORT 2
# 89 "pnglibconf.dfn"
 PNG_DFN "#define PNG_UNKNOWN_CHUNKS_SUPPORTED"
# 122 "pnglibconf.dfn"
 PNG_DFN "#define PNG_INFO_IMAGE_SUPPORTED"
# 158 "pnglibconf.dfn"
 PNG_DFN "#define PNG_SET_UNKNOWN_CHUNKS_SUPPORTED"
# 191 "pnglibconf.dfn"
 PNG_DFN "#define PNG_GET_PALETTE_MAX_SUPPORTED"
# 224 "pnglibconf.dfn"
 PNG_DFN "#define PNG_POINTER_INDEXING_SUPPORTED"
# 257 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WARNINGS_SUPPORTED"
# 290 "pnglibconf.dfn"
 PNG_DFN "#define PNG_FLOATING_ARITHMETIC_SUPPORTED"
# 328 "pnglibconf.dfn"
 PNG_DFN "/*#undef PNG_POWERPC_VSX_CHECK_SUPPORTED*/"
# 357 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_SUPPORTED"
# 393 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_INTERLACING_SUPPORTED"
# 429 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_16BIT_SUPPORTED"
# 462 "pnglibconf.dfn"
 PNG_DFN "#define PNG_EASY_ACCESS_SUPPORTED"
# 498 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_CUSTOMIZE_COMPRESSION_SUPPORTED"
# 531 "pnglibconf.dfn"
 PNG_DFN "#define PNG_CHECK_FOR_INVALID_INDEX_SUPPORTED"
# 567 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_WEIGHTED_FILTER_SUPPORTED"
# 606 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_UNKNOWN_CHUNKS_SUPPORTED"
# 644 "pnglibconf.dfn"
 PNG_DFN "/*#undef PNG_POWERPC_VSX_API_SUPPORTED*/"
# 673 "pnglibconf.dfn"
 PNG_DFN "#define PNG_BUILD_GRAYSCALE_PALETTE_SUPPORTED"
# 706 "pnglibconf.dfn"
 PNG_DFN "#define PNG_FIXED_POINT_SUPPORTED"
# 738 "pnglibconf.dfn"
 PNG_DFN "/*#undef PNG_ERROR_NUMBERS_SUPPORTED*/"
# 767 "pnglibconf.dfn"
 PNG_DFN "#define PNG_ERROR_TEXT_SUPPORTED"
# 800 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_SUPPORTED"
# 833 "pnglibconf.dfn"
 PNG_DFN "#define PNG_BENIGN_ERRORS_SUPPORTED"
# 866 "pnglibconf.dfn"
 PNG_DFN "#define PNG_SETJMP_SUPPORTED"
# 899 "pnglibconf.dfn"
 PNG_DFN "#define PNG_TIME_RFC1123_SUPPORTED"
# 935 "pnglibconf.dfn"
 PNG_DFN "#define PNG_BENIGN_READ_ERRORS_SUPPORTED"
# 971 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_FLUSH_SUPPORTED"
# 1004 "pnglibconf.dfn"
 PNG_DFN "#define PNG_MNG_FEATURES_SUPPORTED"
# 1037 "pnglibconf.dfn"
 PNG_DFN "#define PNG_ALIGNED_MEMORY_SUPPORTED"
# 1070 "pnglibconf.dfn"
 PNG_DFN "#define PNG_FLOATING_POINT_SUPPORTED"
# 1106 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_OPTIMIZE_CMF_SUPPORTED"
# 1139 "pnglibconf.dfn"
 PNG_DFN "#define PNG_INCH_CONVERSIONS_SUPPORTED"
# 1172 "pnglibconf.dfn"
 PNG_DFN "#define PNG_STDIO_SUPPORTED"
# 1205 "pnglibconf.dfn"
 PNG_DFN "#define PNG_USER_MEM_SUPPORTED"
# 1238 "pnglibconf.dfn"
 PNG_DFN "#define PNG_IO_STATE_SUPPORTED"
# 1279 "pnglibconf.dfn"
 PNG_DFN "/*#undef PNG_ARM_NEON_CHECK_SUPPORTED*/"
# 1311 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_ANCILLARY_CHUNKS_SUPPORTED"
# 1342 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_INT_FUNCTIONS_SUPPORTED"
# 1378 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_ANCILLARY_CHUNKS_SUPPORTED"
# 1414 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_FILTER_SUPPORTED"
# 1450 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_zTXt_SUPPORTED"
# 1486 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_iCCP_SUPPORTED"
# 1522 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_TRANSFORMS_SUPPORTED"
# 1558 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_bKGD_SUPPORTED"
# 1594 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_sCAL_SUPPORTED"
# 1630 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_hIST_SUPPORTED"
# 1666 "pnglibconf.dfn"
 PNG_DFN "#define PNG_HANDLE_AS_UNKNOWN_SUPPORTED"
# 1702 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_OPT_PLTE_SUPPORTED"
# 1738 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_gAMA_SUPPORTED"
# 1774 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_GRAY_TO_RGB_SUPPORTED"
# 1810 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_pCAL_SUPPORTED"
# 1846 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_INVERT_ALPHA_SUPPORTED"
# 1882 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_TRANSFORMS_SUPPORTED"
# 1924 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_CHECK_FOR_INVALID_INDEX_SUPPORTED"
# 1960 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_sBIT_SUPPORTED"
# 1996 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_CUSTOMIZE_ZTXT_COMPRESSION_SUPPORTED"
# 2032 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_PACK_SUPPORTED"
# 2068 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_iTXt_SUPPORTED"
# 2107 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_SWAP_SUPPORTED"
# 2143 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_cHRM_SUPPORTED"
# 2179 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_STRIP_16_TO_8_SUPPORTED"
# 2215 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_tIME_SUPPORTED"
# 2246 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_INTERLACING_SUPPORTED"
# 2282 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_tRNS_SUPPORTED"
# 2318 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_pHYs_SUPPORTED"
# 2354 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_INVERT_SUPPORTED"
# 2390 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_sRGB_SUPPORTED"
# 2426 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_oFFs_SUPPORTED"
# 2462 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_FILLER_SUPPORTED"
# 2498 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_TEXT_SUPPORTED"
# 2534 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_SHIFT_SUPPORTED"
# 2570 "pnglibconf.dfn"
 PNG_DFN "#define PNG_PROGRESSIVE_READ_SUPPORTED"
# 2606 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_SHIFT_SUPPORTED"
# 2642 "pnglibconf.dfn"
 PNG_DFN "#define PNG_CONVERT_tIME_SUPPORTED"
# 2678 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_USER_TRANSFORM_SUPPORTED"
# 2714 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_INT_FUNCTIONS_SUPPORTED"
# 2753 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_USER_CHUNKS_SUPPORTED"
# 2789 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_hIST_SUPPORTED"
# 2825 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_16BIT_SUPPORTED"
# 2861 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_SWAP_ALPHA_SUPPORTED"
# 2897 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_COMPOSITE_NODIV_SUPPORTED"
# 2933 "pnglibconf.dfn"
 PNG_DFN "#define PNG_SEQUENTIAL_READ_SUPPORTED"
# 2969 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_QUANTIZE_SUPPORTED"
# 3005 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_zTXt_SUPPORTED"
# 3041 "pnglibconf.dfn"
 PNG_DFN "#define PNG_USER_LIMITS_SUPPORTED"
# 3077 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_iCCP_SUPPORTED"
# 3113 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_STRIP_ALPHA_SUPPORTED"
# 3149 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_PACKSWAP_SUPPORTED"
# 3185 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_sRGB_SUPPORTED"
# 3227 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_CHECK_FOR_INVALID_INDEX_SUPPORTED"
# 3266 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_tEXt_SUPPORTED"
# 3302 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_gAMA_SUPPORTED"
# 3338 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_pCAL_SUPPORTED"
# 3377 "pnglibconf.dfn"
 PNG_DFN "#define PNG_SAVE_UNKNOWN_CHUNKS_SUPPORTED"
# 3413 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_EXPAND_SUPPORTED"
# 3449 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_sPLT_SUPPORTED"
# 3485 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_iTXt_SUPPORTED"
# 3524 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_SWAP_SUPPORTED"
# 3559 "pnglibconf.dfn"
 PNG_DFN "/*#undef PNG_BENIGN_WRITE_ERRORS_SUPPORTED*/"
# 3591 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_tIME_SUPPORTED"
# 3627 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_pHYs_SUPPORTED"
# 3663 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_SWAP_ALPHA_SUPPORTED"
# 3704 "pnglibconf.dfn"
 PNG_DFN "/*#undef PNG_ARM_NEON_API_SUPPORTED*/"
# 3736 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_SCALE_16_TO_8_SUPPORTED"
# 3772 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_TEXT_SUPPORTED"
# 3808 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_BGR_SUPPORTED"
# 3839 "pnglibconf.dfn"
 PNG_DFN "#define PNG_USER_CHUNKS_SUPPORTED"
# 3873 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_GET_PALETTE_MAX_SUPPORTED"
# 3909 "pnglibconf.dfn"
 PNG_DFN "#define PNG_CONSOLE_IO_SUPPORTED"
# 3952 "pnglibconf.dfn"
 PNG_DFN "#define PNG_SET_OPTION_SUPPORTED"
# 3989 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_COMPRESSED_TEXT_SUPPORTED"
# 4026 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_UNKNOWN_CHUNKS_SUPPORTED"
# 4062 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_PACK_SUPPORTED"
# 4098 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_FILLER_SUPPORTED"
# 4134 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_bKGD_SUPPORTED"
# 4170 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_tRNS_SUPPORTED"
# 4206 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_sPLT_SUPPORTED"
# 4242 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_sCAL_SUPPORTED"
# 4278 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_oFFs_SUPPORTED"
# 4317 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_tEXt_SUPPORTED"
# 4353 "pnglibconf.dfn"
 PNG_DFN "#define PNG_SET_USER_LIMITS_SUPPORTED"
# 4389 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_sBIT_SUPPORTED"
# 4425 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_INVERT_SUPPORTED"
# 4461 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_cHRM_SUPPORTED"
# 4495 "pnglibconf.dfn"
 PNG_DFN "#define PNG_16BIT_SUPPORTED"
# 4531 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_USER_TRANSFORM_SUPPORTED"
# 4567 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_BGR_SUPPORTED"
# 4603 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_PACKSWAP_SUPPORTED"
# 4639 "pnglibconf.dfn"
 PNG_DFN "#define PNG_WRITE_INVERT_ALPHA_SUPPORTED"
# 4673 "pnglibconf.dfn"
 PNG_DFN "#define PNG_sCAL_SUPPORTED"
# 4715 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_GAMMA_SUPPORTED"
# 4759 "pnglibconf.dfn"
 PNG_DFN "#define PNG_USER_TRANSFORM_INFO_SUPPORTED"
# 4793 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_GET_PALETTE_MAX_SUPPORTED"
# 4827 "pnglibconf.dfn"
 PNG_DFN "#define PNG_sBIT_SUPPORTED"
# 4864 "pnglibconf.dfn"
 PNG_DFN "#define PNG_STORE_UNKNOWN_CHUNKS_SUPPORTED"
# 4921 "pnglibconf.dfn"
 PNG_DFN "#define PNG_SIMPLIFIED_WRITE_SUPPORTED"
# 4955 "pnglibconf.dfn"
 PNG_DFN "#define PNG_cHRM_SUPPORTED"
# 4994 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_ALPHA_MODE_SUPPORTED"
# 5028 "pnglibconf.dfn"
 PNG_DFN "#define PNG_bKGD_SUPPORTED"
# 5062 "pnglibconf.dfn"
 PNG_DFN "#define PNG_tRNS_SUPPORTED"
# 5096 "pnglibconf.dfn"
 PNG_DFN "#define PNG_oFFs_SUPPORTED"
# 5135 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_RGB_TO_GRAY_SUPPORTED"
# 5177 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_EXPAND_16_SUPPORTED"
# 5221 "pnglibconf.dfn"
 PNG_DFN "#define PNG_USER_TRANSFORM_PTR_SUPPORTED"
# 5258 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_COMPRESSED_TEXT_SUPPORTED"
# 5292 "pnglibconf.dfn"
 PNG_DFN "#define PNG_hIST_SUPPORTED"
# 5331 "pnglibconf.dfn"
 PNG_DFN "#define PNG_SIMPLIFIED_WRITE_AFIRST_SUPPORTED"
# 5365 "pnglibconf.dfn"
 PNG_DFN "#define PNG_zTXt_SUPPORTED"
# 5399 "pnglibconf.dfn"
 PNG_DFN "#define PNG_iCCP_SUPPORTED"
# 5441 "pnglibconf.dfn"
 PNG_DFN "#define PNG_READ_BACKGROUND_SUPPORTED"
# 5475 "pnglibconf.dfn"
 PNG_DFN "#define PNG_sRGB_SUPPORTED"
# 5509 "pnglibconf.dfn"
 PNG_DFN "#define PNG_gAMA_SUPPORTED"
# 5543 "pnglibconf.dfn"
 PNG_DFN "#define PNG_pCAL_SUPPORTED"
# 5577 "pnglibconf.dfn"
 PNG_DFN "#define PNG_iTXt_SUPPORTED"
# 5611 "pnglibconf.dfn"
 PNG_DFN "#define PNG_tIME_SUPPORTED"
# 5645 "pnglibconf.dfn"
 PNG_DFN "#define PNG_pHYs_SUPPORTED"
# 5691 "pnglibconf.dfn"
 PNG_DFN "#define PNG_TEXT_SUPPORTED"
# 5728 "pnglibconf.dfn"
 PNG_DFN "#define PNG_SAVE_INT_32_SUPPORTED"
# 5762 "pnglibconf.dfn"
 PNG_DFN "#define PNG_sPLT_SUPPORTED"
# 5796 "pnglibconf.dfn"
 PNG_DFN "#define PNG_tEXt_SUPPORTED"
# 5845 "pnglibconf.dfn"
 PNG_DFN "#define PNG_COLORSPACE_SUPPORTED"
# 5884 "pnglibconf.dfn"
 PNG_DFN "#define PNG_SIMPLIFIED_WRITE_STDIO_SUPPORTED"
# 5986 "pnglibconf.dfn"
 PNG_DFN "#define PNG_SIMPLIFIED_READ_SUPPORTED"
# 6025 "pnglibconf.dfn"
 PNG_DFN "#define PNG_SIMPLIFIED_WRITE_BGR_SUPPORTED"
# 6064 "pnglibconf.dfn"
 PNG_DFN "#define PNG_SIMPLIFIED_READ_AFIRST_SUPPORTED"
# 6103 "pnglibconf.dfn"
 PNG_DFN "#define PNG_SIMPLIFIED_READ_BGR_SUPPORTED"
# 6152 "pnglibconf.dfn"
 PNG_DFN "#define PNG_GAMMA_SUPPORTED"
# 6186 "pnglibconf.dfn"
 PNG_DFN "#define PNG_FORMAT_AFIRST_SUPPORTED"
# 6220 "pnglibconf.dfn"
 PNG_DFN "#define PNG_FORMAT_BGR_SUPPORTED"





PNG_DFN_END_SORT
 PNG_DFN "/* end of options */"


 PNG_DFN "/* settings */"
PNG_DFN_START_SORT 2
# 6243 "pnglibconf.dfn"
 PNG_DFN "#define PNG_MAX_GAMMA_8 11"
# 6282 "pnglibconf.dfn"
 PNG_DFN "#define PNG_QUANTIZE_RED_BITS 5"
# 6297 "pnglibconf.dfn"
 PNG_DFN "#define PNG_USER_WIDTH_MAX 1000000"
# 6312 "pnglibconf.dfn"
 PNG_DFN "#define PNG_Z_DEFAULT_COMPRESSION @" 
# 6312 "pnglibconf.dfn" 3 4
                                              (-1) 
# 6312 "pnglibconf.dfn"
                                                                    "@"
# 6363 "pnglibconf.dfn"
 PNG_DFN "#define PNG_INFLATE_BUF_SIZE 1024"
# 6378 "pnglibconf.dfn"
 PNG_DFN "#define PNG_QUANTIZE_GREEN_BITS 5"
# 6393 "pnglibconf.dfn"
 PNG_DFN "#define PNG_Z_DEFAULT_NOFILTER_STRATEGY @" 
# 6393 "pnglibconf.dfn" 3 4
                                                    0 
# 6393 "pnglibconf.dfn"
                                                                       "@"
# 6408 "pnglibconf.dfn"
 PNG_DFN "#define PNG_API_RULE 0"
# 6435 "pnglibconf.dfn"
 PNG_DFN "#define PNG_IDAT_READ_SIZE PNG_ZBUF_SIZE"
# 6450 "pnglibconf.dfn"
 PNG_DFN "#define PNG_QUANTIZE_BLUE_BITS 5"
# 6477 "pnglibconf.dfn"
 PNG_DFN "#define PNG_USER_CHUNK_CACHE_MAX 1000"
# 6492 "pnglibconf.dfn"
 PNG_DFN "#define PNG_LINKAGE_FUNCTION extern"
# 6507 "pnglibconf.dfn"
 PNG_DFN "#define PNG_ZLIB_VERNUM @" 
# 6507 "pnglibconf.dfn" 3 4
                                    0x12b0 
# 6507 "pnglibconf.dfn"
                                                "@"
# 6522 "pnglibconf.dfn"
 PNG_DFN "#define PNG_USER_HEIGHT_MAX 1000000"
# 6537 "pnglibconf.dfn"
 PNG_DFN "#define PNG_TEXT_Z_DEFAULT_STRATEGY @" 
# 6537 "pnglibconf.dfn" 3 4
                                                0 
# 6537 "pnglibconf.dfn"
                                                                   "@"
# 6552 "pnglibconf.dfn"
 PNG_DFN "#define PNG_sCAL_PRECISION 5"
# 6567 "pnglibconf.dfn"
 PNG_DFN "#define PNG_LINKAGE_API extern"
# 6582 "pnglibconf.dfn"
 PNG_DFN "#define PNG_TEXT_Z_DEFAULT_COMPRESSION @" 
# 6582 "pnglibconf.dfn" 3 4
                                                   (-1) 
# 6582 "pnglibconf.dfn"
                                                                         "@"
# 6609 "pnglibconf.dfn"
 PNG_DFN "#define PNG_LINKAGE_DATA extern"
# 6624 "pnglibconf.dfn"
 PNG_DFN "#define PNG_LINKAGE_CALLBACK extern"
# 6639 "pnglibconf.dfn"
 PNG_DFN "#define PNG_USER_CHUNK_MALLOC_MAX 8000000"
# 6654 "pnglibconf.dfn"
 PNG_DFN "#define PNG_DEFAULT_READ_MACROS 1"
# 6681 "pnglibconf.dfn"
 PNG_DFN "#define PNG_ZBUF_SIZE 8192"
# 6696 "pnglibconf.dfn"
 PNG_DFN "#define PNG_Z_DEFAULT_STRATEGY @" 
# 6696 "pnglibconf.dfn" 3 4
                                           1 
# 6696 "pnglibconf.dfn"
                                                      "@"
# 6711 "pnglibconf.dfn"
 PNG_DFN "#define PNG_GAMMA_THRESHOLD_FIXED 5000"
# 6726 "pnglibconf.dfn"
 PNG_DFN "#define PNG_sRGB_PROFILE_CHECKS 2"



PNG_DFN_END_SORT
 PNG_DFN "/* end of settings */"
 PNG_DFN "#endif /* PNGLCONF_H */"
# 1 "pnglibconf.c" 2
