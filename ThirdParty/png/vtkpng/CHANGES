#if 0
CHANGES - changes for libpng

version 0.1 [March 29, 1995]
  initial work-in-progress release

version 0.2 [April 1, 1995]
  added reader into png.h
  fixed small problems in stub file

version 0.3 [April 8, 1995]
  added pull reader
  split up pngwrite.c to several files
  added pnglib.txt
  added example.c
  cleaned up writer, adding a few new transformations
  fixed some bugs in writer
  interfaced with zlib 0.5
  added K&R support
  added check for 64 KB blocks for 16 bit machines

version 0.4 [April 26, 1995]
  cleaned up code and commented code
  simplified time handling into png_time
  created png_color_16 and png_color_8 to handle color needs
  cleaned up color type defines
  fixed various bugs
  made various names more consistent
  interfaced with zlib 0.71
  cleaned up zTXt reader and writer (using zlib's Reset functions)
  split transformations into pngrtran.c and pngwtran.c

version 0.5 [April 30, 1995]
  interfaced with zlib 0.8
  fixed many reading and writing bugs
  saved using 3 spaces instead of tabs

version 0.6 [May 1, 1995]
  first beta release
  added png_large_malloc() and png_large_free()
  added png_size_t
  cleaned up some compiler warnings
  added png_start_read_image()

version 0.7 [June 24, 1995]
  cleaned up lots of bugs
  finished dithering and other stuff
  added test program
  changed name from pnglib to libpng

version 0.71 [June 26, 1995]
  changed pngtest.png for zlib 0.93
  fixed error in libpng.txt and example.c

version 0.8 [August 20, 1995]
  cleaned up some bugs
  added png_set_filler()
  split up pngstub.c into pngmem.c, pngio.c, and pngerror.c
  added #define's to remove unwanted code
  moved png_info_init() to png.c
  added old_size into png_realloc()
  added functions to manually set filtering and compression info
  changed compression parameters based on image type
  optimized filter selection code
  added version info
  changed external functions passing floats to doubles (k&r problems?)
  put all the configurable stuff in pngconf.h
  enabled png_set_shift to work with paletted images on read
  added png_read_update_info() - updates info structure with transformations

Version 0.81 [August, 1995]
  incorporated Tim Wegner's medium model code (thanks, Tim)

Version 0.82 [September, 1995]
  [unspecified changes]

Version 0.85 [December, 1995]
  added more medium model code (almost everything's a far)
  added i/o, error, and memory callback functions
  fixed some bugs (16-bit, 4-bit interlaced, etc.)
  added first run progressive reader (barely tested)

Version 0.86 [January, 1996]
  fixed bugs
  improved documentation

Version 0.87 [January, 1996]
  fixed medium model bugs
  fixed other bugs introduced in 0.85 and 0.86
  added some minor documentation

Version 0.88 [January, 1996]
  fixed progressive bugs
  replaced tabs with spaces
  cleaned up documentation
  added callbacks for read/write and warning/error functions

Version 0.89 [June 5, 1996]
  Added new initialization API to make libpng work better with shared libs
    we now have png_create_read_struct(), png_create_write_struct(),
    png_create_info_struct(), png_destroy_read_struct(), and
    png_destroy_write_struct() instead of the separate calls to
    malloc and png_read_init(), png_info_init(), and png_write_init()
  Changed warning/error callback functions to fix bug - this means you
    should use the new initialization API if you were using the old
    png_set_message_fn() calls, and that the old API no longer exists
    so that people are aware that they need to change their code
  Changed filter selection API to allow selection of multiple filters
    since it didn't work in previous versions of libpng anyways
  Optimized filter selection code
  Fixed png_set_background() to allow using an arbitrary RGB color for
    paletted images
  Fixed gamma and background correction for paletted images, so
    png_correct_palette is not needed unless you are correcting an
    external palette (you will need to #define PNG_CORRECT_PALETTE_SUPPORTED
    in pngconf.h) - if nobody uses this, it may disappear in the future.
  Fixed bug with Borland 64K memory allocation (Alexander Lehmann)
  Fixed bug in interlace handling (Smarasderagd, I think)
  Added more error checking for writing and image to reduce invalid files
  Separated read and write functions so that they won't both be linked
    into a binary when only reading or writing functionality is used
  New pngtest image also has interlacing and zTXt
  Updated documentation to reflect new API

Version 0.89c [June 17, 1996]
  Bug fixes.

Version 0.90 [January, 1997]
  Made CRC errors/warnings on critical and ancillary chunks configurable
  libpng will use the zlib CRC routines by (compile-time) default
  Changed DOS small/medium model memory support - needs zlib 1.04 (Tim Wegner)
  Added external C++ wrapper statements to png.h (Gilles Dauphin)
  Allow PNG file to be read when some or all of file signature has already
    been read from the beginning of the stream.  ****This affects the size
    of info_struct and invalidates all programs that use a shared libpng****
  Fixed png_filler() declarations
  Fixed? background color conversions
  Fixed order of error function pointers to match documentation
  Current chunk name is now available in png_struct to reduce the number
    of nearly identical error messages (will simplify multi-lingual
    support when available)
  Try to get ready for unknown-chunk callback functions:
    - previously read critical chunks are flagged, so the chunk handling
      routines can determine if the chunk is in the right place
    - all chunk handling routines have the same prototypes, so we will
      be able to handle all chunks via a callback mechanism
  Try to fix Linux "setjmp" buffer size problems
  Removed png_large_malloc, png_large_free, and png_realloc functions.

Version 0.95 [March, 1997]
  Fixed bug in pngwutil.c allocating "up_row" twice and "avg_row" never
  Fixed bug in PNG file signature compares when start != 0
  Changed parameter type of png_set_filler(...filler...) from png_byte
    to png_uint_32
  Added test for MACOS to ensure that both math.h and fp.h are not #included
  Added macros for libpng to be compiled as a Windows DLL (Andreas Kupries)
  Added "packswap" transformation, which changes the endianness of
    packed-pixel bytes (Kevin Bracey)
  Added "strip_alpha" transformation, which removes the alpha channel of
    input images without using it (not necessarily a good idea)
  Added "swap_alpha" transformation, which puts the alpha channel in front
    of the color bytes instead of after
  Removed all implicit variable tests which assume NULL == 0 (I think)
  Changed several variables to "png_size_t" to show 16/32-bit limitations
  Added new pCAL chunk read/write support
  Added experimental filter selection weighting (Greg Roelofs)
  Removed old png_set_rgbx() and png_set_xrgb() functions that have been
    obsolete for about 2 years now (use png_set_filler() instead)
  Added macros to read 16- and 32-bit ints directly from buffer, to be
    used only on those systems that support it (namely PowerPC and 680x0)
    With some testing, this may become the default for MACOS/PPC systems.
  Only calculate CRC on data if we are going to use it
  Added macros for zTXt compression type PNG_zTXt_COMPRESSION_???
  Added macros for simple libpng debugging output selectable at compile time
  Removed PNG_READ_END_MODE in progressive reader (Smarasderagd)
  More description of info_struct in libpng.txt and png.h
  More instructions in example.c
  More chunk types tested in pngtest.c
  Renamed pngrcb.c to pngset.c, and all png_read_<chunk> functions to be
    png_set_<chunk>.  We now have corresponding png_get_<chunk>
    functions in pngget.c to get information in info_ptr.  This isolates
    the application from the internal organization of png_info_struct
    (good for shared library implementations).

Version 0.96 [May, 1997]
  Fixed serious bug with < 8bpp images introduced in 0.95
  Fixed 256-color transparency bug (Greg Roelofs)
  Fixed up documentation (Greg Roelofs, Laszlo Nyul)
  Fixed "error" in pngconf.h for Linux setjmp() behavior
  Fixed DOS medium model support (Tim Wegner)
  Fixed png_check_keyword() for case with error in static string text
  Added read of CRC after IEND chunk for embedded PNGs (Laszlo Nyul)
  Added typecasts to quiet compiler errors
  Added more debugging info

Version 0.97 [January, 1998]
  Removed PNG_USE_OWN_CRC capability
  Relocated png_set_crc_action from pngrutil.c to pngrtran.c
  Fixed typecasts of "new_key", etc. (Andreas Dilger)
  Added RFC 1152 [sic] date support
  Fixed bug in gamma handling of 4-bit grayscale
  Added 2-bit grayscale gamma handling (Glenn R-P)
  Added more typecasts. 65536L becomes (png_uint_32)65536L, etc. (Glenn R-P)
  Minor corrections in libpng.txt
  Added simple sRGB support (Glenn R-P)
  Easier conditional compiling, e.g.,
    define PNG_READ/WRITE_NOT_FULLY_SUPPORTED;
    all configurable options can be selected from command-line instead
    of having to edit pngconf.h (Glenn R-P)
  Fixed memory leak in pngwrite.c (free info_ptr->text) (Glenn R-P)
  Added more conditions for png_do_background, to avoid changing
    black pixels to background when a background is supplied and
    no pixels are transparent
  Repaired PNG_NO_STDIO behavior
  Tested NODIV support and made it default behavior (Greg Roelofs)
  Added "-m" option and PNGTEST_DEBUG_MEMORY to pngtest (John Bowler)
  Regularized version numbering scheme and bumped shared-library major
    version number to 2 to avoid problems with libpng 0.89 apps
    (Greg Roelofs)

Version 0.98 [January, 1998]
  Cleaned up some typos in libpng.txt and in code documentation
  Fixed memory leaks in pCAL chunk processing (Glenn R-P and John Bowler)
  Cosmetic change "display_gamma" to "screen_gamma" in pngrtran.c
  Changed recommendation about file_gamma for PC images to .51 from .45,
    in example.c and libpng.txt, added comments to distinguish between
    screen_gamma, viewing_gamma, and display_gamma.
  Changed all references to RFC1152 to read RFC1123 and changed the
    PNG_TIME_RFC1152_SUPPORTED macro to PNG_TIME_RFC1123_SUPPORTED
  Added png_invert_alpha capability (Glenn R-P -- suggestion by Jon Vincent)
  Changed srgb_intent from png_byte to int to avoid compiler bugs

Version 0.99 [January 30, 1998]
  Free info_ptr->text instead of end_info_ptr->text in pngread.c (John Bowler)
  Fixed a longstanding "packswap" bug in pngtrans.c
  Fixed some inconsistencies in pngconf.h that prevented compiling with
    PNG_READ_GAMMA_SUPPORTED and PNG_READ_hIST_SUPPORTED undefined
  Fixed some typos and made other minor rearrangement of libpng.txt (Andreas)
  Changed recommendation about file_gamma for PC images to .50 from .51 in
    example.c and libpng.txt, and changed file_gamma for sRGB images to .45
  Added a number of functions to access information from the png structure
    png_get_image_height(), etc. (Glenn R-P, suggestion by Brad Pettit)
  Added TARGET_MACOS similar to zlib-1.0.8
  Define PNG_ALWAYS_EXTERN when __MWERKS__ && WIN32 are defined
  Added type casting to all png_malloc() function calls

Version 0.99a [January 31, 1998]
  Added type casts and parentheses to all returns that return a value.(Tim W.)

Version 0.99b [February 4, 1998]
  Added type cast png_uint_32 on malloc function calls where needed.
  Changed type of num_hist from png_uint_32 to int (same as num_palette).
  Added checks for rowbytes overflow, in case png_size_t is less than 32 bits.
  Renamed makefile.elf to makefile.lnx.

Version 0.99c [February 7, 1998]
  More type casting.  Removed erroneous overflow test in pngmem.c.
  Added png_buffered_memcpy() and png_buffered_memset(), apply them to rowbytes.
  Added UNIX manual pages libpng.3 (incorporating libpng.txt) and  png.5.

Version 0.99d [February 11, 1998]
  Renamed "far_to_near()" "png_far_to_near()"
  Revised libpng.3
  Version 99c "buffered" operations didn't work as intended.  Replaced them
    with png_memcpy_check() and png_memset_check().
  Added many "if (png_ptr == NULL) return" to quell compiler warnings about
    unused png_ptr, mostly in pngget.c and pngset.c.
  Check for overlength tRNS chunk present when indexed-color PLTE is read.
  Cleaned up spelling errors in libpng.3/libpng.txt
  Corrected a problem with png_get_tRNS() which returned undefined trans array

Version 0.99e [February 28, 1998]
  Corrected png_get_tRNS() again.
  Add parentheses for easier reading of pngget.c, fixed "||" should be "&&".
  Touched up example.c to make more of it compileable, although the entire
    file still can't be compiled (Willem van Schaik)
  Fixed a bug in png_do_shift() (Bryan Tsai)
  Added a space in png.h prototype for png_write_chunk_start()
  Replaced pngtest.png with one created with zlib 1.1.1
  Changed pngtest to report PASS even when file size is different (Jean-loup G.)
  Corrected some logic errors in png_do_invert_alpha() (Chris Patterson)

Version 0.99f [March 5, 1998]
  Corrected a bug in pngpread() introduced in version 99c (Kevin Bracey)
  Moved makefiles into a "scripts" directory, and added INSTALL instruction file
  Added makefile.os2 and pngos2.def (A. Zabolotny) and makefile.s2x (W. Sebok)
  Added pointers to "note on libpng versions" in makefile.lnx and README
  Added row callback feature when reading and writing nonprogressive rows
    and added a test of this feature in pngtest.c
  Added user transform callbacks, with test of the feature in pngtest.c

Version 0.99g [March 6, 1998, morning]
  Minor changes to pngtest.c to suppress compiler warnings.
  Removed "beta" language from documentation.

Version 0.99h [March 6, 1998, evening]
  Minor changes to previous minor changes to pngtest.c
  Changed PNG_READ_NOT_FULLY_SUPPORTED to PNG_READ_TRANSFORMS_NOT_SUPPORTED
    and added PNG_PROGRESSIVE_READ_NOT_SUPPORTED macro
  Added user transform capability

Version 1.00 [March 7, 1998]
  Changed several typedefs in pngrutil.c
  Added makefile.wat (Pawel Mrochen), updated makefile.tc3 (Willem van Schaik)
  Replaced "while(1)" with "for(;;)"
  Added PNGARG() to prototypes in pngtest.c and removed some prototypes
  Updated some of the makefiles (Tom Lane)
  Changed some typedefs (s_start, etc.) in pngrutil.c
  Fixed dimensions of "short_months" array in pngwrite.c
  Replaced ansi2knr.c with the one from jpeg-v6

Version 1.0.0 [March 8, 1998]
  Changed name from 1.00 to 1.0.0 (Adam Costello)
  Added smakefile.ppc (with SCOPTIONS.ppc) for Amiga PPC (Andreas Kleinert)

Version 1.0.0a [March 9, 1998]
  Fixed three bugs in pngrtran.c to make gamma+background handling consistent
    (Greg Roelofs)
  Changed format of the PNG_LIBPNG_VER integer to xyyzz instead of xyz
    for major, minor, and bugfix releases.  This is 10001. (Adam Costello,
    Tom Lane)
  Make months range from 1-12 in png_convert_to_rfc1123

Version 1.0.0b [March 13, 1998]
  Quieted compiler complaints about two empty "for" loops in pngrutil.c
  Minor changes to makefile.s2x
  Removed #ifdef/#endif around a png_free() in pngread.c

Version 1.0.1 [March 14, 1998]
  Changed makefile.s2x to reduce security risk of using a relative pathname
  Fixed some typos in the documentation (Greg).
  Fixed a problem with value of "channels" returned by png_read_update_info()

Version 1.0.1a [April 21, 1998]
  Optimized Paeth calculations by replacing abs() function calls with intrinsics
  plus other loop optimizations. Improves avg decoding speed by about 20%.
  Commented out i386istic "align" compiler flags in makefile.lnx.
  Reduced the default warning level in some makefiles, to make them consistent.
  Removed references to IJG and JPEG in the ansi2knr.c copyright statement.
  Fixed a bug in png_do_strip_filler with XXRRGGBB => RRGGBB transformation.
  Added grayscale and 16-bit capability to png_do_read_filler().
  Fixed a bug in pngset.c, introduced in version 0.99c, that sets rowbytes
    too large when writing an image with bit_depth < 8 (Bob Dellaca).
  Corrected some bugs in the experimental weighted filtering heuristics.
  Moved a misplaced pngrutil code block that truncates tRNS if it has more
    than num_palette entries -- test was done before num_palette was defined.
  Fixed a png_convert_to_rfc1123() bug that converts day 31 to 0 (Steve Eddins).
  Changed compiler flags in makefile.wat for better optimization
    (Pawel Mrochen).

Version 1.0.1b [May 2, 1998]
  Relocated png_do_gray_to_rgb() within png_do_read_transformations() (Greg).
  Relocated the png_composite macros from pngrtran.c to png.h (Greg).
  Added makefile.sco (contributed by Mike Hopkirk).
  Fixed two bugs (missing definitions of "istop") introduced in libpng-1.0.1a.
  Fixed a bug in pngrtran.c that would set channels=5 under some circumstances.
  More work on the Paeth-filtering, achieving imperceptible speedup
    (A Kleinert).
  More work on loop optimization which may help when compiled with C++
    compilers.
  Added warnings when people try to use transforms they've defined out.
  Collapsed 4 "i" and "c" loops into single "i" loops in pngrtran and pngwtran.
  Revised paragraph about png_set_expand() in libpng.txt and libpng.3 (Greg)

Version 1.0.1c [May 11, 1998]
  Fixed a bug in pngrtran.c (introduced in libpng-1.0.1a) where the masks for
    filler bytes should have been 0xff instead of 0xf.
  Added max_pixel_depth=32 in pngrutil.c when using FILLER with palette images.
  Moved PNG_WRITE_WEIGHTED_FILTER_SUPPORTED and PNG_WRITE_FLUSH_SUPPORTED
    out of the PNG_WRITE_TRANSFORMS_NOT_SUPPORTED block of pngconf.h
  Added "PNG_NO_WRITE_TRANSFORMS" etc., as alternatives for *_NOT_SUPPORTED,
    for consistency, in pngconf.h
  Added individual "ifndef PNG_NO_[CAPABILITY]" in pngconf.h to make it easier
    to remove unwanted capabilities via the compile line
  Made some corrections to grammar (which, it's) in documentation (Greg).
  Corrected example.c, use of row_pointers in png_write_image().

Version 1.0.1d [May 24, 1998]
  Corrected several statements that used side effects illegally in pngrutil.c
    and pngtrans.c, that were introduced in version 1.0.1b
  Revised png_read_rows() to avoid repeated if-testing for NULL (A Kleinert)
  More corrections to example.c, use of row_pointers in png_write_image()
    and png_read_rows().
  Added pngdll.mak and pngdef.pas to scripts directory, contributed by
    Bob Dellaca, to make a png32bd.dll with Borland C++ 4.5
  Fixed error in example.c with png_set_text: num_text is 3, not 2 (Guido V.)
  Changed several loops from count-down to count-up, for consistency.

Version 1.0.1e [June 6, 1998]
  Revised libpng.txt and libpng.3 description of png_set_read|write_fn(), and
    added warnings when people try to set png_read_fn and png_write_fn in
    the same structure.
  Added a test such that png_do_gamma will be done when num_trans==0
    for truecolor images that have defined a background.  This corrects an
    error that was introduced in libpng-0.90 that can cause gamma processing
    to be skipped.
  Added tests in png.h to include "trans" and "trans_values" in structures
    when PNG_READ_BACKGROUND_SUPPORTED or PNG_READ_EXPAND_SUPPORTED is defined.
  Add png_free(png_ptr->time_buffer) in png_destroy_read_struct()
  Moved png_convert_to_rfc_1123() from pngwrite.c to png.c
  Added capability for user-provided malloc_fn() and free_fn() functions,
    and revised pngtest.c to demonstrate their use, replacing the
    PNGTEST_DEBUG_MEM feature.
  Added makefile.w32, for Microsoft C++ 4.0 and later (Tim Wegner).

Version 1.0.2 [June 14, 1998]
  Fixed two bugs in makefile.bor .

Version 1.0.2a [December 30, 1998]
  Replaced and extended code that was removed from png_set_filler() in 1.0.1a.
  Fixed a bug in png_do_filler() that made it fail to write filler bytes in
    the left-most pixel of each row (Kevin Bracey).
  Changed "static pngcharp tIME_string" to "static char tIME_string[30]"
    in pngtest.c (Duncan Simpson).
  Fixed a bug in pngtest.c that caused pngtest to try to write a tIME chunk
    even when no tIME chunk was present in the source file.
  Fixed a problem in pngrutil.c: gray_to_rgb didn't always work with 16-bit.
  Fixed a problem in png_read_push_finish_row(), which would not skip some
    passes that it should skip, for images that are less than 3 pixels high.
  Interchanged the order of calls to png_do_swap() and png_do_shift()
    in pngwtran.c (John Cromer).
  Added #ifdef PNG_DEBUG/#endif surrounding use of PNG_DEBUG in png.h .
  Changed "bad adaptive filter type" from error to warning in pngrutil.c .
  Fixed a documentation error about default filtering with 8-bit indexed-color.
  Separated the PNG_NO_STDIO macro into PNG_NO_STDIO and PNG_NO_CONSOLE_IO
    (L. Peter Deutsch).
  Added png_set_rgb_to_gray() and png_get_rgb_to_gray_status() functions.
  Added png_get_copyright() and png_get_header_version() functions.
  Revised comments on png_set_progressive_read_fn() in libpng.txt and example.c
  Added information about debugging in libpng.txt and libpng.3 .
  Changed "ln -sf" to "ln -s -f" in makefile.s2x, makefile.lnx, and
    makefile.sco.
  Removed lines after Dynamic Dependencies" in makefile.aco .
  Revised makefile.dec to make a shared library (Jeremie Petit).
  Removed trailing blanks from all files.

Version 1.0.2a [January 6, 1999]
  Removed misplaced #endif and #ifdef PNG_NO_EXTERN near the end of png.h
  Added "if" tests to silence complaints about unused png_ptr in png.h and png.c
  Changed "check_if_png" function in example.c to return true (nonzero) if PNG.
  Changed libpng.txt to demonstrate png_sig_cmp() instead of png_check_sig()
    which is obsolete.

Version 1.0.3 [January 14, 1999]
  Added makefile.hux, for Hewlett Packard HPUX 10.20 and 11.00 (Jim Rice)
  Added a statement of Y2K compliance in png.h, libpng.3, and Y2KINFO.

Version 1.0.3a [August 12, 1999]
  Added check for PNG_READ_INTERLACE_SUPPORTED in pngread.c; issue a warning
    if an attempt is made to read an interlaced image when it's not supported.
  Added check if png_ptr->trans is defined before freeing it in pngread.c
  Modified the Y2K statement to include versions back to version 0.71
  Fixed a bug in the check for valid IHDR bit_depth/color_types in pngrutil.c
  Modified makefile.wat (added -zp8 flag, ".symbolic", changed some comments)
  Replaced leading blanks with tab characters in makefile.hux
  Changed "dworkin.wustl.edu" to "ccrc.wustl.edu" in various documents.
  Changed (float)red and (float)green to (double)red, (double)green
    in png_set_rgb_to_gray() to avoid "promotion" problems in AIX.
  Fixed a bug in pngconf.h that omitted <stdio.h> when PNG_DEBUG==0 (K Bracey).
  Reformatted libpng.3 and libpngpf.3 with proper fonts (script by J. vanZandt).
  Updated documentation to refer to the PNG-1.2 specification.
  Removed ansi2knr.c and left pointers to the latest source for ansi2knr.c
    in makefile.knr, INSTALL, and README (L. Peter Deutsch)
  Fixed bugs in calculation of the length of rowbytes when adding alpha
    channels to 16-bit images, in pngrtran.c (Chris Nokleberg)
  Added function png_set_user_transform_info() to store user_transform_ptr,
    user_depth, and user_channels into the png_struct, and a function
    png_get_user_transform_ptr() to retrieve the pointer (Chris Nokleberg)
  Added function png_set_empty_plte_permitted() to make libpng useable
    in MNG applications.
  Corrected the typedef for png_free_ptr in png.h (Jesse Jones).
  Correct gamma with srgb is 45455 instead of 45000 in pngrutil.c, to be
    consistent with PNG-1.2, and allow variance of 500 before complaining.
  Added assembler code contributed by Intel in file pngvcrd.c and modified
    makefile.w32 to use it (Nirav Chhatrapati, INTEL Corporation,
    Gilles Vollant)
  Changed "ln -s -f" to "ln -f -s" in the makefiles to make Solaris happy.
  Added some aliases for png_set_expand() in pngrtran.c, namely
    png_set_expand_PLTE(), png_set_expand_depth(), and png_set_expand_tRNS()
    (Greg Roelofs, in "PNG: The Definitive Guide").
  Added makefile.beo for BEOS on X86, contributed by Sander Stok.

Version 1.0.3b [August 26, 1999]
  Replaced 2147483647L several places with PNG_MAX_UINT macro, defined in png.h
  Changed leading blanks to tabs in all makefiles.
  Define PNG_USE_PNGVCRD in makefile.w32, to get MMX assembler code.
  Made alternate versions of  png_set_expand() in pngrtran.c, namely
    png_set_gray_1_2_4_to_8, png_set_palette_to_rgb, and png_set_tRNS_to_alpha
    (Greg Roelofs, in "PNG: The Definitive Guide").  Deleted the 1.0.3a aliases.
  Relocated start of 'extern "C"' block in png.h so it doesn't include pngconf.h
  Revised calculation of num_blocks in pngmem.c to avoid a potentially
    negative shift distance, whose results are undefined in the C language.
  Added a check in pngset.c to prevent writing multiple tIME chunks.
  Added a check in pngwrite.c to detect invalid small window_bits sizes.

Version 1.0.3d [September 4, 1999]
  Fixed type casting of igamma in pngrutil.c
  Added new png_expand functions to scripts/pngdef.pas and pngos2.def
  Added a demo read_user_transform_fn that examines the row filters in pngtest.c

Version 1.0.4 [September 24, 1999, not distributed publicly]
  Define PNG_ALWAYS_EXTERN in pngconf.h if __STDC__ is defined
  Delete #define PNG_INTERNAL and include "png.h" from pngasmrd.h
  Made several minor corrections to pngtest.c
  Renamed the makefiles with longer but more user friendly extensions.
  Copied the PNG copyright and license to a separate LICENSE file.
  Revised documentation, png.h, and example.c to remove reference to
    "viewing_gamma" which no longer appears in the PNG specification.
  Revised pngvcrd.c to use MMX code for interlacing only on the final pass.
  Updated pngvcrd.c to use the faster C filter algorithms from libpng-1.0.1a
  Split makefile.win32vc into two versions, makefile.vcawin32 (uses MMX
    assembler code) and makefile.vcwin32 (doesn't).
  Added a CPU timing report to pngtest.c (enabled by defining PNGTEST_TIMING)
  Added a copy of pngnow.png to the distribution.

Version 1.0.4a [September 25, 1999]
  Increase max_pixel_depth in pngrutil.c if a user transform needs it.
  Changed several division operations to right-shifts in pngvcrd.c

Version 1.0.4b [September 30, 1999]
  Added parentheses in line 3732 of pngvcrd.c
  Added a comment in makefile.linux warning about buggy -O3 in pgcc 2.95.1

Version 1.0.4c [October 1, 1999]
  Added a "png_check_version" function in png.c and pngtest.c that will generate
    a helpful compiler error if an old png.h is found in the search path.
  Changed type of png_user_transform_depth|channels from int to png_byte.
  Added "Libpng is OSI Certified Open Source Software" statement to png.h

Version 1.0.4d [October 6, 1999]
  Changed 0.45 to 0.45455 in png_set_sRGB()
  Removed unused PLTE entries from pngnow.png
  Re-enabled some parts of pngvcrd.c (png_combine_row) that work properly.

Version 1.0.4e [October 10, 1999]
  Fixed sign error in pngvcrd.c (Greg Roelofs)
  Replaced some instances of memcpy with simple assignments in pngvcrd (GR-P)

Version 1.0.4f [October 15, 1999]
  Surrounded example.c code with #if 0 .. #endif to prevent people from
    inadvertently trying to compile it.
  Changed png_get_header_version() from a function to a macro in png.h
  Added type casting mostly in pngrtran.c and pngwtran.c
  Removed some pointless "ptr = NULL" in pngmem.c
  Added a "contrib" directory containing the source code from Greg's book.

Version 1.0.5 [October 15, 1999]
  Minor editing of the INSTALL and README files.

Version 1.0.5a [October 23, 1999]
  Added contrib/pngsuite and contrib/pngminus (Willem van Schaik)
  Fixed a typo in the png_set_sRGB() function call in example.c (Jan Nijtmans)
  Further optimization and bugfix of pngvcrd.c
  Revised pngset.c so that it does not allocate or free memory in the user's
    text_ptr structure.  Instead, it makes its own copy.
  Created separate write_end_info_struct in pngtest.c for a more severe test.
  Added code in pngwrite.c to free info_ptr->text[i].key to stop a memory leak.

Version 1.0.5b [November 23, 1999]
  Moved PNG_FLAG_HAVE_CHUNK_HEADER, PNG_FLAG_BACKGROUND_IS_GRAY and
    PNG_FLAG_WROTE_tIME from flags to mode.
  Added png_write_info_before_PLTE() function.
  Fixed some typecasting in contrib/gregbook/*.c
  Updated scripts/makevms.com and added makevms.com to contrib/gregbook
    and contrib/pngminus (Martin Zinser)

Version 1.0.5c [November 26, 1999]
  Moved png_get_header_version from png.h to png.c, to accommodate ansi2knr.
  Removed all global arrays (according to PNG_NO_GLOBAL_ARRAYS macro), to
    accommodate making DLL's: Moved usr_png_ver from global variable to function
    png_get_header_ver() in png.c.  Moved png_sig to png_sig_bytes in png.c and
    eliminated use of png_sig in pngwutil.c.  Moved the various png_CHNK arrays
    into pngtypes.h.  Eliminated use of global png_pass arrays.  Declared the
    png_CHNK and png_pass arrays to be "const".  Made the global arrays
    available to applications (although none are used in libpng itself) when
    PNG_NO_GLOBAL_ARRAYS is not defined or when PNG_GLOBAL_ARRAYS is defined.
  Removed some extraneous "-I" from contrib/pngminus/makefile.std
  Changed the PNG_sRGB_INTENT macros in png.h to be consistent with PNG-1.2.
  Change PNG_SRGB_INTENT to PNG_sRGB_INTENT in libpng.txt and libpng.3

Version 1.0.5d [November 29, 1999]
  Add type cast (png_const_charp) two places in png.c
  Eliminated pngtypes.h; use macros instead to declare PNG_CHNK arrays.
  Renamed "PNG_GLOBAL_ARRAYS" to "PNG_USE_GLOBAL_ARRAYS" and made available
    to applications a macro "PNG_USE_LOCAL_ARRAYS".
  comment out (with #ifdef) all the new declarations when
    PNG_USE_GLOBAL_ARRAYS is defined.
  Added PNG_EXPORT_VAR macro to accommodate making DLL's.

Version 1.0.5e [November 30, 1999]
  Added iCCP, iTXt, and sPLT support; added "lang" member to the png_text
    structure; refactored the inflate/deflate support to make adding new chunks
    with trailing compressed parts easier in the future, and added new functions
    png_free_iCCP, png_free_pCAL, png_free_sPLT, png_free_text, png_get_iCCP,
    png_get_spalettes, png_set_iCCP, png_set_spalettes (Eric S. Raymond).
    NOTE: Applications that write text chunks MUST define png_text->lang
    before calling png_set_text(). It must be set to NULL if you want to
    write tEXt or zTXt chunks.  If you want your application to be able to
    run with older versions of libpng, use

      #ifdef PNG_iTXt_SUPPORTED
         png_text[i].lang = NULL;
      #endif

  Changed png_get_oFFs() and png_set_oFFs() to use signed rather than unsigned
    offsets (Eric S. Raymond).
  Combined PNG_READ_cHNK_SUPPORTED and PNG_WRITE_cHNK_SUPPORTED macros into
    PNG_cHNK_SUPPORTED and combined the three types of PNG_text_SUPPORTED
    macros, leaving the separate macros also available.
  Removed comments on #endifs at the end of many short, non-nested #if-blocks.

Version 1.0.5f [December 6, 1999]
  Changed makefile.solaris to issue a warning about potential problems when
    the ucb "ld" is in the path ahead of the ccs "ld".
  Removed "- [date]" from the "synopsis" line in libpng.3 and libpngpf.3.
  Added sCAL chunk support (Eric S. Raymond).

Version 1.0.5g [December 7, 1999]
  Fixed "png_free_spallettes" typo in png.h
  Added code to handle new chunks in pngpread.c
  Moved PNG_CHNK string macro definitions outside of PNG_NO_EXTERN block
  Added "translated_key" to png_text structure and png_write_iTXt().
  Added code in pngwrite.c to work around a newly discovered zlib bug.

Version 1.0.5h [December 10, 1999]
  NOTE: regarding the note for version 1.0.5e, the following must also
    be included in your code:
        png_text[i].translated_key = NULL;
  Unknown chunk handling is now supported.
  Option to eliminate all floating point support was added.  Some new
    fixed-point functions such as png_set_gAMA_fixed() were added.
  Expanded tabs and removed trailing blanks in source files.

Version 1.0.5i [December 13, 1999]
  Added some type casts to silence compiler warnings.
  Renamed "png_free_spalette" to "png_free_spalettes" for consistency.
  Removed leading blanks from a #define in pngvcrd.c
  Added some parameters to the new png_set_keep_unknown_chunks() function.
  Added a test for up->location != 0 in the first instance of writing
    unknown chunks in pngwrite.c
  Changed "num" to "i" in png_free_spalettes() and png_free_unknowns() to
    prevent recursion.
  Added png_free_hIST() function.
  Various patches to fix bugs in the sCAL and integer cHRM processing,
    and to add some convenience macros for use with sCAL.

Version 1.0.5j [December 21, 1999]
  Changed "unit" parameter of png_write_sCAL from png_byte to int, to work
    around buggy compilers.
  Added new type "png_fixed_point" for integers that hold float*100000 values
  Restored backward compatibility of tEXt/zTXt chunk processing:
    Restored the first four members of png_text to the same order as v.1.0.5d.
    Added members "lang_key" and "itxt_length" to png_text struct.  Set
    text_length=0 when "text" contains iTXt data.  Use the "compression"
    member to distinguish among tEXt/zTXt/iTXt types.  Added
    PNG_ITXT_COMPRESSION_NONE (1) and PNG_ITXT_COMPRESSION_zTXt(2) macros.
    The "Note" above, about backward incompatibility of libpng-1.0.5e, no
    longer applies.
  Fixed png_read|write_iTXt() to read|write parameters in the right order,
    and to write the iTXt chunk after IDAT if it appears in the end_ptr.
  Added pnggccrd.c, version of pngvcrd.c Intel assembler for gcc (Greg Roelofs)
  Reversed the order of trying to write floating-point and fixed-point gAMA.

Version 1.0.5k [December 27, 1999]
  Added many parentheses, e.g., "if (a && b & c)" becomes "if (a && (b & c))"
  Added png_handle_as_unknown() function (Glenn)
  Added png_free_chunk_list() function and chunk_list and num_chunk_list members
    of png_ptr.
  Eliminated erroneous warnings about multiple sPLT chunks and sPLT-after-PLTE.
  Fixed a libpng-1.0.5h bug in pngrutil.c that was issuing erroneous warnings
    about ignoring incorrect gAMA with sRGB (gAMA was in fact not ignored)
  Added png_free_tRNS(); png_set_tRNS() now malloc's its own trans array (ESR).
  Define png_get_int_32 when oFFs chunk is supported as well as when pCAL is.
  Changed type of proflen from png_int_32 to png_uint_32 in png_get_iCCP().

Version 1.0.5l [January 1, 2000]
  Added functions png_set_read_user_chunk_fn() and png_get_user_chunk_ptr()
    for setting a callback function to handle unknown chunks and for
    retrieving the associated user pointer (Glenn).

Version 1.0.5m [January 7, 2000]
  Added high-level functions png_read_png(), png_write_png(), png_free_pixels().

Version 1.0.5n [January 9, 2000]
  Added png_free_PLTE() function, and modified png_set_PLTE() to malloc its
    own memory for info_ptr->palette.  This makes it safe for the calling
    application to free its copy of the palette any time after it calls
    png_set_PLTE().

Version 1.0.5o [January 20, 2000]
  Cosmetic changes only (removed some trailing blanks and TABs)

Version 1.0.5p [January 31, 2000]
  Renamed pngdll.mak to makefile.bd32
  Cosmetic changes in pngtest.c

Version 1.0.5q [February 5, 2000]
  Relocated the makefile.solaris warning about PATH problems.
  Fixed pngvcrd.c bug by pushing/popping registers in mmxsupport (Bruce Oberg)
  Revised makefile.gcmmx
  Added PNG_SETJMP_SUPPORTED, PNG_SETJMP_NOT_SUPPORTED, and PNG_ABORT() macros

Version 1.0.5r [February 7, 2000]
  Removed superfluous prototype for png_get_itxt from png.h
  Fixed a bug in pngrtran.c that improperly expanded the background color.
  Return *num_text=0 from png_get_text() when appropriate, and fix documentation
    of png_get_text() in libpng.txt/libpng.3.

Version 1.0.5s [February 18, 2000]
  Added "png_jmp_env()" macro to pngconf.h, to help people migrate to the
    new error handler that's planned for the next libpng release, and changed
    example.c, pngtest.c, and contrib programs to use this macro.
  Revised some of the DLL-export macros in pngconf.h (Greg Roelofs)
  Fixed a bug in png_read_png() that caused it to fail to expand some images
    that it should have expanded.
  Fixed some mistakes in the unused and undocumented INCH_CONVERSIONS functions
    in pngget.c
  Changed the allocation of palette, history, and trans arrays back to
    the version 1.0.5 method (linking instead of copying) which restores
    backward compatibility with version 1.0.5.  Added some remarks about
    that in example.c.  Added "free_me" member to info_ptr and png_ptr
    and added png_free_data() function.
  Updated makefile.linux and makefile.gccmmx to make directories conditionally.
  Made cosmetic changes to pngasmrd.h
  Added png_set_rows() and png_get_rows(), for use with png_read|write_png().
  Modified png_read_png() to allocate info_ptr->row_pointers only if it
    hasn't already been allocated.

Version 1.0.5t [March 4, 2000]
  Changed png_jmp_env() migration aiding macro to png_jmpbuf().
  Fixed "interlace" typo (should be "interlaced") in contrib/gregbook/read2-x.c
  Fixed bug with use of PNG_BEFORE_IHDR bit in png_ptr->mode, introduced when
    PNG_FLAG_HAVE_CHUNK_HEADER was moved into png_ptr->mode in version 1.0.5b
  Files in contrib/gregbook were revised to use png_jmpbuf() and to select
    a 24-bit visual if one is available, and to allow abbreviated options.
  Files in contrib/pngminus were revised to use the png_jmpbuf() macro.
  Removed spaces in makefile.linux and makefile.gcmmx, introduced in 1.0.5s

Version 1.0.5u [March 5, 2000]
  Simplified the code that detects old png.h in png.c and pngtest.c
  Renamed png_spalette (_p, _pp) to png_sPLT_t (_tp, _tpp)
  Increased precision of rgb_to_gray calculations from 8 to 15 bits and
    added png_set_rgb_to_gray_fixed() function.
  Added makefile.bc32 (32-bit Borland C++, C mode)

Version 1.0.5v [March 11, 2000]
  Added some parentheses to the png_jmpbuf macro definition.
  Updated references to the zlib home page, which has moved to freesoftware.com.
  Corrected bugs in documentation regarding png_read_row() and png_write_row().
  Updated documentation of png_rgb_to_gray calculations in libpng.3/libpng.txt.
  Renamed makefile.borland,turboc3 back to makefile.bor,tc3 as in version 1.0.3,
    revised borland makefiles; added makefile.ibmvac3 and makefile.gcc (Cosmin)

Version 1.0.6 [March 20, 2000]
  Minor revisions of makefile.bor, libpng.txt, and gregbook/rpng2-win.c
  Added makefile.sggcc (SGI IRIX with gcc)

Version 1.0.6d [April 7, 2000]
  Changed sprintf() to strcpy() in png_write_sCAL_s() to work without STDIO
  Added data_length parameter to png_decompress_chunk() function
  Revised documentation to remove reference to abandoned png_free_chnk functions
  Fixed an error in png_rgb_to_gray_fixed()
  Revised example.c, usage of png_destroy_write_struct().
  Renamed makefile.ibmvac3 to makefile.ibmc, added libpng.icc IBM project file
  Added a check for info_ptr->free_me&PNG_FREE_TEXT when freeing text in png.c
  Simplify png_sig_bytes() function to remove use of non-ISO-C strdup().

Version 1.0.6e [April 9, 2000]
  Added png_data_freer() function.
  In the code that checks for over-length tRNS chunks, added check of
    info_ptr->num_trans as well as png_ptr->num_trans (Matthias Benckmann)
  Minor revisions of libpng.txt/libpng.3.
  Check for existing data and free it if the free_me flag is set, in png_set_*()
    and png_handle_*().
  Only define PNG_WEIGHTED_FILTERS_SUPPORTED when PNG_FLOATING_POINT_SUPPORTED
    is defined.
  Changed several instances of PNG_NO_CONSOLE_ID to PNG_NO_STDIO in pngrutil.c
    and mentioned the purposes of the two macros in libpng.txt/libpng.3.

Version 1.0.6f [April 14, 2000]
  Revised png_set_iCCP() and png_set_rows() to avoid prematurely freeing data.
  Add checks in png_set_text() for NULL members of the input text structure.
  Revised libpng.txt/libpng.3.
  Removed superfluous prototype for png_set_iTXt from png.h
  Removed "else" from pngread.c, after png_error(), and changed "0" to "length".
  Changed several png_errors about malformed ancillary chunks to png_warnings.

Version 1.0.6g [April 24, 2000]
  Added png_pass-* arrays to pnggccrd.c when PNG_USE_LOCAL_ARRAYS is defined.
  Relocated paragraph about png_set_background() in libpng.3/libpng.txt
    and other revisions (Matthias Benckmann)
  Relocated info_ptr->free_me, png_ptr->free_me, and other info_ptr and
    png_ptr members to restore binary compatibility with libpng-1.0.5
    (breaks compatibility with libpng-1.0.6).

Version 1.0.6h [April 24, 2000]
  Changed shared library so-number pattern from 2.x.y.z to xy.z (this builds
    libpng.so.10 & libpng.so.10.6h instead of libpng.so.2 & libpng.so.2.1.0.6h)
    This is a temporary change for test purposes.

Version 1.0.6i [May 2, 2000]
  Rearranged some members at the end of png_info and png_struct, to put
    unknown_chunks_num and free_me within the original size of the png_structs
    and free_me, png_read_user_fn, and png_free_fn within the original png_info,
    because some old applications allocate the structs directly instead of
    using png_create_*().
  Added documentation of user memory functions in libpng.txt/libpng.3
  Modified png_read_png so that it will use user_allocated row_pointers
    if present, unless free_me directs that it be freed, and added description
    of the use of png_set_rows() and png_get_rows() in libpng.txt/libpng.3.
  Added PNG_LEGACY_SUPPORTED macro, and #ifdef out all new (since version
    1.00) members of png_struct and png_info, to regain binary compatibility
    when you define this macro.  Capabilities lost in this event
    are user transforms (new in version 1.0.0),the user transform pointer
    (new in version 1.0.2), rgb_to_gray (new in 1.0.5), iCCP, sCAL, sPLT,
    the high-level interface, and unknown chunks support (all new in 1.0.6).
    This was necessary because of old applications that allocate the structs
    directly as authors were instructed to do in libpng-0.88 and earlier,
    instead of using png_create_*().
  Added modes PNG_CREATED_READ_STRUCT and PNG_CREATED_WRITE_STRUCT which
    can be used to detect codes that directly allocate the structs, and
    code to check these modes in png_read_init() and png_write_init() and
    generate a libpng error if the modes aren't set and PNG_LEGACY_SUPPORTED
    was not defined.
  Added makefile.intel and updated makefile.watcom (Pawel Mrochen)

Version 1.0.6j [May 3, 2000]
  Overloaded png_read_init() and png_write_init() with macros that convert
    calls to png_read_init_2() or png_write_init_2() that check the version
    and structure sizes.

Version 1.0.7beta11 [May 7, 2000]
  Removed the new PNG_CREATED_READ_STRUCT and PNG_CREATED_WRITE_STRUCT modes
    which are no longer used.
  Eliminated the three new members of png_text when PNG_LEGACY_SUPPORTED is
    defined or when neither PNG_READ_iTXt_SUPPORTED nor PNG_WRITE_iTXT_SUPPORTED
    is defined.
  Made PNG_NO_READ|WRITE_iTXt the default setting, to avoid memory
    overrun when old applications fill the info_ptr->text structure directly.
  Added PNGAPI macro, and added it to the definitions of all exported functions.
  Relocated version macro definitions ahead of the includes of zlib.h and
    pngconf.h in png.h.

Version 1.0.7beta12 [May 12, 2000]
  Revised pngset.c to avoid a problem with expanding the png_debug macro.
  Deleted some extraneous defines from pngconf.h
  Made PNG_NO_CONSOLE_IO the default condition when PNG_BUILD_DLL is defined.
  Use MSC _RPTn debugging instead of fprintf if _MSC_VER is defined.
  Added png_access_version_number() function.
  Check for mask&PNG_FREE_CHNK (for TEXT, SCAL, PCAL) in png_free_data().
  Expanded libpng.3/libpng.txt information about png_data_freer().

Version 1.0.7beta14 [May 17, 2000] (beta13 was not published)
  Changed pnggccrd.c and pngvcrd.c to handle bad adaptive filter types as
    warnings instead of errors, as pngrutil.c does.
  Set the PNG_INFO_IDAT valid flag in png_set_rows() so png_write_png()
    will actually write IDATs.
  Made the default PNG_USE_LOCAL_ARRAYS depend on PNG_DLL instead of WIN32.
  Make png_free_data() ignore its final parameter except when freeing data
    that can have multiple instances (text, sPLT, unknowns).
  Fixed a new bug in png_set_rows().
  Removed info_ptr->valid tests from png_free_data(), as in version 1.0.5.
  Added png_set_invalid() function.
  Fixed incorrect illustrations of png_destroy_write_struct() in example.c.

Version 1.0.7beta15 [May 30, 2000]
  Revised the deliberately erroneous Linux setjmp code in pngconf.h to produce
    fewer error messages.
  Rearranged checks for Z_OK to check the most likely path first in pngpread.c
    and pngwutil.c.
  Added checks in pngtest.c for png_create_*() returning NULL, and mentioned
    in libpng.txt/libpng.3 the need for applications to check this.
  Changed names of png_default_*() functions in pngtest to pngtest_*().
  Changed return type of png_get_x|y_offset_*() from png_uint_32 to png_int_32.
  Fixed some bugs in the unused PNG_INCH_CONVERSIONS functions in pngget.c
  Set each pointer to NULL after freeing it in png_free_data().
  Worked around a problem in pngconf.h; AIX's strings.h defines an "index"
    macro that conflicts with libpng's png_color_16.index. (Dimitri
    Papadapoulos)
  Added "msvc" directory with MSVC++ project files (Simon-Pierre Cadieux).

Version 1.0.7beta16 [June 4, 2000]
  Revised the workaround of AIX string.h "index" bug.
  Added a check for overlength PLTE chunk in pngrutil.c.
  Added PNG_NO_POINTER_INDEXING macro to use array-indexing instead of pointer
    indexing in pngrutil.c and pngwutil.c to accommodate a buggy compiler.
  Added a warning in png_decompress_chunk() when it runs out of data, e.g.
    when it tries to read an erroneous PhotoShop iCCP chunk.
  Added PNG_USE_DLL macro.
  Revised the copyright/disclaimer/license notice.
  Added contrib/msvctest directory

Version 1.0.7rc1 [June 9, 2000]
  Corrected the definition of PNG_TRANSFORM_INVERT_ALPHA  (0x0400 not 0x0200)
  Added contrib/visupng directory (Willem van Schaik)

Version 1.0.7beta18 [June 23, 2000]
  Revised PNGAPI definition, and pngvcrd.c to work with __GCC__
    and do not redefine PNGAPI if it is passed in via a compiler directive.
  Revised visupng/PngFile.c to remove returns from within the Try block.
  Removed leading underscores from "_PNG_H" and "_PNG_SAVE_BSD_SOURCE" macros.
  Updated contrib/visupng/cexcept.h to version 1.0.0.
  Fixed bugs in pngwrite.c and pngwutil.c that prevented writing iCCP chunks.

Version 1.0.7rc2 [June 28, 2000]
  Updated license to include disclaimers required by UCITA.
  Fixed "DJBPP" typo in pnggccrd.c introduced in beta18.

Version 1.0.7 [July 1, 2000]
  Revised the definition of "trans_values" in libpng.3/libpng.txt

Version 1.0.8beta1 [July 8, 2000]
  Added png_free(png_ptr, key) two places in pngpread.c to stop memory leaks.
  Changed PNG_NO_STDIO to PNG_NO_CONSOLE_IO, several places in pngrutil.c and
    pngwutil.c.
  Changed PNG_EXPORT_VAR to use PNG_IMPEXP, in pngconf.h.
  Removed unused "#include <assert.h>" from png.c
  Added WindowsCE support.
  Revised pnggccrd.c to work with gcc-2.95.2 and in the Cygwin environment.

Version 1.0.8beta2 [July 10, 2000]
  Added project files to the wince directory and made further revisions
    of pngtest.c, pngrio.c, and pngwio.c in support of WindowsCE.

Version 1.0.8beta3 [July 11, 2000]
  Only set the PNG_FLAG_FREE_TRNS or PNG_FREE_TRNS flag in png_handle_tRNS()
    for indexed-color input files to avoid potential double-freeing trans array
    under some unusual conditions; problem was introduced in version 1.0.6f.
  Further revisions to pngtest.c and files in the wince subdirectory.

Version 1.0.8beta4 [July 14, 2000]
  Added the files pngbar.png and pngbar.jpg to the distribution.
  Added makefile.cygwin, and cygwin support in pngconf.h
  Added PNG_NO_ZALLOC_ZERO macro (makes png_zalloc skip zeroing memory)

Version 1.0.8rc1 [July 16, 2000]
  Revised png_debug() macros and statements to eliminate compiler warnings.

Version 1.0.8 [July 24, 2000]
  Added png_flush() in pngwrite.c, after png_write_IEND().
  Updated makefile.hpux to build a shared library.

Version 1.0.9beta1 [November 10, 2000]
  Fixed typo in scripts/makefile.hpux
  Updated makevms.com in scripts and contrib/* and contrib/* (Martin Zinser)
  Fixed seqence-point bug in contrib/pngminus/png2pnm (Martin Zinser)
  Changed "cdrom.com" in documentation to "libpng.org"
  Revised pnggccrd.c to get it all working, and updated makefile.gcmmx (Greg).
  Changed type of "params" from voidp to png_voidp in png_read|write_png().
  Make sure PNGAPI and PNG_IMPEXP are defined in pngconf.h.
  Revised the 3 instances of WRITEFILE in pngtest.c.
  Relocated "msvc" and "wince" project subdirectories into "dll" subdirectory.
  Updated png.rc in dll/msvc project
  Revised makefile.dec to define and use LIBPATH and INCPATH
  Increased size of global png_libpng_ver[] array from 12 to 18 chars.
  Made global png_libpng_ver[], png_sig[] and png_pass_*[] arrays const.
  Removed duplicate png_crc_finish() from png_handle_bKGD() function.
  Added a warning when application calls png_read_update_info() multiple times.
  Revised makefile.cygwin
  Fixed bugs in iCCP support in pngrutil.c and pngwutil.c.
  Replaced png_set_empty_plte_permitted() with png_permit_mng_features().

Version 1.0.9beta2 [November 19, 2000]
  Renamed the "dll" subdirectory "projects".
  Added borland project files to "projects" subdirectory.
  Set VS_FF_PRERELEASE and VS_FF_PATCHED flags in msvc/png.rc when appropriate.
  Add error message in png_set_compression_buffer_size() when malloc fails.

Version 1.0.9beta3 [November 23, 2000]
  Revised PNG_LIBPNG_BUILD_TYPE macro in png.h, used in the msvc project.
  Removed the png_flush() in pngwrite.c that crashes some applications
    that don't set png_output_flush_fn.
  Added makefile.macosx and makefile.aix to scripts directory.

Version 1.0.9beta4 [December 1, 2000]
  Change png_chunk_warning to png_warning in png_check_keyword().
  Increased the first part of msg buffer from 16 to 18 in png_chunk_error().

Version 1.0.9beta5 [December 15, 2000]
  Added support for filter method 64 (for PNG datastreams embedded in MNG).

Version 1.0.9beta6 [December 18, 2000]
  Revised png_set_filter() to accept filter method 64 when appropriate.
  Added new PNG_HAVE_PNG_SIGNATURE bit to png_ptr->mode and use it to
    help prevent applications from using MNG features in PNG datastreams.
  Added png_permit_mng_features() function.
  Revised libpng.3/libpng.txt.  Changed "filter type" to "filter method".

Version 1.0.9rc1 [December 23, 2000]
  Revised test for PNG_HAVE_PNG_SIGNATURE in pngrutil.c
  Fixed error handling of unknown compression type in png_decompress_chunk().
  In pngconf.h, define __cdecl when _MSC_VER is defined.

Version 1.0.9beta7 [December 28, 2000]
  Changed PNG_TEXT_COMPRESSION_zTXt to PNG_COMPRESSION_TYPE_BASE several places.
  Revised memory management in png_set_hIST and png_handle_hIST in a backward
    compatible manner.  PLTE and tRNS were revised similarly.
  Revised the iCCP chunk reader to ignore trailing garbage.

Version 1.0.9beta8 [January 12, 2001]
  Moved pngasmrd.h into pngconf.h.
  Improved handling of out-of-spec garbage iCCP chunks generated by PhotoShop.

Version 1.0.9beta9 [January 15, 2001]
  Added png_set_invalid, png_permit_mng_features, and png_mmx_supported to
    wince and msvc project module definition files.
  Minor revision of makefile.cygwin.
  Fixed bug with progressive reading of narrow interlaced images in pngpread.c

Version 1.0.9beta10 [January 16, 2001]
  Do not typedef png_FILE_p in pngconf.h when PNG_NO_STDIO is defined.
  Fixed "png_mmx_supported" typo in project definition files.

Version 1.0.9beta11 [January 19, 2001]
  Updated makefile.sgi to make shared library.
  Removed png_mmx_support() function and disabled PNG_MNG_FEATURES_SUPPORTED
    by default, for the benefit of DLL forward compatibility.  These will
    be re-enabled in version 1.2.0.

Version 1.0.9rc2 [January 22, 2001]
  Revised cygwin support.

Version 1.0.9 [January 31, 2001]
  Added check of cygwin's ALL_STATIC in pngconf.h
  Added "-nommx" parameter to contrib/gregbook/rpng2-win and rpng2-x demos.

Version 1.0.10beta1 [March 14, 2001]
  Revised makefile.dec, makefile.sgi, and makefile.sggcc; added makefile.hpgcc.
  Reformatted libpng.3 to eliminate bad line breaks.
  Added checks for _mmx_supported in the read_filter_row function of pnggccrd.c
  Added prototype for png_mmx_support() near the top of pnggccrd.c
  Moved some error checking from png_handle_IHDR to png_set_IHDR.
  Added PNG_NO_READ_SUPPORTED and PNG_NO_WRITE_SUPPORTED macros.
  Revised png_mmx_support() function in pnggccrd.c
  Restored version 1.0.8 PNG_WRITE_EMPTY_PLTE_SUPPORTED behavior in pngwutil.c
  Fixed memory leak in contrib/visupng/PngFile.c
  Fixed bugs in png_combine_row() in pnggccrd.c and pngvcrd.c (C version)
  Added warnings when retrieving or setting gamma=0.
  Increased the first part of msg buffer from 16 to 18 in png_chunk_warning().

Version 1.0.10rc1 [March 23, 2001]
  Changed all instances of memcpy, strcpy, and strlen to png_memcpy, png_strcpy,
    and png_strlen.
  Revised png_mmx_supported() function in pnggccrd.c to return proper value.
  Fixed bug in progressive reading (pngpread.c) with small images (height < 8).

Version 1.0.10 [March 30, 2001]
  Deleted extraneous space (introduced in 1.0.9) from line 42 of makefile.cygwin
  Added beos project files (Chris Herborth)

Version 1.0.11beta1 [April 3, 2001]
  Added type casts on several png_malloc() calls (Dimitri Papadapoulos).
  Removed a no-longer needed AIX work-around from pngconf.h
  Changed several "//" single-line comments to C-style in pnggccrd.c

Version 1.0.11beta2 [April 11, 2001]
  Removed PNGAPI from several functions whose prototypes did not have PNGAPI.
  Updated scripts/pngos2.def

Version 1.0.11beta3 [April 14, 2001]
  Added checking the results of many instances of png_malloc() for NULL

Version 1.0.11beta4 [April 20, 2001]
  Undid the changes from version 1.0.11beta3.  Added a check for NULL return
    from user's malloc_fn().
  Removed some useless type casts of the NULL pointer.
  Added makefile.netbsd

Version 1.0.11 [April 27, 2001]
  Revised makefile.netbsd

Version 1.0.12beta1 [May 14, 2001]
  Test for Windows platform in pngconf.h when including malloc.h (Emmanuel Blot)
  Updated makefile.cygwin and handling of Cygwin's ALL_STATIC in pngconf.h
  Added some never-to-be-executed code in pnggccrd.c to quiet compiler warnings.
  Eliminated the png_error about apps using png_read|write_init().  Instead,
    libpng will reallocate the png_struct and info_struct if they are too small.
    This retains future binary compatibility for old applications written for
    libpng-0.88 and earlier.

Version 1.2.0beta1 [May 6, 2001]
  Bumped DLLNUM to 2.
  Re-enabled PNG_MNG_FEATURES_SUPPORTED and enabled PNG_ASSEMBLER_CODE_SUPPORTED
    by default.
  Added runtime selection of MMX features.
  Added png_set_strip_error_numbers function and related macros.

Version 1.2.0beta2 [May 7, 2001]
  Finished merging 1.2.0beta1 with version 1.0.11
  Added a check for attempts to read or write PLTE in grayscale PNG datastreams.

Version 1.2.0beta3 [May 17, 2001]
  Enabled user memory function by default.
  Modified png_create_struct so it passes user mem_ptr to user memory allocator.
  Increased png_mng_features flag from png_byte to png_uint_32.
  Bumped shared-library (so-number) and dll-number to 3.

Version 1.2.0beta4 [June 23, 2001]
  Check for missing profile length field in iCCP chunk and free chunk_data
    in case of truncated iCCP chunk.
  Bumped shared-library number to 3 in makefile.sgi and makefile.sggcc
  Bumped dll-number from 2 to 3 in makefile.cygwin
  Revised contrib/gregbook/rpng*-x.c to avoid a memory leak and to exit cleanly
    if user attempts to run it on an 8-bit display.
  Updated contrib/gregbook
  Use png_malloc instead of png_zalloc to allocate palette in pngset.c
  Updated makefile.ibmc
  Added some typecasts to eliminate gcc 3.0 warnings.  Changed prototypes
    of png_write_oFFS width and height from png_uint_32 to png_int_32.
  Updated example.c
  Revised prototypes for png_debug_malloc and png_debug_free in pngtest.c

Version 1.2.0beta5 [August 8, 2001]
  Revised contrib/gregbook
  Revised makefile.gcmmx
  Revised pnggccrd.c to conditionally compile some thread-unsafe code only
    when PNG_THREAD_UNSAFE_OK is defined.
  Added tests to prevent pngwutil.c from writing a bKGD or tRNS chunk with
    value exceeding 2^bit_depth-1
  Revised makefile.sgi and makefile.sggcc
  Replaced calls to fprintf(stderr,...) with png_warning() in pnggccrd.c
  Removed restriction that do_invert_mono only operate on 1-bit opaque files

Version 1.2.0 [September 1, 2001]
  Changed a png_warning() to png_debug() in pnggccrd.c
  Fixed contrib/gregbook/rpng-x.c, rpng2-x.c to avoid crash with XFreeGC().

Version 1.2.1beta1 [October 19, 2001]
  Revised makefile.std in contrib/pngminus
  Include background_1 in png_struct regardless of gamma support.
  Revised makefile.netbsd and makefile.macosx, added makefile.darwin.
  Revised example.c to provide more details about using row_callback().

Version 1.2.1beta2 [October 25, 2001]
  Added type cast to each NULL appearing in a function call, except for
    WINCE functions.
  Added makefile.so9.

Version 1.2.1beta3 [October 27, 2001]
  Removed type casts from all NULLs.
  Simplified png_create_struct_2().

Version 1.2.1beta4 [November 7, 2001]
  Revised png_create_info_struct() and png_creat_struct_2().
  Added error message if png_write_info() was omitted.
  Type cast NULLs appearing in function calls when _NO_PROTO or
    PNG_TYPECAST_NULL is defined.

Version 1.2.1rc1 [November 24, 2001]
  Type cast NULLs appearing in function calls except when PNG_NO_TYPECAST_NULL
    is defined.
  Changed typecast of "size" argument to png_size_t in pngmem.c calls to
    the user malloc_fn, to agree with the prototype in png.h
  Added a pop/push operation to pnggccrd.c, to preserve Eflag (Maxim Sobolev)
  Updated makefile.sgi to recognize LIBPATH and INCPATH.
  Updated various makefiles so "make clean" does not remove previous major
    version of the shared library.

Version 1.2.1rc2 [December 4, 2001]
  Always allocate 256-entry internal palette, hist, and trans arrays, to
    avoid out-of-bounds memory reference caused by invalid PNG datastreams.
  Added a check for prefix_length > data_length in iCCP chunk handler.

Version 1.2.1 [December 7, 2001]
  None.

Version 1.2.2beta1 [February 22, 2002]
  Fixed a bug with reading the length of iCCP profiles (Larry Reeves).
  Revised makefile.linux, makefile.gcmmx, and makefile.sgi to generate
    libpng.a, libpng12.so (not libpng.so.3), and libpng12/png.h
  Revised makefile.darwin to remove "-undefined suppress" option.
  Added checks for gamma and chromaticity values over 21474.83, which exceed
    the limit for PNG unsigned 32-bit integers when encoded.
  Revised calls to png_create_read_struct() and png_create_write_struct()
    for simpler debugging.
  Revised png_zalloc() so zlib handles errors (uses PNG_FLAG_MALLOC_NULL_MEM_OK)

Version 1.2.2beta2 [February 23, 2002]
  Check chunk_length and idat_size for invalid (over PNG_MAX_UINT) lengths.
  Check for invalid image dimensions in png_get_IHDR.
  Added missing "fi;" in the install target of the SGI makefiles.
  Added install-static to all makefiles that make shared libraries.
  Always do gamma compensation when image is partially transparent.

Version 1.2.2beta3 [March 7, 2002]
  Compute background.gray and background_1.gray even when color_type is RGB
    in case image gets reduced to gray later.
  Modified shared-library makefiles to install pkgconfig/libpngNN.pc.
  Export (with PNGAPI) png_zalloc, png_zfree, and png_handle_as_unknown
  Removed unused png_write_destroy_info prototype from png.h
  Eliminated incorrect use of width_mmx from pnggccrd.c in pixel_bytes == 8 case
  Added install-shared target to all makefiles that make shared libraries.
  Stopped a double free of palette, hist, and trans when not using free_me.
  Added makefile.32sunu for Sun Ultra 32 and makefile.64sunu for Sun Ultra 64.

Version 1.2.2beta4 [March 8, 2002]
  Compute background.gray and background_1.gray even when color_type is RGB
    in case image gets reduced to gray later (Jason Summers).
  Relocated a misplaced /bin/rm in the "install-shared" makefile targets
  Added PNG_1_0_X macro which can be used to build a 1.0.x-compatible library.

Version 1.2.2beta5 [March 26, 2002]
  Added missing PNGAPI to several function definitions.
  Check for invalid bit_depth or color_type in png_get_IHDR(), and
    check for missing PLTE or IHDR in png_push_read_chunk() (Matthias Clasen).
  Revised iTXt support to accept NULL for lang and lang_key.
  Compute gamma for color components of background even when color_type is gray.
  Changed "()" to "{}" in scripts/libpng.pc.in.
  Revised makefiles to put png.h and pngconf.h only in $prefix/include/libpngNN
  Revised makefiles to make symlink to libpng.so.NN in addition to libpngNN.so

Version 1.2.2beta6 [March 31, 2002]

Version 1.0.13beta1 [March 31, 2002]
  Prevent png_zalloc() from trying to memset memory that it failed to acquire.
  Add typecasts of PNG_MAX_UINT in pngset_cHRM_fixed() (Matt Holgate).
  Ensure that the right function (user or default) is used to free the
    png_struct after an error in png_create_read_struct_2().

Version 1.2.2rc1 [April 7, 2002]

Version 1.0.13rc1 [April 7, 2002]
  Save the ebx register in pnggccrd.c (Sami Farin)
  Add "mem_ptr = png_ptr->mem_ptr" in png_destroy_write_struct() (Paul Gardner).
  Updated makefiles to put headers in include/libpng and remove old include/*.h.

Version 1.2.2 [April 15, 2002]

Version 1.0.13 [April 15, 2002]
  Revised description of png_set_filter() in libpng.3/libpng.txt.
  Revised makefile.netbsd and added makefile.neNNbsd and makefile.freebsd

Version 1.0.13patch01 [April 17, 2002]

Version 1.2.2patch01 [April 17, 2002]
  Changed ${PNGMAJ}.${PNGVER} bug to ${PNGVER} in makefile.sgi and
    makefile.sggcc
  Fixed VER -> PNGVER typo in makefile.macosx and added install-static to
    install
  Added install: target to makefile.32sunu and makefile.64sunu

Version 1.0.13patch03 [April 18, 2002]

Version 1.2.2patch03 [April 18, 2002]
  Revised 15 makefiles to link libpng.a to libpngNN.a and the include libpng
  subdirectory to libpngNN subdirectory without the full pathname.
  Moved generation of libpng.pc from "install" to "all" in 15 makefiles.

Version 1.2.3rc1 [April 28, 2002]
  Added install-man target to 15 makefiles (Dimitri Papadopolous-Orfanos).
  Added $(DESTDIR) feature to 24 makefiles (Tim Mooney)
  Fixed bug with $prefix, should be $(prefix) in makefile.hpux.
  Updated cygwin-specific portion of pngconf.h and revised makefile.cygwin
  Added a link from libpngNN.pc to libpng.pc in 15 makefiles.
  Added links from include/libpngNN/*.h to include/*.h in 24 makefiles.
  Revised makefile.darwin to make relative links without full pathname.
  Added setjmp() at the end of png_create_*_struct_2() in case user forgets
    to put one in their application.
  Restored png_zalloc() and png_zfree() prototypes to version 1.2.1 and
    removed them from module definition files.

Version 1.2.3rc2 [May 1, 2002]
  Fixed bug in reporting number of channels in pngget.c and pngset.c,
    that was introduced in version 1.2.2beta5.
  Exported png_zalloc(), png_zfree(), png_default_read(), png_default_write(),
    png_default_flush(), and png_push_fill_buffer() and included them in
    module definition files.
  Added "libpng.pc" dependency to the "install-shared" target in 15 makefiles.

Version 1.2.3rc3 [May 1, 2002]
  Revised prototype for png_default_flush()
  Remove old libpng.pc and libpngNN.pc before installing new ones.

Version 1.2.3rc4 [May 2, 2002]
  Typos in *.def files (png_default_read|write -> png_default_read|write_data)
  In makefiles, changed rm libpng.NN.pc to rm libpngNN.pc
  Added libpng-config and libpngNN-config and modified makefiles to install
    them.
  Changed $(MANPATH) to $(DESTDIR)$(MANPATH) in makefiles
  Added "Win32 DLL VB" configuration to projects/msvc/libpng.dsp

Version 1.2.3rc5 [May 11, 2002]
  Changed "error" and "message" in prototypes to "error_message" and
    "warning_message" to avoid namespace conflict.
  Revised 15 makefiles to build libpng-config from libpng-config-*.in
  Once more restored png_zalloc and png_zfree to regular nonexported form.
  Restored png_default_read|write_data, png_default_flush, png_read_fill_buffer
    to nonexported form, but with PNGAPI, and removed them from module def
    files.

Version 1.2.3rc6 [May 14, 2002]
  Removed "PNGAPI" from png_zalloc() and png_zfree() in png.c
  Changed "Gz" to "Gd" in projects/msvc/libpng.dsp and zlib.dsp.
  Removed leftover libpng-config "sed" script from four makefiles.
  Revised libpng-config creating script in 16 makefiles.

Version 1.2.3 [May 22, 2002]
  Revised libpng-config target in makefile.cygwin.
  Removed description of png_set_mem_fn() from documentation.
  Revised makefile.freebsd.
  Minor cosmetic changes to 15 makefiles, e.g., $(DI) = $(DESTDIR)/$(INCDIR).
  Revised projects/msvc/README.txt
  Changed -lpng to -lpngNN in LDFLAGS in several makefiles.

Version 1.2.4beta1 [May 24, 2002]
  Added libpng.pc and libpng-config to "all:" target in 16 makefiles.
  Fixed bug in 16 makefiles: $(DESTDIR)/$(LIBPATH) to $(DESTDIR)$(LIBPATH)
  Added missing "\" before closing double quote in makefile.gcmmx.
  Plugged various memory leaks; added png_malloc_warn() and png_set_text_2()
    functions.

Version 1.2.4beta2 [June 25, 2002]
  Plugged memory leak of png_ptr->current_text (Matt Holgate).
  Check for buffer overflow before reading CRC in pngpread.c (Warwick Allison)
  Added -soname to the loader flags in makefile.dec, makefile.sgi, and
    makefile.sggcc.
  Added "test-installed" target to makefile.linux, makefile.gcmmx,
    makefile.sgi, and makefile.sggcc.

Version 1.2.4beta3 [June 28, 2002]
  Plugged memory leak of row_buf in pngtest.c when there is a png_error().
  Detect buffer overflow in pngpread.c when IDAT is corrupted with extra data.
  Added "test-installed" target to makefile.32sunu, makefile.64sunu,
    makefile.beos, makefile.darwin, makefile.dec, makefile.macosx,
    makefile.solaris, makefile.hpux, makefile.hpgcc, and makefile.so9.

Version 1.2.4rc1 and 1.0.14rc1 [July 2, 2002]
  Added "test-installed" target to makefile.cygwin and makefile.sco.
  Revised pnggccrd.c to be able to back out version 1.0.x via PNG_1_0_X macro.

Version 1.2.4 and 1.0.14 [July 8, 2002]
  Changed png_warning() to png_error() when width is too large to process.

Version 1.2.4patch01 [July 20, 2002]
  Revised makefile.cygwin to use DLL number 12 instead of 13.

Version 1.2.5beta1 [August 6, 2002]
  Added code to contrib/gregbook/readpng2.c to ignore unused chunks.
  Replaced toucan.png in contrib/gregbook (it has been corrupt since 1.0.11)
  Removed some stray *.o files from contrib/gregbook.
  Changed png_error() to png_warning() about "Too much data" in pngpread.c
    and about "Extra compressed data" in pngrutil.c.
  Prevent png_ptr->pass from exceeding 7 in png_push_finish_row().
  Updated makefile.hpgcc
  Updated png.c and pnggccrd.c handling of return from png_mmx_support()

Version 1.2.5beta2 [August 15, 2002]
  Only issue png_warning() about "Too much data" in pngpread.c when avail_in
    is nonzero.
  Updated makefiles to install a separate libpng.so.3 with its own rpath.

Version 1.2.5rc1 and 1.0.15rc1 [August 24, 2002]
  Revised makefiles to not remove previous minor versions of shared libraries.

Version 1.2.5rc2 and 1.0.15rc2 [September 16, 2002]
  Revised 13 makefiles to remove "-lz" and "-L$(ZLIBLIB)", etc., from shared
    library loader directive.
  Added missing "$OBJSDLL" line to makefile.gcmmx.
  Added missing "; fi" to makefile.32sunu.

Version 1.2.5rc3 and 1.0.15rc3 [September 18, 2002]
  Revised libpng-config script.

Version 1.2.5 and 1.0.15 [October 3, 2002]
  Revised makefile.macosx, makefile.darwin, makefile.hpgcc, and makefile.hpux,
    and makefile.aix.
  Relocated two misplaced PNGAPI lines in pngtest.c

Version 1.2.6beta1 [October 22, 2002]
  Commented out warning about uninitialized mmx_support in pnggccrd.c.
  Changed "IBMCPP__" flag to "__IBMCPP__" in pngconf.h.
  Relocated two more misplaced PNGAPI lines in pngtest.c
  Fixed memory overrun bug in png_do_read_filler() with 16-bit datastreams,
    introduced in version 1.0.2.
  Revised makefile.macosx, makefile.dec, makefile.aix, and makefile.32sunu.

Version 1.2.6beta2 [November 1, 2002]
  Added libpng-config "--ldopts" output.
  Added "AR=ar" and "ARFLAGS=rc" and changed "ar rc" to "$(AR) $(ARFLAGS)"
    in makefiles.

Version 1.2.6beta3 [July 18, 2004]
  Reverted makefile changes from version 1.2.6beta2 and some of the changes
    from version 1.2.6beta1; these will be postponed until version 1.2.7.
    Version 1.2.6 is going to be a simple bugfix release.
  Changed the one instance of "ln -sf" to "ln -f -s" in each Sun makefile.
  Fixed potential overrun in pngerror.c by using strncpy instead of memcpy.
  Added "#!/bin/sh" at the top of configure, for recognition of the
    'x' flag under Cygwin (Cosmin).
  Optimized vacuous tests that silence compiler warnings, in png.c (Cosmin).
  Added support for PNG_USER_CONFIG, in pngconf.h (Cosmin).
  Fixed the special memory handler for Borland C under DOS, in pngmem.c
    (Cosmin).
  Removed some spurious assignments in pngrutil.c (Cosmin).
  Replaced 65536 with 65536L, and 0xffff with 0xffffL, to silence warnings
    on 16-bit platforms (Cosmin).
  Enclosed shift op expressions in parentheses, to silence warnings (Cosmin).
  Used proper type png_fixed_point, to avoid problems on 16-bit platforms,
    in png_handle_sRGB() (Cosmin).
  Added compression_type to png_struct, and optimized the window size
    inside the deflate stream (Cosmin).
  Fixed definition of isnonalpha(), in pngerror.c and pngrutil.c (Cosmin).
  Fixed handling of unknown chunks that come after IDAT (Cosmin).
  Allowed png_error() and png_warning() to work even if png_ptr == NULL
    (Cosmin).
  Replaced row_info->rowbytes with row_bytes in png_write_find_filter()
    (Cosmin).
  Fixed definition of PNG_LIBPNG_VER_DLLNUM (Simon-Pierre).
  Used PNG_LIBPNG_VER and PNG_LIBPNG_VER_STRING instead of the hardcoded
    values in png.c (Simon-Pierre, Cosmin).
  Initialized png_libpng_ver[] with PNG_LIBPNG_VER_STRING (Simon-Pierre).
  Replaced PNG_LIBPNG_VER_MAJOR with PNG_LIBPNG_VER_DLLNUM in png.rc
    (Simon-Pierre).
  Moved the definition of PNG_HEADER_VERSION_STRING near the definitions
    of the other PNG_LIBPNG_VER_... symbols in png.h (Cosmin).
  Relocated #ifndef PNGAPI guards in pngconf.h (Simon-Pierre, Cosmin).
  Updated scripts/makefile.vc(a)win32 (Cosmin).
  Updated the MSVC project (Simon-Pierre, Cosmin).
  Updated the Borland C++ Builder project (Cosmin).
  Avoided access to asm_flags in pngvcrd.c, if PNG_1_0_X is defined (Cosmin).
  Commented out warning about uninitialized mmx_support in pngvcrd.c (Cosmin).
  Removed scripts/makefile.bd32 and scripts/pngdef.pas (Cosmin).
  Added extra guard around inclusion of Turbo C memory headers, in pngconf.h
    (Cosmin).
  Renamed projects/msvc/ to projects/visualc6/, and projects/borland/ to
    projects/cbuilder5/ (Cosmin).
  Moved projects/visualc6/png32ms.def to scripts/pngw32.def,
    and projects/visualc6/png.rc to scripts/pngw32.rc (Cosmin).
  Added projects/visualc6/pngtest.dsp; removed contrib/msvctest/ (Cosmin).
  Changed line endings to DOS style in cbuilder5 and visualc6 files, even
    in the tar.* distributions (Cosmin).
  Updated contrib/visupng/VisualPng.dsp (Cosmin).
  Updated contrib/visupng/cexcept.h to version 2.0.0 (Cosmin).
  Added a separate distribution with "configure" and supporting files (Junichi).

Version 1.2.6beta4 [July 28, 2004]
  Added user ability to change png_size_t via a PNG_SIZE_T macro.
  Added png_sizeof() and png_convert_size() functions.
  Added PNG_SIZE_MAX (maximum value of a png_size_t variable.
  Added check in png_malloc_default() for (size_t)size != (png_uint_32)size
    which would indicate an overflow.
  Changed sPLT failure action from png_error to png_warning and abandon chunk.
  Changed sCAL and iCCP failures from png_error to png_warning and abandon.
  Added png_get_uint_31(png_ptr, buf) function.
  Added PNG_UINT_32_MAX macro.
  Renamed PNG_MAX_UINT to PNG_UINT_31_MAX.
  Made png_zalloc() issue a png_warning and return NULL on potential
    overflow.
  Turn on PNG_NO_ZALLOC_ZERO by default in version 1.2.x
  Revised "clobber list" in pnggccrd.c so it will compile under gcc-3.4.
  Revised Borland portion of png_malloc() to return NULL or issue
    png_error() according to setting of PNG_FLAG_MALLOC_NULL_MEM_OK.
  Added PNG_NO_SEQUENTIAL_READ_SUPPORTED macro to conditionally remove
    sequential read support.
  Added some "#if PNG_WRITE_SUPPORTED" blocks.
  Added #ifdef to remove some redundancy in png_malloc_default().
  Use png_malloc instead of png_zalloc to allocate the pallete.

Version 1.0.16rc1 and 1.2.6rc1 [August 4, 2004]
  Fixed buffer overflow vulnerability (CVE-2004-0597) in png_handle_tRNS().
  Fixed NULL dereference vulnerability (CVE-2004-0598) in png_handle_iCCP().
  Fixed integer overflow vulnerability (CVE-2004-0599) in png_read_png().
  Fixed some harmless bugs in png_handle_sBIT, etc, that would cause
    duplicate chunk types to go undetected.
  Fixed some timestamps in the -config version
  Rearranged order of processing of color types in png_handle_tRNS().
  Added ROWBYTES macro to calculate rowbytes without integer overflow.
  Updated makefile.darwin and removed makefile.macosx from scripts directory.
  Imposed default one million column, one-million row limits on the image
    dimensions, and added png_set_user_limits() function to override them.
  Revised use of PNG_SET_USER_LIMITS_SUPPORTED macro.
  Fixed wrong cast of returns from png_get_user_width|height_max().
  Changed some "keep the compiler happy" from empty statements to returns,
  Revised libpng.txt to remove 1.2.x stuff from the 1.0.x distribution

Version 1.0.16rc2 and 1.2.6rc2 [August 7, 2004]
  Revised makefile.darwin and makefile.solaris.  Removed makefile.macosx.
  Revised pngtest's png_debug_malloc() to use png_malloc() instead of
    png_malloc_default() which is not supposed to be exported.
  Fixed off-by-one error in one of the conversions to PNG_ROWBYTES() in
    pngpread.c.  Bug was introduced in 1.2.6rc1.
  Fixed bug in RGB to RGBX transformation introduced in 1.2.6rc1.
  Fixed old bug in RGB to Gray transformation.
  Fixed problem with 64-bit compilers by casting arguments to abs()
    to png_int_32.
  Changed "ln -sf" to "ln -f -s" in three makefiles (solaris, sco, so9).
  Changed "HANDLE_CHUNK_*" to "PNG_HANDLE_CHUNK_*" (Cosmin)
  Added "-@/bin/rm -f $(DL)/$(LIBNAME).so.$(PNGMAJ)" to 15 *NIX makefiles.
  Added code to update the row_info->colortype in png_do_read_filler() (MSB).

Version 1.0.16rc3 and 1.2.6rc3 [August 9, 2004]
  Eliminated use of "abs()" in testing cHRM and gAMA values, to avoid
    trouble with some 64-bit compilers.  Created PNG_OUT_OF_RANGE() macro.
  Revised documentation of png_set_keep_unknown_chunks().
  Check handle_as_unknown status in pngpread.c, as in pngread.c previously.
  Moved  "PNG_HANDLE_CHUNK_*" macros out of PNG_INTERNAL section of png.h
  Added "rim" definitions for CONST4 and CONST6 in pnggccrd.c

Version 1.0.16rc4 and 1.2.6rc4 [August 10, 2004]
  Fixed mistake in pngtest.c introduced in 1.2.6rc2 (declaration of
    "pinfo" was out of place).

Version 1.0.16rc5 and 1.2.6rc5 [August 10, 2004]
  Moved  "PNG_HANDLE_CHUNK_*" macros out of PNG_ASSEMBLER_CODE_SUPPORTED
    section of png.h where they were inadvertently placed in version rc3.

Version 1.2.6 and 1.0.16 [August 15, 2004]
  Revised pngtest so memory allocation testing is only done when PNG_DEBUG==1.

Version 1.2.7beta1 [August 26, 2004]
  Removed unused pngasmrd.h file.
  Removed references to uu.net for archived files.  Added references to
    PNG Spec (second edition) and the PNG ISO/IEC Standard.
  Added "test-dd" target in 15 makefiles, to run pngtest in DESTDIR.
  Fixed bug with "optimized window size" in the IDAT datastream, that
    causes libpng to write PNG files with incorrect zlib header bytes.

Version 1.2.7beta2 [August 28, 2004]
  Fixed bug with sCAL chunk and big-endian machines (David Munro).
  Undid new code added in 1.2.6rc2 to update the color_type in
    png_set_filler().
  Added png_set_add_alpha() that updates color type.

Version 1.0.17rc1 and 1.2.7rc1 [September 4, 2004]
  Revised png_set_strip_filler() to not remove alpha if color_type has alpha.

Version 1.2.7 and 1.0.17 [September 12, 2004]
  Added makefile.hp64
  Changed projects/msvc/png32ms.def to scripts/png32ms.def in makefile.cygwin

Version 1.2.8beta1 [November 1, 2004]
  Fixed bug in png_text_compress() that would fail to complete a large block.
  Fixed bug, introduced in libpng-1.2.7, that overruns a buffer during
    strip alpha operation in png_do_strip_filler().
  Added PNG_1_2_X definition in pngconf.h
  Use #ifdef to comment out png_info_init in png.c and png_read_init in
    pngread.c (as of 1.3.0)

Version 1.2.8beta2 [November 2, 2004]
  Reduce color_type to a nonalpha type after strip alpha operation in
    png_do_strip_filler().

Version 1.2.8beta3 [November 3, 2004]
  Revised definitions of PNG_MAX_UINT_32, PNG_MAX_SIZE, and PNG_MAXSUM

Version 1.2.8beta4 [November 12, 2004]
  Fixed (again) definition of PNG_LIBPNG_VER_DLLNUM in png.h (Cosmin).
  Added PNG_LIBPNG_BUILD_PRIVATE in png.h (Cosmin).
  Set png_ptr->zstream.data_type to Z_BINARY, to avoid unnecessary detection
    of data type in deflate (Cosmin).
  Deprecated but continue to support SPECIALBUILD and PRIVATEBUILD in favor of
    PNG_LIBPNG_BUILD_SPECIAL_STRING and PNG_LIBPNG_BUILD_PRIVATE_STRING.

Version 1.2.8beta5 [November 20, 2004]
  Use png_ptr->flags instead of png_ptr->transformations to pass
    PNG_STRIP_ALPHA info to png_do_strip_filler(), to preserve ABI
    compatibility.
  Revised handling of SPECIALBUILD, PRIVATEBUILD,
    PNG_LIBPNG_BUILD_SPECIAL_STRING and PNG_LIBPNG_BUILD_PRIVATE_STRING.

Version 1.2.8rc1 [November 24, 2004]
  Moved handling of BUILD macros from pngconf.h to png.h
  Added definition of PNG_LIBPNG_BASE_TYPE in png.h, inadvertently
    omitted from beta5.
  Revised scripts/pngw32.rc
  Despammed mailing addresses by masking "@" with "at".
  Inadvertently installed a supposedly faster test version of pngrutil.c

Version 1.2.8rc2 [November 26, 2004]
  Added two missing "\" in png.h
  Change tests in pngread.c and pngpread.c to
    if (png_ptr->transformations || (png_ptr->flags&PNG_FLAG_STRIP_ALPHA))
       png_do_read_transformations(png_ptr);

Version 1.2.8rc3 [November 28, 2004]
  Reverted pngrutil.c to version libpng-1.2.8beta5.
  Added scripts/makefile.elf with supporting code in pngconf.h for symbol
    versioning (John Bowler).

Version 1.2.8rc4 [November 29, 2004]
  Added projects/visualc7 (Simon-pierre).

Version 1.2.8rc5 [November 29, 2004]
  Fixed new typo in scripts/pngw32.rc

Version 1.2.8 [December 3, 2004]
  Removed projects/visualc7, added projects/visualc71.

Version 1.2.9beta1 [February 21, 2006]
  Initialized some structure members in pngwutil.c to avoid gcc-4.0.0 complaints
  Revised man page and libpng.txt to make it clear that one should not call
    png_read_end or png_write_end after png_read_png or png_write_png.
  Updated references to png-mng-implement mailing list.
  Fixed an incorrect typecast in pngrutil.c
  Added PNG_NO_READ_SUPPORTED conditional for making a write-only library.
  Added PNG_NO_WRITE_INTERLACING_SUPPORTED conditional.
  Optimized alpha-inversion loops in pngwtran.c
  Moved test for nonzero gamma outside of png_build_gamma_table() in pngrtran.c
  Make sure num_trans is <= 256 before copying data in png_set_tRNS().
  Make sure num_palette is <= 256 before copying data in png_set_PLTE().
  Interchanged order of write_swap_alpha and write_invert_alpha transforms.
  Added parentheses in the definition of PNG_LIBPNG_BUILD_TYPE (Cosmin).
  Optimized zlib window flag (CINFO) in contrib/pngsuite/*.png (Cosmin).
  Updated scripts/makefile.bc32 for Borland C++ 5.6 (Cosmin).
  Exported png_get_uint_32, png_save_uint_32, png_get_uint_16, png_save_uint_16,
    png_get_int_32, png_save_int_32, png_get_uint_31 (Cosmin).
  Added type cast (png_byte) in png_write_sCAL() (Cosmin).
  Fixed scripts/makefile.cygwin (Christian Biesinger, Cosmin).
  Default iTXt support was inadvertently enabled.

Version 1.2.9beta2 [February 21, 2006]
  Check for png_rgb_to_gray and png_gray_to_rgb read transformations before
    checking for png_read_dither in pngrtran.c
  Revised checking of chromaticity limits to accommodate extended RGB
    colorspace (John Denker).
  Changed line endings in some of the project files to CRLF, even in the
    "Unix" tar distributions (Cosmin).
  Made png_get_int_32 and png_save_int_32 always available (Cosmin).
  Updated scripts/pngos2.def, scripts/pngw32.def and projects/wince/png32ce.def
    with the newly exported functions.
  Eliminated distributions without the "configure" script.
  Updated INSTALL instructions.

Version 1.2.9beta3 [February 24, 2006]
  Fixed CRCRLF line endings in contrib/visupng/VisualPng.dsp
  Made libpng.pc respect EXEC_PREFIX (D. P. Kreil, J. Bowler)
  Removed reference to pngasmrd.h from Makefile.am
  Renamed CHANGES to ChangeLog.
  Renamed LICENSE to COPYING.
  Renamed ANNOUNCE to NEWS.
  Created AUTHORS file.

Version 1.2.9beta4 [March 3, 2006]
  Changed definition of PKGCONFIG from $prefix/lib to $libdir in configure.ac
  Reverted to filenames LICENSE and ANNOUNCE; removed AUTHORS and COPYING.
  Removed newline from the end of some error and warning messages.
  Removed test for sqrt() from configure.ac and configure.
  Made swap tables in pngtrans.c PNG_CONST (Carlo Bramix).
  Disabled default iTXt support that was inadvertently enabled in
    libpng-1.2.9beta1.
  Added "OS2" to list of systems that don't need underscores, in pnggccrd.c
  Removed libpng version and date from *.c files.

Version 1.2.9beta5 [March 4, 2006]
  Removed trailing blanks from source files.
  Put version and date of latest change in each source file, and changed
    copyright year accordingly.
  More cleanup of configure.ac, Makefile.am, and associated scripts.
  Restored scripts/makefile.elf which was inadvertently deleted.

Version 1.2.9beta6 [March 6, 2006]
  Fixed typo (RELEASE) in configuration files.

Version 1.2.9beta7 [March 7, 2006]
  Removed libpng.vers and libpng.sym from libpng12_la_SOURCES in Makefile.am
  Fixed inconsistent #ifdef's around png_sig_bytes() and png_set_sCAL_s()
    in png.h.
  Updated makefile.elf as suggested by debian.
  Made cosmetic changes to some makefiles, adding LN_SF and other macros.
  Made some makefiles accept "exec_prefix".

Version 1.2.9beta8 [March 9, 2006]
  Fixed some "#if defined (..." which should be "#if defined(..."
    Bug introduced in libpng-1.2.8.
  Fixed inconsistency in definition of png_default_read_data()
  Restored blank that was lost from makefile.sggcc "clean" target in beta7.
  Revised calculation of "current" and "major" for irix in ltmain.sh
  Changed "mkdir" to "MKDIR_P" in some makefiles.
  Separated PNG_EXPAND and PNG_EXPAND_tRNS.
  Added png_set_expand_gray_1_2_4_to_8() and deprecated
    png_set_gray_1_2_4_to_8() which also expands tRNS to alpha.

Version 1.2.9beta9 [March 10, 2006]
  Include "config.h" in pngconf.h when available.
  Added some checks for NULL png_ptr or NULL info_ptr (timeless)

Version 1.2.9beta10 [March 20, 2006]
  Removed extra CR from contrib/visualpng/VisualPng.dsw (Cosmin)
  Made pnggccrd.c PIC-compliant (Christian Aichinger).
  Added makefile.mingw (Wolfgang Glas).
  Revised pngconf.h MMX checking.

Version 1.2.9beta11 [March 22, 2006]
  Fixed out-of-order declaration in pngwrite.c that was introduced in beta9
  Simplified some makefiles by using LIBSO, LIBSOMAJ, and LIBSOVER macros.

Version 1.2.9rc1 [March 31, 2006]
  Defined PNG_USER_PRIVATEBUILD when including "pngusr.h" (Cosmin).
  Removed nonsensical assertion check from pngtest.c (Cosmin).

Version 1.2.9 [April 14, 2006]
  Revised makefile.beos and added "none" selector in ltmain.sh

Version 1.2.10beta1 [April 15, 2006]
  Renamed "config.h" to "png_conf.h" and revised Makefile.am to add
    -DPNG_BUILDING_LIBPNG to compile directive, and modified pngconf.h
    to include png_conf.h only when PNG_BUILDING_LIBPNG is defined.

Version 1.2.10beta2 [April 15, 2006]
  Manually updated Makefile.in and configure.  Changed png_conf.h.in
    back to config.h.

Version 1.2.10beta3 [April 15, 2006]
  Change png_conf.h back to config.h in pngconf.h.

Version 1.2.10beta4 [April 16, 2006]
  Change PNG_BUILDING_LIBPNG to PNG_CONFIGURE_LIBPNG in config/Makefile*.

Version 1.2.10beta5 [April 16, 2006]
  Added a configure check for compiling assembler code in pnggccrd.c

Version 1.2.10beta6 [April 17, 2006]
  Revised the configure check for pnggccrd.c
  Moved -DPNG_CONFIGURE_LIBPNG into @LIBPNG_DEFINES@
  Added @LIBPNG_DEFINES@ to arguments when building libpng.sym

Version 1.2.10beta7 [April 18, 2006]
  Change "exec_prefix=$prefix" to "exec_prefix=$(prefix)" in makefiles.

Version 1.2.10rc1 [April 19, 2006]
  Ensure pngconf.h doesn't define both PNG_USE_PNGGCCRD and PNG_USE_PNGVCRD
  Fixed "LN_FS" typo in makefile.sco and makefile.solaris.

Version 1.2.10rc2 [April 20, 2006]
  Added a backslash between -DPNG_CONFIGURE_LIBPNG and -DPNG_NO_ASSEMBLER_CODE
   in configure.ac and configure
  Made the configure warning about versioned symbols less arrogant.

Version 1.2.10rc3 [April 21, 2006]
  Added a note in libpng.txt that png_set_sig_bytes(8) can be used when
    writing an embedded PNG without the 8-byte signature.
  Revised makefiles and configure to avoid making links to libpng.so.*

Version 1.2.10 [April 23, 2006]
  Reverted configure to "rc2" state.

Version 1.2.11beta1 [May 31, 2006]
  scripts/libpng.pc.in contained "configure" style version info and would
    not work with makefiles.
  The shared-library makefiles were linking to libpng.so.0 instead of
    libpng.so.3 compatibility as the library.

Version 1.2.11beta2 [June 2, 2006]
  Increased sprintf buffer from 50 to 52 chars in pngrutil.c to avoid
    buffer overflow.
  Fixed bug in example.c (png_set_palette_rgb -> png_set_palette_to_rgb)

Version 1.2.11beta3 [June 5, 2006]
  Prepended "#! /bin/sh" to ltmail.sh and contrib/pngminus/*.sh (Cosmin).
  Removed the accidental leftover Makefile.in~ (Cosmin).
  Avoided potential buffer overflow and optimized buffer in
    png_write_sCAL(), png_write_sCAL_s() (Cosmin).
  Removed the include directories and libraries from CFLAGS and LDFLAGS
    in scripts/makefile.gcc (Nelson A. de Oliveira, Cosmin).

Version 1.2.11beta4 [June 6, 2006]
  Allow zero-length IDAT chunks after the entire zlib datastream, but not
    after another intervening chunk type.

Version 1.0.19rc1, 1.2.11rc1 [June 13, 2006]
  Deleted extraneous square brackets from [config.h] in configure.ac

Version 1.0.19rc2, 1.2.11rc2 [June 14, 2006]
  Added prototypes for PNG_INCH_CONVERSIONS functions to png.h
  Revised INSTALL and autogen.sh
  Fixed typo in several makefiles (-W1 should be -Wl)
  Added typedef for png_int_32 and png_uint_32 on 64-bit systems.

Version 1.0.19rc3, 1.2.11rc3 [June 15, 2006]
  Removed the new typedefs for 64-bit systems (delay until version 1.4.0)
  Added one zero element to png_gamma_shift[] array in pngrtran.c to avoid
    reading out of bounds.

Version 1.0.19rc4, 1.2.11rc4 [June 15, 2006]
  Really removed the new typedefs for 64-bit systems.

Version 1.0.19rc5, 1.2.11rc5 [June 22, 2006]
  Removed png_sig_bytes entry from scripts/pngw32.def

Version 1.0.19, 1.2.11 [June 26, 2006]
  None.

Version 1.0.20, 1.2.12 [June 27, 2006]
  Really increased sprintf buffer from 50 to 52 chars in pngrutil.c to avoid
    buffer overflow.

Version 1.2.13beta1 [October 2, 2006]
  Removed AC_FUNC_MALLOC from configure.ac
  Work around Intel-Mac compiler bug by setting PNG_NO_MMX_CODE in pngconf.h
  Change "logical" to "bitwise" throughout documentation.
  Detect and fix attempt to write wrong iCCP profile length (CVE-2006-7244)

Version 1.0.21, 1.2.13 [November 14, 2006]
  Fix potential buffer overflow in sPLT chunk handler.
  Fix Makefile.am to not try to link to noexistent files.
  Check all exported functions for NULL png_ptr.

Version 1.2.14beta1 [November 17, 2006]
  Relocated three misplaced tests for NULL png_ptr.
  Built Makefile.in with automake-1.9.6 instead of 1.9.2.
  Build configure with autoconf-2.60 instead of 2.59

Version 1.2.14beta2 [November 17, 2006]
  Added some typecasts in png_zalloc().

Version 1.2.14rc1 [November 20, 2006]
  Changed "strtod" to "png_strtod" in pngrutil.c

Version 1.0.22, 1.2.14    [November 27, 2006]
  Added missing "$(srcdir)" in Makefile.am and Makefile.in

Version 1.2.15beta1 [December 3, 2006]
  Generated configure with autoconf-2.61 instead of 2.60
  Revised configure.ac to update libpng.pc and libpng-config.

Version 1.2.15beta2 [December 3, 2006]
  Always export MMX asm functions, just stubs if not building pnggccrd.c

Version 1.2.15beta3 [December 4, 2006]
  Add "png_bytep" typecast to profile while calculating length in pngwutil.c

Version 1.2.15beta4 [December 7, 2006]
  Added scripts/CMakeLists.txt
  Changed PNG_NO_ASSEMBLER_CODE to PNG_NO_MMX_CODE in scripts, like 1.4.0beta

Version 1.2.15beta5 [December 7, 2006]
  Changed some instances of PNG_ASSEMBLER_* to PNG_MMX_* in pnggccrd.c
  Revised scripts/CMakeLists.txt

Version 1.2.15beta6 [December 13, 2006]
  Revised scripts/CMakeLists.txt and configure.ac

Version 1.2.15rc1 [December 18, 2006]
  Revised scripts/CMakeLists.txt

Version 1.2.15rc2 [December 21, 2006]
  Added conditional #undef jmpbuf in pngtest.c to undo #define in AIX headers.
  Added scripts/makefile.nommx

Version 1.2.15rc3 [December 25, 2006]
  Fixed shared library numbering error that was introduced in 1.2.15beta6.

Version 1.2.15rc4 [December 27, 2006]
  Fixed handling of rgb_to_gray when png_ptr->color.gray isn't set.

Version 1.2.15rc5 [December 31, 2006]
  Revised handling of rgb_to_gray.

Version 1.2.15 [January 5, 2007]
  Added some (unsigned long) typecasts in pngtest.c to avoid printing errors.

Version 1.2.16beta1 [January 6, 2007]
  Fix bugs in makefile.nommx

Version 1.2.16beta2 [January 16, 2007]
  Revised scripts/CMakeLists.txt

Version 1.2.16 [January 31, 2007]
  No changes.

Version 1.2.17beta1 [March 6, 2007]
  Revised scripts/CMakeLists.txt to install both shared and static libraries.
  Deleted a redundant line from pngset.c.

Version 1.2.17beta2 [April 26, 2007]
  Relocated misplaced test for png_ptr == NULL in pngpread.c
  Change "==" to "&" for testing PNG_RGB_TO_GRAY_ERR & PNG_RGB_TO_GRAY_WARN
    flags.
  Changed remaining instances of PNG_ASSEMBLER_* to PNG_MMX_*
  Added pngerror() when write_IHDR fails in deflateInit2().
  Added "const" to some array declarations.
  Mention examples of libpng usage in the libpng*.txt and libpng.3 documents.

Version 1.2.17rc1 [May 4, 2007]
  No changes.

Version 1.2.17rc2 [May 8, 2007]
  Moved several PNG_HAVE_* macros out of PNG_INTERNAL because applications
    calling set_unknown_chunk_location() need them.
  Changed transformation flag from PNG_EXPAND_tRNS to PNG_EXPAND in
    png_set_expand_gray_1_2_4_to_8().
  Added png_ptr->unknown_chunk to hold working unknown chunk data, so it
    can be free'ed in case of error.  Revised unknown chunk handling in
    pngrutil.c and pngpread.c to use this structure.

Version 1.2.17rc3 [May 8, 2007]
  Revised symbol-handling in configure script.

Version 1.2.17rc4 [May 10, 2007]
  Revised unknown chunk handling to avoid storing unknown critical chunks.

Version 1.0.25 [May 15, 2007]
Version 1.2.17 [May 15, 2007]
  Added "png_ptr->num_trans=0" before error return in png_handle_tRNS,
    to eliminate a vulnerability (CVE-2007-2445, CERT VU#684664)

Version 1.0.26 [May 15, 2007]
Version 1.2.18 [May 15, 2007]
  Reverted the libpng-1.2.17rc3 change to symbol-handling in configure script

Version 1.2.19beta1 [May 18, 2007]
  Changed "const static" to "static PNG_CONST" everywhere, mostly undoing
    change of libpng-1.2.17beta2.  Changed other "const" to "PNG_CONST"
  Changed some handling of unused parameters, to avoid compiler warnings.
    "if (unused == NULL) return;" becomes "unused = unused".

Version 1.2.19beta2 [May 18, 2007]
  Only use the valid bits of tRNS value in png_do_expand() (Brian Cartier)

Version 1.2.19beta3 [May 19, 2007]
  Add some "png_byte" typecasts in png_check_keyword() and write new_key
  instead of key in zTXt chunk (Kevin Ryde).

Version 1.2.19beta4 [May 21, 2007]
  Add png_snprintf() function and use it in place of sprint() for improved
    defense against buffer overflows.

Version 1.2.19beta5 [May 21, 2007]
  Fixed png_handle_tRNS() to only use the valid bits of tRNS value.
  Changed handling of more unused parameters, to avoid compiler warnings.
  Removed some PNG_CONST in pngwutil.c to avoid compiler warnings.

Version 1.2.19beta6 [May 22, 2007]
  Added some #ifdef PNG_MMX_CODE_SUPPORTED where needed in pngvcrd.c
  Added a special "_MSC_VER" case that defines png_snprintf to _snprintf

Version 1.2.19beta7 [May 22, 2007]
  Squelched png_squelch_warnings() in pnggccrd.c and added
    an #ifdef PNG_MMX_CODE_SUPPORTED block around the declarations that caused
    the warnings that png_squelch_warnings was squelching.

Version 1.2.19beta8 [May 22, 2007]
  Removed __MMX__ from test in pngconf.h.

Version 1.2.19beta9 [May 23, 2007]
  Made png_squelch_warnings() available via PNG_SQUELCH_WARNINGS macro.
  Revised png_squelch_warnings() so it might work.
  Updated makefile.sgcc and makefile.solaris; added makefile.solaris-x86.

Version 1.2.19beta10 [May 24, 2007]
  Resquelched png_squelch_warnings(), use "__attribute__((used))" instead.

Version 1.4.0beta1 [April 20, 2006]
  Enabled iTXt support (changes png_struct, thus requires so-number change).
  Cleaned up PNG_ASSEMBLER_CODE_SUPPORTED vs PNG_MMX_CODE_SUPPORTED
  Eliminated PNG_1_0_X and PNG_1_2_X macros.
  Removed deprecated functions png_read_init, png_write_init, png_info_init,
    png_permit_empty_plte, png_set_gray_1_2_4_to_8, png_check_sig, and
    removed the deprecated macro PNG_MAX_UINT.
  Moved "PNG_INTERNAL" parts of png.h and pngconf.h into pngintrn.h
  Removed many WIN32_WCE #ifdefs (Cosmin).
  Reduced dependency on C-runtime library when on Windows (Simon-Pierre)
  Replaced sprintf() with png_sprintf() (Simon-Pierre)

Version 1.4.0beta2 [April 20, 2006]
  Revised makefiles and configure to avoid making links to libpng.so.*
  Moved some leftover MMX-related defines from pngconf.h to pngintrn.h
  Updated scripts/pngos2.def, pngw32.def, and projects/wince/png32ce.def

Version 1.4.0beta3 [May 10, 2006]
  Updated scripts/pngw32.def to comment out MMX functions.
  Added PNG_NO_GET_INT_32 and PNG_NO_SAVE_INT_32 macros.
  Scripts/libpng.pc.in contained "configure" style version info and would
    not work with makefiles.
  Revised pngconf.h and added pngconf.h.in, so makefiles and configure can
    pass defines to libpng and applications.

Version 1.4.0beta4 [May 11, 2006]
  Revised configure.ac, Makefile.am, and many of the makefiles to write
    their defines in pngconf.h.

Version 1.4.0beta5 [May 15, 2006]
  Added a missing semicolon in Makefile.am and Makefile.in
  Deleted extraneous square brackets from configure.ac

Version 1.4.0beta6 [June 2, 2006]
  Increased sprintf buffer from 50 to 52 chars in pngrutil.c to avoid
    buffer overflow.
  Changed sonum from 0 to 1.
  Removed unused prototype for png_check_sig() from png.h

Version 1.4.0beta7 [June 16, 2006]
  Exported png_write_sig (Cosmin).
  Optimized buffer in png_handle_cHRM() (Cosmin).
  Set pHYs = 2835 x 2835 pixels per meter, and added
    sCAL = 0.352778e-3 x 0.352778e-3 meters, in pngtest.png (Cosmin).
  Added png_set_benign_errors(), png_benign_error(), png_chunk_benign_error().
  Added typedef for png_int_32 and png_uint_32 on 64-bit systems.
  Added "(unsigned long)" typecast on png_uint_32 variables in printf lists.

Version 1.4.0beta8 [June 22, 2006]
  Added demonstration of user chunk support in pngtest.c, to support the
    public sTER chunk and a private vpAg chunk.

Version 1.4.0beta9 [July 3, 2006]
  Removed ordinals from scripts/pngw32.def and removed png_info_int and
    png_set_gray_1_2_4_to_8 entries.
  Inline call of png_get_uint_32() in png_get_uint_31().
  Use png_get_uint_31() to get vpAg width and height in pngtest.c
  Removed WINCE and Netware projects.
  Removed standalone Y2KINFO file.

Version 1.4.0beta10 [July 12, 2006]
  Eliminated automatic copy of pngconf.h to pngconf.h.in from configure and
    some makefiles, because it was not working reliably.  Instead, distribute
    pngconf.h.in along with pngconf.h and cause configure and some of the
    makefiles to update pngconf.h from pngconf.h.in.
  Added pngconf.h to DEPENDENCIES in Makefile.am

Version 1.4.0beta11 [August 19, 2006]
  Removed AC_FUNC_MALLOC from configure.ac.
  Added a warning when writing iCCP profile with mismatched profile length.
  Patched pnggccrd.c to assemble on x86_64 platforms.
  Moved chunk header reading into a separate function png_read_chunk_header()
    in pngrutil.c.  The chunk header (len+sig) is now serialized in a single
    operation (Cosmin).
  Implemented support for I/O states. Added png_ptr member io_state, and
    functions png_get_io_chunk_name() and png_get_io_state() in pngget.c
    (Cosmin).
  Added png_get_io_chunk_name and png_get_io_state to scripts/*.def (Cosmin).
  Renamed scripts/pngw32.* to scripts/pngwin.* (Cosmin).
  Removed the include directories and libraries from CFLAGS and LDFLAGS
    in scripts/makefile.gcc (Cosmin).
  Used png_save_uint_32() to set vpAg width and height in pngtest.c (Cosmin).
  Cast to proper type when getting/setting vpAg units in pngtest.c (Cosmin).
  Added pngintrn.h to the Visual C++ projects (Cosmin).
  Removed scripts/list (Cosmin).
  Updated copyright year in scripts/pngwin.def (Cosmin).
  Removed PNG_TYPECAST_NULL and used standard NULL consistently (Cosmin).
  Disallowed the user to redefine png_size_t, and enforced a consistent use
    of png_size_t across libpng (Cosmin).
  Changed the type of png_ptr->rowbytes, PNG_ROWBYTES() and friends
    to png_size_t (Cosmin).
  Removed png_convert_size() and replaced png_sizeof with sizeof (Cosmin).
  Removed some unnecessary type casts (Cosmin).
  Changed prototype of png_get_compression_buffer_size() and
    png_set_compression_buffer_size() to work with png_size_t instead of
    png_uint_32 (Cosmin).
  Removed png_memcpy_check() and png_memset_check() (Cosmin).
  Fixed a typo (png_byte --> png_bytep) in libpng.3 and libpng.txt (Cosmin).
  Clarified that png_zalloc() does not clear the allocated memory,
    and png_zalloc() and png_zfree() cannot be PNGAPI (Cosmin).
  Renamed png_mem_size_t to png_alloc_size_t, fixed its definition in
    pngconf.h, and used it in all memory allocation functions (Cosmin).
  Renamed pngintrn.h to pngpriv.h, added a comment at the top of the file
    mentioning that the symbols declared in that file are private, and
    updated the scripts and the Visual C++ projects accordingly (Cosmin).
  Removed circular references between pngconf.h and pngconf.h.in in
    scripts/makefile.vc*win32 (Cosmin).
  Removing trailing '.' from the warning and error messages (Cosmin).
  Added pngdefs.h that is built by makefile or configure, instead of
    pngconf.h.in (Glenn).
  Detect and fix attempt to write wrong iCCP profile length.

Version 1.4.0beta12 [October 19, 2006]
  Changed "logical" to "bitwise" in the documentation.
  Work around Intel-Mac compiler bug by setting PNG_NO_MMX_CODE in pngconf.h
  Add a typecast to stifle compiler warning in pngrutil.c

Version 1.4.0beta13 [November 10, 2006]
  Fix potential buffer overflow in sPLT chunk handler.
  Fix Makefile.am to not try to link to noexistent files.

Version 1.4.0beta14 [November 15, 2006]
  Check all exported functions for NULL png_ptr.

Version 1.4.0beta15 [November 17, 2006]
  Relocated two misplaced tests for NULL png_ptr.
  Built Makefile.in with automake-1.9.6 instead of 1.9.2.
  Build configure with autoconf-2.60 instead of 2.59
  Add "install: all" in Makefile.am so "configure; make install" will work.

Version 1.4.0beta16 [November 17, 2006]
  Added a typecast in png_zalloc().

Version 1.4.0beta17 [December 4, 2006]
  Changed "new_key[79] = '\0';" to "(*new_key)[79] = '\0';" in pngwutil.c
  Add "png_bytep" typecast to profile while calculating length in pngwutil.c

Version 1.4.0beta18 [December 7, 2006]
  Added scripts/CMakeLists.txt

Version 1.4.0beta19 [May 16, 2007]
  Revised scripts/CMakeLists.txt
  Rebuilt configure and Makefile.in with newer tools.
  Added conditional #undef jmpbuf in pngtest.c to undo #define in AIX headers.
  Added scripts/makefile.nommx

Version 1.4.0beta20 [July 9, 2008]
  Moved several PNG_HAVE_* macros from pngpriv.h to png.h because applications
    calling set_unknown_chunk_location() need them.
  Moved several macro definitions from pngpriv.h to pngconf.h
  Merge with changes to the 1.2.X branch, as of 1.2.30beta04.
  Deleted all use of the MMX assembler code and Intel-licensed optimizations.
  Revised makefile.mingw

Version 1.4.0beta21 [July 21, 2008]
  Moved local array "chunkdata" from pngrutil.c to the png_struct, so
    it will be freed by png_read_destroy() in case of a read error (Kurt
    Christensen).

Version 1.4.0beta22 [July 21, 2008]
  Change "purpose" and "buffer" to png_ptr->chunkdata to avoid memory leaking.

Version 1.4.0beta23 [July 22, 2008]
  Change "chunkdata = NULL" to "png_ptr->chunkdata = NULL" several places in
    png_decompress_chunk().

Version 1.4.0beta24 [July 25, 2008]
  Change all remaining "chunkdata" to "png_ptr->chunkdata" in
    png_decompress_chunk(), and remove "chunkdata" from parameter list.
  Put a call to png_check_chunk_name() in png_read_chunk_header().
  Revised png_check_chunk_name() to reject a name with a lowercase 3rd byte.
  Removed two calls to png_check_chunk_name() occurring later in the process.
  Define PNG_NO_ERROR_NUMBERS by default in pngconf.h

Version 1.4.0beta25 [July 30, 2008]
  Added a call to png_check_chunk_name() in pngpread.c
  Reverted png_check_chunk_name() to accept a name with a lowercase 3rd byte.
  Added png_push_have_buffer() function to pngpread.c
  Eliminated PNG_BIG_ENDIAN_SUPPORTED and associated png_get_* macros.
  Made inline expansion of png_get_*() optional with PNG_USE_READ_MACROS.
  Eliminated all PNG_USELESS_TESTS and PNG_CORRECT_PALETTE_SUPPORTED code.
  Synced contrib directory and configure files with libpng-1.2.30beta06.
  Eliminated no-longer-used pngdefs.h (but it's still built in the makefiles)
  Relocated a misplaced "#endif /* PNG_NO_WRITE_FILTER */" in pngwutil.c

Version 1.4.0beta26 [August 4, 2008]
  Removed png_push_have_buffer() function in pngpread.c.  It increased the
    compiled library size slightly.
  Changed "-Wall" to "-W -Wall" in the CFLAGS in all makefiles (Cosmin Truta)
  Declared png_ptr "volatile" in pngread.c and pngwrite.c to avoid warnings.
  Updated contrib/visupng/cexcept.h to version 2.0.1
  Added PNG_LITERAL_CHARACTER macros for #, [, and ].

Version 1.4.0beta27 [August 5, 2008]
  Revised usage of PNG_LITERAL_SHARP in pngerror.c.
  Moved newline character from individual png_debug messages into the
    png_debug macros.
  Allow user to #define their own png_debug, png_debug1, and png_debug2.

Version 1.4.0beta28 [August 5, 2008]
  Revised usage of PNG_LITERAL_SHARP in pngerror.c.
  Added PNG_STRING_NEWLINE macro

Version 1.4.0beta29 [August 9, 2008]
  Revised usage of PNG_STRING_NEWLINE to work on non-ISO compilers.
  Added PNG_STRING_COPYRIGHT macro.
  Added non-ISO versions of png_debug macros.

Version 1.4.0beta30 [August 14, 2008]
  Added premultiplied alpha feature (Volker Wiendl).

Version 1.4.0beta31 [August 18, 2008]
  Moved png_set_premultiply_alpha from pngtrans.c to pngrtran.c
  Removed extra crc check at the end of png_handle_cHRM().  Bug introduced
    in libpng-1.4.0beta20.

Version 1.4.0beta32 [August 19, 2008]
  Added PNG_WRITE_FLUSH_SUPPORTED block around new png_flush() call.
  Revised PNG_NO_STDIO version of png_write_flush()

Version 1.4.0beta33 [August 20, 2008]
  Added png_get|set_chunk_cache_max() to limit the total number of sPLT,
    text, and unknown chunks that can be stored.

Version 1.4.0beta34 [September 6, 2008]
  Shortened tIME_string to 29 bytes in pngtest.c
  Fixed off-by-one error introduced in png_push_read_zTXt() function in
    libpng-1.2.30beta04/pngpread.c (Harald van Dijk)

Version 1.4.0beta35 [October 6, 2008]
  Changed "trans_values" to "trans_color".
  Changed so-number from 0 to 14.  Some OS do not like 0.
  Revised makefile.darwin to fix shared library numbering.
  Change png_set_gray_1_2_4_to_8() to png_set_expand_gray_1_2_4_to_8()
    in example.c (debian bug report)

Version 1.4.0beta36 [October 25, 2008]
  Sync with tEXt vulnerability fix in libpng-1.2.33rc02.

Version 1.4.0beta37 [November 13, 2008]
  Added png_check_cHRM in png.c and moved checking from pngget.c, pngrutil.c,
    and pngwrite.c

Version 1.4.0beta38 [November 22, 2008]
  Added check for zero-area RGB cHRM triangle in png_check_cHRM() and
    png_check_cHRM_fixed().

Version 1.4.0beta39 [November 23, 2008]
  Revised png_warning() to write its message on standard output by default
    when warning_fn is NULL.

Version 1.4.0beta40 [November 24, 2008]
  Eliminated png_check_cHRM().  Instead, always use png_check_cHRM_fixed().
  In png_check_cHRM_fixed(), ensure white_y is > 0, and removed redundant
    check for all-zero coordinates that is detected by the triangle check.

Version 1.4.0beta41 [November 26, 2008]
  Fixed string vs pointer-to-string error in png_check_keyword().
  Rearranged test expressions in png_check_cHRM_fixed() to avoid internal
    overflows.
  Added PNG_NO_CHECK_cHRM conditional.

Version 1.4.0beta42, 43 [December 1, 2008]
  Merge png_debug with version 1.2.34beta04.

Version 1.4.0beta44 [December 6, 2008]
  Removed redundant check for key==NULL before calling png_check_keyword()
    to ensure that new_key gets initialized and removed extra warning
    (Merge with version 1.2.34beta05 -- Arvan Pritchard).

Version 1.4.0beta45 [December 9, 2008]
  In png_write_png(), respect the placement of the filler bytes in an earlier
    call to png_set_filler() (Jim Barry).

Version 1.4.0beta46 [December 10, 2008]
  Undid previous change and added PNG_TRANSFORM_STRIP_FILLER_BEFORE and
    PNG_TRANSFORM_STRIP_FILLER_AFTER conditionals and deprecated
    PNG_TRANSFORM_STRIP_FILLER (Jim Barry).

Version 1.4.0beta47 [December 15, 2008]
  Support for dithering was disabled by default, because it has never
    been well tested and doesn't work very well.  The code has not
    been removed, however, and can be enabled by building libpng with
    PNG_READ_DITHER_SUPPORTED defined.

Version 1.4.0beta48 [February 14, 2009]
  Added new exported function png_calloc().
  Combined several instances of png_malloc(); png_memset() into png_calloc().
  Removed prototype for png_freeptr() that was added in libpng-1.4.0beta24
    but was never defined.

Version 1.4.0beta49 [February 28, 2009]
  Added png_fileno() macro to pngconf.h, used in pngwio.c
  Corrected order of #ifdef's in png_debug definition in png.h
  Fixed bug introduced in libpng-1.4.0beta48 with the memset arguments
    for pcal_params.
  Fixed order of #ifdef directives in the png_debug defines in png.h
    (bug introduced in libpng-1.2.34/1.4.0beta29).
  Revised comments in png_set_read_fn() and png_set_write_fn().

Version 1.4.0beta50 [March 18, 2009]
  Use png_calloc() instead of png_malloc() to allocate big_row_buf when
    reading an interlaced file, to avoid a possible UMR.
  Undid revision of PNG_NO_STDIO version of png_write_flush().  Users
    having trouble with fflush() can build with PNG_NO_WRITE_FLUSH defined
    or supply their own flush_fn() replacement.
  Revised libpng*.txt and png.h documentation about use of png_write_flush()
    and png_set_write_fn().
  Removed fflush() from pngtest.c.
  Added "#define PNG_NO_WRITE_FLUSH" to contrib/pngminim/encoder/pngusr.h

Version 1.4.0beta51 [March 21, 2009]
  Removed new png_fileno() macro from pngconf.h .

Version 1.4.0beta52 [March 27, 2009]
  Relocated png_do_chop() ahead of building gamma tables in pngrtran.c
    This avoids building 16-bit gamma tables unnecessarily.
  Removed fflush() from pngtest.c.
  Added "#define PNG_NO_WRITE_FLUSH" to contrib/pngminim/encoder/pngusr.h
  Added a section on differences between 1.0.x and 1.2.x to libpng.3/libpng.txt

Version 1.4.0beta53 [April 1, 2009]
  Removed some remaining MMX macros from pngpriv.h
  Fixed potential memory leak of "new_name" in png_write_iCCP() (Ralph Giles)

Version 1.4.0beta54 [April 13, 2009]
  Added "ifndef PNG_SKIP_SETJMP_CHECK" block in pngconf.h to allow
    application code writers to bypass the check for multiple inclusion
    of setjmp.h when they know that it is safe to ignore the situation.
  Eliminated internal use of setjmp() in pngread.c and pngwrite.c
  Reordered ancillary chunks in pngtest.png to be the same as what
    pngtest now produces, and made some cosmetic changes to pngtest output.
  Eliminated deprecated png_read_init_3() and png_write_init_3() functions.

Version 1.4.0beta55 [April 15, 2009]
  Simplified error handling in pngread.c and pngwrite.c by putting
    the new png_read_cleanup() and png_write_cleanup() functions inline.

Version 1.4.0beta56 [April 25, 2009]
  Renamed "user_chunk_data" to "my_user_chunk_data" in pngtest.c to suppress
    "shadowed declaration" warning from gcc-4.3.3.
  Renamed "gamma" to "png_gamma" in pngset.c to avoid "shadowed declaration"
    warning about a global "gamma" variable in math.h on some platforms.

Version 1.4.0beta57 [May 2, 2009]
  Removed prototype for png_freeptr() that was added in libpng-1.4.0beta24
    but was never defined (again).
  Rebuilt configure scripts with autoconf-2.63 instead of 2.62
  Removed pngprefs.h and MMX from makefiles

Version 1.4.0beta58 [May 14, 2009]
  Changed pngw32.def to pngwin.def in makefile.mingw (typo was introduced
    in beta57).
  Clarified usage of sig_bit versus sig_bit_p in example.c (Vincent Torri)

Version 1.4.0beta59 [May 15, 2009]
  Reformated sources in libpng style (3-space intentation, comment format)
  Fixed typo in libpng docs (PNG_FILTER_AVE should be PNG_FILTER_AVG)
  Added sections about the git repository and our coding style to the
    documentation
  Relocated misplaced #endif in pngwrite.c, sCAL chunk handler.

Version 1.4.0beta60 [May 19, 2009]
  Conditionally compile png_read_finish_row() which is not used by
    progressive readers.
  Added contrib/pngminim/preader to demonstrate building minimal progressive
    decoder, based on contrib/gregbook with embedded libpng and zlib.

Version 1.4.0beta61 [May 20, 2009]
  In contrib/pngminim/*, renamed "makefile.std" to "makefile", since there
    is only one makefile in those directories, and revised the README files
    accordingly.
  More reformatting of comments, mostly to capitalize sentences.

Version 1.4.0beta62 [June 2, 2009]
  Added "#define PNG_NO_WRITE_SWAP" to contrib/pngminim/encoder/pngusr.h
    and "define PNG_NO_READ_SWAP" to decoder/pngusr.h and preader/pngusr.h
  Reformatted several remaining "else statement" into two lines.
  Added a section to the libpng documentation about using png_get_io_ptr()
    in configure scripts to detect the presence of libpng.

Version 1.4.0beta63 [June 15, 2009]
  Revised libpng*.txt and libpng.3 to mention calling png_set_IHDR()
    multiple times and to specify the sample order in the tRNS chunk,
    because the ISO PNG specification has a typo in the tRNS table.
  Changed several PNG_UNKNOWN_CHUNK_SUPPORTED to
    PNG_HANDLE_AS_UNKNOWN_SUPPORTED, to make the png_set_keep mechanism
    available for ignoring known chunks even when not saving unknown chunks.
  Adopted preference for consistent use of "#ifdef" and "#ifndef" versus
    "#if defined()" and "if !defined()" where possible.

Version 1.4.0beta64 [June 24, 2009]
  Eliminated PNG_LEGACY_SUPPORTED code.
  Moved the various unknown chunk macro definitions outside of the
    PNG_READ|WRITE_ANCILLARY_CHUNK_SUPPORTED blocks.

Version 1.4.0beta65 [June 26, 2009]
  Added a reference to the libpng license in each file.

Version 1.4.0beta66 [June 27, 2009]
  Refer to the libpng license instead of the libpng license in each file.

Version 1.4.0beta67 [July 6, 2009]
  Relocated INVERT_ALPHA within png_read_png() and png_write_png().
  Added high-level API transform PNG_TRANSFORM_GRAY_TO_RGB.
  Added an "xcode" project to the projects directory (Alam Arias).

Version 1.4.0beta68 [July 19, 2009]
  Avoid some tests in filter selection in pngwutil.c

Version 1.4.0beta69 [July 25, 2009]
  Simplified the new filter-selection test.  This runs faster in the
    common "PNG_ALL_FILTERS" and PNG_FILTER_NONE cases.
  Removed extraneous declaration from the new call to png_read_gray_to_rgb()
    (bug introduced in libpng-1.4.0beta67).
  Fixed up xcode project (Alam Arias)
  Added a prototype for png_64bit_product() in png.c

Version 1.4.0beta70 [July 27, 2009]
  Avoid a possible NULL dereference in debug build, in png_set_text_2().
    (bug introduced in libpng-0.95, discovered by Evan Rouault)

Version 1.4.0beta71 [July 29, 2009]
  Rebuilt configure scripts with autoconf-2.64.

Version 1.4.0beta72 [August 1, 2009]
  Replaced *.tar.lzma with *.tar.xz in distribution.  Get the xz codec
    from <http://tukaani.org/xz>.

Version 1.4.0beta73 [August 1, 2009]
  Reject attempt to write iCCP chunk with negative embedded profile length
    (JD Chen) (CVE-2009-5063).

Version 1.4.0beta74 [August 8, 2009]
  Changed png_ptr and info_ptr member "trans" to "trans_alpha".

Version 1.4.0beta75 [August 21, 2009]
  Removed an extra png_debug() recently added to png_write_find_filter().
  Fixed incorrect #ifdef in pngset.c regarding unknown chunk support.

Version 1.4.0beta76 [August 22, 2009]
  Moved an incorrectly located test in png_read_row() in pngread.c

Version 1.4.0beta77 [August 27, 2009]
  Removed lpXYZ.tar.bz2 (with CRLF), KNOWNBUG, libpng-x.y.z-KNOWNBUG.txt,
    and the "noconfig" files from the distribution.
  Moved CMakeLists.txt from scripts into the main libpng directory.
  Various bugfixes and improvements to CMakeLists.txt (Philip Lowman)

Version 1.4.0beta78 [August 31, 2009]
  Converted all PNG_NO_* tests to PNG_*_SUPPORTED everywhere except pngconf.h
  Eliminated PNG_NO_FREE_ME and PNG_FREE_ME_SUPPORTED macros.
  Use png_malloc plus a loop instead of png_calloc() to initialize
    row_pointers in png_read_png().

Version 1.4.0beta79 [September 1, 2009]
  Eliminated PNG_GLOBAL_ARRAYS and PNG_LOCAL_ARRAYS; always use local arrays.
  Eliminated PNG_CALLOC_SUPPORTED macro and always provide png_calloc().

Version 1.4.0beta80 [September 17, 2009]
  Removed scripts/libpng.icc
  Changed typecast of filler from png_byte to png_uint_16 in png_set_filler().
    (Dennis Gustafsson)
  Fixed typo introduced in beta78 in pngtest.c ("#if def " should be "#ifdef ")

Version 1.4.0beta81 [September 23, 2009]
  Eliminated unused PNG_FLAG_FREE_* defines from pngpriv.h
  Expanded TAB characters in pngrtran.c
  Removed PNG_CONST from all "PNG_CONST PNG_CHNK" declarations to avoid
    compiler complaints about doubly declaring things "const".
  Changed all "#if [!]defined(X)" to "if[n]def X" where possible.
  Eliminated unused png_ptr->row_buf_size

Version 1.4.0beta82 [September 25, 2009]
  Moved redundant IHDR checking into new png_check_IHDR() in png.c
    and report all errors found in the IHDR data.
  Eliminated useless call to png_check_cHRM() from pngset.c

Version 1.4.0beta83 [September 25, 2009]
  Revised png_check_IHDR() to eliminate bogus complaint about filter_type.

Version 1.4.0beta84 [September 30, 2009]
  Fixed some inconsistent indentation in pngconf.h
  Revised png_check_IHDR() to add a test for width variable less than 32-bit.

Version 1.4.0beta85 [October 1, 2009]
  Revised png_check_IHDR() again, to check info_ptr members instead of
    the contents of the returned parameters.

Version 1.4.0beta86 [October 9, 2009]
  Updated the "xcode" project (Alam Arias).
  Eliminated a shadowed declaration of "pp" in png_handle_sPLT().

Version 1.4.0rc01 [October 19, 2009]
  Trivial cosmetic changes.

Version 1.4.0beta87 [October 30, 2009]
  Moved version 1.4.0 back into beta.

Version 1.4.0beta88 [October 30, 2009]
  Revised libpng*.txt section about differences between 1.2.x and 1.4.0
    because most of the new features have now been ported back to 1.2.41

Version 1.4.0beta89 [November 1, 2009]
  More bugfixes and improvements to CMakeLists.txt (Philip Lowman)
  Removed a harmless extra png_set_invert_alpha() from pngwrite.c
  Apply png_user_chunk_cache_max within png_decompress_chunk().
  Merged libpng-1.2.41.txt with libpng-1.4.0.txt where appropriate.

Version 1.4.0beta90 [November 2, 2009]
  Removed all remaining WIN32_WCE #ifdefs except those involving the
    time.h "tm" structure

Version 1.4.0beta91 [November 3, 2009]
  Updated scripts/pngw32.def and projects/wince/png32ce.def
  Copied projects/wince/png32ce.def to the scripts directory.
  Added scripts/makefile.wce
  Patched ltmain.sh for wince support.
  Added PNG_CONVERT_tIME_SUPPORTED macro.

Version 1.4.0beta92 [November 4, 2009]
  Make inclusion of time.h in pngconf.h depend on PNG_CONVERT_tIME_SUPPORTED
  Make #define PNG_CONVERT_tIME_SUPPORTED depend on PNG_WRITE_tIME_SUPPORTED
  Revised libpng*.txt to describe differences from 1.2.40 to 1.4.0 (instead
    of differences from 1.2.41 to 1.4.0)

Version 1.4.0beta93 [November 7, 2009]
  Added PNG_DEPSTRUCT, PNG_DEPRECATED, PNG_USE_RESULT, PNG_NORETURN, and
    PNG_ALLOCATED macros to detect deprecated direct access to the
    png_struct or info_struct members and other deprecated usage in
    applications (John Bowler).
  Updated scripts/makefile* to add "-DPNG_CONFIGURE_LIBPNG" to CFLAGS,
    to prevent warnings about direct access to png structs by libpng
    functions while building libpng.  They need to be tested, especially
    those using compilers other than gcc.
  Updated projects/visualc6 and visualc71 with "/d PNG_CONFIGURE_LIBPNG".
    They should work but still need to be updated to remove
    references to pnggccrd.c or pngvcrd.c and ASM building.
  Added README.txt to the beos, cbuilder5, netware, and xcode projects warning
    that they need to be updated, to remove references to pnggccrd.c and
    pngvcrd.c and to depend on pngpriv.h
  Removed three direct references to read_info_ptr members in pngtest.c
    that were detected by the new PNG_DEPSTRUCT macro.
  Moved the png_debug macro definitions and the png_read_destroy(),
    png_write_destroy() and png_far_to_near() prototypes from png.h
    to pngpriv.h (John Bowler)
  Moved the synopsis lines for png_read_destroy(), png_write_destroy()
    png_debug(), png_debug1(), and png_debug2() from libpng.3 to libpngpf.3.

Version 1.4.0beta94 [November 9, 2009]
  Removed the obsolete, unused pnggccrd.c and pngvcrd.c files.
  Updated CMakeLists.txt to add "-DPNG_CONFIGURE_LIBPNG" to the definitions.
  Removed dependency of pngtest.o on pngpriv.h in the makefiles.
  Only #define PNG_DEPSTRUCT, etc. in pngconf.h if not already defined.

Version 1.4.0beta95 [November 10, 2009]
  Changed png_check_sig() to !png_sig_cmp() in contrib programs.
  Added -DPNG_CONFIGURE_LIBPNG to contrib/pngminm/*/makefile
  Changed png_check_sig() to !png_sig_cmp() in contrib programs.
  Corrected the png_get_IHDR() call in contrib/gregbook/readpng2.c
  Changed pngminim/*/gather.sh to stop trying to remove pnggccrd.c and pngvcrd.c
  Added dependency on pngpriv.h in contrib/pngminim/*/makefile

Version 1.4.0beta96 [November 12, 2009]
  Renamed scripts/makefile.wce to scripts/makefile.cegcc
  Revised Makefile.am to use libpng.sys while building libpng.so
    so that only PNG_EXPORT functions are exported.
  Removed the deprecated png_check_sig() function/macro.
  Removed recently removed function names from scripts/*.def
  Revised pngtest.png to put chunks in the same order written by pngtest
    (evidently the same change made in libpng-1.0beta54 was lost).
  Added PNG_PRIVATE macro definition in pngconf.h for possible future use.

Version 1.4.0beta97 [November 13, 2009]
  Restored pngtest.png to the libpng-1.4.0beta7 version.
  Removed projects/beos and netware.txt; no one seems to be supporting them.
  Revised Makefile.in

Version 1.4.0beta98 [November 13, 2009]
  Added the "xcode" project to zip distributions,
  Fixed a typo in scripts/pngwin.def introduced in beta97.

Version 1.4.0beta99 [November 14, 2009]
  Moved libpng-config.in and libpng.pc-configure.in out of the scripts
    directory, to libpng-config.in and libpng-pc.in, respectively, and
    modified Makefile.am and configure.ac accordingly.  Now "configure"
    needs nothing from the "scripts" directory.
  Avoid redefining PNG_CONST in pngconf.h

Version 1.4.0beta100 [November 14, 2009]
  Removed ASM builds from projects/visualc6 and projects/visualc71
  Removed scripts/makefile.nommx and makefile.vcawin32
  Revised CMakeLists.txt to account for new location of libpng-config.in
    and libpng-pc.in
  Updated INSTALL to reflect removal and relocation of files.

Version 1.4.0beta101 [November 14, 2009]
  Restored the binary files (*.jpg, *.png, some project files) that were
    accidentally deleted from the zip and 7z distributions when the xcode
    project was added.

Version 1.4.0beta102 [November 18, 2009]
  Added libpng-config.in and libpng-pc.in to the zip and 7z distributions.
  Fixed a typo in projects/visualc6/pngtest.dsp, introduced in beta100.
  Moved descriptions of makefiles and other scripts out of INSTALL into
    scripts/README.txt
  Updated the copyright year in scripts/pngwin.rc from 2006 to 2009.

Version 1.4.0beta103 [November 21, 2009]
  Removed obsolete comments about ASM from projects/visualc71/README_zlib.txt
  Align row_buf on 16-byte boundary in memory.
  Restored the PNG_WRITE_FLUSH_AFTER_IEND_SUPPORTED guard around the call
    to png_flush() after png_write_IEND().  See 1.4.0beta32, 1.4.0beta50
    changes above and 1.2.30, 1.2.30rc01 and rc03 in 1.2.41 CHANGES.  Someone
    needs this feature.
  Make the 'png_jmpbuf' macro expand to a call that records the correct
    longjmp function as well as returning a pointer to the setjmp
    jmp_buf buffer, and marked direct access to jmpbuf 'deprecated'.
    (John Bowler)

Version 1.4.0beta104 [November 22, 2009]
  Removed png_longjmp_ptr from scripts/*.def and libpng.3
  Rebuilt configure scripts with autoconf-2.65

Version 1.4.0beta105 [November 25, 2009]
  Use fast integer PNG_DIVIDE_BY_255() or PNG_DIVIDE_BY_65535()
    to accomplish alpha premultiplication when
    PNG_READ_COMPOSITE_NODIV_SUPPORTED is defined.
  Changed "/255" to "/255.0" in background calculations to make it clear
    that the 255 is used as a double.

Version 1.4.0beta106 [November 27, 2009]
  Removed premultiplied alpha feature.

Version 1.4.0beta107 [December 4, 2009]
  Updated README
  Added "#define PNG_NO_PEDANTIC_WARNINGS" in the libpng source files.
  Removed "-DPNG_CONFIGURE_LIBPNG" from the makefiles and projects.
  Revised scripts/makefile.netbsd, makefile.openbsd, and makefile.sco
    to put png.h and pngconf.h in $prefix/include, like the other scripts,
    instead of in $prefix/include/libpng.  Also revised makefile.sco
    to put them in $prefix/include/libpng15 instead of in
    $prefix/include/libpng/libpng15.

Version 1.4.0beta108 [December 11, 2009]
  Removed leftover "-DPNG_CONFIGURE_LIBPNG" from contrib/pngminim/*/makefile
  Relocated png_do_chop() to its original position in pngrtran.c; the
    change in version 1.2.41beta08 caused transparency to be handled wrong
    in some 16-bit datastreams (Yusaku Sugai).

Version 1.4.0beta109 [December 13, 2009]
  Added "bit_depth" parameter to the private png_build_gamma_table() function.
  Pass bit_depth=8 to png_build_gamma_table() when bit_depth is 16 but the
    PNG_16_TO_8 transform has been set, to avoid unnecessary build of 16-bit
    tables.

Version 1.4.0rc02 [December 20, 2009]
  Declared png_cleanup_needed "volatile" in pngread.c and pngwrite.c

Version 1.4.0rc03 [December 22, 2009]
  Renamed libpng-pc.in back to libpng.pc.in and revised CMakeLists.txt
    (revising the change in 1.4.0beta99)

Version 1.4.0rc04 [December 25, 2009]
  Swapped PNG_UNKNOWN_CHUNKS_SUPPORTED and PNG_HANDLE_AS_UNKNOWN_SUPPORTED
    in pngset.c to be consistent with other changes in version 1.2.38.

Version 1.4.0rc05 [December 25, 2009]
  Changed "libpng-pc.in" to "libpng.pc.in" in configure.ac, configure, and
    Makefile.in to be consistent with changes in libpng-1.4.0rc03

Version 1.4.0rc06 [December 29, 2009]
  Reverted the gamma_table changes from libpng-1.4.0beta109.
  Fixed some indentation errors.

Version 1.4.0rc07 [January 1, 2010]
  Revised libpng*.txt and libpng.3 about 1.2.x->1.4.x differences.
  Use png_calloc() instead of png_malloc(); png_memset() in pngrutil.c
  Update copyright year to 2010.

Version 1.4.0rc08 [January 2, 2010]
  Avoid deprecated references to png_ptr-io_ptr and png_ptr->error_ptr
    in pngtest.c

Version 1.4.0 [January 3, 2010]
  No changes.

Version 1.4.1beta01 [January 8, 2010]
  Updated CMakeLists.txt for consistent indentation and to avoid an
    unclosed if-statement warning (Philip Lowman).
  Revised Makefile.am and Makefile.in to remove references to Y2KINFO,
    KNOWNBUG, and libpng.la (Robert Schwebel).
  Revised the makefiles to install the same files and symbolic
    links as configure, except for libpng.la and libpng14.la.
  Make png_set|get_compression_buffer_size() available even when
    PNG_WRITE_SUPPORTED is not enabled.
  Revised Makefile.am and Makefile.in to simplify their maintenance.
  Revised scripts/makefile.linux to install a link to libpng14.so.14.1

Version 1.4.1beta02 [January 9, 2010]
  Revised the rest of the makefiles to install a link to libpng14.so.14.1

Version 1.4.1beta03 [January 10, 2010]
  Removed png_set_premultiply_alpha() from scripts/*.def

Version 1.4.1rc01 [January 16, 2010]
  No changes.

Version 1.4.1beta04 [January 23, 2010]
  Revised png_decompress_chunk() to improve speed and memory usage when
    decoding large chunks.
  Added png_set|get_chunk_malloc_max() functions.

Version 1.4.1beta05 [January 26, 2010]
  Relocated "int k" declaration in pngtest.c to minimize its scope.

Version 1.4.1beta06 [January 28, 2010]
  Revised png_decompress_chunk() to use a two-pass method suggested by
    John Bowler.

Version 1.4.1beta07 [February 6, 2010]
  Folded some long lines in the source files.
  Added defineable PNG_USER_CHUNK_CACHE_MAX, PNG_USER_CHUNK_MALLOC_MAX,
    and a PNG_USER_LIMITS_SUPPORTED flag.
  Eliminated use of png_ptr->irowbytes and reused the slot in png_ptr as
    png_ptr->png_user_chunk_malloc_max.
  Revised png_push_save_buffer() to do fewer but larger png_malloc() calls.

Version 1.4.1beta08 [February 6, 2010]
  Minor cleanup and updating of dates and copyright year.

Version 1.5.0beta01 [February 7, 2010]
  Moved declaration of png_struct into private pngstruct.h and png_info
    into pnginfo.h

Version 1.4.1beta09 and 1.5.0beta02 [February 7, 2010]
  Reverted to original png_push_save_buffer() code.

Version 1.4.1beta10 and 1.5.0beta03 [February 8, 2010]
  Return allocated "old_buffer" in png_push_save_buffer() before
    calling png_error(), to avoid a potential memory leak.
  Updated configure script to use SO number 15.

Version 1.5.0beta04 [February 9, 2010]
  Removed malformed "incomplete struct declaration" of png_info from png.h

Version 1.5.0beta05 [February 12, 2010]
  Removed PNG_DEPSTRUCT markup in pngstruct.h and pnginfo.h, and undid the
    linewrapping that it entailed.
  Revised comments in pngstruct.h and pnginfo.h and added pointers to
    the libpng license.
  Changed PNG_INTERNAL to PNG_EXPOSE_INTERNAL_STRUCTURES
  Removed the cbuilder5 project, which has not been updated to 1.4.0.

Version 1.4.1beta12 and 1.5.0beta06 [February 14, 2010]
  Fixed type declaration of png_get_chunk_malloc_max() in pngget.c (Daisuke
    Nishikawa)

Version 1.5.0beta07 [omitted]

Version 1.5.0beta08 [February 19, 2010]
  Changed #ifdef PNG_NO_STDIO_SUPPORTED to #ifdef PNG_NO_CONSOLE_IO_SUPPORTED
    wherever png_snprintf() is used to construct error and warning messages.
  Noted in scripts/makefile.mingw that it expects to be run under MSYS.
  Removed obsolete unused MMX-querying support from contrib/gregbook
  Added exported png_longjmp() function.
  Removed the AIX redefinition of jmpbuf in png.h
  Added -D_ALLSOURCE in configure.ac, makefile.aix, and CMakeLists.txt
    when building on AIX.

Version 1.5.0beta09 [February 19, 2010]
  Removed -D_ALLSOURCE from configure.ac, makefile.aix, and CMakeLists.txt.
  Changed the name of png_ptr->jmpbuf to png_ptr->png_jmpbuf in pngstruct.h

Version 1.5.0beta10 [February 25, 2010]
  Removed unused gzio.c from contrib/pngminim gather and makefile scripts
  Removed replacement error handlers from contrib/gregbook.  Because of
    the new png_longjmp() function they are no longer needed.

Version 1.5.0beta11 [March 6, 2010]
  Removed checking for already-included setjmp.h from pngconf.h
  Fixed inconsistent indentations and made numerous cosmetic changes.
  Revised the "SEE ALSO" style of libpng.3, libpngpf.3, and png.5

Version 1.5.0beta12 [March 9, 2010]
  Moved "#include png.h" inside pngpriv.h and removed "#include png.h" from
    the source files, along with "#define PNG_EXPOSE_INTERNAL_STRUCTURES"
    and "#define PNG_NO_PEDANTIC_WARNINGS" (John Bowler).
  Created new pngdebug.h and moved debug definitions there.

Version 1.5.0beta13 [March 10, 2010]
  Protect pngstruct.h, pnginfo.h, and pngdebug.h from being included twice.
  Revise the "#ifdef" blocks in png_inflate() so it will compile when neither
    PNG_USER_CHUNK_MALLOC_MAX nor PNG_SET_CHUNK_MALLOC_LIMIT_SUPPORTED
    is defined.
  Removed unused png_measure_compressed_chunk() from pngpriv.h and libpngpf.3
  Moved the 'config.h' support from pngconf.h to pngpriv.h
  Removed PNGAPI from the png_longjmp_ptr typedef.
  Eliminated dependence of pngtest.c on the private pngdebug.h file.
  Make all png_debug macros into *unterminated* statements or
    expressions (i.e. a trailing ';' must always be added) and correct
    the format statements in various png_debug messages.

Version 1.5.0beta14 [March 14, 2010]
  Removed direct access to png_ptr->io_ptr from the Windows code in pngtest.c
  Revised Makefile.am to account for recent additions and replacements.
  Corrected CE and OS/2 DEF files (scripts/png*def) for symbols removed and
    added ordinal numbers to the Windows DEF file and corrected the duplicated
    ordinal numbers on CE symbols that are commented out.
  Added back in export symbols that can be present in the Windows build but
    are disabled by default.
  PNG_EXPORT changed to include an 'ordinal' field for DEF file generation.
    PNG_CALLBACK added to make callback definitions uniform.  PNGAPI split
    into PNGCAPI (base C form), PNGAPI (exports) and PNGCBAPI (callbacks),
    and appropriate changes made to all files.  Cygwin builds re-hinged to
    allow procedure call standard changes and to remove the need for the DEF
    file (fixes build on Cygwin).
  Enabled 'attribute' warnings that are relevant to library APIs and callbacks.
  Changed rules for generation of the various symbol files and added a new
    rule for a DEF file (which is also added to the distribution).
  Updated the symbol file generation to stop it adding spurious spaces
    to EOL (coming from preprocessor macro expansion).  Added a facility
    to join tokens in the output and rewrite *.dfn to use this.
  Eliminated scripts/*.def in favor of libpng.def; updated projects/visualc71
    and removed scripts/makefile.cygwin.
  Made PNG_BUILD_DLL safe: it can be set whenever a DLL is being built.
  Removed the include of sys/types.h - apparently unnecessary now on the
    platforms on which it happened (all but Mac OS and RISC OS).
  Moved the Mac OS test into pngpriv.h (the only place it is used.)

Version 1.5.0beta15 [March 17, 2010]
  Added symbols.chk target to Makefile.am to validate the symbols in png.h
    against the new DEF file scripts/symbols.def.
  Changed the default DEF file back to pngwin.def.
  Removed makefile.mingw.
  Eliminated PNG_NO_EXTERN and PNG_ALL_EXTERN

Version 1.5.0beta16 [April 1, 2010]
  Make png_text_struct independent of PNG_iTXt_SUPPORTED, so that
    fields are initialized in all configurations.  The READ/WRITE
    macros (PNG_(READ|WRITE)_iTXt_SUPPORTED) still function as
    before to disable code to actually read or write iTXt chunks
    and iTXt_SUPPORTED can be used to detect presence of either
    read or write support (but it is probably better to check for
    the one actually required - read or write.)
  Combined multiple png_warning() calls for a single error.
  Restored the macro definition of png_check_sig().

Version 1.5.0beta17 [April 17, 2010]
  Added some "(long)" typecasts to printf calls in png_handle_cHRM().
  Documented the fact that png_set_dither() was disabled since libpng-1.4.0.
  Reenabled png_set_dither() but renamed it to png_set_quantize() to reflect
    more accurately what it actually does.  At the same time, renamed
    the PNG_DITHER_[RED,GREEN_BLUE]_BITS macros to
    PNG_QUANTIZE_[RED,GREEN,BLUE]_BITS.
  Added some "(long)" typecasts to printf calls in png_handle_cHRM().
  Freeze build-time only configuration in the build.
    In all prior versions of libpng most configuration options
    controlled by compiler #defines had to be repeated by the
    application code that used libpng.  This patch changes this
    so that compilation options that can only be changed at build
    time are frozen in the build.  Options that are compiler
    dependent (and those that are system dependent) are evaluated
    each time - pngconf.h holds these.  Options that can be changed
    per-file in the application are in png.h.  Frozen options are
    in the new installed header file pnglibconf.h (John Bowler)
  Removed the xcode project because it has not been updated to work
    with libpng-1.5.0.
  Removed the ability to include optional pngusr.h

Version 1.5.0beta18 [April 17, 2010]
  Restored the ability to include optional pngusr.h
  Moved replacements for png_error() and png_warning() from the
    contrib/pngminim project to pngerror.c, for use when warnings or
    errors are disabled via PNG_NO_WARN or PNG_NO_ERROR_TEXT, to avoid
    storing unneeded error/warning text.
  Updated contrib/pngminim project to work with the new pnglibconf.h
  Added some PNG_NO_* defines to contrib/pngminim/*/pngusr.h to save space.

Version 1.5.0beta19 [April 24, 2010]
  Added PNG_{READ,WRITE}_INT_FUNCTIONS_SUPPORTED.  This allows the functions
    to read and write ints to be disabled independently of PNG_USE_READ_MACROS,
    which allows libpng to be built with the functions even though the default
    is to use the macros - this allows applications to choose at app build
    time whether or not to use macros (previously impossible because the
    functions weren't in the default build.)
  Changed Windows calling convention back to __cdecl for API functions.
    For Windows/x86 platforms only:
      __stdcall is no longer needed for Visual Basic, so libpng-1.5.0 uses
      __cdecl throughout (both API functions and callbacks) on Windows/x86
      platforms.
  Replaced visualc6 and visualc71 projects with new vstudio project
  Relaxed the overly-restrictive permissions of some files.

Version 1.5.0beta20 [April 24, 2010]
  Relaxed more overly-restrictive permissions of some files.

Version 1.5.0beta21 [April 27, 2010]
  Removed some unwanted binary bytes and changed CRLF to NEWLINE in the new
    vstudio project files, and some trivial editing of some files in the
    scripts directory.
  Set PNG_NO_READ_BGR, PNG_NO_IO_STATE, and PNG_NO_TIME_RFC1123 in
    contrib/pngminim/decoder/pngusr.h to make a smaller decoder application.

Version 1.5.0beta22 [April 28, 2010]
  Fixed dependencies of GET_INT_32 - it does not require READ_INT_FUNCTIONS
    because it has a macro equivalent.
  Improved the options.awk script; added an "everything off" option.
  Revised contrib/pngminim to use the "everything off" option in pngusr.dfa.

Version 1.5.0beta23 [April 29, 2010]
  Corrected PNG_REMOVED macro to take five arguments.
    The macro was documented with two arguments (name,ordinal), however
    the symbol checking .dfn files assumed five arguments.  The five
    argument form seems more useful so it is changed to that.
  Corrected PNG_UNKNOWN_CHUNKS_SUPPORTED to PNG_HANDLE_AS_UNKNOWN_SUPPORTED
    in gregbook/readpng2.c
  Corrected protection of png_get_user_transform_ptr. The API declaration in
    png.h is removed if both READ and WRITE USER_TRANSFORM are turned off
    but was left defined in pngtrans.c
  Added logunsupported=1 to cause pnglibconf.h to document disabled options.
    This makes the installed pnglibconf.h more readable but causes no
    other change.  The intention is that users of libpng will find it
    easier to understand if an API they need is missing.
  Include png_reset_zstream() in png.c only when PNG_READ_SUPPORTED is defined.
  Removed dummy_inflate.c from contrib/pngminim/encoder
  Removed contrib/pngminim/*/gather.sh; gathering is now done in the makefile.

Version 1.5.0beta24 [May 7, 2010]
  Use bitwise "&" instead of arithmetic mod in pngrutil.c calculation of the
    offset of the png_ptr->rowbuf pointer into png_ptr->big_row_buf.
  Added more blank lines for readability.

Version 1.5.0beta25 [June 18, 2010]
  In pngpread.c: png_push_have_row() add check for new_row > height
  Removed the now-redundant check for out-of-bounds new_row from example.c

Version 1.5.0beta26 [June 18, 2010]
  In pngpread.c: png_push_process_row() add check for too many rows.

Version 1.5.0beta27 [June 18, 2010]
  Removed the check added in beta25 as it is now redundant.

Version 1.5.0beta28 [June 20, 2010]
  Rewrote png_process_IDAT_data to consistently treat extra data as warnings
    and handle end conditions more cleanly.
  Removed the new (beta26) check in png_push_process_row().

Version 1.5.0beta29 [June 21, 2010]
  Revised scripts/options.awk to work on Sunos (but still doesn't work)
  Added comment to options.awk and contrib/pngminim/*/makefile to try nawk.

Version 1.5.0beta30 [June 22, 2010]
  Stop memory leak when reading a malformed sCAL chunk.

Version 1.5.0beta31 [June 26, 2010]
  Revised pngpread.c patch of beta28 to avoid an endless loop.
  Removed some trailing blanks.

Version 1.5.0beta32 [June 26, 2010]
  Removed leftover scripts/options.patch and scripts/options.rej

Version 1.5.0beta33 [July 6, 3010]
  Made FIXED and FLOATING options consistent in the APIs they enable and
    disable.  Corrected scripts/options.awk to handle both command line
    options and options specified in the .dfa files.
  Changed char *msg to PNG_CONST char *msg in pngrutil.c
  Make png_set_sRGB_gAMA_and_cHRM set values using either the fixed or
    floating point APIs, but not both.
  Reversed patch to remove error handler when the jmp_buf is stored in the
    main program structure, not the png_struct.
    The error handler is needed because the default handler in libpng will
    always use the jmp_buf in the library control structure; this is never
    set.  The gregbook code is a useful example because, even though it
    uses setjmp/longjmp, it shows how error handling can be implemented
    using control mechanisms not directly supported by libpng.  The
    technique will work correctly with mechanisms such as Microsoft
    Structure Exceptions or C++ exceptions (compiler willing - note that gcc
    does not by default support interworking of C and C++ error handling.)
  Reverted changes to call png_longjmp in contrib/gregbook where it is not
    appropriate.  If mainprog->jmpbuf is used by setjmp, then png_longjmp
    cannot be used.
  Changed "extern PNG_EXPORT" to "PNG_EXPORT" in png.h (Jan Nijtmans)
  Changed "extern" to "PNG_EXTERN" in pngpriv.h (except for the 'extern "C" {')

Version 1.5.0beta34 [July 12, 2010]
  Put #ifndef PNG_EXTERN, #endif around the define PNG_EXTERN in pngpriv.h

Version 1.5.0beta35 [July 24, 2010]
  Removed some newly-added TAB characters.
  Added -DNO_PNG_SNPRINTF to CFLAGS in scripts/makefile.dj2
  Moved the definition of png_snprintf() outside of the enclosing
    #ifdef blocks in pngconf.h

Version 1.5.0beta36 [July 29, 2010]
  Patches by John Bowler:
  Fixed point APIs are now supported throughout (no missing APIs).
  Internal fixed point arithmetic support exists for all internal floating
    point operations.
  sCAL validates the floating point strings it is passed.
  Safe, albeit rudimentary, Watcom support is provided by PNG_API_RULE==2
  Two new APIs exist to get the number of passes without turning on the
    PNG_INTERLACE transform and to get the number of rows in the current
    pass.
  A new test program, pngvalid.c, validates the gamma code.
  Errors in the 16-bit gamma correction (overflows) have been corrected.
  cHRM chunk testing is done consistently (previously the floating point
    API bypassed it, because the test really didn't work on FP, now the test
    is performed on the actual values to be stored in the PNG file so it
    works in the FP case too.)
  Most floating point APIs now simply call the fixed point APIs after
    converting the values to the fixed point form used in the PNG file.
  The standard headers no longer include zlib.h, which is currently only
    required for pngstruct.h and can therefore be internal.
  Revised png_get_int_32 to undo the PNG two's complement representation of
    negative numbers.

Version 1.5.0beta37 [July 30, 2010]
  Added a typecast in png_get_int_32() in png.h and pngrutil.h to avoid
    a compiler warning.
  Replaced oFFs 0,0 with oFFs -10,20 in pngtest.png

Version 1.5.0beta38 [July 31, 2010]
  Implemented remaining "_fixed" functions.
  Corrected a number of recently introduced warnings mostly resulting from
    safe but uncast assignments to shorter integers.  Also added a zlib
    VStudio release library project because the latest zlib Official Windows
    build does not include such a thing.
  Revised png_get_int_16() to be similar to png_get_int_32().
  Restored projects/visualc71.

Version 1.5.0beta39 [August 2, 2010]
  VisualC/GCC warning fixes, VisualC build fixes
  The changes include support for function attributes in VC in addition to
    those already present in GCC - necessary because without these some
    warnings are unavoidable.  Fixes include signed/unsigned fixes in
    pngvalid and checks with gcc -Wall -Wextra -Wunused.
  VC requires function attributes on function definitions as well as
    declarations, PNG_FUNCTION has been added to enable this and the
    relevant function definitions changed.

Version 1.5.0beta40 [August 6, 2010]
  Correct use of _WINDOWS_ in pngconf.h
  Removed png_mem_ #defines; they are no longer used.
  Added the sRGB chunk to pngtest.png

Version 1.5.0beta41 [August 11, 2010]
  Added the cHRM chunk to pngtest.png
  Don't try to use version-script with cygwin/mingw.
  Revised contrib/gregbook to work under cygwin/mingw.

Version 1.5.0beta42 [August 18, 2010]
  Add .dll.a to the list of extensions to be symlinked by Makefile.am (Yaakov)
  Made all API functions that have const arguments and constant string
    literal pointers declare them (John Bowler).

Version 1.5.0beta43 [August 20, 2010]
  Removed spurious tabs, shorten long lines (no source change)
    Also added scripts/chkfmt to validate the format of all the files that can
    reasonably be validated (it is suggested to run "make distclean" before
    checking, because some machine generated files have long lines.)
  Reformatted the CHANGES file to be more consistent throughout.
  Made changes to address various issues identified by GCC, mostly
    signed/unsigned and shortening problems on assignment but also a few
    difficult to optimize (for GCC) loops.
  Fixed non-GCC fixed point builds.  In png.c a declaration was misplaced
    in an earlier update.  Fixed to declare the auto variables at the head.
  Use cexcept.h in pngvalid.c.

Version 1.5.0beta44 [August 24, 2010]
  Updated CMakeLists.txt to use CMAKE_INSTALL_LIBDIR variable; useful for
    installing libpng in /usr/lib64 (Funda Wang).
  Revised CMakeLists.txt to put the man pages in share/man/man* not man/man*
  Revised CMakeLists.txt to make symlinks instead of copies when installing.
  Changed PNG_LIB_NAME from pngNN to libpngNN in CMakeLists.txt (Philip Lowman)
  Implemented memory checks within pngvalid
  Reformatted/rearranged pngvalid.c to assist use of progressive reader.
  Check interlaced images in pngvalid
  Clarified pngusr.h comments in pnglibconf.dfa
  Simplified the pngvalid error-handling code now that cexcept.h is in place.
  Implemented progressive reader in pngvalid.c for standard tests
  Implemented progressive read in pngvalid.c gamma tests
  Turn on progressive reader in pngvalid.c by default and tidy code.

Version 1.5.0beta45 [August 26, 2010]
  Added an explicit make step to projects/vstudio for pnglibconf.h
    Also corrected zlib.vcxproj into which Visual Studio had introduced
    what it calls an "authoring error".  The change to make pnglibconf.h
    simply copies the file; in the future it may actually generate the
    file from scripts/pnglibconf.dfa as the other build systems do.
  Changed pngvalid to work when floating point APIs are disabled
  Renamed the prebuilt scripts/pnglibconf.h to scripts/pnglibconf.h.prebuilt
  Supply default values for PNG_USER_PRIVATEBUILD and PNG_USER_DLLFNAME_POSTFIX
    in pngpriv.h in case the user neglected to define them in their pngusr.h

Version 1.5.0beta46 [August 28, 2010]
  Added new private header files to libpng_sources in CMakeLists.txt
  Added PNG_READ_16BIT, PNG_WRITE_16BIT, and PNG_16BIT options.
  Added reference to scripts/pnglibconf.h.prebuilt in the visualc71 project.

Version 1.5.0beta47 [September 11, 2010]
  Fixed a number of problems with 64-bit compilation reported by Visual
    Studio 2010 (John Bowler).

Version 1.5.0beta48 [October 4, 2010]
  Updated CMakeLists.txt (Philip Lowman).
  Revised autogen.sh to recognize and use $AUTOCONF, $AUTOMAKE, $AUTOHEADER,
    $AUTOPOINT, $ACLOCAL and $LIBTOOLIZE
  Fixed problem with symbols creation in Makefile.am which was assuming that
    all versions of ccp write to standard output by default (Martin Banky). The
    bug was introduced in libpng-1.2.9beta5.
  Removed unused mkinstalldirs.

Version 1.5.0beta49 [October 8, 2010]
  Undid Makefile.am revision of 1.5.0beta48.

Version 1.5.0beta50 [October 14, 2010]
  Revised Makefile.in to account for mkinstalldirs being removed.
  Added some "(unsigned long)" typecasts in printf statements in pngvalid.c.
  Suppressed a compiler warning in png_handle_sPLT().
  Check for out-of-range text compression mode in png_set_text().

Version 1.5.0beta51 [October 15, 2010]
  Changed embedded dates to "(PENDING RELEASE) in beta releases (and future
    rc releases) to minimize the difference between releases.

Version 1.5.0beta52 [October 16, 2010]
  Restored some of the embedded dates (in png.h, png.c, documentation, etc.)

Version 1.5.0beta53 [October 18, 2010]
  Updated INSTALL to mention using "make maintainer-clean" and to remove
    obsolete statement about a custom ltmain.sh
  Disabled "color-tests" by default in Makefile.am so it will work with
    automake versions earlier than 1.11.1
  Use document name "libpng-manual.txt" instead of "libpng-<version>.txt"
    to simplify version differences.
  Removed obsolete remarks about setjmp handling from INSTALL.
  Revised and renamed the typedef in png.h and png.c that was designed
    to catch library and header mismatch.

Version 1.5.0beta54 [November 10, 2010]
  Require 48 bytes, not 64 bytes, for big_row_buf in overflow checks.
  Used a consistent structure for the pngget.c functions.

Version 1.5.0beta55 [November 21, 2010]
  Revised png_get_uint_32, png_get_int_32, png_get_uint_16 (Cosmin)
  Moved reading of file signature into png_read_sig (Cosmin)
  Fixed atomicity of chunk header serialization (Cosmin)
  Added test for io_state in pngtest.c (Cosmin)
  Added "#!/bin/sh" at the top of contrib/pngminim/*/gather.sh scripts.
  Changes to remove gcc warnings (John Bowler)
    Certain optional gcc warning flags resulted in warnings in libpng code.
    With these changes only -Wconversion and -Wcast-qual cannot be turned on.
    Changes are trivial rearrangements of code.  -Wconversion is not possible
    for pngrutil.c (because of the widespread use of += et al on variables
    smaller than (int) or (unsigned int)) and -Wcast-qual is not possible
    with pngwio.c and pngwutil.c because the 'write' callback and zlib
    compression both fail to declare their input buffers with 'const'.

Version 1.5.0beta56 [December 7, 2010]
  Added the private PNG_UNUSED() macro definition in pngpriv.h.
  Added some commentary about PNG_EXPORT in png.h and pngconf.h
  Revised PNG_EXPORT() macro and added PNG_EXPORTA() macro, with the
    objective of simplifying and improving the cosmetic appearance of png.h.
  Fixed some incorrect "=" macro names in pnglibconf.dfa
  Included documentation of changes in 1.5.0 from 1.4.x in libpng-manual.txt

Version 1.5.0beta57 [December 9, 2010]
  Documented the pngvalid gamma error summary with additional comments and
    print statements.
  Improved missing symbol handling in checksym.awk; symbols missing in both
    the old and new files can now be optionally ignored, treated as errors
    or warnings.
  Removed references to pngvcrd.c and pnggccrd.c from the vstudio project.
  Updated "libpng14" to "libpng15" in the visualc71 project.
  Enabled the strip16 tests in pngvalid.`
  Don't display test results (except PASS/FAIL) when running "make test".
    Instead put them in pngtest-log.txt
  Added "--with-zprefix=<string>" to configure.ac
  Updated the prebuilt configuration files to autoconf version 2.68

Version 1.5.0beta58 [December 19, 2010]
  Fixed interlace image handling and add test cases (John Bowler)
  Fixed the clean rule in Makefile.am to remove pngtest-log.txt
  Made minor changes to work around warnings in gcc 3.4

Version 1.5.0rc01 [December 27, 2010]
  No changes.

Version 1.5.0rc02 [December 27, 2010]
  Eliminated references to the scripts/*.def files in project/visualc71.

Version 1.5.0rc03 [December 28, 2010]
  Eliminated scripts/*.def and revised Makefile.am accordingly

Version 1.5.0rc04 [December 29, 2010]
  Fixed bug in background transformation handling in pngrtran.c (it was
    looking for the flag in png_ptr->transformations instead of in
    png_ptr->flags) (David Raymond).

Version 1.5.0rc05 [December 31, 2010]
  Fixed typo in a comment in CMakeLists.txt (libpng14 => libpng15) (Cosmin)

Version 1.5.0rc06 [January 4, 2011]
  Changed the new configure option "zprefix=string" to "zlib-prefix=string"

Version 1.5.0rc07 [January 4, 2011]
  Updated copyright year.

Version 1.5.0 [January 6, 2011]
  No changes.

version 1.5.1beta01 [January 8, 2011]
  Added description of png_set_crc_action() to the manual.
  Added a note in the manual that the type of the iCCP profile was changed
    from png_charpp to png_bytepp in png_get_iCCP().  This change happened
    in version 1.5.0beta36 but is not noted in the CHANGES.  Similarly,
    it was changed from png_charpp to png_const_bytepp in png_set_iCCP().
  Ensure that png_rgb_to_gray ignores palette mapped images, if libpng
    internally happens to call it with one, and fixed a failure to handle
    palette mapped images correctly.  This fixes CVE-2690.

Version 1.5.1beta02 [January 14, 2011]
  Fixed a bug in handling of interlaced images (bero at arklinux.org).
  Updated CMakeLists.txt (Clifford Yapp)

Version 1.5.1beta03 [January 14, 2011]
  Fixed typecasting of some png_debug() statements (Cosmin)

Version 1.5.1beta04 [January 16, 2011]
  Updated documentation of png_set|get_tRNS() (Thomas Klausner).
  Mentioned in the documentation that applications must #include "zlib.h"
    if they need access to anything in zlib.h, and that a number of
    macros such as png_memset() are no longer accessible by applications.
  Corrected pngvalid gamma test "sample" function to access all of the color
    samples of each pixel, instead of sampling the red channel three times.
  Prefixed variable names index, div, exp, gamma with "png_" to avoid "shadow"
    warnings, and (mistakenly) changed png_exp() to exp().

Version 1.5.1beta05 [January 16, 2011]
  Changed variable names png_index, png_div, png_exp, and png_gamma to
    char_index, divisor, exp_b10, and gamma_val, respectively, and
    changed exp() back to png_exp().

Version 1.5.1beta06 [January 20, 2011]
  Prevent png_push_crc_skip() from hanging while reading an unknown chunk
    or an over-large compressed zTXt chunk with the progressive reader.
  Eliminated more GCC "shadow" warnings.
  Revised png_fixed() in png.c to avoid compiler warning about reaching the
    end without returning anything.

Version 1.5.1beta07 [January 22, 2011]
  In the manual, describe the png_get_IHDR() arguments in the correct order.
  Added const_png_structp and const_png_infop types, and used them in
    prototypes for most png_get_*() functions.

Version 1.5.1beta08 [January 23, 2011]
  Added png_get_io_chunk_type() and deprecated png_get_io_chunk_name()
  Added synopses for the IO_STATE functions and other missing synopses
    to the manual. Removed the synopses from libpngpf.3 because they
    were out of date and no longer useful.  Better information can be
    obtained by reading the prototypes and comments in pngpriv.h
  Attempted to fix cpp on Solaris with S. Studio 12 cc, fix build
    Added a make macro DFNCPP that is a CPP that will accept the tokens in
    a .dfn file and adds configure stuff to test for such a CPP.  ./configure
    should fail if one is not available.
  Corrected const_png_ in png.h to png_const_ to avoid polluting the namespace.
  Added png_get_current_row_number and png_get_current_pass_number for the
    benefit of the user transform callback.
  Added png_process_data_pause and png_process_data_skip for the benefit of
    progressive readers that need to stop data processing or want to optimize
    skipping of unread data (e.g., if the reader marks a chunk to be skipped.)

Version 1.5.1beta09 [January 24, 2011]
  Enhanced pngvalid, corrected an error in gray_to_rgb, corrected doc error.
    pngvalid contains tests of transforms, which tests are currently disabled
    because they are incompletely tested.  gray_to_rgb was failing to expand
    the bit depth for smaller bit depth images; this seems to be a long
    standing error and resulted, apparently, in invalid output
    (CVE-2011-0408, CERT VU#643140).  The documentation did not accurately
    describe what libpng really does when converting RGB to gray.

Version 1.5.1beta10 [January 27, 2010]
  Fixed incorrect examples of callback prototypes in the manual, that were
    introduced in libpng-1.0.0.
  In addition the order of the png_get_uint macros with respect to the
    relevant function definitions has been reversed.  This helps the
    preprocessing of the symbol files be more robust.  Furthermore, the
    symbol file preprocessing now uses -DPNG_NO_USE_READ_MACROS even when
    the library may actually be built with PNG_USE_READ_MACROS; this stops
    the read macros interfering with the symbol file format.
  Made the manual, synopses, and function prototypes use the function
    argument names file_gamma, int_file_gamma, and srgb_intent consistently.

Version 1.5.1beta11 [January 28, 2011]
  Changed PNG_UNUSED from "param=param;" to "{if(param){}}".
  Corrected local variable type in new API png_process_data_skip()
    The type was self-evidently incorrect but only causes problems on 64-bit
    architectures.
  Added transform tests to pngvalid and simplified the arguments.

Version 1.5.1rc01 [January 29, 2011]
  No changes.

Version 1.5.1rc02 [January 31, 2011]
  Added a request in the manual that applications do not use "png_" or
    "PNG_" to begin any of their own symbols.
  Changed PNG_UNUSED to "(void)param;" and updated the commentary in pngpriv.h

Version 1.5.1 [February 3, 2011]
  No changes.

Version 1.5.2beta01 [February 13, 2011]
  More -Wshadow fixes for older gcc compilers.  Older gcc versions apparently
    check formal parameters names in function declarations (as well as
    definitions) to see if they match a name in the global namespace.
  Revised PNG_EXPORTA macro to not use an empty parameter, to accommodate the
    old VisualC++ preprocessor.
  Turned on interlace handling in png_read_png().
  Fixed gcc pendantic warnings.
  Handle longjmp in Cygwin.
  Fixed png_get_current_row_number() in the interlaced case.
  Cleaned up ALPHA flags and transformations.
  Implemented expansion to 16 bits.

Version 1.5.2beta02 [February 19, 2011]
  Fixed mistake in the descriptions of user read_transform and write_transform
    function prototypes in the manual.  The row_info struct is png_row_infop.
  Reverted png_get_current_row_number() to previous (1.5.2beta01) behavior.
  Corrected png_get_current_row_number documentation
  Fixed the read/write row callback documentation.
    This documents the current behavior, where the callback is called after
    every row with information pertaining to the next row.

Version 1.5.2beta03 [March 3, 2011]
  Fixed scripts/makefile.vcwin32
  Updated contrib/pngsuite/README to add the word "modify".
  Define PNG_ALLOCATED to blank when _MSC_VER<1300.

Version 1.5.2rc01 [March 19, 2011]
  Define remaining attributes to blank when MSC_VER<1300.
  ifdef out mask arrays in pngread.c when interlacing is not supported.

Version 1.5.2rc02 [March 22, 2011]
  Added a hint to try CPP=/bin/cpp if "cpp -E" fails in scripts/pnglibconf.mak
    and in contrib/pngminim/*/makefile, eg., on SunOS 5.10, and removed "strip"
    from the makefiles.
  Fixed a bug (present since libpng-1.0.7) that makes png_handle_sPLT() fail
    to compile when PNG_NO_POINTER_INDEXING is defined (Chubanov Kirill)

Version 1.5.2rc03 [March 24, 2011]
  Don't include standard header files in png.h while building the symbol table,
    to avoid cpp failure on SunOS (introduced PNG_BUILDING_SYMBOL_TABLE macro).

Version 1.5.2 [March 31, 2011]
  No changes.

Version 1.5.3beta01 [April 1, 2011]
  Re-initialize the zlib compressor before compressing non-IDAT chunks.
  Added API functions (png_set_text_compression_level() and four others) to
    set parameters for zlib compression of non-IDAT chunks.

Version 1.5.3beta02 [April 3, 2011]
  Updated scripts/symbols.def with new API functions.
  Only compile the new zlib re-initializing code when text or iCCP is
    supported, using PNG_WRITE_COMPRESSED_TEXT_SUPPORTED macro.
  Improved the optimization of the zlib CMF byte (see libpng-1.2.6beta03).
  Optimize the zlib CMF byte in non-IDAT compressed chunks

Version 1.5.3beta03 [April 16, 2011]
  Fixed gcc -ansi -pedantic compile. A strict ANSI system does not have
    snprintf, and the "__STRICT_ANSI__" detects that condition more reliably
    than __STDC__ (John Bowler).
  Removed the PNG_PTR_NORETURN attribute because it too dangerous. It tells
    the compiler that a user supplied callback (the error handler) does not
    return, yet there is no guarantee in practice that the application code
    will correctly implement the error handler because the compiler only
    issues a warning if there is a mistake (John Bowler).
  Removed the no-longer-used PNG_DEPSTRUCT macro.
  Updated the zlib version to 1.2.5 in the VStudio project.
  Fixed 64-bit builds where png_uint_32 is smaller than png_size_t in
    pngwutil.c (John Bowler).
  Fixed bug with stripping the filler or alpha channel when writing, that
    was introduced in libpng-1.5.2beta01 (bug report by Andrew Church).

Version 1.5.3beta04 [April 27, 2011]
  Updated pngtest.png with the new zlib CMF optimization.
  Cleaned up conditional compilation code and of background/gamma handling
    Internal changes only except a new option to avoid compiling the
    png_build_grayscale_palette API (which is not used at all internally.)
    The main change is to move the transform tests (READ_TRANSFORMS,
    WRITE_TRANSFORMS) up one level to the caller of the APIs.  This avoids
    calls to spurious functions if all transforms are disabled and slightly
    simplifies those functions.  Pngvalid modified to handle this.
    A minor change is to stop the strip_16 and expand_16 interfaces from
    disabling each other; this allows the future alpha premultiplication
    code to use 16-bit intermediate values while still producing 8-bit output.
    png_do_background and png_do_gamma have been simplified to take a single
    pointer to the png_struct rather than pointers to every item required
    from the png_struct. This makes no practical difference to the internal
    code.
  A serious bug in the pngvalid internal routine 'standard_display_init' has
    been fixed - this failed to initialize the red channel and accidentally
    initialized the alpha channel twice.
  Changed png_struct jmp_buf member name from png_jmpbuf to tmp_jmpbuf to
    avoid a possible clash with the png_jmpbuf macro on some platforms.

Version 1.5.3beta05 [May 6, 2011]
  Added the "_POSIX_SOURCE" feature test macro to ensure libpng sees the
    correct API. _POSIX_SOURCE is defined in pngpriv.h, pngtest.c and
    pngvalid.c to ensure that POSIX conformant systems disable non-POSIX APIs.
  Removed png_snprintf and added formatted warning messages.  This change adds
    internal APIs to allow png_warning messages to have parameters without
    requiring the host OS to implement snprintf.  As a side effect the
    dependency of the tIME-supporting RFC1132 code on stdio is removed and
    PNG_NO_WARNINGS does actually work now.
  Pass "" instead of '\0' to png_default_error() in png_err().  This mistake
    was introduced in libpng-1.2.20beta01.  This fixes CVE-2011-2691.
  Added PNG_WRITE_OPTIMIZE_CMF_SUPPORTED macro to make the zlib "CMF" byte
    optimization configureable.
  IDAT compression failed if preceded by a compressed text chunk (bug
    introduced in libpng-1.5.3beta01-02).  This was because the attempt to
    reset the zlib stream in png_write_IDAT happened after the first IDAT
    chunk had been deflated - much too late.  In this change internal
    functions were added to claim/release the z_stream and, hopefully, make
    the code more robust.  Also deflateEnd checking is added - previously
    libpng would ignore an error at the end of the stream.

Version 1.5.3beta06 [May 8, 2011]
  Removed the -D_ALL_SOURCE from definitions for AIX in CMakeLists.txt
  Implemented premultiplied alpha support: png_set_alpha_mode API

Version 1.5.3beta07 [May 11, 2011]
  Added expand_16 support to the high level interface.
  Added named value and 'flag' gamma support to png_set_gamma.  Made a minor
    change from the previous (unreleased) ABI/API to hide the exact value used
    for Macs - it's not a good idea to embed this in the ABI!
  Moved macro definitions for PNG_HAVE_IHDR, PNG_HAVE_PLTE, and PNG_AFTER_IDAT
    from pngpriv.h to png.h because they must be visible to applications
    that call png_set_unknown_chunks().
  Check for up->location !PNG_AFTER_IDAT when writing unknown chunks
    before IDAT.

Version 1.5.3beta08 [May 16, 2011]
  Improved "pngvalid --speed" to exclude more of pngvalid from the time.
  Documented png_set_alpha_mode(), other changes in libpng.3/libpng-manual.txt
  The cHRM chunk now sets the defaults for png_set_rgb_to_gray() (when negative
    parameters are supplied by the caller), while in the absence of cHRM
    sRGB/Rec 709 values are still used.  This introduced a divide-by-zero
    bug in png_handle_cHRM().
  The bKGD chunk no longer overwrites the background value set by
    png_set_background(), allowing the latter to be used before the file
    header is read. It never performed any useful function to override
    the default anyway.
  Added memory overwrite and palette image checks to pngvalid.c
    Previously palette image code was poorly checked. Since the transformation
    code has a special palette path in most cases this was a severe weakness.
  Minor cleanup and some extra checking in pngrutil.c and pngrtran.c. When
    expanding an indexed image, always expand to RGBA if transparency is
    present.

Version 1.5.3beta09 [May 17, 2011]
  Reversed earlier 1.5.3 change of transformation order; move png_expand_16
    back where it was.  The change doesn't work because it requires 16-bit
    gamma tables when the code only generates 8-bit ones.  This fails
    silently; the libpng code just doesn't do any gamma correction.  Moving
    the tests back leaves the old, inaccurate, 8-bit gamma calculations, but
    these are clearly better than none!

Version 1.5.3beta10 [May 20, 2011]

  png_set_background() and png_expand_16() did not work together correctly.
    This problem is present in 1.5.2; if png_set_background is called with
    need_expand false and the matching 16 bit color libpng erroneously just
    treats it as an 8-bit color because of where png_do_expand_16 is in the
    transform list.  This simple fix reduces the supplied colour to 8-bits,
    so it gets smashed, but this is better than the current behavior.
  Added tests for expand16, more fixes for palette image tests to pngvalid.
    Corrects the code for palette image tests and disables attempts to
    validate palette colors.

Version 1.5.3rc01 [June 3, 2011]
  No changes.

Version 1.5.3rc02 [June 8, 2011]
  Fixed uninitialized memory read in png_format_buffer() (Bug report by
    Frank Busse, CVE-2011-2501, related to CVE-2004-0421).

Version 1.5.3beta11 [June 11, 2011]
  Fixed png_handle_sCAL which is broken in 1.5. This fixes CVE 2011-2692.
  Added sCAL to pngtest.png
  Revised documentation about png_set_user_limits() to say that it also affects
    png writing.
  Revised handling of png_set_user_limits() so that it can increase the
    limit beyond the PNG_USER_WIDTH|HEIGHT_MAX; previously it could only
    reduce it.
  Make the 16-to-8 scaling accurate. Dividing by 256 with no rounding is
    wrong (high by one) 25% of the time. Dividing by 257 with rounding is
    wrong in 128 out of 65536 cases. Getting the right answer all the time
    without division is easy.
  Added "_SUPPORTED" to the PNG_WRITE_CUSTOMIZE_ZTXT_COMPRESSION macro.
  Added projects/owatcom, an IDE project for OpenWatcom to replace
    scripts/makefile.watcom.  This project works with OpenWatcom 1.9. The
    IDE autogenerates appropriate makefiles (libpng.mk) for batch processing.
    The project is configurable, unlike the Visual Studio project, so long
    as the developer has an awk.
  Changed png_set_gAMA to limit the gamma value range so that the inverse
    of the stored value cannot overflow the fixed point representation,
    and changed other things OpenWatcom warns about.
  Revised pngvalid.c to test PNG_ALPHA_MODE_SUPPORTED correctly. This allows
    pngvalid to build when ALPHA_MODE is not supported, which is required if
    it is to build on libpng 1.4.
  Removed string/memory macros that are no longer used and are not
    necessarily fully supportable, particularly png_strncpy and png_snprintf.
  Added log option to pngvalid.c and attempted to improve gamma messages.

Version 1.5.3 [omitted]
  People found the presence of a beta release following an rc release
    to be confusing; therefore we bump the version to libpng-1.5.4beta01
    and there will be no libpng-1.5.3 release.

Version 1.5.4beta01 [June 14, 2011]
  Made it possible to undefine PNG_READ_16_TO_8_ACCURATE_SCALE_SUPPORTED
    to get the same (inaccurate) output as libpng-1.5.2 and earlier.
  Moved definitions of PNG_HAVE_IHDR, PNG_AFTER_IDAT, and PNG_HAVE_PLTE
    outside of an unknown-chunk block in png.h because they are also
    needed for other uses.

Version 1.5.4beta02 [June 14, 2011]
  Fixed and clarified LEGACY 16-to-8 scaling code.
  Added png_set_chop_16() API, to match inaccurate results from previous
    libpng versions.
  Removed the ACCURATE and LEGACY options (they are no longer useable)
  Use the old scaling method for background if png_set_chop_16() was
    called.
  Made png_set_chop_16() API removeable by disabling PNG_CHOP_16_TO_8_SUPPORTED

Version 1.5.4beta03 [June 15, 2011]
  Fixed a problem in png_do_expand_palette() exposed by optimization in
    1.5.3beta06
  Also removed a spurious and confusing "trans" member ("trans") from png_info.
  The palette expand optimization prevented expansion to an intermediate RGBA
    form if tRNS was present but alpha was marked to be stripped; this exposed
    a check for tRNS in png_do_expand_palette() which is inconsistent with the
    code elsewhere in libpng.
  Correction to the expand_16 code; removed extra instance of
    png_set_scale_16_to_8 from pngpriv.h

Version 1.5.4beta04 [June 16, 2011]
  Added a missing "#ifdef PNG_READ_BACKGROUND_SUPPORTED/#endif" in pngrtran.c
  Added PNG_TRANSFORM_CHOP_16 to the high-level read transforms.
  Made PNG_READ_16_TO_8_ACCURATE_SCALE configurable again.  If this is
    not enabled, png_set_strip_16() and png_do_scale_16_to_8() aren't built.
  Revised contrib/visupng, gregbook, and pngminim to demonstrate chop_16_to_8

Version 1.5.4beta05 [June 16, 2011]
  Renamed png_set_strip_16() to png_set_scale_16() and renamed
    png_set_chop_16() to png_set_strip(16) in an attempt to minimize the
    behavior changes between libpng14 and libpng15.

Version 1.5.4beta06 [June 18, 2011]
  Fixed new bug that was causing both strip_16 and scale_16 to be applied.

Version 1.5.4beta07 [June 19, 2011]
  Fixed pngvalid, simplified macros, added checking for 0 in sCAL.
    The ACCURATE scale macro is no longer defined in 1.5 - call the
    png_scale_16_to_8 API.  Made sure that PNG_READ_16_TO_8 is still defined
    if the png_strip_16_to_8 API is present.  png_check_fp_number now
    maintains some state so that positive, negative and zero values are
    identified.  sCAL uses these to be strictly spec conformant.

Version 1.5.4beta08 [June 23, 2011]
  Fixed pngvalid if ACCURATE_SCALE is defined.
  Updated scripts/pnglibconf.h.prebuilt.

Version 1.5.4rc01 [June 30, 2011]
  Define PNG_ALLOCATED to "restrict" only if MSC_VER >= 1400.

Version 1.5.4 [July 7, 2011]
  No changes.

Version 1.5.5beta01 [July 13, 2011]
  Fixed some typos and made other minor changes in the manual.
  Updated contrib/pngminus/makefile.std (Samuli Souminen)

Version 1.5.5beta02 [July 14, 2011]
  Revised Makefile.am and Makefile.in to look in the right directory for
    pnglibconf.h.prebuilt

Version 1.5.5beta03 [July 27, 2011]
  Enabled compilation with g++ compiler.  This compiler does not recognize
    the file extension, so it always compiles with C++ rules.  Made minor
    changes to pngrutil.c to cast results where C++ expects it but C does not.
  Minor editing of libpng.3 and libpng-manual.txt.

Version 1.5.5beta04 [July 29, 2011]
  Revised CMakeLists.txt (Clifford Yapp)
  Updated commentary about the png_rgb_to_gray() default coefficients
    in the manual and in pngrtran.c

Version 1.5.5beta05 [August 17, 2011]
  Prevent unexpected API exports from non-libpng DLLs on Windows.  The "_DLL"
    is removed from the test of whether a DLL is being built (this erroneously
    caused the libpng APIs to be marked as DLL exports in static builds under
    Microsoft Visual Studio).  Almost all of the libpng building configuration
    is moved from pngconf.h to pngpriv.h, but PNG_DLL_EXPORT remains in
    pngconf.h, though, so that it is colocated with the import definition (it
    is no longer used anywhere in the installed headers).  The VStudio project
    definitions have been cleaned up: "_USRDLL" has been removed from the
    static library builds (this was incorrect), and PNG_USE_DLL has been added
    to pngvalid to test the functionality (pngtest does not supply it,
    deliberately).  The spurious "_EXPORTS" has been removed from the
    libpng build (all these errors were a result of copy/paste between project
    configurations.)
  Added new types and internal functions for CIE RGB end point handling to
    pngpriv.h (functions yet to be implemented).

Version 1.5.5beta06 [August 26, 2011]
  Ensure the CMAKE_LIBRARY_OUTPUT_DIRECTORY is set in CMakeLists.txt
    (Clifford Yap)
  Fixes to rgb_to_gray and cHRM XYZ APIs (John Bowler):
    The rgb_to_gray code had errors when combined with gamma correction.
    Some pixels were treated as true grey when they weren't and such pixels
    and true grey ones were not gamma corrected (the original value of the
    red component was used instead).  APIs to get and set cHRM using color
    space end points have been added and the rgb_to_gray code that defaults
    based on cHRM, and the divide-by-zero bug in png_handle_cHRM (CERT
    VU#477046, CVE-2011-3328, introduced in 1.5.4) have been corrected.
  A considerable number of tests has been added to pngvalid for the
    rgb_to_gray transform.
  Arithmetic errors in rgb_to_gray whereby the calculated gray value was
    truncated to the bit depth rather than rounded have been fixed except in
    the 8-bit non-gamma-corrected case (where consistency seems more important
    than correctness.)  The code still has considerable inaccuracies in the
    8-bit case because 8-bit linear arithmetic is used.

Version 1.5.5beta07 [September 7, 2011]
  Added "$(ARCH)" option to makefile.darwin
  Added SunOS support to configure.ac and Makefile.am
  Changed png_chunk_benign_error() to png_warning() in png.c, in
    png_XYZ_from_xy_checked().

Version 1.5.5beta08 [September 10, 2011]
  Fixed 64-bit compilation errors (gcc). The errors fixed relate
    to conditions where types that are 32 bits in the GCC 32-bit
    world (uLong and png_size_t) become 64 bits in the 64-bit
    world.  This produces potential truncation errors which the
    compiler correctly flags.
  Relocated new HAVE_SOLARIS_LD definition in configure.ac
  Constant changes for 64-bit compatibility (removal of L suffixes). The
    16-bit cases still use "L" as we don't have a 16-bit test system.

Version 1.5.5rc01 [September 15, 2011]
  Removed "L" suffixes in pngpriv.h

Version 1.5.5 [September 22, 2011]
  No changes.

Version 1.5.6beta01 [September 22, 2011]
  Fixed some 64-bit type conversion warnings in pngrtran.c
  Moved row_info from png_struct to a local variable.
  The various interlace mask arrays have been made into arrays of
    bytes and made PNG_CONST and static (previously some arrays were
    marked PNG_CONST and some weren't).
  Additional checks have been added to the transform code to validate the
    pixel depths after the transforms on both read and write.
  Removed some redundant code from pngwrite.c, in png_destroy_write_struct().
  Changed chunk reading/writing code to use png_uint_32 instead of png_byte[4].
    This removes the need to allocate temporary strings for chunk names on
    the stack in the read/write code.  Unknown chunk handling still uses the
    string form because this is exposed in the API.

Version 1.5.6beta02 [September 26, 2011]
  Added a note in the manual the png_read_update_info() must be called only
    once with a particular info_ptr.
  Fixed a typo in the definition of the new PNG_STRING_FROM_CHUNK(s,c) macro.

Version 1.5.6beta03 [September 28, 2011]
  Revised test-pngtest.sh to report FAIL when pngtest fails.
  Added "--strict" option to pngtest, to report FAIL when the failure is
    only because the resulting valid files are different.
  Revised CMakeLists.txt to work with mingw and removed some material from
    CMakeLists.txt that is no longer useful in libpng-1.5.

Version 1.5.6beta04 [October 5, 2011]
  Fixed typo in Makefile.in and Makefile.am ("-M Wl" should be "-M -Wl")."

Version 1.5.6beta05 [October 12, 2011]
  Speed up png_combine_row() for interlaced images. This reduces the generality
    of the code, allowing it to be optimized for Adam7 interlace.  The masks
    passed to png_combine_row() are now generated internally, avoiding
    some code duplication and localizing the interlace handling somewhat.
  Align png_struct::row_buf - previously it was always unaligned, caused by
    a bug in the code that attempted to align it; the code needs to subtract
    one from the pointer to take account of the filter byte prepended to
    each row.
  Optimized png_combine_row() when rows are aligned. This gains a small
    percentage for 16-bit and 32-bit pixels in the typical case where the
    output row buffers are appropriately aligned. The optimization was not
    previously possible because the png_struct buffer was always misaligned.
  Fixed bug in png_write_chunk_header() debug print, introduced in 1.5.6beta01.

Version 1.5.6beta06 [October 17, 2011]
  Removed two redundant tests for unitialized row.
  Fixed a relatively harmless memory overwrite in compressed text writing
    with a 1 byte zlib buffer.
  Add ability to call png_read_update_info multiple times to pngvalid.c.
  Fixes for multiple calls to png_read_update_info. These fixes attend to
    most of the errors revealed in pngvalid, however doing the gamma work
    twice results in inaccuracies that can't be easily fixed.  There is now
    a warning in the code if this is going to happen.
  Turned on multiple png_read_update_info in pngvalid transform tests.
  Prevent libpng from overwriting unused bits at the end of the image when
    it is not byte aligned, while reading. Prior to libpng-1.5.6 libpng would
    overwrite the partial byte at the end of each row if the row width was not
    an exact multiple of 8 bits and the image is not interlaced.

Version 1.5.6beta07 [October 21, 2011]
  Made png_ptr->prev_row an aligned pointer into png_ptr->big_prev_row
    (Mans Rullgard).

Version 1.5.6rc01 [October 26, 2011]
  Changed misleading "Missing PLTE before cHRM" warning to "Out of place cHRM"

Version 1.5.6rc02 [October 27, 2011]
  Added LSR() macro to defend against buggy compilers that evaluate non-taken
    code branches and complain about out-of-range shifts.

Version 1.5.6rc03 [October 28, 2011]
  Renamed the LSR() macro to PNG_LSR() and added PNG_LSL() macro.
  Fixed compiler warnings with Intel and MSYS compilers. The logical shift
    fix for Microsoft Visual C is required by other compilers, so this
    enables that fix for all compilers when using compile-time constants.
    Under MSYS 'byte' is a name declared in a system header file, so we
    changed the name of a local variable to avoid the warnings that result.
  Added #define PNG_ALIGN_TYPE PNG_ALIGN_NONE to contrib/pngminim/*/pngusr.h

Version 1.5.6 [November 3, 2011]
  No changes.

Version 1.5.7beta01 [November 4, 2011]
  Added support for ARM processor, when decoding all PNG up-filtered rows
    and any other-filtered rows with 3 or 4 bytes per pixel (Mans Rullgard).
  Fixed bug in pngvalid on early allocation failure; fixed type cast in
    pngmem.c; pngvalid would attempt to call png_error() if the allocation
    of a png_struct or png_info failed. This would probably have led to a
    crash.  The pngmem.c implementation of png_malloc() included a cast
    to png_size_t which would fail on large allocations on 16-bit systems.
  Fix for the preprocessor of the Intel C compiler. The preprocessor
    splits adjacent @ signs with a space; this changes the concatentation
    token from @-@-@ to PNG_JOIN; that should work with all compiler
    preprocessors.
  Paeth filter speed improvements from work by Siarhei Siamashka. This
    changes the 'Paeth' reconstruction function to improve the GCC code
    generation on x86. The changes are only part of the suggested ones;
    just the changes that definitely improve speed and remain simple.
    The changes also slightly increase the clarity of the code.

Version 1.5.7beta02 [November 11, 2011]
  Check compression_type parameter in png_get_iCCP and remove spurious
    casts. The compression_type parameter is always assigned to, so must
    be non-NULL. The cast of the profile length potentially truncated the
    value unnecessarily on a 16-bit int system, so the cast of the (byte)
    compression type to (int) is specified by ANSI-C anyway.
  Fixed FP division by zero in pngvalid.c; the 'test_pixel' code left
    the sBIT fields in the test pixel as 0, which resulted in a floating
    point division by zero which was irrelevant but causes systems where
    FP exceptions cause a crash. Added code to pngvalid to turn on FP
    exceptions if the appropriate glibc support is there to ensure this is
    tested in the future.
  Updated scripts/pnglibconf.mak and scripts/makefile.std to handle the
    new PNG_JOIN macro.
  Added versioning to pnglibconf.h comments.
  Simplified read/write API initial version; basic read/write tested on
    a variety of images, limited documentation (in the header file.)
  Installed more accurate linear to sRGB conversion tables. The slightly
    modified tables reduce the number of 16-bit values that
    convert to an off-by-one 8-bit value.  The "makesRGB.c" code that was used
    to generate the tables is now in a contrib/sRGBtables sub-directory.

Version 1.5.7beta03 [November 17, 2011]
  Removed PNG_CONST from the sRGB table declarations in pngpriv.h and png.c
  Added run-time detection of NEON support.
  Added contrib/libtests; includes simplified API test and timing test and
    a color conversion utility for rapid checking of failed 'pngstest' results.
  Multiple transform bug fixes plus a work-round for double gamma correction.
    libpng does not support more than one transform that requires linear data
    at once - if this is tried typically the results is double gamma
    correction. Since the simplified APIs can need rgb to gray combined with
    a compose operation it is necessary to do one of these outside the main
    libpng transform code. This check-in also contains fixes to various bugs
    in the simplified APIs themselves and to some bugs in compose and rgb to
    gray (on palette) itself.
  Fixes for C++ compilation using g++ When libpng source is compiled
    using g++. The compiler imposes C++ rules on the C source; thus it
    is desireable to make the source work with either C or C++ rules
    without throwing away useful error information.  This change adds
    png_voidcast to allow C semantic (void*) cases or the corresponding
    C++ static_cast operation, as appropriate.
  Added --noexecstack to assembler file compilation. GCC does not set
    this on assembler compilation, even though it does on C compilation.
    This creates security issues if assembler code is enabled; the
    work-around is to set it by default in the flags for $(CCAS)
  Work around compilers that don't support declaration of const data. Some
    compilers fault 'extern const' data declarations (because the data is
    not initialized); this turns on const-ness only for compilers where
    this is known to work.

Version 1.5.7beta04 [November 17, 2011]
  Since the gcc driver does not recognize the --noexecstack flag, we must
    use the -Wa prefix to have it passed through to the assembler.
    Also removed a duplicate setting of this flag.
  Added files that were omitted from the libpng-1.5.7beta03 zip distribution.

Version 1.5.7beta05 [November 25, 2011]
  Removed "zTXt" from warning in generic chunk decompression function.
  Validate time settings passed to png_set_tIME() and png_convert_to_rfc1123()
    (Frank Busse). Note: This prevented CVE-2015-7981 from affecting
    libpng-1.5.7 and later.
  Added MINGW support to CMakeLists.txt
  Reject invalid compression flag or method when reading the iTXt chunk.
  Backed out 'simplified' API changes. The API seems too complex and there
    is a lack of consensus or enthusiasm for the proposals.  The API also
    reveals significant bugs inside libpng (double gamma correction and the
    known bug of being unable to retrieve a corrected palette). It seems
    better to wait until the bugs, at least, are corrected.
  Moved pngvalid.c into contrib/libtests
  Rebuilt Makefile.in, configure, etc., with autoconf-2.68

Version 1.5.7rc01 [December 1, 2011]
  Replaced an "#if" with "#ifdef" in pngrtran.c
  Revised #if PNG_DO_BC block in png.c (use #ifdef and add #else)

Version 1.5.7rc02 [December 5, 2011]
  Revised project files and contrib/pngvalid/pngvalid.c to account for
    the relocation of pngvalid into contrib/libtests.
  Revised pngconf.h to use " __declspec(restrict)" only when MSC_VER >= 1400,
    as in libpng-1.5.4.
  Put CRLF line endings in the owatcom project files.

Version 1.5.7rc03 [December 7, 2011]
  Updated CMakeLists.txt to account for the relocation of pngvalid.c

Version 1.5.7 [December 15, 2011]
  Minor fixes to pngvalid.c for gcc 4.6.2 compatibility to remove warnings
    reported by earlier versions.
  Fixed minor memset/sizeof errors in pngvalid.c.

Version 1.6.0beta01 [December 15, 2011]
  Removed machine-generated configure files from the GIT repository (they will
    continue to appear in the tarball distributions and in the libpng15 and
    earlier GIT branches).
  Restored the new 'simplified' API, which was started in libpng-1.5.7beta02
    but later deleted from libpng-1.5.7beta05.
  Added example programs for the new 'simplified' API.
  Added ANSI-C (C90) headers and require them, and take advantage of the
    change. Also fixed some of the projects/* and contrib/* files that needed
    updates for libpng16 and the move of pngvalid.c.
    With this change the required ANSI-C header files are assumed to exist: the
    implementation must provide float.h, limits.h, stdarg.h and stddef.h and
    libpng relies on limits.h and stddef.h existing and behaving as defined
    (the other two required headers aren't used).  Non-ANSI systems that don't
    have stddef.h or limits.h will have to provide an appropriate fake
    containing the relevant types and #defines.
  Dropped support for 16-bit platforms. The use of FAR/far has been eliminated
    and the definition of png_alloc_size_t is now controlled by a flag so
    that 'small size_t' systems can select it if necessary.  Libpng 1.6 may
    not currently work on such systems -- it seems likely that it will
    ask 'malloc' for more than 65535 bytes with any image that has a
    sufficiently large row size (rather than simply failing to read such
    images).
  New tools directory containing tools used to generate libpng code.
  Fixed race conditions in parallel make builds. With higher degrees of
    parallelism during 'make' the use of the same temporary file names such
    as 'dfn*' can result in a race where a temporary file from one arm of the
    build is deleted or overwritten in another arm.  This changes the
    temporary files for suffix rules to always use $* and ensures that the
    non-suffix rules use unique file names.

Version 1.6.0beta02 [December 21, 2011]
  Correct configure builds where build and source directories are separate.
    The include path of 'config.h' was erroneously made relative in pngvalid.c
    in libpng 1.5.7.

Version 1.6.0beta03 [December 22, 2011]
  Start-up code size improvements, error handler flexibility. These changes
    alter how the tricky allocation of the initial png_struct and png_info
    structures are handled. png_info is now handled in pretty much the same
    way as everything else, except that the allocations handle NULL return
    silently.  png_struct is changed in a similar way on allocation and on
    deallocation a 'safety' error handler is put in place (which should never
    be required).  The error handler itself is changed to permit mismatches
    in the application and libpng error buffer size; however, this means a
    silent change to the API to return the jmp_buf if the size doesn't match
    the size from the libpng compilation; libpng now allocates the memory and
    this may fail.  Overall these changes result in slight code size
    reductions; however, this is a reduction in code that is always executed
    so is particularly valuable.  Overall on a 64-bit system the libpng DLL
    decreases in code size by 1733 bytes.  pngerror.o increases in size by
    about 465 bytes because of the new functionality.
  Added png_convert_to_rfc1123_buffer() and deprecated png_convert_to_rfc1123()
    to avoid including a spurious buffer in the png_struct.

Version 1.6.0beta04 [December 30, 2011]
  Regenerated configure scripts with automake-1.11.2
  Eliminated png_info_destroy(). It is now used only in png.c and only calls
    one other internal function and memset().
  Enabled png_get_sCAL_fixed() if floating point APIs are enabled. Previously
    it was disabled whenever internal fixed point arithmetic was selected,
    which meant it didn't exist even on systems where FP was available but not
    preferred.
  Added pngvalid.c compile time checks for const APIs.
  Implemented 'restrict' for png_info and png_struct. Because of the way
    libpng works both png_info and png_struct are always accessed via a
    single pointer.  This means adding C99 'restrict' to the pointer gives
    the compiler some opportunity to optimize the code.  This change allows
    that.
  Moved AC_MSG_CHECKING([if libraries can be versioned]) later to the proper
    location in configure.ac (Gilles Espinasse).
  Changed png_memcpy to C assignment where appropriate. Changed all those
    uses of png_memcpy that were doing a simple assignment to assignments
    (all those cases where the thing being copied is a non-array C L-value).
  Added some error checking to png_set_*() routines.
  Removed the reference to the non-exported function png_memcpy() from
    example.c.
  Fixed the Visual C 64-bit build - it requires jmp_buf to be aligned, but
    it had become misaligned.
  Revised contrib/pngminus/pnm2png.c to avoid warnings when png_uint_32
    and unsigned long are of different sizes.

Version 1.6.0beta05 [January 15, 2012]
  Updated manual with description of the simplified API (copied from png.h)
  Fix bug in pngerror.c: some long warnings were being improperly truncated
    (CVE-2011-3464, bug introduced in libpng-1.5.3beta05).

Version 1.6.0beta06 [January 24, 2012]
  Added palette support to the simplified APIs. This commit
    changes some of the macro definitions in png.h, app code
    may need corresponding changes.
  Increased the formatted warning buffer to 192 bytes.
  Added color-map support to simplified API. This is an initial version for
    review; the documentation has not yet been updated.
  Fixed Min/GW uninstall to remove libpng.dll.a

Version 1.6.0beta07 [January 28, 2012]
  Eliminated Intel icc/icl compiler warnings. The Intel (GCC derived)
    compiler issues slightly different warnings from those issued by the
    current vesions of GCC. This eliminates those warnings by
    adding/removing casts and small code rewrites.
  Updated configure.ac from autoupdate: added --enable-werror option.
    Also some layout regularization and removal of introduced tab characters
    (replaced with 3-character indentation).  Obsolete macros identified by
    autoupdate have been removed; the replacements are all in 2.59 so
    the pre-req hasn't been changed.  --enable-werror checks for support
    for -Werror (or the given argument) in the compiler.  This mimics the
    gcc configure option by allowing -Werror to be turned on safely; without
    the option the tests written in configure itself fail compilation because
    they cause compiler warnings.
  Rewrote autogen.sh to run autoreconf instead of running tools one-by-one.
  Conditionalize the install rules for MINGW and CYGWIN in CMakeLists.txt and
    set CMAKE_LIBRARY_OUTPUT_DIRECTORY to "lib" on all platforms (C. Yapp).
  Freeze libtool files in the 'scripts' directory. This version of autogen.sh
    attempts to dissuade people from running it when it is not, or should not,
    be necessary.  In fact, autogen.sh does not work when run in a libpng
    directory extracted from a tar distribution anymore. You must run it in
    a GIT clone instead.
  Added two images to contrib/pngsuite (1-bit and 2-bit transparent grayscale),
    and renamed three whose names were inconsistent with those in
    pngsuite/README.txt.

Version 1.6.0beta08 [February 1, 2012]
  Fixed Image::colormap misalignment in pngstest.c
  Check libtool/libtoolize version number (2.4.2) in configure.ac
  Divide test-pngstest.sh into separate pngstest runs for basic and
    transparent images.
  Moved automake options to AM_INIT_AUTOMAKE in configure.ac
  Added color-tests, silent-rules (Not yet implemented in Makefile.am) and
    version checking to configure.ac
  Improved pngstest speed by not doing redundant tests and add const to
    the background parameter of png_image_finish_read. The --background
    option is now done automagically only when required, so that commandline
    option no longer exists.
  Cleaned up pngpriv.h to consistently declare all functions and data.
    Also eliminated PNG_CONST_DATA, which is apparently not needed but we
    can't be sure until it is gone.
  Added symbol prefixing that allows all the libpng external symbols
    to be prefixed (suggested by Reuben Hawkins).
  Updated "ftbb*.png" list in the owatcom and vstudio projects.
  Fixed 'prefix' builds on clean systems. The generation of pngprefix.h
    should not require itself.
  Updated INSTALL to explain that autogen.sh must be run in a GIT clone,
    not in a libpng directory extracted from a tar distribution.

Version 1.6.0beta09 [February 1, 2012]
  Reverted the prebuilt configure files to libpng-1.6.0beta05 condition.

Version 1.6.0beta10 [February 3, 2012]
  Added Z_SOLO for zlib-1.2.6+ and correct pngstest tests
  Updated list of test images in CMakeLists.txt
  Updated the prebuilt configure files to current condition.
  Revised INSTALL information about autogen.sh; it works in tar distributions.

Version 1.6.0beta11 [February 16, 2012]
  Fix character count in pngstest command in projects/owatcom/pngstest.tgt
  Revised test-pngstest.sh to report PASS/FAIL for each image.
  Updated documentation about the simplified API.
  Corrected estimate of error in libpng png_set_rgb_to_gray API.  The API is
    extremely inaccurate for sRGB conversions because it uses an 8-bit
    intermediate linear value and it does not use the sRGB transform, so it
    suffers from the known instability in gamma transforms for values close
    to 0 (see Poynton).  The net result is that the calculation has a maximum
    error of 14.99/255; 0.5/255^(1/2.2).  pngstest now uses 15 for the
    permitted 8-bit error. This may still not be enough because of arithmetic
    error.
  Removed some unused arrays (with #ifdef) from png_read_push_finish_row().
  Fixed a memory overwrite bug in simplified read of RGB PNG with
    non-linear gamma Also bugs in the error checking in pngread.c and changed
    quite a lot of the checks in pngstest.c to be correct; either correctly
    written or not over-optimistic.  The pngstest changes are insufficient to
    allow all possible RGB transforms to be passed; pngstest cmppixel needs
    to be rewritten to make it clearer which errors it allows and then changed
    to permit known inaccuracies.
  Removed tests for no-longer-used *_EMPTY_PLTE_SUPPORTED from pngstruct.h
  Fixed fixed/float API export conditionals. 1) If FIXED_POINT or
    FLOATING_POINT options were switched off, png.h ended up with lone ';'
    characters.  This is not valid ANSI-C outside a function.  The ';'
    characters have been moved inside the definition of PNG_FP_EXPORT and
    PNG_FIXED_EXPORT. 2) If either option was switched off, the declaration
    of the corresponding functions were completely omitted, even though some
    of them are still used internally.  The result is still valid, but
    produces warnings from gcc with some warning options (including -Wall). The
    fix is to cause png.h to declare the functions with PNG_INTERNAL_FUNCTION
    when png.h is included from pngpriv.h.
  Check for invalid palette index while reading paletted PNG.  When one is
    found, issue a warning and increase png_ptr->num_palette accordingly.
    Apps are responsible for checking to see if that happened.

Version 1.6.0beta12 [February 18, 2012]
  Do not increase num_palette on invalid_index.
  Relocated check for invalid palette index to pngrtran.c, after unpacking
    the sub-8-bit pixels.
  Fixed CVE-2011-3026 buffer overrun bug.  This bug was introduced when
    iCCP chunk support was added at libpng-1.0.6. Deal more correctly with the
    test on iCCP chunk length. Also removed spurious casts that may hide
    problems on 16-bit systems.

Version 1.6.0beta13 [February 24, 2012]
  Eliminated redundant png_push_read_tEXt|zTXt|iTXt|unknown code from
    pngpread.c and use the sequential png_handle_tEXt, etc., in pngrutil.c;
    now that png_ptr->buffer is inaccessible to applications, the special
    handling is no longer useful.
  Added PNG_SAFE_LIMITS feature to pnglibconf.dfa, pngpriv.h, and new
    pngusr.dfa to reset the user limits to safe ones if PNG_SAFE_LIMITS is
    defined.  To enable, use "CPPFLAGS=-DPNG_SAFE_LIMITS_SUPPORTED=1" on the
    configure command or put #define PNG_SAFE_LIMITS_SUPPORTED in
    pnglibconf.h.prebuilt and pnglibconf.h.

Version 1.6.0beta14 [February 27, 2012]
  Added information about the new limits in the manual.
  Updated Makefile.in

Version 1.6.0beta15 [March 2, 2012]
  Removed unused "current_text" members of png_struct and the png_free()
    of png_ptr->current_text from pngread.c
  Rewrote pngstest.c for substantial speed improvement.
  Fixed transparent pixel and 16-bit rgb tests in pngstest and removed a
    spurious check in pngwrite.c
  Added PNG_IMAGE_FLAG_FAST for the benefit of applications that store
    intermediate files, or intermediate in-memory data, while processing
    image data with the simplified API.  The option makes the files larger
    but faster to write and read.  pngstest now uses this by default; this
    can be disabled with the --slow option.
  Improved pngstest fine tuning of error numbers, new test file generator.
    The generator generates images that test the full range of sample values,
    allow the error numbers in pngstest to be tuned and checked.  makepng
    also allows generation of images with extra chunks, although this is
    still work-in-progress.
  Added check for invalid palette index while reading.
  Fixed some bugs in ICC profile writing. The code should now accept
    all potentially valid ICC profiles and reject obviously invalid ones.
    It now uses png_error() to do so rather than casually writing a PNG
    without the necessary color data.
  Removed whitespace from the end of lines in all source files and scripts.

Version 1.6.0beta16 [March 6, 2012]
  Relocated palette-index checking function from pngrutil.c to pngtrans.c
  Added palette-index checking while writing.
  Changed png_inflate() and calling routines to avoid overflow problems.
    This is an intermediate check-in that solves the immediate problems and
    introduces one performance improvement (avoiding a copy via png_ptr->zbuf.)
    Further changes will be made to make ICC profile handling more secure.
  Fixed build warnings (MSVC, GCC, GCC v3). Cygwin GCC with default options
    declares 'index' as a global, causing a warning if it is used as a local
    variable.  GCC 64-bit warns about assigning a (size_t) (unsigned 64-bit)
    to an (int) (signed 32-bit).  MSVC, however, warns about using the
    unary '-' operator on an unsigned value (even though it is well defined
    by ANSI-C to be ~x+1).  The padding calculation was changed to use a
    different method.  Removed the tests on png_ptr->pass.
  Added contrib/libtests/tarith.c to test internal arithmetic functions from
    png.c. This is a libpng maintainer program used to validate changes to the
    internal arithmetic functions.
  Made read 'inflate' handling like write 'deflate' handling. The read
    code now claims and releases png_ptr->zstream, like the write code.
    The bug whereby the progressive reader failed to release the zstream
    is now fixed, all initialization is delayed, and the code checks for
    changed parameters on deflate rather than always calling
    deflatedEnd/deflateInit.
  Validate the zTXt strings in pngvalid.
  Added code to validate the windowBits value passed to deflateInit2().
    If the call to deflateInit2() is wrong a png_warning will be issued
    (in fact this is harmless, but the PNG data produced may be sub-optimal).

Version 1.6.0beta17 [March 10, 2012]
  Fixed PNG_LIBPNG_BUILD_BASE_TYPE definition. 
  Reject all iCCP chunks after the first, even if the first one is invalid.
  Deflate/inflate was reworked to move common zlib calls into single
    functions [rw]util.c.  A new shared keyword check routine was also added
    and the 'zbuf' is no longer allocated on progressive read.  It is now
    possible to call png_inflate() incrementally.  A warning is no longer
    issued if the language tag or translated keyword in the iTXt chunk
    has zero length.
  If benign errors are disabled use maximum window on ancilliary inflate.
    This works round a bug introduced in 1.5.4 where compressed ancillary
    chunks could end up with a too-small windowBits value in the deflate
    header.

Version 1.6.0beta18 [March 16, 2012]
  Issue a png_benign_error() instead of png_warning() about bad palette index.
  In pngtest, treat benign errors as errors if "-strict" is present.
  Fixed an off-by-one error in the palette index checking function.
  Fixed a compiler warning under Cygwin (Windows-7, 32-bit system)
  Revised example.c to put text strings in a temporary character array
    instead of directly assigning string constants to png_textp members.
    This avoids compiler warnings when -Wwrite-strings is enabled.
  Added output flushing to aid debugging under Visual Studio. Unfortunately
    this is necessary because the VS2010 output window otherwise simply loses
    the error messages on error (they weren't flushed to the window before
    the process exited, apparently!)
  Added configuration support for benign errors and changed the read
    default. Also changed some warnings in the iCCP and sRGB handling
    from to benign errors. Configuration now makes read benign
    errors warnings and write benign errors to errors by default (thus
    changing the behavior on read).  The simplified API always forces
    read benign errors to warnings (regardless of the system default, unless
    this is disabled in which case the simplified API can't be built.)

Version 1.6.0beta19 [March 18, 2012]
  Work around for duplicate row start calls; added warning messages.
    This turns on PNG_FLAG_DETECT_UNINITIALIZED to detect app code that
    fails to call one of the 'start' routines (not enabled in libpng-1.5
    because it is technically an API change, since it did normally work
    before.)  It also makes duplicate calls to png_read_start_row (an
    internal function called at the start of the image read) benign, as
    they were before changes to use png_inflate_claim. Somehow webkit is
    causing this to happen; this is probably a mis-feature in the zlib
    changes so this commit is only a work-round.
  Removed erroneous setting of DETECT_UNINITIALIZED and added more
    checks. The code now does a png_error if an attempt is made to do the
    row initialization twice; this is an application error and it has
    serious consequences because the transform data in png_struct is
    changed by each call.
  Added application error reporting and added chunk names to read
    benign errors; also added --strict to pngstest - not enabled
    yet because a warning is produced.
  Avoid the double gamma correction warning in the simplified API.
    This allows the --strict option to pass in the pngstest checks

Version 1.6.0beta20 [March 29, 2012]
  Changed chunk handler warnings into benign errors, incrementally load iCCP
  Added checksum-icc.c to contrib/tools
  Prevent PNG_EXPAND+PNG_SHIFT doing the shift twice.
  Recognize known sRGB ICC profiles while reading; prefer writing the
    iCCP profile over writing the sRGB chunk, controlled by the
    PNG_sRGB_PROFILE_CHECKS option.
  Revised png_set_text_2() to avoid potential memory corruption (fixes
    CVE-2011-3048, also known as CVE-2012-3425).

Version 1.6.0beta21 [April 27, 2012]
  Revised scripts/makefile.darwin: use system zlib; remove quotes around
    architecture list; add missing ppc architecture; add architecture options
    to shared library link; don't try to create a shared lib based on missing
    RELEASE variable.
  Enable png_set_check_for_invalid_index() for both read and write.
  Removed #ifdef PNG_HANDLE_AS_UNKNOWN_SUPPORTED in pngpriv.h around
    declaration of png_handle_unknown().
  Added -lssp_nonshared in a comment in scripts/makefile.freebsd
    and changed deprecated NOOBJ and NOPROFILE to NO_OBJ and NO_PROFILE.

Version 1.6.0beta22 [May 23, 2012]
  Removed need for -Wno-cast-align with clang.  clang correctly warns on
    alignment increasing pointer casts when -Wcast-align is passed. This
    fixes the cases that clang warns about either by eliminating the
    casts from png_bytep to png_uint_16p (pngread.c), or, for pngrutil.c
    where the cast is previously verified or pngstest.c where it is OK, by
    introducing new png_aligncast macros to do the cast in a way that clang
    accepts.

Version 1.6.0beta23 [June 6, 2012]
  Revised CMakeLists.txt to not attempt to make a symlink under mingw.
  Made fixes for new optimization warnings from gcc 4.7.0. The compiler
    performs an optimization which is safe; however it then warns about it.
    Changing the type of 'palette_number' in pngvalid.c removes the warning.
  Do not depend upon a GCC feature macro being available for use in generating
    the linker mapfile symbol prefix.
  Improved performance of new do_check_palette_indexes() function (only
    update the value when it actually increases, move test for whether
    the check is wanted out of the function.

Version 1.6.0beta24 [June 7, 2012]
  Don't check palette indexes if num_palette is 0 (as it can be in MNG files).

Version 1.6.0beta25 [June 16, 2012]
  Revised png_set_keep_unknown_chunks() so num_chunks < 0 means ignore all
    unknown chunks and all known chunks except for IHDR, PLTE, tRNS, IDAT,
    and IEND.  Previously it only meant ignore all unknown chunks, the
    same as num_chunks == 0. Revised png_image_skip_unused_chunks() to
    provide a list of chunks to be processed instead of a list of chunks to
    ignore.  Revised contrib/gregbook/readpng2.c accordingly.

Version 1.6.0beta26 [July 10, 2012]
  Removed scripts/makefile.cegcc from the *.zip and *.7z distributions; it
    depends on configure, which is not included in those archives.
  Moved scripts/chkfmt to contrib/tools.
  Changed "a+w" to "u+w" in Makefile.in to fix CVE-2012-3386.

Version 1.6.0beta27 [August 11, 2012]
  Do not compile PNG_DEPRECATED, PNG_ALLOC and PNG_PRIVATE when __GNUC__ < 3.
  Do not use __restrict when GNUC is <= 3.1
  Removed references to png_zalloc() and png_zfree() from the manual.
  Fixed configurations where floating point is completely disabled.  Because
    of the changes to support symbol prefixing PNG_INTERNAL_FUNCTION declares
    floating point APIs during libpng builds even if they are completely
    disabled. This requires the png floating point types (png_double*) to be
    declared even though the functions are never actually defined.  This
    change provides a dummy definition so that the declarations work, yet any
    implementation will fail to compile because of an incomplete type.
  Re-eliminated the use of strcpy() in pngtest.c.  An unncessary use of
    strcpy() was accidentally re-introduced in libpng16; this change replaces
    it with strncpy().
  Eliminated use of png_sizeof(); use sizeof() instead.
  Use a consistent style for (sizeof type) and (sizeof (array))
  Cleanup of png_set_filler().  This function does very different things on
    read and write.  In libpng 1.6 the two cases can be distinguished and
    considerable code cleanup, and extra error checking, is possible.  This
    makes calls on the write side that have no effect be ignored with a
    png_app_error(), which can be disabled in the app using
    png_set_benign_errors(), and removes the spurious use of usr_channels
    on the read side.
  Insist on autotools 1.12.1 for git builds because there are security issues
    with 1.12 and insisting on anything less would allow 1.12 to be used.
  Removed info_ptr->signature[8] from WRITE-only builds.
  Add some conditions for compiling png_fixed().  This is a small function
    but it requires "-lm" on some platforms.
  Cause pngtest --strict to fail on any warning from libpng (not just errors)
    and cause it not to fail at the comparison step if libpng lacks support
    for writing chunks that it reads from the input (currently only implemented
    for compressed text chunks).
  Make all three "make check" test programs work without READ or WRITE support.
    Now "make check" will succeed even if libpng is compiled with -DPNG_NO_READ
    or -DPNG_NO_WRITE.  The tests performed are reduced, but the basic reading
    and writing of a PNG file is always tested by one or more of the tests.
  Consistently use strlen(), memset(), memcpy(), and memcmp() instead of the
    png_strlen(), png_memset(), png_memcpy(), and png_memcmp() macros.
  Removed the png_sizeof(), png_strlen(), png_memset(), png_memcpy(), and
    png_memcmp() macros.
  Work around gcc 3.x and Microsoft Visual Studio 2010 complaints. Both object
    to the split initialization of num_chunks.

Version 1.6.0beta28 [August 29, 2012]
  Unknown handling fixes and clean up. This adds more correct option
    control of the unknown handling, corrects the pre-existing bug where
    the per-chunk 'keep' setting is ignored and makes it possible to skip
    IDAT chunks in the sequential reader (broken in earlier 1.6 versions).
    There is a new test program, test-unknown.c, which is a work in progress
    (not currently part of the test suite).  Comments in the header files now
    explain how the unknown handling works.
  Allow fine grain control of unknown chunk APIs. This change allows
    png_set_keep_unknown_chunks() to be turned off if not required and causes
    both read and write to behave appropriately (on read this is only possible
    if the user callback is used to handle unknown chunks).  The change
    also removes the support for storing unknown chunks in the info_struct
    if the only unknown handling enabled is via the callback, allowing libpng
    to be configured with callback reading and none of the unnecessary code.
  Corrected fix for unknown handling in pngtest. This reinstates the
    libpng handling of unknown chunks other than vpAg and sTER (including
    unsafe-to-copy chunks which were dropped before) and eliminates the
    repositioning of vpAg and sTER in pngtest.png by changing pngtest.png
    (so the chunks are where libpng would put them).
  Added "tunknown" test and corrected a logic error in png_handle_unknown()
    when SAVE support is absent.  Moved the shell test scripts for
    contrib/libtests from the libpng top directory to contrib/libtests.
    png_handle_unknown() must always read or skip the chunk, if
    SAVE_UNKNOWN_CHUNKS is turned off *and* the application does not set
    a user callback an unknown chunk will not be read, leading to a read
    error, which was revealed by the "tunknown" test.
  Cleaned up and corrected ICC profile handling.
    contrib/libtests/makepng: corrected 'rgb' and 'gray' cases.  profile_error
    messages could be truncated; made a correct buffer size calculation and
    adjusted pngerror.c appropriately. png_icc_check_* checking improved;
    changed the functions to receive the correct color type of the PNG on read
    or write and check that it matches the color space of the profile (despite
    what the comments said before, there is danger in assuming the app will
    cope correctly with an RGB profile on a grayscale image and, since it
    violates the PNG spec, allowing it is certain to produce inconsistent
    app behavior and might even cause app crashes.) Check that profiles
    contain the tags needed to process the PNG (tags all required by the ICC
    spec). Removed unused PNG_STATIC from pngpriv.h.

Version 1.6.0beta29 [September 4, 2012]
  Fixed the simplified API example programs to add the *colormap parameter
    to several of he API and improved the error message if the version field
    is not set.
  Added contrib/examples/* to the *.zip and *.7z distributions.
  Updated simplified API synopses and description of the png_image structure
    in the manual.
  Made makepng and pngtest produce identical PNGs, add "--relaxed" option
    to pngtest. The "--relaxed" option turns off the benign errors that are
    enabled by default in pre-RC builds. makepng can now write ICC profiles
    where the length has not been extended to a multiple of 4, and pngtest
    now intercepts all libpng errors, allowing the previously-introduced
    "--strict test" on no warnings to actually work.
  Improved ICC profile handling including cHRM chunk generation and fixed
    Cygwin+MSVC build errors. The ICC profile handling now includes more
    checking.  Several errors that caused rejection of the profile are now
    handled with a warning in such a way that the invalid profiles will be
    read by default in release (but not pre-RC) builds but will not be
    written by default.  The easy part of handling the cHRM chunk is written,
    where the ICC profile contains the required data.  The more difficult
    part plus guessing a gAMA value requires code to pass selected RGB values
    through the profile.

Version 1.6.0beta30 [October 24, 2012]
  Changed ICC profile matrix/vector types to not depend on array type rules.
    By the ANSI-C standard the new types should be identical to the previous
    versions, and all known versions of gcc tested with the previous versions
    except for GCC-4.2.1 work with this version.  The change makes the ANSI-C
    rule that const applied to an array of elements applies instead to the
    elements in the array moot by explicitly applying const to the base
    elements of the png_icc_matrix and png_icc_vector types. The accidental
    (harmless) 'const' previously applied to the parameters of two of the
    functions have also been removed.
  Added a work around for GCC 4.2 optimization bug.
  Marked the broken (bad white point) original HP sRGB profiles correctly and
    correct comments.
  Added -DZ_SOLO to contrib/pngminim/*/makefile to work with zlib-1.2.7
  Use /MDd for vstudio debug builds. Also added pngunkown to the vstudio
    builds, fixed build errors and corrected a minor exit code error in
    pngvalid if the 'touch' file name is invalid.
  Add updated WARNING file to projects/vstudio from libpng 1.5/vstudio
  Fixed build when using #define PNG_NO_READ_GAMMA in png_do_compose() in
    pngrtran.c (Domani Hannes).

Version 1.6.0beta31 [November 1, 2012]
  Undid the erroneous change to vstudio/pngvalid build in libpng-1.6.0beta30.
  Made pngvalid so that it will build outside the libpng source tree.
  Made builds -DPNG_NO_READ_GAMMA compile (the unit tests still fail).
  Made PNG_NO_READ_GAMMA switch off interfaces that depend on READ_GAMMA.
    Prior to 1.6.0 switching off READ_GAMMA did unpredictable things to the
    interfaces that use it (specifically, png_do_background in 1.4 would
    simply display composite for grayscale images but do composition
    with the incorrect arithmetic for color ones). In 1.6 the semantic
    of -DPNG_NO_READ_GAMMA is changed to simply disable any interface that
    depends on it; this obliges people who set it to consider whether they
    really want it off if they happen to use any of the interfaces in
    question (typically most users who disable it won't).
  Fixed GUIDs in projects/vstudio. Some were duplicated or missing,
    resulting in VS2010 having to update the files.
  Removed non-working ICC profile support code that was mostly added to
    libpng-1.6.0beta29 and beta30. There was too much code for too little
    gain; implementing full ICC color correction may be desireable but is left
    up to applications.

Version 1.6.0beta32 [November 25, 2012]
  Fixed an intermittent SEGV in pngstest due to an uninitialized array element.
  Added the ability for contrib/libtests/makepng.c to make a PNG with just one
    color. This is useful for debugging pngstest color inaccuracy reports.
  Fixed error checking in the simplified write API (Olaf van der Spek)
  Made png_user_version_check() ok to use with libpng version 1.10.x and later.

Version 1.6.0beta33 [December 15, 2012]
  Fixed typo in png.c (PNG_SET_CHUNK_MALLOC_MAX should be PNG_CHUNK_MALLOC_MAX)
    that causes the MALLOC_MAX limit not to work (John Bowler)
  Change png_warning() to png_app_error() in pngwrite.c and comment the
    fall-through condition.
  Change png_warning() to png_app_warning() in png_write_tRNS().
  Rearranged the ARM-NEON optimizations: Isolated the machine specific code
    to the hardware subdirectory and added comments to pngrutil.c so that
    implementors of other optimizations know what to do.
  Fixed cases of unquoted DESTDIR in Makefile.am
  Rebuilt Makefile.in, etc., with autoconf-2.69 and automake-1.12.5.

Version 1.6.0beta34 [December 19, 2012]
  Cleaned up whitespace in the synopsis portion of the manpage "libpng.3"
  Disassembled the version number in scripts/options.awk (necessary for
    building on SunOs).

Version 1.6.0beta35 [December 23, 2012]
  Made default Zlib compression settings be configurable. This adds #defines to
    pnglibconf.h to control the defaults.
  Fixed Windows build issues, enabled ARM compilation. Various warnings issued
    by earlier versions of GCC fixed for Cygwin and Min/GW (which both use old
    GCCs.) ARM support is enabled by default in zlib.props (unsupported by
    Microsoft) and ARM compilation is made possible by deleting the check for
    x86. The test programs cannot be run because they are not signed.

Version 1.6.0beta36 [January 2, 2013]
  Discontinued distributing libpng-1.x.x.tar.bz2.
  Discontinued distributing libpng-1.7.0-1.6.0-diff.txt and similar.
  Rebuilt configure with autoconf-2.69 (inadvertently not done in beta33)
  Fixed 'make distcheck' on SUN OS - libpng.so was not being removed

Version 1.6.0beta37 [January 10, 2013]
  Fixed conceivable but difficult to repro overflow. Also added two test
    programs to generate and test a PNG which should have the problem.

Version 1.6.0beta39 [January 19, 2013]
  Again corrected attempt at overflow detection in png_set_unknown_chunks()
  (CVE-2013-7353).  Added overflow detection in png_set_sPLT() and
  png_set_text_2() (CVE-2013-7354).

Version 1.6.0beta40 [January 20, 2013]
  Use consistent handling of overflows in text, sPLT and unknown png_set_* APIs

Version 1.6.0rc01 [January 26, 2013]
  No changes.

Version 1.6.0rc02 [February 4, 2013]
  Added png_get_palette_max() function.

Version 1.6.0rc03 [February 5, 2013]
  Fixed the png_get_palette_max API.

Version 1.6.0rc04 [February 7, 2013]
  Turn serial tests back on (recently turned off by autotools upgrade).

Version 1.6.0rc05 [February 8, 2013]
  Update manual about png_get_palette_max().

Version 1.6.0rc06 [February 9, 2013]
  Fixed missing dependency in --prefix builds The intermediate
    internal 'prefix.h' file can only be generated correctly after
    pnglibconf.h, however the dependency was not in Makefile.am.  The
    symptoms are unpredictable depending on the order make chooses to
    build pngprefix.h and pnglibconf.h, often the error goes unnoticed
    because there is a system pnglibconf.h to use instead.

Version 1.6.0rc07 [February 10, 2013]
  Enclosed the new png_get_palette_max in #ifdef PNG_GET_PALETTE_MAX_SUPPORTED
    block, and revised pnglibconf.h and pnglibconf.h.prebuilt accordingly.

Version 1.6.0rc08 [February 10, 2013]
  Fix typo in png.h #ifdef

Version 1.6.0 [February 14, 2013]
  No changes.

Version 1.6.1beta01 [February 16, 2013]
  Made symbol prefixing work with the ARM neon optimizations. Also allow
    pngpriv.h to be included for preprocessor definitions only, so it can
    be used in non-C/C++ files. Back ported from libpng 1.7.
  Made sRGB check numbers consistent.
  Ported libpng 1.5 options.awk/dfn file handling to 1.6, fixed one bug.
  Removed cc -E workround, corrected png_get_palette_max API Tested on
    SUN OS cc 5.9, which demonstrates the tokenization problem previously
    avoided by using /lib/cpp.  Since all .dfn output is now protected in
    double quotes unless it is to be macro substituted the fix should
    work everywhere.
  Enabled parallel tests - back ported from libpng-1.7.
  scripts/pnglibconf.dfa formatting improvements back ported from libpng17.
  Fixed a race condition in the creation of the build 'scripts' directory
    while building with a parallel make.
  Use approved/supported Android method to check for NEON, use Linux/POSIX
    1003.1 API to check /proc/self/auxv avoiding buffer allocation and other
    library calls (ported from libpng15).

Version 1.6.1beta02 [February 19, 2013]
  Use parentheses more consistently in "#if defined(MACRO)" tests.
  Folded long lines.
  Reenabled code to allow zero length PLTE chunks for MNG.

Version 1.6.1beta03 [February 22, 2013]
  Fixed ALIGNED_MEMORY support.
  Added a new configure option:
    --enable-arm-neon=always will stop the run-time checks. New checks
    within arm/arm_init.c will cause the code not to be compiled unless
    __ARM_NEON__ is set. This should make it fail safe (if someone asks
    for it on then the build will fail if it can't be done.)
  Updated the INSTALL document.

Version 1.6.1beta04 [February 27, 2013]
  Revised INSTALL to recommend using CPPFLAGS instead of INCLUDES.
  Revised scripts/makefile.freebsd to respect ZLIBLIB and ZLIBINC.
  Revised scripts/dfn.awk to work with the buggy MSYS awk that has trouble
    with CRLF line endings.

Version 1.6.1beta05 [March 1, 2013]
  Avoid a possible memory leak in contrib/gregbook/readpng.c

Version 1.6.1beta06 [March 4, 2013]
  Better documentation of unknown handling API interactions.
  Corrected Android builds and corrected libpng.vers with symbol
    prefixing.  It also makes those tests compile and link on Android.
  Added an API png_set_option() to set optimization options externally,
    providing an alternative and general solution for the non-portable
    run-time tests used by the ARM Neon code, using the PNG_ARM_NEON option.
  The order of settings vs options in pnglibconf.h is reversed to allow
    settings to depend on options and options can now set (or override) the
    defaults for settings.

Version 1.6.1beta07 [March 7, 2013]
  Corrected simplified API default gamma for color-mapped output, added
    a flag to change default. In 1.6.0 when the simplified API was used
    to produce color-mapped output from an input image with no gamma
    information the gamma assumed for the input could be different from
    that assumed for non-color-mapped output.  In particular 16-bit depth
    input files were assumed to be sRGB encoded, whereas in the 'direct'
    case they were assumed to have linear data.  This was an error.  The
    fix makes the simplified API treat all input files the same way and
    adds a new flag to the png_image::flags member to allow the
    application/user to specify that 16-bit files contain sRGB data
    rather than the default linear.
  Fixed bugs in the pngpixel and makepng test programs.

Version 1.6.1beta08 [March 7, 2013]
  Fixed CMakelists.txt to allow building a single variant of the library
    (Claudio Bley):
  Introduced a PNG_LIB_TARGETS variable that lists all activated library
    targets.  It is an error if this variable ends up empty, ie. you have
    to build at least one library variant.
  Made the *_COPY targets only depend on library targets actually being build.
  Use PNG_LIB_TARGETS to unify a code path.
  Changed the CREATE_SYMLINK macro to expect the full path to a file as the
    first argument. When symlinking the filename component of that path is
    determined and used as the link target.
  Use copy_if_different in the CREATE_SYMLINK macro.

Version 1.6.1beta09 [March 13, 2013]
  Eliminated two warnings from the Intel C compiler. The warnings are
    technically valid, although a reasonable treatment of division would
    show it to be incorrect.

Version 1.6.1rc01 [March 21, 2013]
  No changes.

Version 1.6.1 [March 28, 2013]
  No changes.

Version 1.6.2beta01 [April 14, 2013]
  Updated documentation of 1.5.x to 1.6.x changes in iCCP chunk handling.
  Fixed incorrect warning of excess deflate data. End condition - the
    warning would be produced if the end of the deflate stream wasn't read
    in the last row.  The warning is harmless.
  Corrected the test on user transform changes on read. It was in the
    png_set of the transform function, but that doesn't matter unless the
    transform function changes the rowbuf size, and that is only valid if
    transform_info is called.
  Corrected a misplaced closing bracket in contrib/libtests/pngvalid.c
    (Flavio Medeiros).
  Corrected length written to uncompressed iTXt chunks (Samuli Suominen).
    Bug was introduced in libpng-1.6.0.

Version 1.6.2rc01 [April 18, 2013]
  Added contrib/tools/fixitxt.c, to repair the erroneous iTXt chunk length
    written by libpng-1.6.0 and 1.6.1.
  Disallow storing sRGB information when the sRGB is not supported.

Version 1.6.2rc02 [April 18, 2013]
  Merge pngtest.c with libpng-1.7.0

Version 1.6.2rc03 [April 22, 2013]
  Trivial spelling cleanup.

Version 1.6.2rc04 and 1.6.2rc05 [omitted]

Version 1.6.2rc06 [April 24, 2013]
  Reverted to version 1.6.2rc03.  Recent changes to arm/neon support
    have been ported to libpng-1.7.0beta09 and will reappear in version
    1.6.3beta01.

Version 1.6.2 [April 25, 2013]
  No changes.

Version 1.6.3beta01 [April 25, 2013]
  Revised stack marking in arm/filter_neon.S and configure.ac.
  Ensure that NEON filter stuff is completely disabled when switched 'off'.
    Previously the ARM NEON specific files were still built if the option
    was switched 'off' as opposed to being explicitly disabled.

Version 1.6.3beta02 [April 26, 2013]
  Test for 'arm*' not just 'arm' in the host_cpu configure variable.
  Rebuilt the configure scripts.

Version 1.6.3beta03 [April 30, 2013]
  Expanded manual paragraph about writing private chunks, particularly
    the need to call png_set_keep_unknown_chunks() when writing them.
  Avoid dereferencing NULL pointer possibly returned from
    png_create_write_struct() (Andrew Church).

Version 1.6.3beta05 [May 9, 2013]
  Calculate our own zlib windowBits when decoding rather than trusting the
    CMF bytes in the PNG datastream.
  Added an option to force maximum window size for inflating, which was
    the behavior of libpng15 and earlier, via a new PNG_MAXIMUM_INFLATE_WINDOW
    option for png_set_options().
  Added png-fix-itxt and png-fix-too-far-back to the built programs and
    removed warnings from the source code and timepng that are revealed as
    a result.
  Detect wrong libpng versions linked to png-fix-too-far-back, which currently
    only works with libpng versions that can be made to reliably fail when
    the deflate data contains an out-of-window reference.  This means only
    1.6 and later.
  Fixed gnu issues: g++ needs a static_cast, gcc 4.4.7 has a broken warning
    message which it is easier to work round than ignore.
  Updated contrib/pngminus/pnm2png.c (Paul Stewart):
    Check for EOF
    Ignore "#" delimited comments in input file to pnm2png.c.
    Fixed whitespace handling
    Added a call to png_set_packing()
    Initialize dimension values so if sscanf fails at least we have known
      invalid values.
  Attempt to detect configuration issues with png-fix-too-far-back, which
    requires both the correct libpng and the correct zlib to function
    correctly.
  Check ZLIB_VERNUM for mismatches, enclose #error in quotes
  Added information in the documentation about problems with and fixes for
    the bad CRC and bad iTXt chunk situations.

Version 1.6.3beta06 [May 12, 2013]
  Allow contrib/pngminus/pnm2png.c to compile without WRITE_INVERT and
    WRITE_PACK supported (writes error message that it can't read P1 or
    P4 PBM files).
  Improved png-fix-too-far-back usage message, added --suffix option.
  Revised contrib/pngminim/*/makefile to generate pnglibconf.h with the
    right zlib header files.
  Separated CPPFLAGS and CFLAGS in contrib/pngminim/*/makefile

Version 1.6.3beta07 [June 8, 2013]
  Removed a redundant test in png_set_IHDR().
  Added set(CMAKE_CONFIGURATION_TYPES ...) to CMakeLists.txt (Andrew Hundt)
  Deleted set(CMAKE_BUILD_TYPE) block from CMakeLists.txt
  Enclose the prototypes for the simplified write API in
    #ifdef PNG_STDIO_SUPPORTED/#endif
  Make ARM NEON support work at compile time (not just configure time).
    This moves the test on __ARM_NEON__ into pngconf.h to avoid issues when
    using a compiler that compiles for multiple architectures at one time.
  Removed PNG_FILTER_OPTIMIZATIONS and PNG_ARM_NEON_SUPPORTED from
    pnglibconf.h, allowing more of the decisions to be made internally
    (pngpriv.h) during the compile.  Without this, symbol prefixing is broken
    under certain circumstances on ARM platforms.  Now only the API parts of
    the optimizations ('check' vs 'api') are exposed in the public header files
    except that the new setting PNG_ARM_NEON_OPT documents how libpng makes the
    decision about whether or not to use the optimizations.
  Protect symbol prefixing against CC/CPPFLAGS/CFLAGS useage.
    Previous iOS/Xcode fixes for the ARM NEON optimizations moved the test
    on __ARM_NEON__ from configure time to compile time.  This breaks symbol
    prefixing because the definition of the special png_init_filter_functions
    call was hidden at configure time if the relevant compiler arguments are
    passed in CFLAGS as opposed to CC.  This change attempts to avoid all
    the confusion that would result by declaring the init function even when
    it is not used, so that it will always get prefixed.

Version 1.6.3beta08 [June 18, 2013]
  Revised libpng.3 so that "doclifter" can process it.

Version 1.6.3beta09 [June 27, 2013]
  Revised example.c to illustrate use of PNG_DEFAULT_sRGB and PNG_GAMMA_MAC_18
    as parameters for png_set_gamma().  These have been available since
    libpng-1.5.4.
  Renamed contrib/tools/png-fix-too-far-back.c to pngfix.c and revised it
    to check all compressed chunks known to libpng.

Version 1.6.3beta10 [July 5, 2013]
  Updated documentation to show default behavior of benign errors correctly.
  Only compile ARM code when PNG_READ_SUPPORTED is defined.
  Fixed undefined behavior in contrib/tools/pngfix.c and added new strip
    option. pngfix relied on undefined behavior and even a simple change from
    gcc to g++ caused it to fail.  The new strip option 'unsafe' has been
    implemented and is the default if --max is given.  Option names have
    been clarified, with --strip=transform now stripping the bKGD chunk,
    which was stripped previously with --strip=unused.
  Added all documented chunk types to pngpriv.h
  Unified pngfix.c source with libpng17.

Version 1.6.3rc01 [July 11, 2013]
  No changes.

Version 1.6.3 [July 18, 2013]
  Revised manual about changes in iTXt chunk handling made in libpng-1.6.0.
  Added "/* SAFE */" comments in pngrutil.c and pngrtran.c where warnings
    may be erroneously issued by code-checking applications.

Version 1.6.4beta01 [August 21, 2013]
  Added information about png_set_options() to the manual.
  Delay calling png_init_filter_functions() until a row with nonzero filter
    is found.

Version 1.6.4beta02 [August 30, 2013]
  Fixed inconsistent conditional compilation of png_chunk_unknown_handling()
    prototype, definition, and usage.  Made it depend on
    PNG_HANDLE_AS_UNKNOWN_SUPPORTED everywhere.

Version 1.6.4rc01 [September 5, 2013]
  No changes.

Version 1.6.4 [September 12, 2013]
  No changes.

Version 1.6.5 [September 14, 2013]
  Removed two stray lines of code from arm/arm_init.c.

Version 1.6.6 [September 16, 2013]
  Removed two stray lines of code from arm/arm_init.c, again.

Version 1.6.7beta01 [September 30, 2013]
  Revised unknown chunk code to correct several bugs in the NO_SAVE_/NO_WRITE
    combination
  Allow HANDLE_AS_UNKNOWN to work when other options are configured off. Also
    fixed the pngminim makefiles to work when $(MAKEFLAGS) contains stuff
    which terminates the make options (as by default in recent versions of
    Gentoo).
  Avoid up-cast warnings in pngvalid.c. On ARM the alignment requirements of
    png_modifier are greater than that of png_store and as a consequence
    compilation of pngvalid.c results in a warning about increased alignment
    requirements because of the bare cast to (png_modifier*). The code is safe,
    because the pointer is known to point to a stack allocated png_modifier,
    but this change avoids the warning.
  Fixed default behavior of ARM_NEON_API. If the ARM NEON API option was
    compiled without the CHECK option it defaulted to on, not off.
  Check user callback behavior in pngunknown.c. Previous versions compiled
    if SAVE_UNKNOWN was not available but did nothing since the callback
    was never implemented.
  Merged pngunknown.c with 1.7 version and back ported 1.7 improvements/fixes

Version 1.6.7beta02 [October 12, 2013]
  Made changes for compatibility with automake 1.14:
    1) Added the 'compile' program to the list of programs that must be cleaned
       in autogen.sh
    2) Added 'subdir-objects' which causes .c files in sub-directories to be
       compiled such that the corresponding .o files are also in the
       sub-directory.  This is because automake 1.14 warns that the
       current behavior of compiling to the top level directory may be removed
       in the future.
    3) Updated dependencies on pnglibconf.h to match the new .o locations and
       added all the files in contrib/libtests and contrib/tools that depend
       on pnglibconf.h
    4) Added 'BUILD_SOURCES = pnglibconf.h'; this is the automake recommended
       way of handling the dependencies of sources that are machine generated;
       unfortunately it only works if the user does 'make all' or 'make check',
       so the dependencies (3) are still required.
  Cleaned up (char*) casts of zlib messages. The latest version of the Intel C
    compiler complains about casting a string literal as (char*), so copied the
    treatment of z_const from the library code into pngfix.c
  Simplified error message code in pngunknown. The simplification has the
    useful side effect of avoiding a bogus warning generated by the latest
    version of the Intel C compiler (it objects to
    condition ? string-literal : string-literal).
  Make autogen.sh work with automake 1.13 as well as 1.14. Do this by always
    removing the 1.14 'compile' script but never checking for it.

Version 1.6.7beta03 [October 19, 2013]
  Added ARMv8 support (James Yu <james.yu at linaro.org>).  Added file
    arm/filter_neon_intrinsics.c; enable with -mfpu=neon.
  Revised pngvalid to generate size images with as many filters as it can
    manage, limited by the number of rows.
  Cleaned up ARM NEON compilation handling. The tests are now in pngpriv.h
    and detect the broken GCC compilers.

Version 1.6.7beta04 [October 26, 2013]
  Allow clang derived from older GCC versions to use ARM intrinsics. This
    causes all clang builds that use -mfpu=neon to use the intrinsics code,
    not the assembler code.  This has only been tested on iOS 7. It may be
    necessary to exclude some earlier clang versions but this seems unlikely.
  Changed NEON implementation selection mechanism. This allows assembler
    or intrinsics to be turned on at compile time during the build by defining
    PNG_ARM_NEON_IMPLEMENTATION to the correct value (2 or 1).  This macro
    is undefined by default and the build type is selected in pngpriv.h.

Version 1.6.7rc01 [November 2, 2013]
  No changes.

Version 1.6.7rc02 [November 7, 2013]
  Fixed #include in filter_neon_intrinsics.c and ctype macros. The ctype char
    checking macros take an unsigned char argument, not a signed char.

Version 1.6.7 [November 14, 2013]
  No changes.

Version 1.6.8beta01 [November 24, 2013]
  Moved prototype for png_handle_unknown() in pngpriv.h outside of
    the #ifdef PNG_SET_UNKNOWN_CHUNKS_SUPPORTED/#endif block.
  Added "-Wall" to CFLAGS in contrib/pngminim/*/makefile
  Conditionally compile some unused functions reported by -Wall in
    pngminim.
  Fixed 'minimal' builds. Various obviously useful minimal configurations
    don't build because of missing contrib/libtests test programs and
    overly complex dependencies in scripts/pnglibconf.dfa. This change
    adds contrib/conftest/*.dfa files that can be used in automatic build
    scripts to ensure that these configurations continue to build.
  Enabled WRITE_INVERT and WRITE_PACK in contrib/pngminim/encoder.
  Fixed pngvalid 'fail' function declaration on the Intel C Compiler.
    This reverts to the previous 'static' implementation and works round
    the 'unused static function' warning by using PNG_UNUSED().

Version 1.6.8beta02 [November 30, 2013]
  Removed or marked PNG_UNUSED some harmless "dead assignments" reported
    by clang scan-build.
  Changed tabs to 3 spaces in png_debug macros and changed '"%s"m'
    to '"%s" m' to improve portability among compilers.
  Changed png_free_default() to free() in pngtest.c

Version 1.6.8rc01 [December 12, 2013]
  Tidied up pngfix inits and fixed pngtest no-write builds.

Version 1.6.8rc02 [December 14, 2013]
  Handle zero-length PLTE chunk or NULL palette with png_error()
    instead of png_chunk_report(), which by default issues a warning
    rather than an error, leading to later reading from a NULL pointer
    (png_ptr->palette) in png_do_expand_palette(). This is CVE-2013-6954
    and VU#650142.  Libpng-1.6.1 through 1.6.7 are vulnerable.
    Libpng-1.6.0 and earlier do not have this bug.

Version 1.6.8 [December 19, 2013]
  No changes.

Version 1.6.9beta01 [December 26, 2013]
  Bookkeeping: Moved functions around (no changes). Moved transform
    function definitions before the place where they are called so that
    they can be made static. Move the intrapixel functions and the
    grayscale palette builder out of the png?tran.c files. The latter
    isn't a transform function and is no longer used internally, and the
    former MNG specific functions are better placed in pngread/pngwrite.c
  Made transform implementation functions static. This makes the internal
    functions called by png_do_{read|write}_transformations static. On an
    x86-64 DLL build (Gentoo Linux) this reduces the size of the text
    segment of the DLL by 1208 bytes, about 0.6%. It also simplifies
    maintenance by removing the declarations from pngpriv.h and allowing
    easier changes to the internal interfaces.
  Rebuilt configure scripts with automake-1.14.1 and autoconf-2.69
    in the tar distributions.

Version 1.6.9beta02 [January 1, 2014]
  Added checks for libpng 1.5 to pngvalid.c.  This supports the use of
    this version of pngvalid in libpng 1.5
  Merged with pngvalid.c from libpng-1.7 changes to create a single
    pngvalid.c
  Removed #error macro from contrib/tools/pngfix.c (Thomas Klausner).
  Merged pngrio.c, pngtrans.c, pngwio.c, and pngerror.c with libpng-1.7.0
  Merged libpng-1.7.0 changes to make no-interlace configurations work
    with test programs.
  Revised pngvalid.c to support libpng 1.5, which does not support the
    PNG_MAXIMUM_INFLATE_WINDOW option, so #define it out when appropriate in
    pngvalid.c
  Allow unversioned links created on install to be disabled in configure.
    In configure builds 'make install' changes/adds links like png.h
    and libpng.a to point to the newly installed, versioned, files (e.g.
    libpng17/png.h and libpng17.a). Three new configure options and some
    rearrangement of Makefile.am allow creation of these links to be disabled.

Version 1.6.9beta03 [January 10, 2014]
  Removed potentially misleading warning from png_check_IHDR().

Version 1.6.9beta04 [January 20, 2014]
  Updated scripts/makefile.* to use CPPFLAGS (Cosmin).
  Added clang attribute support (Cosmin).

Version 1.6.9rc01 [January 28, 2014]
  No changes.

Version 1.6.9rc02 [January 30, 2014]
  Quiet an uninitialized memory warning from VC2013 in png_get_png().

Version 1.6.9 [February 6, 2014]

Version 1.6.10beta01 [February 9, 2014]
  Backported changes from libpng-1.7.0beta30 and beta31:
  Fixed a large number of instances where PNGCBAPI was omitted from
    function definitions.
  Added pngimage test program for png_read_png() and png_write_png()
    with two new test scripts.
  Removed dependence on !PNG_READ_EXPAND_SUPPORTED for calling
    png_set_packing() in png_read_png().
  Fixed combination of ~alpha with shift. On read invert alpha, processing
    occurred after shift processing, which causes the final values to be
    outside the range that should be produced by the shift. Reversing the
    order on read makes the two transforms work together correctly and mirrors
    the order used on write.
  Do not read invalid sBIT chunks. Previously libpng only checked sBIT
    values on write, so a malicious PNG writer could therefore cause
    the read code to return an invalid sBIT chunk, which might lead to
    application errors or crashes.  Such chunks are now skipped (with
    chunk_benign_error).
  Make png_read_png() and png_write_png() prototypes in png.h depend
    upon PNG_READ_SUPPORTED and PNG_WRITE_SUPPORTED.
  Support builds with unsupported PNG_TRANSFORM_* values.  All of the
    PNG_TRANSFORM_* values are always defined in png.h and, because they
    are used for both read and write in some cases, it is not reliable
    to #if out ones that are totally unsupported. This change adds error
    detection in png_read_image() and png_write_image() to do a
    png_app_error() if the app requests something that cannot be done
    and it adds corresponding code to pngimage.c to handle such options
    by not attempting to test them.

Version 1.6.10beta02 [February 23, 2014]
  Moved redefines of png_error(), png_warning(), png_chunk_error(),
    and png_chunk_warning() from pngpriv.h to png.h to make them visible
    to libpng-calling applications.
  Moved OS dependent code from arm/arm_init.c, to allow the included
    implementation of the ARM NEON discovery function to be set at
    build-time and provide sample implementations from the current code in the
    contrib/arm-neon subdirectory. The __linux__ code has also been changed to
    compile and link on Android by using /proc/cpuinfo, and the old linux code
    is in contrib/arm-neon/linux-auxv.c.  The new code avoids POSIX and Linux
    dependencies apart from opening /proc/cpuinfo and is C90 compliant.
  Check for info_ptr == NULL early in png_read_end() so we don't need to
    run all the png_handle_*() and depend on them to return if info_ptr == NULL.
    This improves the performance of png_read_end(png_ptr, NULL) and makes
    it more robust against future programming errors.
  Check for __has_extension before using it in pngconf.h, to
    support older Clang versions (Jeremy Sequoia).
  Treat CRC error handling with png_set_crc_action(), instead of with
    png_set_benign_errors(), which has been the case since libpng-1.6.0beta18.
  Use a user warning handler in contrib/gregbook/readpng2.c instead of default,
    so warnings will be put on stderr even if libpng has CONSOLE_IO disabled.
  Added png_ptr->process_mode = PNG_READ_IDAT_MODE in png_push_read_chunk
    after recognizing the IDAT chunk, which avoids an infinite loop while
    reading a datastream whose first IDAT chunk is of zero-length.
    This fixes CERT VU#684412 and CVE-2014-0333.
  Don't recognize known sRGB profiles as sRGB if they have been hacked,
    but don't reject them and don't issue a copyright violation warning.

Version 1.6.10beta03 [February 25, 2014]
  Moved some documentation from png.h to libpng.3 and libpng-manual.txt
  Minor editing of contrib/arm-neon/README and contrib/examples/*.c

Version 1.6.10rc01 [February 27, 2014]
  Fixed typos in the manual and in scripts/pnglibconf.dfa (CFLAGS -> CPPFLAGS
    and PNG_USR_CONFIG -> PNG_USER_CONFIG).

Version 1.6.10rc02 [February 28, 2014]
  Removed unreachable return statement after png_chunk_error()
    in pngrutil.c

Version 1.6.10rc03 [March 4, 2014]
  Un-deprecated png_data_freer().

Version 1.6.10 [March 6, 2014]
  No changes.

Version 1.6.11beta01 [March 17, 2014]
  Use "if (value != 0)" instead of "if (value)" consistently.
  Changed ZlibSrcDir from 1.2.5 to 1.2.8 in projects/vstudio.
  Moved configuration information from the manual to the INSTALL file.

Version 1.6.11beta02 [April 6, 2014]
  Removed #if/#else/#endif from inside two pow() calls in pngvalid.c because
    they were handled improperly by Portland Group's PGI-14.1 - PGI-14.3
    when using its "__builtin_pow()" function.
  Silence 'unused parameter' build warnings (Cosmin Truta).
  $(CP) is now used alongside $(RM_F).  Also, use 'copy' instead of 'cp'
    where applicable, and applied other minor makefile changes (Cosmin).
  Don't warn about invalid dimensions exceeding user limits (Cosmin).
  Allow an easy replacement of the default pre-built configuration
    header with a custom header, via the make PNGLIBCONF_H_PREBUILT
    macro (Cosmin).

Version 1.6.11beta03 [April 6, 2014]
  Fixed a typo in pngrutil.c, introduced in libpng-1.5.6, that interferes
    with "blocky" expansion of sub-8-bit interlaced PNG files (Eric Huss).
  Optionally use  __builtin_bswap16() in png_do_swap().

Version 1.6.11beta04 [April 19, 2014]
  Made progressive reading of interlaced images consistent with the
    behavior of the sequential reader and consistent with the manual, by
    moving some code out of the PNG_READ_INTERLACING_SUPPORTED blocks. The
    row_callback now receives the proper pass number and unexpanded rows, when
    png_combine_row() isn't built or used, and png_set_interlace_handling()
    is not called.
  Allow PNG_sRGB_PROFILE_CHECKING = (-1) to mean no sRGB profile checking.

Version 1.6.11beta05 [April 26, 2014]
  Do not reject ICC V2 profiles that lack padding (Kai-Uwe Behrmann).
  Relocated closing bracket of the sRGB profile test loop to avoid getting
    "Not recognizing known sRGB profile that has been edited" warning for
    ICC V2 profiles that lack the MD5 signature in the profile header.

Version 1.6.11beta06 [May 19, 2014]
  Added PNG_SKIP_sRGB_CHECK_PROFILE choice for png_set_option().

Version 1.6.11rc01 [May 27, 2014]
  No changes.

Version 1.6.11rc02 [June 3, 2014]
  Test ZLIB_VERNUM instead of PNG_ZLIB_VERNUM in contrib/tools/pngfix.c

Version 1.6.11 [June 5, 2014]
  No changes.

Version 1.6.12rc01 [June 6, 2014]
  Relocated new code from 1.6.11beta06 in png.c to a point after the
    declarations (Max Stepin).

Version 1.6.12rc02 [June 7, 2014]
  Changed file permissions of contrib/tools/intgamma.sh,
    test-driver, and compile from 0644 to 0755 (Cosmin).

Version 1.6.12rc03 [June 8, 2014]
  Ensure "__has_attribute()" macro exists before trying to use it with
    old clang compilers (MacPorts Ticket #43939).

Version 1.6.12 [June 12, 2014]
  No changes.

Version 1.6.13beta01 [July 4, 2014]
  Quieted -Wsign-compare and -Wclobber compiler warnings in
    contrib/pngminus/*.c
  Added "(void) png_ptr;" where needed in contrib/gregbook to quiet
    compiler complaints about unused pointers.
  Split a long output string in contrib/gregbook/rpng2-x.c.
  Added "PNG_SET_OPTION" requirement for sRGB chunk support to pnglibconf.dfa,
    Needed for write-only support (John Bowler).
  Changed "if defined(__ARM_NEON__)" to
    "if (defined(__ARM_NEON__) || defined(__ARM_NEON))" (James Wu).
  Fixed clang no-warning builds: png_digit was defined but never used.
    
Version 1.6.13beta02 [July 21, 2014]
  Fixed an incorrect separator ("/" should be "\") in scripts/makefile.vcwin32
    (bug report from Wolfgang S. Kechel).  Bug was introduced in libpng-1.6.11.
    Also fixed makefile.bc32, makefile.bor, makefile.msc, makefile.intel, and
    makefile.tc3 similarly.

Version 1.6.13beta03 [August 3, 2014]
  Removed scripts/makefile.elf. It has not worked since libpng-1.5.0beta14
    due to elimination of the PNG_FUNCTION_EXPORT and PNG_DATA_EXPORT
    definitions from pngconf.h.
  Ensure that CMakeLists.txt makes the target "lib" directory before making
    symbolic link into it (SourceForge bug report #226 by Rolf Timmermans).

Version 1.6.13beta04 [August 8, 2014]
  Added opinion that the ECCN (Export Control Classification Number) for
    libpng is EAR99 to the README file.
  Eliminated use of "$<" in makefile explicit rules, when copying
    $PNGLIBCONF_H_PREBUILT.  This does not work on some versions of make;
    bug introduced in libpng version 1.6.11.

Version 1.6.13rc01 [August 14, 2014]
  Made "ccopts" agree with "CFLAGS" in scripts/makefile.hp* and makefile.*sunu

Version 1.6.13 [August 21, 2014]
  No changes.

Version 1.6.14beta01 [September 14, 2014]
  Guard usage of png_ptr->options with #ifdef PNG_SET_OPTION_SUPPORTED.
  Do not build contrib/tools/pngfix.c when PNG_SETJMP_NOT_SUPPORTED,
    to allow "make" to complete without setjmp support (bug report by
    Claudio Fontana)
  Add "#include <setjmp.h>" to contrib/tools/pngfix.c (John Bowler)

Version 1.6.14beta02 [September 18, 2014]
  Use nanosleep() instead of usleep() in contrib/gregbook/rpng2-x.c
    because usleep() is deprecated.
  Define usleep() in contrib/gregbook/rpng2-x.c if not already defined
    in unistd.h and nanosleep() is not available; fixes error introduced
    in libpng-1.6.13.
  Disable floating point exception handling in pngvalid.c when
    PNG_FLOATING_ARITHMETIC is not supported (bug report by "zootus
    at users.sourceforge.net").

Version 1.6.14beta03 [September 19, 2014]
  Define FE_DIVBYZERO, FE_INVALID, and FE_OVERFLOW in pngvalid.c if not
    already defined.  Revert floating point exception handling in pngvalid.c
    to version 1.6.14beta01 behavior.

Version 1.6.14beta04 [September 27, 2014]
  Fixed incorrect handling of the iTXt compression flag in pngrutil.c
    (bug report by Shunsaku Hirata).  Bug was introduced in libpng-1.6.0.

Version 1.6.14beta05 [October 1, 2014]
  Added "option READ_iCCP enables READ_COMPRESSED_TEXT" to pnglibconf.dfa

Version 1.6.14beta06 [October 5, 2014]
  Removed unused "text_len" parameter from private function png_write_zTXt().
  Conditionally compile some code in png_deflate_claim(), when
    PNG_WARNINGS_SUPPORTED and PNG_ERROR_TEXT_SUPPORTED are disabled.
  Replaced repeated code in pngpread.c with PNG_PUSH_SAVE_BUFFER_IF_FULL.
  Added "chunk iTXt enables TEXT" and "chunk zTXt enables TEXT"
    to pnglibconf.dfa.
  Removed "option READ_COMPRESSED_TEXT enables READ_TEXT" from pnglibconf.dfa,
    to make it possible to configure a libpng that supports iCCP but not TEXT.

Version 1.6.14beta07 [October 7, 2014]
  Removed "option WRITE_COMPRESSED_TEXT enables WRITE_TEXT" from pnglibconf.dfa
  Only mark text chunks as written after successfully writing them.

Version 1.6.14rc01 [October 15, 2014]
  Fixed some typos in comments.

Version 1.6.14rc02 [October 17, 2014]
  Changed png_convert_to_rfc_1123() to png_convert_to_rfc_1123_buffer()
    in the manual, to reflect the change made in libpng-1.6.0.
  Updated README file to explain that direct access to the png_struct
    and info_struct members has not been permitted since libpng-1.5.0.

Version 1.6.14 [October 23, 2014]
  No changes.

Version 1.6.15beta01 [October 29, 2014]
  Changed "if (!x)" to "if (x == 0)" and "if (x)" to "if (x != 0)"
  Simplified png_free_data().
  Added missing "ptr = NULL" after some instances of png_free().

Version 1.6.15beta02 [November 1, 2014]
  Changed remaining "if (!x)" to "if (x == 0)" and "if (x)" to "if (x != 0)"

Version 1.6.15beta03 [November 3, 2014]
  Added PNG_USE_ARM_NEON configuration flag (Marcin Juszkiewicz).

Version 1.6.15beta04 [November 4, 2014]
  Removed new PNG_USE_ARM_NEON configuration flag and made a one-line
    revision to configure.ac to support ARM on aarch64 instead (John Bowler).

Version 1.6.15beta05 [November 5, 2014]
  Use png_get_libpng_ver(NULL) instead of PNG_LIBPNG_VER_STRING in
    example.c, pngtest.c, and applications in the contrib directory.
  Fixed an out-of-range read in png_user_version_check() (Bug report from
    Qixue Xiao, CVE-2015-8540).
  Simplified and future-proofed png_user_version_check().
  Fixed GCC unsigned int->float warnings. Various versions of GCC
    seem to generate warnings when an unsigned value is implicitly
    converted to double. This is probably a GCC bug but this change
    avoids the issue by explicitly converting to (int) where safe.
  Free all allocated memory in pngimage. The file buffer cache was left
    allocated at the end of the program, harmless but it causes memory
    leak reports from clang.
  Fixed array size calculations to avoid warnings. At various points
    in the code the number of elements in an array is calculated using
    sizeof.  This generates a compile time constant of type (size_t) which
    is then typically assigned to an (unsigned int) or (int). Some versions
    of GCC on 64-bit systems warn about the apparent narrowing, even though
    the same compiler does apparently generate the correct, in-range,
    numeric constant.  This adds appropriate, safe, casts to make the
    warnings go away.

Version 1.6.15beta06 [November 6, 2014]
  Reverted use png_get_libpng_ver(NULL) instead of PNG_LIBPNG_VER_STRING
    in the manual, example.c, pngtest.c, and applications in the contrib
    directory.  It was incorrect advice.

Version 1.6.15beta07 [November 7, 2014]
  Removed #ifdef PNG_16BIT_SUPPORTED/#endif around png_product2(); it is
    needed by png_reciprocal2().
  Added #ifdef PNG_16BIT_SUPPORTED/#endif around png_log16bit() and
    png_do_swap().
  Changed all "#endif /* PNG_FEATURE_SUPPORTED */" to "#endif /* FEATURE */"

Version 1.6.15beta08 [November 8, 2014]
  More housecleaning in *.h

Version 1.6.15rc01 [November 13, 2014]

Version 1.6.15rc02 [November 14, 2014]
  The macros passed in the command line to Borland make were ignored if
    similarly-named macros were already defined in makefiles. This behavior
    is different from POSIX make and other make programs.  Surround the
    macro definitions with ifndef guards (Cosmin).

Version 1.6.15rc03 [November 16, 2014]
  Added "-D_CRT_SECURE_NO_WARNINGS" to CFLAGS in scripts/makefile.vcwin32.
  Removed the obsolete $ARCH variable from scripts/makefile.darwin.

Version 1.6.15 [November 20, 2014]
  No changes.

Version 1.6.16beta01 [December 14, 2014]
  Added ".align 2" to arm/filter_neon.S to support old GAS assemblers that
    don't do alignment correctly.
  Revised Makefile.am and scripts/symbols.dfn to work with MinGW/MSYS
    (Bob Friesenhahn).

Version 1.6.16beta02 [December 15, 2014]
  Revised Makefile.am and scripts/*.dfn again to work with MinGW/MSYS;
    renamed scripts/*.dfn to scripts/*.c (John Bowler).

Version 1.6.16beta03 [December 21, 2014]
  Quiet a "comparison always true" warning in pngstest.c (John Bowler).

Version 1.6.16rc01 [December 21, 2014]
  Restored a test on width that was removed from png.c at libpng-1.6.9
    (Bug report by Alex Eubanks, CVE-2015-0973).

Version 1.6.16rc02 [December 21, 2014]
  Undid the update to pngrutil.c in 1.6.16rc01.

Version 1.6.16rc03 [December 21, 2014]
  Fixed an overflow in png_combine_row() with very wide interlaced images
    (Bug report and fix by John Bowler, CVE-2014-9495).

Version 1.6.16 [December 22, 2014]
  No changes.

Version 1.6.17beta01 [January 29, 2015]
  Removed duplicate PNG_SAFE_LIMITS_SUPPORTED handling from pngconf.h
  Corrected the width limit calculation in png_check_IHDR().
  Removed user limits from pngfix. Also pass NULL pointers to
    png_read_row to skip the unnecessary row de-interlace stuff.
  Added testing of png_set_packing() to pngvalid.c
  Regenerated configure scripts in the *.tar distributions with libtool-2.4.4
  Implement previously untested cases of libpng transforms in pngvalid.c
  Fixed byte order in png_do_read_filler() with 16-bit input. Previously
    the high and low bytes of the filler, from png_set_filler() or from
    png_set_add_alpha(), were read in the wrong order.
  Made the check for out-of-range values in png_set_tRNS() detect
    values that are exactly 2^bit_depth, and work on 16-bit platforms.
  Merged some parts of libpng-1.6.17beta01 and libpng-1.7.0beta47.
  Added #ifndef __COVERITY__ where needed in png.c, pngrutil.c and
    pngset.c to avoid warnings about dead code.
  Added "& 0xff" to many instances of expressions that are typecast
    to (png_byte), to avoid Coverity warnings.

Version 1.6.17beta02 [February 7, 2015]
  Work around one more Coverity-scan dead-code warning.
  Do not build png_product2() when it is unused.

Version 1.6.17beta03 [February 17, 2015]
  Display user limits in the output from pngtest.
  Eliminated the PNG_SAFE_LIMITS macro and restored the 1-million-column
    and 1-million-row default limits in pnglibconf.dfa, that can be reset
    by the user at build time or run time.  This provides a more robust
    defense against DOS and as-yet undiscovered overflows.

Version 1.6.17beta04 [February 21, 2015]
  Added PNG_WRITE_CUSTOMIZE_COMPRESSION_SUPPORTED macro, on by default.
  Allow user to call png_get_IHDR() with NULL arguments (Reuben Hawkins).
  Rebuilt configure scripts with automake-1.15 and libtool-2.4.6

Version 1.6.17beta05 [February 25, 2015]
  Restored compiling of png_reciprocal2 with PNG_NO_16BIT.

Version 1.6.17beta06 [February 27, 2015]
  Moved png_set_filter() prototype into a PNG_WRITE_SUPPORTED block
    of png.h.
  Avoid runtime checks when converting integer to png_byte with
    Visual Studio (Sergey Kosarevsky)

Version 1.6.17rc01 [March 4, 2015]
  No changes.

Version 1.6.17rc02 [March 9, 2015]
  Removed some comments that the configure script did not handle
    properly from scripts/pnglibconf.dfa and pnglibconf.h.prebuilt.
  Free the unknown_chunks structure even when it contains no data.

Version 1.6.17rc03 [March 12, 2015]
  Updated CMakeLists.txt to add OSX framework, change YES/NO to ON/OFF
    for consistency, and remove some useless tests (Alexey Petruchik).

Version 1.6.17rc04 [March 16, 2015]
  Remove pnglibconf.h, pnglibconf.c, and pnglibconf.out instead of
    pnglibconf.* in "make clean" (Cosmin).
  Fix bug in calculation of maxbits, in png_write_sBIT, introduced
    in libpng-1.6.17beta01 (John Bowler).

Version 1.6.17rc05 [March 21, 2015]
  Define PNG_FILTER_* and PNG_FILTER_VALUE_* in png.h even when WRITE
    is not supported (John Bowler).  This fixes an error introduced in
    libpng-1.6.17beta06.
  Reverted "& 0xff" additions of version 1.6.17beta01. Libpng passes
    the Coverity scan without them.

Version 1.6.17rc06 [March 23, 2015]
  Remove pnglibconf.dfn and pnglibconf.pre with "make clean".
  Reformatted some "&0xff" instances to "& 0xff".
  Fixed simplified 8-bit-linear to sRGB alpha. The calculated alpha
    value was wrong.  It's not clear if this affected the final stored
    value; in the obvious code path the upper and lower 8-bits of the
    alpha value were identical and the alpha was truncated to 8-bits
    rather than dividing by 257 (John Bowler).

Version 1.6.17 [March 26, 2015]
  No changes.

Version 1.6.18beta01 [April 1, 2015]
  Removed PNG_SET_CHUNK_[CACHE|MALLOC]_LIMIT_SUPPORTED macros.  They
    have been combined with PNG_SET_USER_LIMITS_SUPPORTED (resolves
    bug report by Andrew Church).
  Fixed rgb_to_gray checks and added tRNS checks to pngvalid.c.  This
    fixes some arithmetic errors that caused some tests to fail on
    some 32-bit platforms (Bug reports by Peter Breitenlohner [i686]
    and Petr Gajdos [i586]).

Version 1.6.18beta02 [April 26, 2015]
  Suppressed some warnings from the Borland C++ 5.5.1/5.82 compiler
    (Bug report by Viktor Szakats).

Version 1.6.18beta03 [May 6, 2015]
  Replaced "unexpected" with an integer (0xabadca11) in pngset.c
    where a long was expected, to avoid a compiler warning when PNG_DEBUG > 1.
  Added contrib/examples/simpleover.c, to demonstrate how to handle
    alpha compositing of multiple images, using the "simplified API"
    and an example PNG generation tool, contrib/examples/genpng.c
    (John Bowler).

Version 1.6.18beta04 [May 20, 2015]
  PNG_RELEASE_BUILD replaces tests where the code depended on the build base
    type and can be defined on the command line, allowing testing in beta
    builds (John Bowler).
  Avoid Coverity issue 80858 (REVERSE NULL) in pngtest.c PNG_DEBUG builds.
  Avoid a harmless potential integer overflow in png_XYZ_from_xy() (Bug
    report from Christopher Ferris).

Version 1.6.18beta05 [May 31, 2015]
  Backport filter selection code from libpng-1.7.0beta51, to combine
    sub_row, up_row, avg_row, and paeth_row into try_row and tst_row.
  Changed png_voidcast(), etc., to voidcast(), etc., in contrib/tools/pngfix.c
    to avoid confusion with the libpng private macros.
  Fixed old cut&paste bug in the weighted filter selection code in
    pngwutil.c, introduced in libpng-0.95, March 1997.

Version 1.6.18beta06 [June 1, 2015]
  Removed WRITE_WEIGHTED_FILTERED code, to save a few kbytes of the
    compiled library size. It never worked properly and as far as we can
    tell, no one uses it. The png_set_filter_heuristics() and
    png_set_filter_heuristics_fixed() APIs are retained but deprecated
    and do nothing.

Version 1.6.18beta07 [June 6, 2015]
  Removed non-working progressive reader 'skip' function. This
    function has apparently never been used. It was implemented
    to support back-door modification of png_struct in libpng-1.4.x
    but (because it does nothing and cannot do anything) was apparently
    never tested (John Bowler).
  Fixed cexcept.h in which GCC 5 now reports that one of the auto
    variables in the Try macro needs to be volatile to prevent value
    being lost over the setjmp (John Bowler).
  Fixed NO_WRITE_FILTER and -Wconversion build breaks (John Bowler).
  Fix g++ build breaks (John Bowler).
  Quieted some Coverity issues in pngfix.c, png-fix-itxt.c, pngvalid.c,
    pngstest.c, and pngimage.c. Most seem harmless, but png-fix-itxt
    would only work with iTXt chunks with length 255 or less.
  Added #ifdef's to contrib/examples programs so people don't try
    to compile them without the minimum required support enabled
    (suggested by Flavio Medeiros).

Version 1.6.18beta08 [June 30, 2015]
  Eliminated the final two Coverity defects (insecure temporary file
    handling in contrib/libtests/pngstest.c; possible overflow of
    unsigned char in contrib/tools/png-fix-itxt.c). To use the "secure"
    file handling, define PNG_USE_MKSTEMP, otherwise "tmpfile()" will
    be used.
  Removed some unused WEIGHTED_FILTER macros from png.h and pngstruct.h

Version 1.6.18beta09 [July 5, 2015]
  Removed some useless typecasts from contrib/tools/png-fix-itxt.c
  Fixed a new signed-unsigned comparison in pngrtran.c (Max Stepin).
  Replaced arbitrary use of 'extern' with #define PNG_LINKAGE_*.  To
    preserve API compatibility, the new defines all default to "extern"
    (requested by Jan Nijtmans).

Version 1.6.18rc01 [July 9, 2015]
  Belatedly added Mans Rullgard and James Yu to the list of Contributing
    Authors.

Version 1.6.18rc02 [July 12, 2015]
  Restored unused FILTER_HEURISTIC macros removed at libpng-1.6.18beta08
    to png.h to avoid compatibility warnings.

Version 1.6.18rc03 [July 15, 2015]
  Minor changes to the man page

Version 1.6.18 [July 23, 2015]
  No changes.

Version 1.6.19beta01 [July 30, 2015]
  Updated obsolete information about the simplified API macros in the
    manual pages (Bug report by Arc Riley).
  Avoid potentially dereferencing NULL info_ptr in png_info_init_3().
  Rearranged png.h to put the major sections in the same order as
    in libpng17.
  Eliminated unused PNG_COST_SHIFT, PNG_WEIGHT_SHIFT, PNG_COST_FACTOR, and
    PNG_WEIGHT_FACTOR macros.
  Suppressed some warnings from the Borland C++ 5.5.1/5.82 compiler
    (Bug report by Viktor Szakats).  Several warnings remain and are
    unavoidable, where we test for overflow.
  Fixed potential leak of png_pixels in contrib/pngminus/pnm2png.c
  Fixed uninitialized variable in contrib/gregbook/rpng2-x.c

Version 1.6.19beta02 [August 19, 2015]
  Moved config.h.in~ from the "libpng_autotools_files" list to the
    "libpng_autotools_extra" list in autogen.sh because it was causing a
    false positive for missing files (bug report by Robert C. Seacord).
  Removed unreachable "break" statements in png.c, pngread.c, and pngrtran.c
    to suppress clang warnings (Bug report by Viktor Szakats).
  Fixed some bad links in the man page.
  Changed "n bit" to "n-bit" in comments.
  Added signed/unsigned 16-bit safety net. This removes the dubious
    0x8000 flag definitions on 16-bit systems. They aren't supported
    yet the defs *probably* work, however it seems much safer to do this
    and be advised if anyone, contrary to advice, is building libpng 1.6
    on a 16-bit system. It also adds back various switch default clauses
    for GCC; GCC errors out if they are not present (with an appropriately
    high level of warnings).
  Safely convert num_bytes to a png_byte in png_set_sig_bytes() (Robert
    Seacord).
  Fixed the recently reported 1's complement security issue by replacing
    the value that is illegal in the PNG spec, in both signed and unsigned
    values, with 0. Illegal unsigned values (anything greater than or equal
    to  0x80000000) can still pass through, but since these are not illegal
    in ANSI-C (unlike 0x80000000 in the signed case) the checking that
    occurs later can catch them (John Bowler).

Version 1.6.19beta03 [September 26, 2015]
  Fixed png_save_int_32 when int is not 2's complement (John Bowler).
  Updated libpng16 with all the recent test changes from libpng17,
    including changes to pngvalid.c to ensure that the original,
    distributed, version of contrib/visupng/cexcept.h can be used
    (John Bowler).
  pngvalid contains the correction to the use of SAVE/STORE_
    UNKNOWN_CHUNKS; a bug revealed by changes in libpng 1.7. More
    tests contain the --strict option to detect warnings and the
    pngvalid-standard test has been corrected so that it does not
    turn on progressive-read. There is a separate test which does
    that. (John Bowler)
  Also made some signed/unsigned fixes.
  Make pngstest error limits version specific. Splitting the machine
    generated error structs out to a file allows the values to be updated
    without changing pngstest.c itself. Since libpng 1.6 and 1.7 have
    slightly different error limits this simplifies maintenance. The
    makepngs.sh script has also been updated to more accurately reflect
    current problems in libpng 1.7 (John Bowler).
  Incorporated new test PNG files into make check.  tests/pngstest-*
    are changed so that the new test files are divided into 8 groups by
    gamma and alpha channel.  These tests have considerably better code
    and pixel-value coverage than contrib/pngsuite; however,coverage is
    still incomplete (John Bowler).
  Removed the '--strict' in 1.6 because of the double-gamma-correction
    warning, updated pngstest-errors.h for the errors detected with the
    new contrib/testspngs PNG test files (John Bowler).

Version 1.6.19beta04 [October 15, 2015]
  Worked around rgb-to-gray issues in libpng 1.6.  The previous
    attempts to ignore the errors in the code aren't quite enough to
    deal with the 'channel selection' encoding added to libpng 1.7; abort.
    pngvalid.c is changed to drop this encoding in prior versions.
  Fixed 'pow' macros in pngvalid.c. It is legal for 'pow' to be a
    macro, therefore the argument list cannot contain preprocessing
    directives.  Make sure pow is a function where this happens. This is
    a minimal safe fix, the issue only arises in non-performance-critical
    code (bug report by Curtis Leach, fix by John Bowler).
  Added sPLT support to pngtest.c

Version 1.6.19rc01 [October 23, 2015]
  No changes.

Version 1.6.19rc02 [October 31, 2015]
  Prevent setting or writing over-length PLTE chunk (Cosmin Truta).
  Silently truncate over-length PLTE chunk while reading.
  Libpng incorrectly calculated the output rowbytes when the application
    decreased either the number of channels or the bit depth (or both) in
    a user transform.  This was safe; libpng overallocated buffer space
   (potentially by quite a lot; up to 4 times the amount required) but,
   from 1.5.4 on, resulted in a png_error (John Bowler).

Version 1.6.19rc03 [November 3, 2015]
  Fixed some inconsequential cut-and-paste typos in png_set_cHRM_XYZ_fixed().
  Clarified COPYRIGHT information to state explicitly that versions
    are derived from previous versions.
  Removed much of the long list of previous versions from png.h and
    libpng.3.

Version 1.6.19rc04 [November 5, 2015]
  Fixed new bug with CRC error after reading an over-length palette
    (bug report by Cosmin Truta) (CVE-2015-8126).

Version 1.6.19 [November 12, 2015]
  Cleaned up coding style in png_handle_PLTE().

Version 1.6.20beta01 [November 20, 2015]
  Avoid potential pointer overflow/underflow in png_handle_sPLT() and
    png_handle_pCAL() (Bug report by John Regehr).

Version 1.6.20beta02 [November 23, 2015]
  Fixed incorrect implementation of png_set_PLTE() that uses png_ptr
    not info_ptr, that left png_set_PLTE() open to the CVE-2015-8126
    vulnerability.  Fixes CVE-2015-8472.

Version 1.6.20beta03 [November 24, 2015]
  Backported tests from libpng-1.7.0beta69.

Version 1.6.20rc01 [November 26, 2015]
  Fixed an error in handling of bad zlib CMINFO field in pngfix, found by
    American Fuzzy Lop, reported by Brian Carpenter.  inflate() doesn't
    immediately fault a bad CMINFO field; instead a 'too far back' error
    happens later (at least some times).  pngfix failed to limit CMINFO to
    the allowed values but then assumed that window_bits was in range,
    triggering an assert. The bug is mostly harmless; the PNG file cannot
    be fixed.

Version 1.6.20rc02 [November 29, 2015]
  In libpng 1.6 zlib initialization was changed to use the window size
    in the zlib stream, not a fixed value. This causes some invalid images,
    where CINFO is too large, to display 'correctly' if the rest of the
    data is valid.  This provides a workaround for zlib versions where the
    error arises (ones that support the API change to use the window size
    in the stream).

Version 1.6.20 [December 3, 2015]
  No changes.

Version 1.6.21beta01 [December 11, 2015]
  Fixed syntax "$(command)" in tests/pngstest that some shells other than
    bash could not parse (Bug report by Nelson Beebe). Use `command` instead.

Version 1.6.21beta02 [December 14, 2015]
  Moved png_check_keyword() from pngwutil.c to pngset.c
  Removed LE/BE dependencies in pngvalid, to 'fix' the current problem
    in the BigEndian tests by not testing it, making the BE code the same 
    as the LE version.
  Fixes to pngvalid for various reduced build configurations (eliminate unused
    statics) and a fix for the case in rgb_to_gray when the digitize option
    reduces graylo to 0, producing a large error.

Version 1.6.21beta03 [December 18, 2015]
  Widened the 'limit' check on the internally calculated error limits in
    the 'DIGITIZE' case (the code used prior to 1.7 for rgb_to_gray error
    checks) and changed the check to only operate in non-release builds
    (base build type not RC or RELEASE.)
  Fixed undefined behavior in pngvalid.c, undefined because
    (png_byte) << shift is undefined if it changes the signed bit
    (because png_byte is promoted to int). The libpng exported functions
    png_get_uint_32 and png_get_uint_16 handle this. (Bug reported by
    David Drysdale as a result of reports from UBSAN in clang 3.8).
  This changes pngvalid to use BE random numbers; this used to produce
    errors but these should not be fixed as a result of the previous changes.

Version 1.6.21rc01 [January 4, 2016]
  In projects/vstudio, combined readme.txt and WARNING into README.txt

Version 1.6.21rc02 [January 7, 2016]
  Relocated assert() in contrib/tools/pngfix.c, bug found by American
    Fuzzy Lop, reported by Brian Carpenter.
  Marked 'limit' UNUSED in transform_range_check().  This only affects
    release builds.

Version 1.6.21 [January 15, 2016]
  Worked around a false-positive Coverity issue in pngvalid.c.

Version 1.6.22beta01 [January 23, 2016]
  Changed PNG_USE_MKSTEMP to __COVERITY__ to select alternate
    "tmpfile()" implementation in contrib/libtests/pngstest.c
  Fixed NO_STDIO build of pngunknown.c to skip calling png_init_io()
    if there is no stdio.h support.
  Added a png_image_write_to_memory() API and a number of assist macros
    to allow an application that uses the simplified API write to bypass
    stdio and write directly to memory.
  Added some warnings (png.h) and some check code to detect *possible*
    overflow in the ROW_STRIDE and simplified image SIZE macros.  This
    disallows image width/height/format that *might* overflow.  This is
    a quiet API change that limits in-memory image size (uncompressed) to
    less than 4GByte and image row size (stride) to less than 2GByte.
  Revised workaround for false-positive Coverity issue in pngvalid.c.

Version 1.6.22beta02 [February 8, 2016]
  Only use exit(77) in configure builds.
  Corrected error in PNG_IMAGE_PNG_SIZE_MAX. This new macro underreported
    the palette size because it failed to take into account that the memory
    palette has to be expanded to full RGB when it is written to PNG.
  Updated CMakeLists.txt, added supporting scripts/gen*.cmake.in
    and test.cmake.in (Roger Leigh).
  Relaxed limit checks on gamma values in pngrtran.c. As suggested in
    the comments gamma values outside the range currently permitted
    by png_set_alpha_mode are useful for HDR data encoding.  These values
    are already permitted by png_set_gamma so it is reasonable caution to
    extend the png_set_alpha_mode range as HDR imaging systems are starting
    to emerge.

Version 1.6.22beta03 [March 9, 2016]
  Added a common-law trademark notice and export control information
    to the LICENSE file, png.h, and the man page.
  Restored "& 0xff" in png_save_uint_16() and png_save_uint_32() that
    were accidentally removed from libpng-1.6.17. 
  Changed PNG_INFO_cHNK and PNG_FREE_cHNK from 0xnnnn to 0xnnnnU in png.h
    (Robert C. Seacord).
  Removed dubious "#if INT_MAX" test from png.h that was added to
    libpng-1.6.19beta02 (John Bowler).
  Add ${INCLUDES} in scripts/genout.cmake.in (Bug report by Nixon Kwok).
  Updated LICENSE to say files in the contrib directory are not
    necessarily under the libpng license, and that some makefiles have
    other copyright owners.
  Added INTEL-SSE2 support (Mike Klein and Matt Sarett, Google, Inc.).
  Made contrib/libtests/timepng more robust.  The code no longer gives
    up/fails on invalid PNG data, it just skips it (with error messages).
    The code no longer fails on PNG files with data beyond IEND.  Options
    exist to use png_read_png (reading the whole image, not by row) and, in
    that case, to apply any of the supported transforms.  This makes for
    more realistic testing; the decoded data actually gets used in a
    meaningful fashion (John Bowler).
  Fixed some misleading indentation (Krishnaraj Bhat).

Version 1.6.22beta04 [April 5, 2016]
  Force GCC compilation to C89 if needed (Dagobert Michelsen).
  SSE filter speed improvements for bpp=3:
    memcpy-free implementations of load3() / store3().
    call load3() only when needed at the end of a scanline.

Version 1.6.22beta05 [April 27, 2016]
  Added PNG_FAST_FILTERS macro (defined as
    PNG_FILTER_NONE|PNG_FILTER_SUB|PNG_FILTER_UP).
  Various fixes for contrib/libtests/timepng.c
  Moved INTEL-SSE code from pngpriv.h into contrib/intel/intel_sse.patch.
  Fixed typo (missing underscore) in #define PNG_READ_16_TO_8_SUPPORTED
    (Bug report by Y.Ohashik).

Version 1.6.22beta06 [May 5, 2016]
  Rebased contrib/intel_sse.patch.
  Quieted two Coverity issues in contrib/libtests/timepng.c.
  Fixed issues with scripts/genout.cmake.in (David Capello, Nixon Kwok):
    Added support to use multiple directories in ZLIBINCDIR variable,
    Fixed CMAKE_C_FLAGS with multiple values when genout is compiled on MSVC,
    Fixed pnglibconf.c compilation on OS X including the sysroot path.

Version 1.6.22rc01 [May 14, 2016]
  No changes.

Version 1.6.22rc02 [May 16, 2016]
  Removed contrib/timepng from default build; it does not build on platforms
    that don't supply clock_gettime().

Version 1.6.22rc03 [May 17, 2016]
  Restored contrib/timepng to default build but check for the presence
    of clock_gettime() in configure.ac and Makefile.am.

Version 1.6.22 [May 26, 2016]
  No changes.

Version 1.6.23beta01 [May 29, 2016]
  Stop a potential memory leak in png_set_tRNS() (Bug report by Ted Ying).
  Fixed the progressive reader to handle empty first IDAT chunk properly
    (patch by Timothy Nikkel).  This bug was introduced in libpng-1.6.0 and
    only affected the libpng16 branch.
  Added tests in pngvalid.c to check zero-length IDAT chunks in various
    positions.  Fixed the sequential reader to handle these more robustly
    (John Bowler).

Version 1.6.23rc01 [June 2, 2016]
  Corrected progressive read input buffer in pngvalid.c. The previous version
    the code invariably passed just one byte at a time to libpng.  The intent
    was to pass a random number of bytes in the range 0..511.
  Moved sse2 prototype from pngpriv.h to contrib/intel/intel_sse.patch.
  Added missing ")" in pngerror.c (Matt Sarrett).

Version 1.6.23rc02 [June 4, 2016]
  Fixed undefined behavior in png_push_save_buffer(). Do not call
    memcpy() with a null source, even if count is zero (Leon Scroggins III).

Version 1.6.23 [June 9, 2016]
  Fixed bad link to RFC2083 in png.5 (Nikola Forro).

Version 1.6.24beta01 [June 11, 2016]
  Avoid potential overflow of the PNG_IMAGE_SIZE macro.  This macro
    is not used within libpng, but is used in some of the examples.

Version 1.6.24beta02 [June 23, 2016]
  Correct filter heuristic overflow handling. This was broken when the
    write filter code was moved out-of-line; if there is a single filter and
    the heuristic sum overflows the calculation of the filtered line is not
    completed.  In versions prior to 1.6 the code was duplicated in-line
    and the check not performed, so the filter operation completed; however,
    in the multi-filter case where the sum is performed the 'none' filter would
    be selected if all the sums overflowed, even if it wasn't in the filter
    list.  The fix to the first problem is simply to provide PNG_SIZE_MAX as
    the current lmins sum value; this means the sum can never exceed it and
    overflows silently.  A reasonable compiler that does choose to inline
    the code will simply eliminate the sum check.
  The fix to the second problem is to use high precision arithmetic (this is
    implemented in 1.7), however a simple safe fix here is to chose the lowest
    numbered filter in the list from png_set_filter (this only works if the
    first problem is also fixed) (John Bowler).
  Use a more efficient absolute value calculation on SSE2 (Matthieu Darbois).
  Fixed the case where PNG_IMAGE_BUFFER_SIZE can overflow in the application
    as a result of the application using an increased 'row_stride'; previously
    png_image_finish_read only checked for overflow on the base calculation of
    components.  (I.e. it checked for overflow of a 32-bit number on the total
    number of pixel components in the output format, not the possibly padded row
    length and not the number of bytes, which for linear formats is twice the
    number of components.)
  MSVC does not like '-(unsigned)', so replaced it with 0U-(unsigned)
  MSVC does not like (uInt) = -(unsigned) (i.e. as an initializer), unless
    the conversion is explicitly invoked by a cast.
  Put the SKIP definition in the correct place. It needs to come after the
    png.h include (see all the other .c files in contrib/libtests) because it
    depends on PNG_LIBPNG_VER.
  Removed the three compile warning options from the individual project
    files into the zlib.props globals.  It increases the warning level from 4
    to All and adds a list of the warnings that need to be turned off.  This is
    semi-documentary; the intent is to tell libpng users which warnings have
    been examined and judged non-fixable at present.  The warning about
    structure padding is fixable, but it would be a signficant change (moving
    structure members around).

Version 1.6.24beta03 [July 4, 2016]
  Optimized absolute value calculation in filter selection, similar to
    code in the PAETH decoder in pngrutil.c. Build with PNG_USE_ABS to
    use this.
  Added pngcp to the build together with a pngcp.dfa configuration test.
  Added high resolution timing to pngcp.
  Added "Common linking failures" section to INSTALL.
  Relocated misplaced #endif in png.c sRGB profile checking.
  Fixed two Coverity issues in pngcp.c.

Version 1.6.24beta04 [July 8, 2016]
  Avoid filter-selection heuristic sum calculations in cases where only one
    filter is a candidate for selection. This trades off code size (added
    private png_setup_*_row_only() functions) for speed.

Version 1.6.24beta05 [July 13, 2016]
  Fixed some indentation to comply with our coding style.
  Added contrib/tools/reindent.

Version 1.6.24beta06 [July 18, 2016]
  Fixed more indentation to comply with our coding style.
  Eliminated unnecessary tests of boolean png_isaligned() vs 0.

Version 1.6.24rc01 [July 25, 2016]
  No changes.

Version 1.6.24rc02 [August 1, 2016]
  Conditionally compile SSE2 headers in contrib/intel/intel_sse.patch
  Conditionally compile png_decompress_chunk().

Version 1.6.24rc03 [August 2, 2016]
  Conditionally compile ARM_NEON headers in pngpriv.h
  Updated contrib/intel/intel_sse.patch

Version 1.6.24[August 4, 2016]
  No changes.

Version 1.6.25beta01 [August 12, 2016]
  Reject oversized iCCP profile immediately.
  Cleaned up PNG_DEBUG compile of pngtest.c.
  Conditionally compile png_inflate().

Version 1.6.25beta02 [August 18, 2016]
  Don't install pngcp; it conflicts with pngcp in the pngtools package.
  Minor editing of INSTALL, (whitespace, added copyright line)

Version 1.6.25rc01 [August 24, 2016]
  No changes.

Version 1.6.25rc02 [August 29, 2016]
  Added MIPS support (Mandar Sahastrabuddhe <<EMAIL>>).
  Only the UP filter is currently implemented.

Version 1.6.25rc03 [August 29, 2016]
  Rebased contrib/intel/intel_sse.patch after the MIPS implementation.

Version 1.6.25rc04 [August 30, 2016]
  Added MIPS support for SUB, AVG, and PAETH filters (Mandar Sahastrabuddhe).

Version 1.6.25rc05 [August 30, 2016]
  Rebased contrib/intel/intel_sse.patch after the MIPS implementation update..

Version 1.6.25 [September 1, 2016]
  No changes.

Version 1.6.26beta01 [September 26, 2016]
  Fixed handling zero length IDAT in pngfix (bug report by Agostino Sarubbo,
    bugfix by John Bowler).
  Do not issue a png_error() on read in png_set_pCAL() because png_handle_pCAL
    has allocated memory that libpng needs to free.
  Conditionally compile png_set_benign_errors() in pngread.c and pngtest.c
  Issue a png_benign_error instead of a png_error on ADLER32 mismatch
    while decoding compressed data chunks.
  Changed PNG_ZLIB_VERNUM to ZLIB_VERNUM in pngpriv.h, pngstruct.h, and
    pngrutil.c.
  If CRC handling of critical chunks has been set to PNG_CRC_QUIET_USE,
    ignore the ADLER32 checksum in the IDAT chunk as well as the chunk CRCs.
  Issue png_benign_error() on ADLER32 checksum mismatch instead of png_error().
  Add tests/badcrc.png and tests/badadler.png to tests/pngtest.
  Merged pngtest.c with libpng-1.7.0beta84/pngtest.c

Version 1.6.26beta02 [October 1, 2016]
  Updated the documentation about CRC and ADLER32 handling.
  Quieted 117 warnings from clang-3.8 in pngtrans.c, pngread.c,
     pngwrite.c, pngunknown.c, and pngvalid.c.
  Quieted 58 (out of 144) -Wconversion compiler warnings by changing
    flag definitions in pngpriv.h from 0xnnnn to 0xnnnnU and trivial changes
    in png.c, pngread.c, and pngwutil.c.

Version 1.6.26beta03 [October 2, 2016]
  Removed contrib/libtests/*.orig and *.rej that slipped into the tarballs.
  Quieted the 86 remaining -Wconversion compiler warnings by
    revising the png_isaligned() macro and trivial changes in png.c,
    pngerror.c, pngget.c, pngmem.c, pngset.c, pngrtran.c, pngrutil.c,
    pngwtran.c, pngwrite.c, and pngwutil.c.

Version 1.6.26beta04 [October 3, 2016]
  Quieted (bogus?) clang warnings about "absolute value has no effect"
    when PNG_USE_ABS is defined.
  Fixed offsets in contrib/intel/intel_sse.patch

Version 1.6.26beta05 [October 6, 2016]
  Changed integer constant 4294967294 to unsigned 4294967294U in pngconf.h
    to avoid a signed/unsigned compare in the preprocessor.

Version 1.6.26beta06 [October 7, 2016]
  Use zlib-******* inflateValidate() instead of inflateReset2() to
    optionally avoid ADLER32 evaluation.

Version 1.6.26rc01 [October 12, 2016]
  No changes.

Version 1.6.26 [October 20, 2016]
  Cosmetic change, "ptr != 0" to "ptr != NULL" in png.c and pngrutil.c
  Despammed email addresses (replaced "@" with " at ").

Version 1.6.27beta01 [November 2, 2016]
  Restrict the new ADLER32-skipping to IDAT chunks.  It broke iCCP chunk
    handling: an erroneous iCCP chunk would throw a png_error and reject the
    entire PNG image instead of rejecting just the iCCP chunk with a warning,
    if built with zlib-*******.

Version 1.6.27rc01 [December 27, 2016]
  Control ADLER32 checking with new PNG_IGNORE_ADLER32 option.
  Removed the use of a macro containing the pre-processor 'defined'
    operator.  It is unclear whether this is valid; a macro that
    "generates" 'defined' is not permitted, but the use of the word
    "generates" within the C90 standard seems to imply more than simple
    substitution of an expression itself containing a well-formed defined
    operation.
  Added ARM support to CMakeLists.txt (Andreas Franek).

Version 1.6.27 [December 29, 2016]
  Fixed a potential null pointer dereference in png_set_text_2() (bug report
    and patch by Patrick Keshishian, CVE-2016-10087).

Version 1.6.28rc01 [January 3, 2017]
  Fixed arm/aarch64 detection in CMakeLists.txt (Gianfranco Costamagna).
  Added option to Cmake build allowing a custom location of zlib to be
    specified in a scenario where libpng is being built as a subproject
    alongside zlib by another project (Sam Serrels).
  Changed png_ptr->options from a png_byte to png_uint_32, to accomodate
    up to 16 options.

Version 1.6.28rc02 [January 4, 2017]
  Added "include(GNUInstallDirs)" to CMakeLists.txt (Gianfranco Costamagna).
  Moved SSE2 optimization code into the main libpng source directory.
    Configure libpng with "configure --enable-intel-sse" or compile
    libpng with "-DPNG_INTEL_SSE" in CPPFLAGS to enable it.

Version 1.6.28rc03 [January 4, 2017]
  Backed out the SSE optimization and last CMakeLists.txt to allow time for QA.

Version 1.6.28 [January 5, 2017]
  No changes.

Version 1.6.29beta01 [January 12, 2017]
  Readded "include(GNUInstallDirs)" to CMakeLists.txt (Gianfranco Costamagna).
  Moved SSE2 optimization code into the main libpng source directory.
    Configure libpng with "configure --enable-intel-sse" or compile
    libpng with "-DPNG_INTEL_SSE" in CPPFLAGS to enable it.
  Simplified conditional compilation in pngvalid.c, for AIX (Michael Felt).

Version 1.6.29beta02 [February 22, 2017]
  Avoid conditional directives that break statements in pngrutil.c (Romero
    Malaquias)
  The contrib/examples/pngtopng.c recovery code was in the wrong "if"
    branches; the comments were correct.
  Added code for PowerPC VSX optimisation (Vadim Barkov).

Version 1.6.29beta03 [March 1, 2017]
  Avoid potential overflow of shift operations in png_do_expand() (Aaron Boxer).
  Change test ZLIB_VERNUM >= 0x1281 to ZLIB_VERNUM >= 0x1290 in pngrutil.c
    because Solaris 11 distributes zlib-1.2.8.f that is older than *******.
  Suppress clang warnings about implicit sign changes in png.c

Version 1.6.29 [March 16, 2017]
  No changes.

Send comments/corrections/commendations to png-mng-implement at lists.sf.net
(subscription required; visit
https://lists.sourceforge.net/lists/listinfo/png-mng-implement
to subscribe)
or to glennrp at users.sourceforge.net

Glenn R-P
#endif
