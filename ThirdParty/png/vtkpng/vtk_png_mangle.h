#ifndef vtk_png_mangle_h
#define vtk_png_mangle_h

#define png_access_version_number vtkpng_png_access_version_number
#define png_app_error vtkpng_png_app_error
#define png_app_warning vtkpng_png_app_warning
#define png_ascii_from_fixed vtkpng_png_ascii_from_fixed
#define png_ascii_from_fp vtkpng_png_ascii_from_fp
#define png_benign_error vtkpng_png_benign_error
#define png_build_gamma_table vtkpng_png_build_gamma_table
#define png_build_grayscale_palette vtkpng_png_build_grayscale_palette
#define png_calculate_crc vtkpng_png_calculate_crc
#define png_calloc vtkpng_png_calloc
#define png_check_chunk_name vtkpng_png_check_chunk_name
#define png_check_fp_number vtkpng_png_check_fp_number
#define png_check_fp_string vtkpng_png_check_fp_string
#define png_check_IHDR vtkpng_png_check_IHDR
#define png_check_keyword vtkpng_png_check_keyword
#define png_chunk_benign_error vtkpng_png_chunk_benign_error
#define png_chunk_error vtkpng_png_chunk_error
#define png_chunk_report vtkpng_png_chunk_report
#define png_chunk_unknown_handling vtkpng_png_chunk_unknown_handling
#define png_chunk_warning vtkpng_png_chunk_warning
#define png_colorspace_set_chromaticities vtkpng_png_colorspace_set_chromaticities
#define png_colorspace_set_endpoints vtkpng_png_colorspace_set_endpoints
#define png_colorspace_set_gamma vtkpng_png_colorspace_set_gamma
#define png_colorspace_set_ICC vtkpng_png_colorspace_set_ICC
#define png_colorspace_set_rgb_coefficients vtkpng_png_colorspace_set_rgb_coefficients
#define png_colorspace_set_sRGB vtkpng_png_colorspace_set_sRGB
#define png_colorspace_sync vtkpng_png_colorspace_sync
#define png_colorspace_sync_info vtkpng_png_colorspace_sync_info
#define png_combine_row vtkpng_png_combine_row
#define png_compress_IDAT vtkpng_png_compress_IDAT
#define png_convert_from_struct_tm vtkpng_png_convert_from_struct_tm
#define png_convert_from_time_t vtkpng_png_convert_from_time_t
#define png_convert_to_rfc1123 vtkpng_png_convert_to_rfc1123
#define png_convert_to_rfc1123_buffer vtkpng_png_convert_to_rfc1123_buffer
#define png_crc_error vtkpng_png_crc_error
#define png_crc_finish vtkpng_png_crc_finish
#define png_crc_read vtkpng_png_crc_read
#define png_create_info_struct vtkpng_png_create_info_struct
#define png_create_png_struct vtkpng_png_create_png_struct
#define png_create_read_struct vtkpng_png_create_read_struct
#define png_create_read_struct_2 vtkpng_png_create_read_struct_2
#define png_create_write_struct vtkpng_png_create_write_struct
#define png_create_write_struct_2 vtkpng_png_create_write_struct_2
#define png_data_freer vtkpng_png_data_freer
#define png_default_flush vtkpng_png_default_flush
#define png_default_read_data vtkpng_png_default_read_data
#define png_default_write_data vtkpng_png_default_write_data
#define png_destroy_gamma_table vtkpng_png_destroy_gamma_table
#define png_destroy_info_struct vtkpng_png_destroy_info_struct
#define png_destroy_png_struct vtkpng_png_destroy_png_struct
#define png_destroy_read_struct vtkpng_png_destroy_read_struct
#define png_destroy_write_struct vtkpng_png_destroy_write_struct
#define png_do_bgr vtkpng_png_do_bgr
#define png_do_check_palette_indexes vtkpng_png_do_check_palette_indexes
#define png_do_invert vtkpng_png_do_invert
#define png_do_packswap vtkpng_png_do_packswap
#define png_do_read_interlace vtkpng_png_do_read_interlace
#define png_do_read_transformations vtkpng_png_do_read_transformations
#define png_do_strip_channel vtkpng_png_do_strip_channel
#define png_do_swap vtkpng_png_do_swap
#define png_do_write_interlace vtkpng_png_do_write_interlace
#define png_do_write_transformations vtkpng_png_do_write_transformations
#define png_error vtkpng_png_error
#define png_fixed vtkpng_png_fixed
#define png_fixed_error vtkpng_png_fixed_error
#define png_flush vtkpng_png_flush
#define png_format_number vtkpng_png_format_number
#define png_formatted_warning vtkpng_png_formatted_warning
#define png_free vtkpng_png_free
#define png_free_buffer_list vtkpng_png_free_buffer_list
#define png_free_data vtkpng_png_free_data
#define png_free_default vtkpng_png_free_default
#define png_free_jmpbuf vtkpng_png_free_jmpbuf
#define png_gamma_16bit_correct vtkpng_png_gamma_16bit_correct
#define png_gamma_8bit_correct vtkpng_png_gamma_8bit_correct
#define png_gamma_correct vtkpng_png_gamma_correct
#define png_gamma_significant vtkpng_png_gamma_significant
#define png_get_bit_depth vtkpng_png_get_bit_depth
#define png_get_bKGD vtkpng_png_get_bKGD
#define png_get_channels vtkpng_png_get_channels
#define png_get_cHRM vtkpng_png_get_cHRM
#define png_get_cHRM_fixed vtkpng_png_get_cHRM_fixed
#define png_get_cHRM_XYZ vtkpng_png_get_cHRM_XYZ
#define png_get_cHRM_XYZ_fixed vtkpng_png_get_cHRM_XYZ_fixed
#define png_get_chunk_cache_max vtkpng_png_get_chunk_cache_max
#define png_get_chunk_malloc_max vtkpng_png_get_chunk_malloc_max
#define png_get_color_type vtkpng_png_get_color_type
#define png_get_compression_buffer_size vtkpng_png_get_compression_buffer_size
#define png_get_compression_type vtkpng_png_get_compression_type
#define png_get_copyright vtkpng_png_get_copyright
#define png_get_current_pass_number vtkpng_png_get_current_pass_number
#define png_get_current_row_number vtkpng_png_get_current_row_number
#define png_get_error_ptr vtkpng_png_get_error_ptr
#define png_get_filter_type vtkpng_png_get_filter_type
#define png_get_gAMA vtkpng_png_get_gAMA
#define png_get_gAMA_fixed vtkpng_png_get_gAMA_fixed
#define png_get_header_ver vtkpng_png_get_header_ver
#define png_get_header_version vtkpng_png_get_header_version
#define png_get_hIST vtkpng_png_get_hIST
#define png_get_iCCP vtkpng_png_get_iCCP
#define png_get_IHDR vtkpng_png_get_IHDR
#define png_get_image_height vtkpng_png_get_image_height
#define png_get_image_width vtkpng_png_get_image_width
#define png_get_int_32 vtkpng_png_get_int_32
#define png_get_interlace_type vtkpng_png_get_interlace_type
#define png_get_io_chunk_type vtkpng_png_get_io_chunk_type
#define png_get_io_ptr vtkpng_png_get_io_ptr
#define png_get_io_state vtkpng_png_get_io_state
#define png_get_libpng_ver vtkpng_png_get_libpng_ver
#define png_get_mem_ptr vtkpng_png_get_mem_ptr
#define png_get_oFFs vtkpng_png_get_oFFs
#define png_get_palette_max vtkpng_png_get_palette_max
#define png_get_pCAL vtkpng_png_get_pCAL
#define png_get_pHYs vtkpng_png_get_pHYs
#define png_get_pHYs_dpi vtkpng_png_get_pHYs_dpi
#define png_get_pixel_aspect_ratio vtkpng_png_get_pixel_aspect_ratio
#define png_get_pixel_aspect_ratio_fixed vtkpng_png_get_pixel_aspect_ratio_fixed
#define png_get_pixels_per_inch vtkpng_png_get_pixels_per_inch
#define png_get_pixels_per_meter vtkpng_png_get_pixels_per_meter
#define png_get_PLTE vtkpng_png_get_PLTE
#define png_get_progressive_ptr vtkpng_png_get_progressive_ptr
#define png_get_rgb_to_gray_status vtkpng_png_get_rgb_to_gray_status
#define png_get_rowbytes vtkpng_png_get_rowbytes
#define png_get_rows vtkpng_png_get_rows
#define png_get_sBIT vtkpng_png_get_sBIT
#define png_get_sCAL vtkpng_png_get_sCAL
#define png_get_sCAL_fixed vtkpng_png_get_sCAL_fixed
#define png_get_sCAL_s vtkpng_png_get_sCAL_s
#define png_get_signature vtkpng_png_get_signature
#define png_get_sPLT vtkpng_png_get_sPLT
#define png_get_sRGB vtkpng_png_get_sRGB
#define png_get_text vtkpng_png_get_text
#define png_get_tIME vtkpng_png_get_tIME
#define png_get_tRNS vtkpng_png_get_tRNS
#define png_get_uint_16 vtkpng_png_get_uint_16
#define png_get_uint_31 vtkpng_png_get_uint_31
#define png_get_uint_32 vtkpng_png_get_uint_32
#define png_get_unknown_chunks vtkpng_png_get_unknown_chunks
#define png_get_user_chunk_ptr vtkpng_png_get_user_chunk_ptr
#define png_get_user_height_max vtkpng_png_get_user_height_max
#define png_get_user_transform_ptr vtkpng_png_get_user_transform_ptr
#define png_get_user_width_max vtkpng_png_get_user_width_max
#define png_get_valid vtkpng_png_get_valid
#define png_get_x_offset_inches vtkpng_png_get_x_offset_inches
#define png_get_x_offset_inches_fixed vtkpng_png_get_x_offset_inches_fixed
#define png_get_x_offset_microns vtkpng_png_get_x_offset_microns
#define png_get_x_offset_pixels vtkpng_png_get_x_offset_pixels
#define png_get_x_pixels_per_inch vtkpng_png_get_x_pixels_per_inch
#define png_get_x_pixels_per_meter vtkpng_png_get_x_pixels_per_meter
#define png_get_y_offset_inches vtkpng_png_get_y_offset_inches
#define png_get_y_offset_inches_fixed vtkpng_png_get_y_offset_inches_fixed
#define png_get_y_offset_microns vtkpng_png_get_y_offset_microns
#define png_get_y_offset_pixels vtkpng_png_get_y_offset_pixels
#define png_get_y_pixels_per_inch vtkpng_png_get_y_pixels_per_inch
#define png_get_y_pixels_per_meter vtkpng_png_get_y_pixels_per_meter
#define png_handle_as_unknown vtkpng_png_handle_as_unknown
#define png_handle_bKGD vtkpng_png_handle_bKGD
#define png_handle_cHRM vtkpng_png_handle_cHRM
#define png_handle_gAMA vtkpng_png_handle_gAMA
#define png_handle_hIST vtkpng_png_handle_hIST
#define png_handle_iCCP vtkpng_png_handle_iCCP
#define png_handle_IEND vtkpng_png_handle_IEND
#define png_handle_IHDR vtkpng_png_handle_IHDR
#define png_handle_iTXt vtkpng_png_handle_iTXt
#define png_handle_oFFs vtkpng_png_handle_oFFs
#define png_handle_pCAL vtkpng_png_handle_pCAL
#define png_handle_pHYs vtkpng_png_handle_pHYs
#define png_handle_PLTE vtkpng_png_handle_PLTE
#define png_handle_sBIT vtkpng_png_handle_sBIT
#define png_handle_sCAL vtkpng_png_handle_sCAL
#define png_handle_sPLT vtkpng_png_handle_sPLT
#define png_handle_sRGB vtkpng_png_handle_sRGB
#define png_handle_tEXt vtkpng_png_handle_tEXt
#define png_handle_tIME vtkpng_png_handle_tIME
#define png_handle_tRNS vtkpng_png_handle_tRNS
#define png_handle_unknown vtkpng_png_handle_unknown
#define png_handle_zTXt vtkpng_png_handle_zTXt
#define png_icc_check_header vtkpng_png_icc_check_header
#define png_icc_check_length vtkpng_png_icc_check_length
#define png_icc_check_tag_table vtkpng_png_icc_check_tag_table
#define png_icc_set_sRGB vtkpng_png_icc_set_sRGB
#define png_image_begin_read_from_file vtkpng_png_image_begin_read_from_file
#define png_image_begin_read_from_memory vtkpng_png_image_begin_read_from_memory
#define png_image_begin_read_from_stdio vtkpng_png_image_begin_read_from_stdio
#define png_image_error vtkpng_png_image_error
#define png_image_finish_read vtkpng_png_image_finish_read
#define png_image_free vtkpng_png_image_free
#define png_image_write_to_file vtkpng_png_image_write_to_file
#define png_image_write_to_memory vtkpng_png_image_write_to_memory
#define png_image_write_to_stdio vtkpng_png_image_write_to_stdio
#define png_info_init_3 vtkpng_png_info_init_3
#define png_init_io vtkpng_png_init_io
#define png_init_read_transformations vtkpng_png_init_read_transformations
#define png_longjmp vtkpng_png_longjmp
#define png_malloc vtkpng_png_malloc
#define png_malloc_array vtkpng_png_malloc_array
#define png_malloc_base vtkpng_png_malloc_base
#define png_malloc_default vtkpng_png_malloc_default
#define png_malloc_warn vtkpng_png_malloc_warn
#define png_muldiv vtkpng_png_muldiv
#define png_muldiv_warn vtkpng_png_muldiv_warn
#define png_permit_mng_features vtkpng_png_permit_mng_features
#define png_process_data vtkpng_png_process_data
#define png_process_data_pause vtkpng_png_process_data_pause
#define png_process_data_skip vtkpng_png_process_data_skip
#define png_process_IDAT_data vtkpng_png_process_IDAT_data
#define png_process_some_data vtkpng_png_process_some_data
#define png_progressive_combine_row vtkpng_png_progressive_combine_row
#define png_push_fill_buffer vtkpng_png_push_fill_buffer
#define png_push_have_end vtkpng_png_push_have_end
#define png_push_have_info vtkpng_png_push_have_info
#define png_push_have_row vtkpng_png_push_have_row
#define png_push_process_row vtkpng_png_push_process_row
#define png_push_read_chunk vtkpng_png_push_read_chunk
#define png_push_read_IDAT vtkpng_png_push_read_IDAT
#define png_push_read_sig vtkpng_png_push_read_sig
#define png_push_restore_buffer vtkpng_png_push_restore_buffer
#define png_push_save_buffer vtkpng_png_push_save_buffer
#define png_read_chunk_header vtkpng_png_read_chunk_header
#define png_read_data vtkpng_png_read_data
#define png_read_end vtkpng_png_read_end
#define png_read_filter_row vtkpng_png_read_filter_row
#define png_read_finish_IDAT vtkpng_png_read_finish_IDAT
#define png_read_finish_row vtkpng_png_read_finish_row
#define png_read_IDAT_data vtkpng_png_read_IDAT_data
#define png_read_image vtkpng_png_read_image
#define png_read_info vtkpng_png_read_info
#define png_read_png vtkpng_png_read_png
#define png_read_push_finish_row vtkpng_png_read_push_finish_row
#define png_read_row vtkpng_png_read_row
#define png_read_rows vtkpng_png_read_rows
#define png_read_sig vtkpng_png_read_sig
#define png_read_start_row vtkpng_png_read_start_row
#define png_read_transform_info vtkpng_png_read_transform_info
#define png_read_update_info vtkpng_png_read_update_info
#define png_realloc_array vtkpng_png_realloc_array
#define png_reciprocal vtkpng_png_reciprocal
#define png_reciprocal2 vtkpng_png_reciprocal2
#define png_reset_crc vtkpng_png_reset_crc
#define png_reset_zstream vtkpng_png_reset_zstream
#define png_safecat vtkpng_png_safecat
#define png_safe_error vtkpng_png_safe_error
#define png_safe_execute vtkpng_png_safe_execute
#define png_safe_warning vtkpng_png_safe_warning
#define png_save_int_32 vtkpng_png_save_int_32
#define png_save_uint_16 vtkpng_png_save_uint_16
#define png_save_uint_32 vtkpng_png_save_uint_32
#define png_set_add_alpha vtkpng_png_set_add_alpha
#define png_set_alpha_mode vtkpng_png_set_alpha_mode
#define png_set_alpha_mode_fixed vtkpng_png_set_alpha_mode_fixed
#define png_set_background vtkpng_png_set_background
#define png_set_background_fixed vtkpng_png_set_background_fixed
#define png_set_benign_errors vtkpng_png_set_benign_errors
#define png_set_bgr vtkpng_png_set_bgr
#define png_set_bKGD vtkpng_png_set_bKGD
#define png_set_check_for_invalid_index vtkpng_png_set_check_for_invalid_index
#define png_set_cHRM vtkpng_png_set_cHRM
#define png_set_cHRM_fixed vtkpng_png_set_cHRM_fixed
#define png_set_cHRM_XYZ vtkpng_png_set_cHRM_XYZ
#define png_set_cHRM_XYZ_fixed vtkpng_png_set_cHRM_XYZ_fixed
#define png_set_chunk_cache_max vtkpng_png_set_chunk_cache_max
#define png_set_chunk_malloc_max vtkpng_png_set_chunk_malloc_max
#define png_set_compression_buffer_size vtkpng_png_set_compression_buffer_size
#define png_set_compression_level vtkpng_png_set_compression_level
#define png_set_compression_mem_level vtkpng_png_set_compression_mem_level
#define png_set_compression_method vtkpng_png_set_compression_method
#define png_set_compression_strategy vtkpng_png_set_compression_strategy
#define png_set_compression_window_bits vtkpng_png_set_compression_window_bits
#define png_set_crc_action vtkpng_png_set_crc_action
#define png_set_error_fn vtkpng_png_set_error_fn
#define png_set_expand vtkpng_png_set_expand
#define png_set_expand_16 vtkpng_png_set_expand_16
#define png_set_expand_gray_1_2_4_to_8 vtkpng_png_set_expand_gray_1_2_4_to_8
#define png_set_filler vtkpng_png_set_filler
#define png_set_filter vtkpng_png_set_filter
#define png_set_filter_heuristics vtkpng_png_set_filter_heuristics
#define png_set_filter_heuristics_fixed vtkpng_png_set_filter_heuristics_fixed
#define png_set_flush vtkpng_png_set_flush
#define png_set_gAMA vtkpng_png_set_gAMA
#define png_set_gAMA_fixed vtkpng_png_set_gAMA_fixed
#define png_set_gamma vtkpng_png_set_gamma
#define png_set_gamma_fixed vtkpng_png_set_gamma_fixed
#define png_set_gray_to_rgb vtkpng_png_set_gray_to_rgb
#define png_set_hIST vtkpng_png_set_hIST
#define png_set_iCCP vtkpng_png_set_iCCP
#define png_set_IHDR vtkpng_png_set_IHDR
#define png_set_interlace_handling vtkpng_png_set_interlace_handling
#define png_set_invalid vtkpng_png_set_invalid
#define png_set_invert_alpha vtkpng_png_set_invert_alpha
#define png_set_invert_mono vtkpng_png_set_invert_mono
#define png_set_keep_unknown_chunks vtkpng_png_set_keep_unknown_chunks
#define png_set_longjmp_fn vtkpng_png_set_longjmp_fn
#define png_set_mem_fn vtkpng_png_set_mem_fn
#define png_set_oFFs vtkpng_png_set_oFFs
#define png_set_option vtkpng_png_set_option
#define png_set_packing vtkpng_png_set_packing
#define png_set_packswap vtkpng_png_set_packswap
#define png_set_palette_to_rgb vtkpng_png_set_palette_to_rgb
#define png_set_pCAL vtkpng_png_set_pCAL
#define png_set_pHYs vtkpng_png_set_pHYs
#define png_set_PLTE vtkpng_png_set_PLTE
#define png_set_progressive_read_fn vtkpng_png_set_progressive_read_fn
#define png_set_quantize vtkpng_png_set_quantize
#define png_set_read_fn vtkpng_png_set_read_fn
#define png_set_read_status_fn vtkpng_png_set_read_status_fn
#define png_set_read_user_chunk_fn vtkpng_png_set_read_user_chunk_fn
#define png_set_read_user_transform_fn vtkpng_png_set_read_user_transform_fn
#define png_set_rgb_to_gray vtkpng_png_set_rgb_to_gray
#define png_set_rgb_to_gray_fixed vtkpng_png_set_rgb_to_gray_fixed
#define png_set_rows vtkpng_png_set_rows
#define png_set_sBIT vtkpng_png_set_sBIT
#define png_set_sCAL vtkpng_png_set_sCAL
#define png_set_scale_16 vtkpng_png_set_scale_16
#define png_set_sCAL_fixed vtkpng_png_set_sCAL_fixed
#define png_set_sCAL_s vtkpng_png_set_sCAL_s
#define png_set_shift vtkpng_png_set_shift
#define png_set_sig_bytes vtkpng_png_set_sig_bytes
#define png_set_sPLT vtkpng_png_set_sPLT
#define png_set_sRGB vtkpng_png_set_sRGB
#define png_set_sRGB_gAMA_and_cHRM vtkpng_png_set_sRGB_gAMA_and_cHRM
#define png_set_strip_16 vtkpng_png_set_strip_16
#define png_set_strip_alpha vtkpng_png_set_strip_alpha
#define png_set_swap vtkpng_png_set_swap
#define png_set_swap_alpha vtkpng_png_set_swap_alpha
#define png_set_text vtkpng_png_set_text
#define png_set_text_2 vtkpng_png_set_text_2
#define png_set_text_compression_level vtkpng_png_set_text_compression_level
#define png_set_text_compression_mem_level vtkpng_png_set_text_compression_mem_level
#define png_set_text_compression_method vtkpng_png_set_text_compression_method
#define png_set_text_compression_strategy vtkpng_png_set_text_compression_strategy
#define png_set_text_compression_window_bits vtkpng_png_set_text_compression_window_bits
#define png_set_tIME vtkpng_png_set_tIME
#define png_set_tRNS vtkpng_png_set_tRNS
#define png_set_tRNS_to_alpha vtkpng_png_set_tRNS_to_alpha
#define png_set_unknown_chunk_location vtkpng_png_set_unknown_chunk_location
#define png_set_unknown_chunks vtkpng_png_set_unknown_chunks
#define png_set_user_limits vtkpng_png_set_user_limits
#define png_set_user_transform_info vtkpng_png_set_user_transform_info
#define png_set_write_fn vtkpng_png_set_write_fn
#define png_set_write_status_fn vtkpng_png_set_write_status_fn
#define png_set_write_user_transform_fn vtkpng_png_set_write_user_transform_fn
#define png_sig_cmp vtkpng_png_sig_cmp
#define png_sRGB_base vtkpng_png_sRGB_base
#define png_sRGB_delta vtkpng_png_sRGB_delta
#define png_sRGB_table vtkpng_png_sRGB_table
#define png_start_read_image vtkpng_png_start_read_image
#define png_user_version_check vtkpng_png_user_version_check
#define png_warning vtkpng_png_warning
#define png_warning_parameter vtkpng_png_warning_parameter
#define png_warning_parameter_signed vtkpng_png_warning_parameter_signed
#define png_warning_parameter_unsigned vtkpng_png_warning_parameter_unsigned
#define png_write_bKGD vtkpng_png_write_bKGD
#define png_write_cHRM_fixed vtkpng_png_write_cHRM_fixed
#define png_write_chunk vtkpng_png_write_chunk
#define png_write_chunk_data vtkpng_png_write_chunk_data
#define png_write_chunk_end vtkpng_png_write_chunk_end
#define png_write_chunk_start vtkpng_png_write_chunk_start
#define png_write_data vtkpng_png_write_data
#define png_write_end vtkpng_png_write_end
#define png_write_find_filter vtkpng_png_write_find_filter
#define png_write_finish_row vtkpng_png_write_finish_row
#define png_write_flush vtkpng_png_write_flush
#define png_write_gAMA_fixed vtkpng_png_write_gAMA_fixed
#define png_write_hIST vtkpng_png_write_hIST
#define png_write_iCCP vtkpng_png_write_iCCP
#define png_write_IEND vtkpng_png_write_IEND
#define png_write_IHDR vtkpng_png_write_IHDR
#define png_write_image vtkpng_png_write_image
#define png_write_info vtkpng_png_write_info
#define png_write_info_before_PLTE vtkpng_png_write_info_before_PLTE
#define png_write_iTXt vtkpng_png_write_iTXt
#define png_write_oFFs vtkpng_png_write_oFFs
#define png_write_pCAL vtkpng_png_write_pCAL
#define png_write_pHYs vtkpng_png_write_pHYs
#define png_write_PLTE vtkpng_png_write_PLTE
#define png_write_png vtkpng_png_write_png
#define png_write_row vtkpng_png_write_row
#define png_write_rows vtkpng_png_write_rows
#define png_write_sBIT vtkpng_png_write_sBIT
#define png_write_sCAL_s vtkpng_png_write_sCAL_s
#define png_write_sig vtkpng_png_write_sig
#define png_write_sPLT vtkpng_png_write_sPLT
#define png_write_sRGB vtkpng_png_write_sRGB
#define png_write_start_row vtkpng_png_write_start_row
#define png_write_tEXt vtkpng_png_write_tEXt
#define png_write_tIME vtkpng_png_write_tIME
#define png_write_tRNS vtkpng_png_write_tRNS
#define png_write_zTXt vtkpng_png_write_zTXt
#define png_zalloc vtkpng_png_zalloc
#define png_zfree vtkpng_png_zfree
#define png_zlib_inflate vtkpng_png_zlib_inflate
#define png_zstream_error vtkpng_png_zstream_error

#endif
