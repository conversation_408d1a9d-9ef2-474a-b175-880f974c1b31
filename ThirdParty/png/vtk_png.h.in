/*=========================================================================

  Program:   Visualization Toolkit
  Module:    vtk_png.h

  Copyright (c) <PERSON>, <PERSON>, <PERSON>
  All rights reserved.
  See Copyright.txt or http://www.kitware.com/Copyright.htm for details.

     This software is distributed WITHOUT ANY WARRANTY; without even
     the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
     PURPOSE.  See the above copyright notice for more information.

=========================================================================*/
#ifndef vtk_png_h
#define vtk_png_h

/* Use the png library configured for VTK.  */
#cmakedefine VTK_USE_SYSTEM_PNG
#ifdef VTK_USE_SYSTEM_PNG
# include <png.h>
#else
# include <vtkpng/png.h>
#endif

#endif
