#define PyMPI_HAVE_MPI_UNDEFINED 1
#define PyMPI_HAVE_MPI_ANY_SOURCE 1
#define PyMPI_HAVE_MPI_ANY_TAG 1
#define PyMPI_HAVE_MPI_PROC_NULL 1

#define PyMPI_HAVE_MPI_Aint 1
#define PyMPI_HAVE_MPI_Datatype 1
#define PyMPI_HAVE_MPI_DATATYPE_NULL 1
#define PyMPI_HAVE_MPI_UB 1
#define PyMPI_HAVE_MPI_LB 1
#define PyMPI_HAVE_MPI_PACKED 1
#define PyMPI_HAVE_MPI_BYTE 1
#define PyMPI_HAVE_MPI_CHAR 1
#define PyMPI_HAVE_MPI_SHORT 1
#define PyMPI_HAVE_MPI_INT 1
#define PyMPI_HAVE_MPI_LONG 1
#define PyMPI_HAVE_MPI_LONG_LONG_INT 1
#define PyMPI_HAVE_MPI_UNSIGNED_CHAR 1
#define PyMPI_HAVE_MPI_UNSIGNED_SHORT 1
#define PyMPI_HAVE_MPI_UNSIGNED 1
#define PyMPI_HAVE_MPI_UNSIGNED_LONG 1
#define PyMPI_HAVE_MPI_FLOAT 1
#define PyMPI_HAVE_MPI_DOUBLE 1
#define PyMPI_HAVE_MPI_LONG_DOUBLE 1
#define PyMPI_HAVE_MPI_SHORT_INT 1
#define PyMPI_HAVE_MPI_2INT 1
#define PyMPI_HAVE_MPI_LONG_INT 1
#define PyMPI_HAVE_MPI_FLOAT_INT 1
#define PyMPI_HAVE_MPI_DOUBLE_INT 1
#define PyMPI_HAVE_MPI_LONG_DOUBLE_INT 1

#define PyMPI_HAVE_MPI_CHARACTER 1
#define PyMPI_HAVE_MPI_LOGICAL 1
#define PyMPI_HAVE_MPI_INTEGER 1
#define PyMPI_HAVE_MPI_REAL 1
#define PyMPI_HAVE_MPI_DOUBLE_PRECISION 1
#define PyMPI_HAVE_MPI_COMPLEX 1
#define PyMPI_HAVE_MPI_DOUBLE_COMPLEX 1

#define PyMPI_HAVE_MPI_BOTTOM 1
#define PyMPI_HAVE_MPI_Address 1
#define PyMPI_HAVE_MPI_Type_size 1
#define PyMPI_HAVE_MPI_Type_extent 1
#define PyMPI_HAVE_MPI_Type_lb 1
#define PyMPI_HAVE_MPI_Type_ub 1
#define PyMPI_HAVE_MPI_Type_dup 1
#define PyMPI_HAVE_MPI_Type_contiguous 1
#define PyMPI_HAVE_MPI_Type_vector 1
#define PyMPI_HAVE_MPI_Type_indexed 1
#define PyMPI_HAVE_MPI_Type_hvector 1
#define PyMPI_HAVE_MPI_Type_hindexed 1
#define PyMPI_HAVE_MPI_Type_struct 1
#define PyMPI_HAVE_MPI_Type_commit 1
#define PyMPI_HAVE_MPI_Type_free 1
#define PyMPI_HAVE_MPI_Pack 1
#define PyMPI_HAVE_MPI_Unpack 1
#define PyMPI_HAVE_MPI_Pack_size 1

#define PyMPI_HAVE_MPI_Status 1
#define PyMPI_HAVE_MPI_Get_count 1
#define PyMPI_HAVE_MPI_Get_elements 1
#define PyMPI_HAVE_MPI_Test_cancelled 1

#define PyMPI_HAVE_MPI_Request 1
#define PyMPI_HAVE_MPI_REQUEST_NULL 1
#define PyMPI_HAVE_MPI_Request_free 1
#define PyMPI_HAVE_MPI_Wait 1
#define PyMPI_HAVE_MPI_Test 1
#define PyMPI_HAVE_MPI_Request_get_status 1
#define PyMPI_HAVE_MPI_Cancel 1
#define PyMPI_HAVE_MPI_Waitany 1
#define PyMPI_HAVE_MPI_Testany 1
#define PyMPI_HAVE_MPI_Waitall 1
#define PyMPI_HAVE_MPI_Testall 1
#define PyMPI_HAVE_MPI_Waitsome 1
#define PyMPI_HAVE_MPI_Testsome 1
#define PyMPI_HAVE_MPI_Start 1
#define PyMPI_HAVE_MPI_Startall 1

#define PyMPI_HAVE_MPI_Op 1
#define PyMPI_HAVE_MPI_OP_NULL 1
#define PyMPI_HAVE_MPI_MAX 1
#define PyMPI_HAVE_MPI_MIN 1
#define PyMPI_HAVE_MPI_SUM 1
#define PyMPI_HAVE_MPI_PROD 1
#define PyMPI_HAVE_MPI_LAND 1
#define PyMPI_HAVE_MPI_BAND 1
#define PyMPI_HAVE_MPI_LOR 1
#define PyMPI_HAVE_MPI_BOR 1
#define PyMPI_HAVE_MPI_LXOR 1
#define PyMPI_HAVE_MPI_BXOR 1
#define PyMPI_HAVE_MPI_MAXLOC 1
#define PyMPI_HAVE_MPI_MINLOC 1
#define PyMPI_HAVE_MPI_REPLACE 1
#define PyMPI_HAVE_MPI_Op_free 1
#define PyMPI_HAVE_MPI_User_function 1
#define PyMPI_HAVE_MPI_Op_create 1

#define PyMPI_HAVE_MPI_Group 1
#define PyMPI_HAVE_MPI_GROUP_NULL 1
#define PyMPI_HAVE_MPI_GROUP_EMPTY 1
#define PyMPI_HAVE_MPI_Group_free 1
#define PyMPI_HAVE_MPI_Group_size 1
#define PyMPI_HAVE_MPI_Group_rank 1
#define PyMPI_HAVE_MPI_Group_translate_ranks 1
#define PyMPI_HAVE_MPI_IDENT 1
#define PyMPI_HAVE_MPI_CONGRUENT 1
#define PyMPI_HAVE_MPI_SIMILAR 1
#define PyMPI_HAVE_MPI_UNEQUAL 1

#define PyMPI_HAVE_MPI_Group_compare 1
#define PyMPI_HAVE_MPI_Group_union 1
#define PyMPI_HAVE_MPI_Group_intersection 1
#define PyMPI_HAVE_MPI_Group_difference 1
#define PyMPI_HAVE_MPI_Group_incl 1
#define PyMPI_HAVE_MPI_Group_excl 1
#define PyMPI_HAVE_MPI_Group_range_incl 1
#define PyMPI_HAVE_MPI_Group_range_excl 1

#define PyMPI_HAVE_MPI_Comm 1
#define PyMPI_HAVE_MPI_COMM_NULL 1
#define PyMPI_HAVE_MPI_COMM_SELF 1
#define PyMPI_HAVE_MPI_COMM_WORLD 1
#define PyMPI_HAVE_MPI_Comm_free 1
#define PyMPI_HAVE_MPI_Comm_group 1
#define PyMPI_HAVE_MPI_Comm_size 1
#define PyMPI_HAVE_MPI_Comm_rank 1
#define PyMPI_HAVE_MPI_Comm_compare 1
#define PyMPI_HAVE_MPI_Topo_test 1
#define PyMPI_HAVE_MPI_Comm_test_inter 1
#define PyMPI_HAVE_MPI_Abort 1
#define PyMPI_HAVE_MPI_Send 1
#define PyMPI_HAVE_MPI_Recv 1
#define PyMPI_HAVE_MPI_Sendrecv 1
#define PyMPI_HAVE_MPI_Sendrecv_replace 1
#define PyMPI_HAVE_MPI_BSEND_OVERHEAD 1
#define PyMPI_HAVE_MPI_Buffer_attach 1
#define PyMPI_HAVE_MPI_Buffer_detach 1
#define PyMPI_HAVE_MPI_Bsend 1
#define PyMPI_HAVE_MPI_Ssend 1
#define PyMPI_HAVE_MPI_Rsend 1
#define PyMPI_HAVE_MPI_Isend 1
#define PyMPI_HAVE_MPI_Ibsend 1
#define PyMPI_HAVE_MPI_Issend 1
#define PyMPI_HAVE_MPI_Irsend 1
#define PyMPI_HAVE_MPI_Irecv 1
#define PyMPI_HAVE_MPI_Send_init 1
#define PyMPI_HAVE_MPI_Bsend_init 1
#define PyMPI_HAVE_MPI_Ssend_init 1
#define PyMPI_HAVE_MPI_Rsend_init 1
#define PyMPI_HAVE_MPI_Recv_init 1
#define PyMPI_HAVE_MPI_Probe 1
#define PyMPI_HAVE_MPI_Iprobe 1
#define PyMPI_HAVE_MPI_Barrier 1
#define PyMPI_HAVE_MPI_Bcast 1
#define PyMPI_HAVE_MPI_Gather 1
#define PyMPI_HAVE_MPI_Gatherv 1
#define PyMPI_HAVE_MPI_Scatter 1
#define PyMPI_HAVE_MPI_Scatterv 1
#define PyMPI_HAVE_MPI_Allgather 1
#define PyMPI_HAVE_MPI_Allgatherv 1
#define PyMPI_HAVE_MPI_Alltoall 1
#define PyMPI_HAVE_MPI_Alltoallv 1
#define PyMPI_HAVE_MPI_Reduce 1
#define PyMPI_HAVE_MPI_Allreduce 1
#define PyMPI_HAVE_MPI_Reduce_scatter 1
#define PyMPI_HAVE_MPI_Scan 1
#define PyMPI_HAVE_MPI_Comm_dup 1
#define PyMPI_HAVE_MPI_Comm_create 1
#define PyMPI_HAVE_MPI_Comm_split 1
#define PyMPI_HAVE_MPI_CART 1
#define PyMPI_HAVE_MPI_Cart_create 1
#define PyMPI_HAVE_MPI_Cartdim_get 1
#define PyMPI_HAVE_MPI_Cart_get 1
#define PyMPI_HAVE_MPI_Cart_rank 1
#define PyMPI_HAVE_MPI_Cart_coords 1
#define PyMPI_HAVE_MPI_Cart_shift 1
#define PyMPI_HAVE_MPI_Cart_sub 1
#define PyMPI_HAVE_MPI_Cart_map 1
#define PyMPI_HAVE_MPI_Dims_create 1
#define PyMPI_HAVE_MPI_GRAPH 1
#define PyMPI_HAVE_MPI_Graph_create 1
#define PyMPI_HAVE_MPI_Graphdims_get 1
#define PyMPI_HAVE_MPI_Graph_get 1
#define PyMPI_HAVE_MPI_Graph_map 1
#define PyMPI_HAVE_MPI_Graph_neighbors_count 1
#define PyMPI_HAVE_MPI_Graph_neighbors 1
#define PyMPI_HAVE_MPI_Intercomm_create 1
#define PyMPI_HAVE_MPI_Comm_remote_group 1
#define PyMPI_HAVE_MPI_Comm_remote_size 1
#define PyMPI_HAVE_MPI_Intercomm_merge 1
#define PyMPI_HAVE_MPI_Errhandler_get 1
#define PyMPI_HAVE_MPI_Errhandler_set 1
#define PyMPI_HAVE_MPI_Handler_function 1
#define PyMPI_HAVE_MPI_Errhandler_create 1

#define PyMPI_HAVE_MPI_Init 1
#define PyMPI_HAVE_MPI_Finalize 1
#define PyMPI_HAVE_MPI_Initialized 1
#define PyMPI_HAVE_MPI_Finalized 1

#define PyMPI_HAVE_MPI_MAX_PROCESSOR_NAME 1
#define PyMPI_HAVE_MPI_Get_processor_name 1
#define PyMPI_HAVE_MPI_Wtime 1
#define PyMPI_HAVE_MPI_Wtick 1
#define PyMPI_HAVE_MPI_Pcontrol 1

#define PyMPI_HAVE_MPI_Errhandler 1
#define PyMPI_HAVE_MPI_ERRHANDLER_NULL 1
#define PyMPI_HAVE_MPI_ERRORS_RETURN 1
#define PyMPI_HAVE_MPI_ERRORS_ARE_FATAL 1
#define PyMPI_HAVE_MPI_Errhandler_free 1

#define PyMPI_HAVE_MPI_KEYVAL_INVALID 1
#define PyMPI_HAVE_MPI_TAG_UB 1
#define PyMPI_HAVE_MPI_HOST 1
#define PyMPI_HAVE_MPI_IO 1
#define PyMPI_HAVE_MPI_WTIME_IS_GLOBAL 1
#define PyMPI_HAVE_MPI_Attr_get 1
#define PyMPI_HAVE_MPI_Attr_put 1
#define PyMPI_HAVE_MPI_Attr_delete 1
#define PyMPI_HAVE_MPI_Copy_function 1
#define PyMPI_HAVE_MPI_Delete_function 1
#define PyMPI_HAVE_MPI_DUP_FN 1
#define PyMPI_HAVE_MPI_NULL_COPY_FN 1
#define PyMPI_HAVE_MPI_NULL_DELETE_FN 1
#define PyMPI_HAVE_MPI_Keyval_create 1
#define PyMPI_HAVE_MPI_Keyval_free 1

#define PyMPI_HAVE_MPI_SUCCESS 1
#define PyMPI_HAVE_MPI_ERR_LASTCODE 1
#define PyMPI_HAVE_MPI_ERR_COMM 1
#define PyMPI_HAVE_MPI_ERR_GROUP 1
#define PyMPI_HAVE_MPI_ERR_TYPE 1
#define PyMPI_HAVE_MPI_ERR_REQUEST 1
#define PyMPI_HAVE_MPI_ERR_OP 1
#define PyMPI_HAVE_MPI_ERR_BUFFER 1
#define PyMPI_HAVE_MPI_ERR_COUNT 1
#define PyMPI_HAVE_MPI_ERR_TAG 1
#define PyMPI_HAVE_MPI_ERR_RANK 1
#define PyMPI_HAVE_MPI_ERR_ROOT 1
#define PyMPI_HAVE_MPI_ERR_TRUNCATE 1
#define PyMPI_HAVE_MPI_ERR_IN_STATUS 1
#define PyMPI_HAVE_MPI_ERR_PENDING 1
#define PyMPI_HAVE_MPI_ERR_TOPOLOGY 1
#define PyMPI_HAVE_MPI_ERR_DIMS 1
#define PyMPI_HAVE_MPI_ERR_ARG 1
#define PyMPI_HAVE_MPI_ERR_OTHER 1
#define PyMPI_HAVE_MPI_ERR_UNKNOWN 1
#define PyMPI_HAVE_MPI_ERR_INTERN 1

#define PyMPI_HAVE_MPI_MAX_ERROR_STRING 1
#define PyMPI_HAVE_MPI_Error_class 1
#define PyMPI_HAVE_MPI_Error_string 1
