/*=========================================================================

  Program:   Visualization Toolkit
  Module:    vtkTableBasedClipCases.h

  Copyright (c) <PERSON>, <PERSON>, <PERSON>
  All rights reserved.
  See Copyright.txt or http://www.kitware.com/Copyright.htm for details.

     This software is distributed WITHOUT ANY WARRANTY; without even
     the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
     PURPOSE.  See the above copyright notice for more information.

=========================================================================*/

#ifndef vtkTableBasedClipCases_h
#define vtkTableBasedClipCases_h


/*****************************************************************************
*
* Copyright (c) 2000 - 2009, Lawrence Livermore National Security, LLC
* Produced at the Lawrence Livermore National Laboratory
* LLNL-CODE-400124
* All rights reserved.
*
* This file is a combination of several VisIt files containing the triangulation
* and clipping tables used in VisIt cutter and clipper / slicer. For details,
* see https://visit.llnl.gov/.  The full copyright notice is contained in the
* file COPYRIGHT located at the root of the VisIt distribution or at
* http://www.llnl.gov/visit/copyright.html.
*
*****************************************************************************/


// ============================================================================
// ====================== vtkTriangulationTables (begin) ======================
// ============================================================================

namespace vtkTableBasedClipperTriangulationTables
{

static const int TetTriangulationTable[16][7] = {
  {-1, -1, -1, -1, -1, -1, -1},
  { 0, 3, 2, -1, -1, -1, -1},
  { 0, 1, 4, -1, -1, -1, -1},
  { 3, 2, 4, 4, 2, 1, -1},
  { 1, 2, 5, -1, -1, -1, -1},
  { 3, 5, 1, 3, 1, 0, -1},
  { 0, 2, 5, 0, 5, 4, -1},
  { 3, 5, 4, -1, -1, -1, -1},
  { 3, 4, 5, -1, -1, -1, -1},
  { 0, 4, 5, 0, 5, 2, -1},
  { 0, 5, 3, 0, 1, 5, -1},
  { 5, 2, 1, -1, -1, -1, -1},
  { 3, 4, 1, 3, 1, 2, -1},
  { 0, 4, 1, -1, -1, -1, -1},
  { 0, 2, 3, -1, -1, -1, -1},
  {-1, -1, -1, -1, -1, -1, -1}
};

static const int TetVerticesFromEdges[6][2] =
{
    { 0, 1 },   /* Edge 0 */
    { 1, 2 },   /* Edge 1 */
    { 2, 0 },   /* Edge 2 */
    { 0, 3 },   /* Edge 3 */
    { 1, 3 },   /* Edge 4 */
    { 2, 3 }    /* Edge 5 */
};

static const int TetTriangleFaces[4][3] =
{
    { 0, 1, 3 },
    { 1, 2, 3 },
    { 2, 0, 3 },
    { 0, 2, 1 }
};

static const int PyramidTriangulationTable[32][13] = {
  {-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //0
  { 0,  4,  3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //1
  { 0,  1,  5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //2
  { 4,  1,  5,  4,  3,  1, -1, -1, -1, -1, -1, -1, -1}, //3
  { 1,  2,  6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //4
  { 0,  4,  3,  1,  2,  6, -1, -1, -1, -1, -1, -1, -1}, //5
  { 0,  2,  5,  5,  2,  6, -1, -1, -1, -1, -1, -1, -1}, //6
  { 4,  3,  2,  5,  4,  2,  6,  5,  2, -1, -1, -1, -1}, //7
  { 3,  7,  2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //8
  { 4,  7,  2,  2,  0,  4, -1, -1, -1, -1, -1, -1, -1}, //9
  { 0,  1,  5,  3,  7,  2, -1, -1, -1, -1, -1, -1, -1}, //10
  { 1,  5,  2,  2,  5,  4,  2,  4,  7, -1, -1, -1, -1}, //11
  { 1,  3,  6,  6,  3,  7, -1, -1, -1, -1, -1, -1, -1}, //12
  { 1,  7,  6,  1,  4,  7,  1,  0,  4, -1, -1, -1, -1}, //13
  { 0,  3,  5,  5,  3,  6,  6,  3,  7, -1, -1, -1, -1}, //14
  { 5,  4,  7,  6,  5,  7, -1, -1, -1, -1, -1, -1, -1}, //15
  { 5,  7,  4,  6,  7,  5, -1, -1, -1, -1, -1, -1, -1}, //16 *
  { 0,  5,  3,  5,  6,  3,  6,  7,  3, -1, -1, -1, -1}, //17 *
  { 0,  1,  4,  1,  7,  4,  1,  6,  7, -1, -1, -1, -1}, //18 *
  { 1,  6,  3,  6,  7,  3, -1, -1, -1, -1, -1, -1, -1}, //19 *
  { 1,  2,  5,  2,  4,  5,  2,  7,  4, -1, -1, -1, -1}, //20
  { 0,  7,  3,  0,  5,  7,  5,  2,  7,  5,  1,  2, -1}, //21
  { 0,  2,  4,  4,  2,  7, -1, -1, -1, -1, -1, -1, -1}, //22
  { 3,  2,  7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //23
  { 3,  4,  2,  2,  4,  5,  2,  5,  6, -1, -1, -1, -1}, //24
  { 0,  5,  2,  5,  6,  2, -1, -1, -1, -1, -1, -1, -1}, //25
  { 0,  1,  6,  0,  6,  4,  4,  6,  3,  6,  2,  3, -1}, //26
  { 1,  6,  2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //27
  { 3,  4,  1,  4,  5,  1, -1, -1, -1, -1, -1, -1, -1}, //28
  { 0,  5,  1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //29
  { 0,  3,  4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //30
  {-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}  //31
};

static const int PyramidVerticesFromEdges[8][2] =
{
    { 0, 1 },   /* Edge 0 */
    { 1, 2 },   /* Edge 1 */
    { 2, 3 },   /* Edge 2 */
    { 3, 0 },   /* Edge 3 */
    { 0, 4 },   /* Edge 4 */
    { 1, 4 },   /* Edge 5 */
    { 2, 4 },   /* Edge 6 */
    { 3, 4 }    /* Edge 7 */
};

static const int PyramidTriangleFaces[4][3] =
{
    { 0, 1, 4 },
    { 1, 2, 4 },
    { 2, 3, 4 },
    { 3, 0, 4 }
};

static const int PyramidQuadFaces[1][4] =
{
    { 0, 3, 2, 1 }
};

static const int WedgeTriangulationTable[64][13] = {
  {-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //0
  { 0,  6,  2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //1
  { 0,  1,  7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //2
  { 6,  1,  7,  6,  2,  1, -1, -1, -1, -1, -1, -1, -1}, //3
  { 1,  2,  8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //4
  { 6,  1,  0,  6,  8,  1, -1, -1, -1, -1, -1, -1, -1}, //5
  { 0,  2,  8,  7,  0,  8, -1, -1, -1, -1, -1, -1, -1}, //6
  { 7,  6,  8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //7
  { 3,  5,  6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //8
  { 3,  5,  0,  5,  2,  0, -1, -1, -1, -1, -1, -1, -1}, //9
  { 0,  1,  7,  6,  3,  5, -1, -1, -1, -1, -1, -1, -1}, //10
  { 1,  7,  3,  1,  3,  5,  1,  5,  2, -1, -1, -1, -1}, //11
  { 2,  8,  1,  6,  3,  5, -1, -1, -1, -1, -1, -1, -1}, //12
  { 0,  3,  1,  1,  3,  5,  1,  5,  8, -1, -1, -1, -1}, //13
  { 6,  3,  5,  0,  8,  7,  0,  2,  8, -1, -1, -1, -1}, //14
  { 7,  3,  5,  7,  5,  8, -1, -1, -1, -1, -1, -1, -1}, //15
  { 7,  4,  3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //16
  { 7,  4,  3,  0,  6,  2, -1, -1, -1, -1, -1, -1, -1}, //17
  { 0,  1,  3,  1,  4,  3, -1, -1, -1, -1, -1, -1, -1}, //18
  { 1,  4,  3,  1,  3,  6,  1,  6,  2, -1, -1, -1, -1}, //19
  { 7,  4,  3,  2,  8,  1, -1, -1, -1, -1, -1, -1, -1}, //20
  { 7,  4,  3,  6,  1,  0,  6,  8,  1, -1, -1, -1, -1}, //21
  { 0,  4,  3,  0,  8,  4,  0,  2,  8, -1, -1, -1, -1}, //22
  { 6,  8,  3,  3,  8,  4, -1, -1, -1, -1, -1, -1, -1}, //23
  { 6,  7,  4,  6,  4,  5, -1, -1, -1, -1, -1, -1, -1}, //24
  { 0,  7,  5,  7,  4,  5,  2,  0,  5, -1, -1, -1, -1}, //25
  { 1,  6,  0,  1,  5,  6,  1,  4,  5, -1, -1, -1, -1}, //26 *
  { 2,  1,  5,  5,  1,  4, -1, -1, -1, -1, -1, -1, -1}, //27
  { 2,  8,  1,  6,  7,  5,  7,  4,  5, -1, -1, -1, -1}, //28
  { 0,  7,  5,  7,  4,  5,  0,  5,  1,  1,  5,  8, -1}, //29
  { 0,  2,  8,  0,  8,  4,  0,  4,  5,  0,  5,  6, -1}, //30
  { 8,  4,  5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //31
  { 4,  8,  5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //32
  { 4,  8,  5,  0,  6,  2, -1, -1, -1, -1, -1, -1, -1}, //33
  { 4,  8,  5,  0,  1,  7, -1, -1, -1, -1, -1, -1, -1}, //34
  { 4,  8,  5,  6,  1,  7,  6,  2,  1, -1, -1, -1, -1}, //35
  { 1,  5,  4,  2,  5,  1, -1, -1, -1, -1, -1, -1, -1}, //36
  { 1,  5,  4,  1,  6,  5,  1,  0,  6, -1, -1, -1, -1}, //37
  { 5,  4,  7,  5,  7,  0,  5,  0,  2, -1, -1, -1, -1}, //38 *
  { 6,  4,  7,  6,  5,  4, -1, -1, -1, -1, -1, -1, -1}, //39 *
  { 6,  3,  8,  3,  4,  8, -1, -1, -1, -1, -1, -1, -1}, //40 *
  { 0,  3,  4,  0,  4,  8,  0,  8,  2, -1, -1, -1, -1}, //41 *
  { 7,  0,  1,  6,  3,  4,  6,  4,  8, -1, -1, -1, -1}, //42
  { 1,  7,  3,  1,  3,  2,  2,  3,  8,  8,  3,  4, -1}, //43 *
  { 2,  6,  1,  6,  3,  1,  3,  4,  1, -1, -1, -1, -1}, //44 *
  { 0,  3,  1,  1,  3,  4, -1, -1, -1, -1, -1, -1, -1}, //45 *
  { 7,  0,  4,  4,  0,  2,  4,  2,  3,  3,  2,  6, -1}, //46 *
  { 7,  3,  4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //47 *
  { 7,  8,  5,  7,  5,  3, -1, -1, -1, -1, -1, -1, -1}, //48
  { 0,  6,  2,  7,  8,  5,  7,  5,  3, -1, -1, -1, -1}, //49
  { 0,  1,  3,  1,  5,  3,  1,  8,  5, -1, -1, -1, -1}, //50
  { 2,  1,  6,  6,  1,  3,  5,  1,  8,  3,  1,  5, -1}, //51
  { 1,  3,  7,  1,  5,  3,  1,  2,  5, -1, -1, -1, -1}, //52
  { 1,  0,  6,  1,  6,  5,  1,  5,  7,  7,  5,  3, -1}, //53
  { 0,  2,  5,  0,  5,  3, -1, -1, -1, -1, -1, -1, -1}, //54
  { 3,  6,  5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //55
  { 7,  8,  6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //56
  { 0,  7,  8,  0,  8,  2, -1, -1, -1, -1, -1, -1, -1}, //57
  { 0,  1,  6,  1,  8,  6, -1, -1, -1, -1, -1, -1, -1}, //58
  { 2,  1,  8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //59
  { 6,  7,  1,  6,  1,  2, -1, -1, -1, -1, -1, -1, -1}, //60 *
  { 0,  7,  1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //61 *
  { 0,  2,  6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}, //62 *
  {-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}  //63
};

static const int WedgeVerticesFromEdges[9][2] =
{
    { 0, 1 },   /* Edge 0 */
    { 1, 2 },   /* Edge 1 */
    { 2, 0 },   /* Edge 2 */
    { 3, 4 },   /* Edge 3 */
    { 4, 5 },   /* Edge 4 */
    { 5, 3 },   /* Edge 5 */
    { 0, 3 },   /* Edge 6 */
    { 1, 4 },   /* Edge 7 */
    { 2, 5 },   /* Edge 8 */
};

static const int WedgeTriangleFaces[2][3] =
{
    { 0, 1, 2 },
    { 3, 5, 4 }
};

static const int WedgeQuadFaces[3][4] =
{
    { 0, 3, 4, 1 },
    { 1, 4, 5, 2 },
    { 2, 5, 3, 0 }
};

static const int HexTriangulationTable[256][16] = {
{-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 0 */
{ 0, 3, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 1 */
{ 0, 9, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 2 */
{ 1, 3, 8, 9, 1, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},            /* 3 */
{ 1, 11, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 4 */
{ 0, 3, 8, 1, 11, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 5 */
{ 9, 11, 2, 0, 9, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 6 */
{ 2, 3, 8, 2, 8, 11, 11, 8, 9, -1, -1, -1, -1, -1, -1, -1},             /* 7 */
{ 3, 2, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 8 */
{ 0, 2, 10, 8, 0, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 9 */
{ 1, 0, 9, 2, 10, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 10 */
{ 1, 2, 10, 1, 10, 9, 9, 10, 8, -1, -1, -1, -1, -1, -1, -1},           /* 11 */
{ 3, 1, 11, 10, 3, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 12 */
{ 0, 1, 11, 0, 11, 8, 8, 11, 10, -1, -1, -1, -1, -1, -1, -1},          /* 13 */
{ 3, 0, 9, 3, 9, 10, 10, 9, 11, -1, -1, -1, -1, -1, -1, -1},           /* 14 */
{ 9, 11, 8, 11, 10, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 15 */
{ 4, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 16 */
{ 4, 0, 3, 7, 4, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 17 */
{ 0, 9, 1, 8, 7, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 18 */
{ 4, 9, 1, 4, 1, 7, 7, 1, 3, -1, -1, -1, -1, -1, -1, -1},              /* 19 */
{ 1, 11, 2, 8, 7, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 20 */
{ 3, 7, 4, 3, 4, 0, 1, 11, 2, -1, -1, -1, -1, -1, -1, -1},             /* 21 */
{ 9, 11, 2, 9, 2, 0, 8, 7, 4, -1, -1, -1, -1, -1, -1, -1},             /* 22 */
{ 2, 9, 11, 2, 7, 9, 2, 3, 7, 7, 4, 9, -1, -1, -1, -1},                /* 23 */
{ 8, 7, 4, 3, 2, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 24 */
{10, 7, 4, 10, 4, 2, 2, 4, 0, -1, -1, -1, -1, -1, -1, -1},             /* 25 */
{ 9, 1, 0, 8, 7, 4, 2, 10, 3, -1, -1, -1, -1, -1, -1, -1},             /* 26 */
{ 4, 10, 7, 9, 10, 4, 9, 2, 10, 9, 1, 2, -1, -1, -1, -1},              /* 27 */
{ 3, 1, 11, 3, 11, 10, 7, 4, 8, -1, -1, -1, -1, -1, -1, -1},           /* 28 */
{ 1, 11, 10, 1, 10, 4, 1, 4, 0, 7, 4, 10, -1, -1, -1, -1},             /* 29 */
{ 4, 8, 7, 9, 10, 0, 9, 11, 10, 10, 3, 0, -1, -1, -1, -1},             /* 30 */
{ 4, 10, 7, 4, 9, 10, 9, 11, 10, -1, -1, -1, -1, -1, -1, -1},          /* 31 */
{ 9, 4, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 32 */
{ 9, 4, 5, 0, 3, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 33 */
{ 0, 4, 5, 1, 0, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 34 */
{ 8, 4, 5, 8, 5, 3, 3, 5, 1, -1, -1, -1, -1, -1, -1, -1},              /* 35 */
{ 1, 11, 2, 9, 4, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 36 */
{ 3, 8, 0, 1, 11, 2, 4, 5, 9, -1, -1, -1, -1, -1, -1, -1},             /* 37 */
{ 5, 11, 2, 5, 2, 4, 4, 2, 0, -1, -1, -1, -1, -1, -1, -1},             /* 38 */
{ 2, 5, 11, 3, 5, 2, 3, 4, 5, 3, 8, 4, -1, -1, -1, -1},                /* 39 */
{ 9, 4, 5, 2, 10, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 40 */
{ 0, 2, 10, 0, 10, 8, 4, 5, 9, -1, -1, -1, -1, -1, -1, -1},            /* 41 */
{ 0, 4, 5, 0, 5, 1, 2, 10, 3, -1, -1, -1, -1, -1, -1, -1},             /* 42 */
{ 2, 5, 1, 2, 8, 5, 2, 10, 8, 4, 5, 8, -1, -1, -1, -1},                /* 43 */
{11, 10, 3, 11, 3, 1, 9, 4, 5, -1, -1, -1, -1, -1, -1, -1},            /* 44 */
{ 4, 5, 9, 0, 1, 8, 8, 1, 11, 8, 11, 10, -1, -1, -1, -1},              /* 45 */
{ 5, 0, 4, 5, 10, 0, 5, 11, 10, 10, 3, 0, -1, -1, -1, -1},             /* 46 */
{ 5, 8, 4, 5, 11, 8, 11, 10, 8, -1, -1, -1, -1, -1, -1, -1},           /* 47 */
{ 9, 8, 7, 5, 9, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 48 */
{ 9, 0, 3, 9, 3, 5, 5, 3, 7, -1, -1, -1, -1, -1, -1, -1},              /* 49 */
{ 0, 8, 7, 0, 7, 1, 1, 7, 5, -1, -1, -1, -1, -1, -1, -1},              /* 50 */
{ 1, 3, 5, 3, 7, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 51 */
{ 9, 8, 7, 9, 7, 5, 11, 2, 1, -1, -1, -1, -1, -1, -1, -1},             /* 52 */
{11, 2, 1, 9, 0, 5, 5, 0, 3, 5, 3, 7, -1, -1, -1, -1},                 /* 53 */
{ 8, 2, 0, 8, 5, 2, 8, 7, 5, 11, 2, 5, -1, -1, -1, -1},                /* 54 */
{ 2, 5, 11, 2, 3, 5, 3, 7, 5, -1, -1, -1, -1, -1, -1, -1},             /* 55 */
{ 7, 5, 9, 7, 9, 8, 3, 2, 10, -1, -1, -1, -1, -1, -1, -1},             /* 56 */
{ 9, 7, 5, 9, 2, 7, 9, 0, 2, 2, 10, 7, -1, -1, -1, -1},                /* 57 */
{ 2, 10, 3, 0, 8, 1, 1, 8, 7, 1, 7, 5, -1, -1, -1, -1},                /* 58 */
{10, 1, 2, 10, 7, 1, 7, 5, 1, -1, -1, -1, -1, -1, -1, -1},             /* 59 */
{ 9, 8, 5, 8, 7, 5, 11, 3, 1, 11, 10, 3, -1, -1, -1, -1},              /* 60 */
{ 5, 0, 7, 5, 9, 0, 7, 0, 10, 1, 11, 0, 10, 0, 11, -1},                /* 61 */
{10, 0, 11, 10, 3, 0, 11, 0, 5, 8, 7, 0, 5, 0, 7, -1},                 /* 62 */
{10, 5, 11, 7, 5, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 63 */
{11, 5, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 64 */
{ 0, 3, 8, 5, 6, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 65 */
{ 9, 1, 0, 5, 6, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 66 */
{ 1, 3, 8, 1, 8, 9, 5, 6, 11, -1, -1, -1, -1, -1, -1, -1},             /* 67 */
{ 1, 5, 6, 2, 1, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 68 */
{ 1, 5, 6, 1, 6, 2, 3, 8, 0, -1, -1, -1, -1, -1, -1, -1},              /* 69 */
{ 9, 5, 6, 9, 6, 0, 0, 6, 2, -1, -1, -1, -1, -1, -1, -1},              /* 70 */
{ 5, 8, 9, 5, 2, 8, 5, 6, 2, 3, 8, 2, -1, -1, -1, -1},                 /* 71 */
{ 2, 10, 3, 11, 5, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 72 */
{10, 8, 0, 10, 0, 2, 11, 5, 6, -1, -1, -1, -1, -1, -1, -1},            /* 73 */
{ 0, 9, 1, 2, 10, 3, 5, 6, 11, -1, -1, -1, -1, -1, -1, -1},            /* 74 */
{ 5, 6, 11, 1, 2, 9, 9, 2, 10, 9, 10, 8, -1, -1, -1, -1},              /* 75 */
{ 6, 10, 3, 6, 3, 5, 5, 3, 1, -1, -1, -1, -1, -1, -1, -1},             /* 76 */
{ 0, 10, 8, 0, 5, 10, 0, 1, 5, 5, 6, 10, -1, -1, -1, -1},              /* 77 */
{ 3, 6, 10, 0, 6, 3, 0, 5, 6, 0, 9, 5, -1, -1, -1, -1},                /* 78 */
{ 6, 9, 5, 6, 10, 9, 10, 8, 9, -1, -1, -1, -1, -1, -1, -1},            /* 79 */
{ 5, 6, 11, 4, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 80 */
{ 4, 0, 3, 4, 3, 7, 6, 11, 5, -1, -1, -1, -1, -1, -1, -1},             /* 81 */
{ 1, 0, 9, 5, 6, 11, 8, 7, 4, -1, -1, -1, -1, -1, -1, -1},             /* 82 */
{11, 5, 6, 1, 7, 9, 1, 3, 7, 7, 4, 9, -1, -1, -1, -1},                 /* 83 */
{ 6, 2, 1, 6, 1, 5, 4, 8, 7, -1, -1, -1, -1, -1, -1, -1},              /* 84 */
{ 1, 5, 2, 5, 6, 2, 3, 4, 0, 3, 7, 4, -1, -1, -1, -1},                 /* 85 */
{ 8, 7, 4, 9, 5, 0, 0, 5, 6, 0, 6, 2, -1, -1, -1, -1},                 /* 86 */
{ 7, 9, 3, 7, 4, 9, 3, 9, 2, 5, 6, 9, 2, 9, 6, -1},                    /* 87 */
{ 3, 2, 10, 7, 4, 8, 11, 5, 6, -1, -1, -1, -1, -1, -1, -1},            /* 88 */
{ 5, 6, 11, 4, 2, 7, 4, 0, 2, 2, 10, 7, -1, -1, -1, -1},               /* 89 */
{ 0, 9, 1, 4, 8, 7, 2, 10, 3, 5, 6, 11, -1, -1, -1, -1},               /* 90 */
{ 9, 1, 2, 9, 2, 10, 9, 10, 4, 7, 4, 10, 5, 6, 11, -1},                /* 91 */
{ 8, 7, 4, 3, 5, 10, 3, 1, 5, 5, 6, 10, -1, -1, -1, -1},               /* 92 */
{ 5, 10, 1, 5, 6, 10, 1, 10, 0, 7, 4, 10, 0, 10, 4, -1},               /* 93 */
{ 0, 9, 5, 0, 5, 6, 0, 6, 3, 10, 3, 6, 8, 7, 4, -1},                   /* 94 */
{ 6, 9, 5, 6, 10, 9, 4, 9, 7, 7, 9, 10, -1, -1, -1, -1},               /* 95 */
{11, 9, 4, 6, 11, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 96 */
{ 4, 6, 11, 4, 11, 9, 0, 3, 8, -1, -1, -1, -1, -1, -1, -1},            /* 97 */
{11, 1, 0, 11, 0, 6, 6, 0, 4, -1, -1, -1, -1, -1, -1, -1},             /* 98 */
{ 8, 1, 3, 8, 6, 1, 8, 4, 6, 6, 11, 1, -1, -1, -1, -1},                /* 99 */
{ 1, 9, 4, 1, 4, 2, 2, 4, 6, -1, -1, -1, -1, -1, -1, -1},             /* 100 */
{ 3, 8, 0, 1, 9, 2, 2, 9, 4, 2, 4, 6, -1, -1, -1, -1},                /* 101 */
{ 0, 4, 2, 4, 6, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 102 */
{ 8, 2, 3, 8, 4, 2, 4, 6, 2, -1, -1, -1, -1, -1, -1, -1},             /* 103 */
{11, 9, 4, 11, 4, 6, 10, 3, 2, -1, -1, -1, -1, -1, -1, -1},           /* 104 */
{ 0, 2, 8, 2, 10, 8, 4, 11, 9, 4, 6, 11, -1, -1, -1, -1},             /* 105 */
{ 3, 2, 10, 0, 6, 1, 0, 4, 6, 6, 11, 1, -1, -1, -1, -1},              /* 106 */
{ 6, 1, 4, 6, 11, 1, 4, 1, 8, 2, 10, 1, 8, 1, 10, -1},                /* 107 */
{ 9, 4, 6, 9, 6, 3, 9, 3, 1, 10, 3, 6, -1, -1, -1, -1},               /* 108 */
{ 8, 1, 10, 8, 0, 1, 10, 1, 6, 9, 4, 1, 6, 1, 4, -1},                 /* 109 */
{ 3, 6, 10, 3, 0, 6, 0, 4, 6, -1, -1, -1, -1, -1, -1, -1},            /* 110 */
{ 6, 8, 4, 10, 8, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 111 */
{ 7, 6, 11, 7, 11, 8, 8, 11, 9, -1, -1, -1, -1, -1, -1, -1},          /* 112 */
{ 0, 3, 7, 0, 7, 11, 0, 11, 9, 6, 11, 7, -1, -1, -1, -1},             /* 113 */
{11, 7, 6, 1, 7, 11, 1, 8, 7, 1, 0, 8, -1, -1, -1, -1},               /* 114 */
{11, 7, 6, 11, 1, 7, 1, 3, 7, -1, -1, -1, -1, -1, -1, -1},            /* 115 */
{ 1, 6, 2, 1, 8, 6, 1, 9, 8, 8, 7, 6, -1, -1, -1, -1},                /* 116 */
{ 2, 9, 6, 2, 1, 9, 6, 9, 7, 0, 3, 9, 7, 9, 3, -1},                   /* 117 */
{ 7, 0, 8, 7, 6, 0, 6, 2, 0, -1, -1, -1, -1, -1, -1, -1},             /* 118 */
{ 7, 2, 3, 6, 2, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 119 */
{ 2, 10, 3, 11, 8, 6, 11, 9, 8, 8, 7, 6, -1, -1, -1, -1},             /* 120 */
{ 2, 7, 0, 2, 10, 7, 0, 7, 9, 6, 11, 7, 9, 7, 11, -1},                /* 121 */
{ 1, 0, 8, 1, 8, 7, 1, 7, 11, 6, 11, 7, 2, 10, 3, -1},                /* 122 */
{10, 1, 2, 10, 7, 1, 11, 1, 6, 6, 1, 7, -1, -1, -1, -1},              /* 123 */
{ 8, 6, 9, 8, 7, 6, 9, 6, 1, 10, 3, 6, 1, 6, 3, -1},                  /* 124 */
{ 0, 1, 9, 10, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 125 */
{ 7, 0, 8, 7, 6, 0, 3, 0, 10, 10, 0, 6, -1, -1, -1, -1},              /* 126 */
{ 7, 6, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},      /* 127 */
{ 7, 10, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},      /* 128 */
{ 3, 8, 0, 10, 6, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 129 */
{ 0, 9, 1, 10, 6, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 130 */
{ 8, 9, 1, 8, 1, 3, 10, 6, 7, -1, -1, -1, -1, -1, -1, -1},            /* 131 */
{11, 2, 1, 6, 7, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 132 */
{ 1, 11, 2, 3, 8, 0, 6, 7, 10, -1, -1, -1, -1, -1, -1, -1},           /* 133 */
{ 2, 0, 9, 2, 9, 11, 6, 7, 10, -1, -1, -1, -1, -1, -1, -1},           /* 134 */
{ 6, 7, 10, 2, 3, 11, 11, 3, 8, 11, 8, 9, -1, -1, -1, -1},            /* 135 */
{ 7, 3, 2, 6, 7, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 136 */
{ 7, 8, 0, 7, 0, 6, 6, 0, 2, -1, -1, -1, -1, -1, -1, -1},             /* 137 */
{ 2, 6, 7, 2, 7, 3, 0, 9, 1, -1, -1, -1, -1, -1, -1, -1},             /* 138 */
{ 1, 2, 6, 1, 6, 8, 1, 8, 9, 8, 6, 7, -1, -1, -1, -1},                /* 139 */
{11, 6, 7, 11, 7, 1, 1, 7, 3, -1, -1, -1, -1, -1, -1, -1},            /* 140 */
{11, 6, 7, 1, 11, 7, 1, 7, 8, 1, 8, 0, -1, -1, -1, -1},               /* 141 */
{ 0, 7, 3, 0, 11, 7, 0, 9, 11, 6, 7, 11, -1, -1, -1, -1},             /* 142 */
{ 7, 11, 6, 7, 8, 11, 8, 9, 11, -1, -1, -1, -1, -1, -1, -1},          /* 143 */
{ 6, 4, 8, 10, 6, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 144 */
{ 3, 10, 6, 3, 6, 0, 0, 6, 4, -1, -1, -1, -1, -1, -1, -1},            /* 145 */
{ 8, 10, 6, 8, 6, 4, 9, 1, 0, -1, -1, -1, -1, -1, -1, -1},            /* 146 */
{ 9, 6, 4, 9, 3, 6, 9, 1, 3, 10, 6, 3, -1, -1, -1, -1},               /* 147 */
{ 6, 4, 8, 6, 8, 10, 2, 1, 11, -1, -1, -1, -1, -1, -1, -1},           /* 148 */
{ 1, 11, 2, 3, 10, 0, 0, 10, 6, 0, 6, 4, -1, -1, -1, -1},             /* 149 */
{ 4, 8, 10, 4, 10, 6, 0, 9, 2, 2, 9, 11, -1, -1, -1, -1},             /* 150 */
{11, 3, 9, 11, 2, 3, 9, 3, 4, 10, 6, 3, 4, 3, 6, -1},                 /* 151 */
{ 8, 3, 2, 8, 2, 4, 4, 2, 6, -1, -1, -1, -1, -1, -1, -1},             /* 152 */
{ 0, 2, 4, 4, 2, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 153 */
{ 1, 0, 9, 2, 4, 3, 2, 6, 4, 4, 8, 3, -1, -1, -1, -1},                /* 154 */
{ 1, 4, 9, 1, 2, 4, 2, 6, 4, -1, -1, -1, -1, -1, -1, -1},             /* 155 */
{ 8, 3, 1, 8, 1, 6, 8, 6, 4, 6, 1, 11, -1, -1, -1, -1},               /* 156 */
{11, 0, 1, 11, 6, 0, 6, 4, 0, -1, -1, -1, -1, -1, -1, -1},            /* 157 */
{ 4, 3, 6, 4, 8, 3, 6, 3, 11, 0, 9, 3, 11, 3, 9, -1},                 /* 158 */
{11, 4, 9, 6, 4, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 159 */
{ 4, 5, 9, 7, 10, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 160 */
{ 0, 3, 8, 4, 5, 9, 10, 6, 7, -1, -1, -1, -1, -1, -1, -1},            /* 161 */
{ 5, 1, 0, 5, 0, 4, 7, 10, 6, -1, -1, -1, -1, -1, -1, -1},            /* 162 */
{10, 6, 7, 8, 4, 3, 3, 4, 5, 3, 5, 1, -1, -1, -1, -1},                /* 163 */
{ 9, 4, 5, 11, 2, 1, 7, 10, 6, -1, -1, -1, -1, -1, -1, -1},           /* 164 */
{ 6, 7, 10, 1, 11, 2, 0, 3, 8, 4, 5, 9, -1, -1, -1, -1},              /* 165 */
{ 7, 10, 6, 5, 11, 4, 4, 11, 2, 4, 2, 0, -1, -1, -1, -1},             /* 166 */
{ 3, 8, 4, 3, 4, 5, 3, 5, 2, 11, 2, 5, 10, 6, 7, -1},                 /* 167 */
{ 7, 3, 2, 7, 2, 6, 5, 9, 4, -1, -1, -1, -1, -1, -1, -1},             /* 168 */
{ 9, 4, 5, 0, 6, 8, 0, 2, 6, 6, 7, 8, -1, -1, -1, -1},                /* 169 */
{ 3, 2, 6, 3, 6, 7, 1, 0, 5, 5, 0, 4, -1, -1, -1, -1},                /* 170 */
{ 6, 8, 2, 6, 7, 8, 2, 8, 1, 4, 5, 8, 1, 8, 5, -1},                   /* 171 */
{ 9, 4, 5, 11, 6, 1, 1, 6, 7, 1, 7, 3, -1, -1, -1, -1},               /* 172 */
{ 1, 11, 6, 1, 6, 7, 1, 7, 0, 8, 0, 7, 9, 4, 5, -1},                  /* 173 */
{ 4, 11, 0, 4, 5, 11, 0, 11, 3, 6, 7, 11, 3, 11, 7, -1},              /* 174 */
{ 7, 11, 6, 7, 8, 11, 5, 11, 4, 4, 11, 8, -1, -1, -1, -1},            /* 175 */
{ 6, 5, 9, 6, 9, 10, 10, 9, 8, -1, -1, -1, -1, -1, -1, -1},           /* 176 */
{ 3, 10, 6, 0, 3, 6, 0, 6, 5, 0, 5, 9, -1, -1, -1, -1},               /* 177 */
{ 0, 8, 10, 0, 10, 5, 0, 5, 1, 5, 10, 6, -1, -1, -1, -1},             /* 178 */
{ 6, 3, 10, 6, 5, 3, 5, 1, 3, -1, -1, -1, -1, -1, -1, -1},            /* 179 */
{ 1, 11, 2, 9, 10, 5, 9, 8, 10, 10, 6, 5, -1, -1, -1, -1},            /* 180 */
{ 0, 3, 10, 0, 10, 6, 0, 6, 9, 5, 9, 6, 1, 11, 2, -1},                /* 181 */
{10, 5, 8, 10, 6, 5, 8, 5, 0, 11, 2, 5, 0, 5, 2, -1},                 /* 182 */
{ 6, 3, 10, 6, 5, 3, 2, 3, 11, 11, 3, 5, -1, -1, -1, -1},             /* 183 */
{ 5, 9, 8, 5, 8, 2, 5, 2, 6, 3, 2, 8, -1, -1, -1, -1},                /* 184 */
{ 9, 6, 5, 9, 0, 6, 0, 2, 6, -1, -1, -1, -1, -1, -1, -1},             /* 185 */
{ 1, 8, 5, 1, 0, 8, 5, 8, 6, 3, 2, 8, 6, 8, 2, -1},                   /* 186 */
{ 1, 6, 5, 2, 6, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 187 */
{ 1, 6, 3, 1, 11, 6, 3, 6, 8, 5, 9, 6, 8, 6, 9, -1},                  /* 188 */
{11, 0, 1, 11, 6, 0, 9, 0, 5, 5, 0, 6, -1, -1, -1, -1},               /* 189 */
{ 0, 8, 3, 5, 11, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 190 */
{11, 6, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 191 */
{10, 11, 5, 7, 10, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 192 */
{10, 11, 5, 10, 5, 7, 8, 0, 3, -1, -1, -1, -1, -1, -1, -1},           /* 193 */
{ 5, 7, 10, 5, 10, 11, 1, 0, 9, -1, -1, -1, -1, -1, -1, -1},          /* 194 */
{11, 5, 7, 11, 7, 10, 9, 1, 8, 8, 1, 3, -1, -1, -1, -1},              /* 195 */
{10, 2, 1, 10, 1, 7, 7, 1, 5, -1, -1, -1, -1, -1, -1, -1},            /* 196 */
{ 0, 3, 8, 1, 7, 2, 1, 5, 7, 7, 10, 2, -1, -1, -1, -1},               /* 197 */
{ 9, 5, 7, 9, 7, 2, 9, 2, 0, 2, 7, 10, -1, -1, -1, -1},               /* 198 */
{ 7, 2, 5, 7, 10, 2, 5, 2, 9, 3, 8, 2, 9, 2, 8, -1},                  /* 199 */
{ 2, 11, 5, 2, 5, 3, 3, 5, 7, -1, -1, -1, -1, -1, -1, -1},            /* 200 */
{ 8, 0, 2, 8, 2, 5, 8, 5, 7, 11, 5, 2, -1, -1, -1, -1},               /* 201 */
{ 9, 1, 0, 5, 3, 11, 5, 7, 3, 3, 2, 11, -1, -1, -1, -1},              /* 202 */
{ 9, 2, 8, 9, 1, 2, 8, 2, 7, 11, 5, 2, 7, 2, 5, -1},                  /* 203 */
{ 1, 5, 3, 3, 5, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 204 */
{ 0, 7, 8, 0, 1, 7, 1, 5, 7, -1, -1, -1, -1, -1, -1, -1},             /* 205 */
{ 9, 3, 0, 9, 5, 3, 5, 7, 3, -1, -1, -1, -1, -1, -1, -1},             /* 206 */
{ 9, 7, 8, 5, 7, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 207 */
{ 5, 4, 8, 5, 8, 11, 11, 8, 10, -1, -1, -1, -1, -1, -1, -1},          /* 208 */
{ 5, 4, 0, 5, 0, 10, 5, 10, 11, 10, 0, 3, -1, -1, -1, -1},            /* 209 */
{ 0, 9, 1, 8, 11, 4, 8, 10, 11, 11, 5, 4, -1, -1, -1, -1},            /* 210 */
{11, 4, 10, 11, 5, 4, 10, 4, 3, 9, 1, 4, 3, 4, 1, -1},                /* 211 */
{ 2, 1, 5, 2, 5, 8, 2, 8, 10, 4, 8, 5, -1, -1, -1, -1},               /* 212 */
{ 0, 10, 4, 0, 3, 10, 4, 10, 5, 2, 1, 10, 5, 10, 1, -1},              /* 213 */
{ 0, 5, 2, 0, 9, 5, 2, 5, 10, 4, 8, 5, 10, 5, 8, -1},                 /* 214 */
{ 9, 5, 4, 2, 3, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 215 */
{ 2, 11, 5, 3, 2, 5, 3, 5, 4, 3, 4, 8, -1, -1, -1, -1},               /* 216 */
{ 5, 2, 11, 5, 4, 2, 4, 0, 2, -1, -1, -1, -1, -1, -1, -1},            /* 217 */
{ 3, 2, 11, 3, 11, 5, 3, 5, 8, 4, 8, 5, 0, 9, 1, -1},                 /* 218 */
{ 5, 2, 11, 5, 4, 2, 1, 2, 9, 9, 2, 4, -1, -1, -1, -1},               /* 219 */
{ 8, 5, 4, 8, 3, 5, 3, 1, 5, -1, -1, -1, -1, -1, -1, -1},             /* 220 */
{ 0, 5, 4, 1, 5, 0, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 221 */
{ 8, 5, 4, 8, 3, 5, 9, 5, 0, 0, 5, 3, -1, -1, -1, -1},                /* 222 */
{ 9, 5, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 223 */
{ 4, 7, 10, 4, 10, 9, 9, 10, 11, -1, -1, -1, -1, -1, -1, -1},         /* 224 */
{ 0, 3, 8, 4, 7, 9, 9, 7, 10, 9, 10, 11, -1, -1, -1, -1},             /* 225 */
{ 1, 10, 11, 1, 4, 10, 1, 0, 4, 7, 10, 4, -1, -1, -1, -1},            /* 226 */
{ 3, 4, 1, 3, 8, 4, 1, 4, 11, 7, 10, 4, 11, 4, 10, -1},               /* 227 */
{ 4, 7, 10, 9, 4, 10, 9, 10, 2, 9, 2, 1, -1, -1, -1, -1},             /* 228 */
{ 9, 4, 7, 9, 7, 10, 9, 10, 1, 2, 1, 10, 0, 3, 8, -1},                /* 229 */
{10, 4, 7, 10, 2, 4, 2, 0, 4, -1, -1, -1, -1, -1, -1, -1},            /* 230 */
{10, 4, 7, 10, 2, 4, 8, 4, 3, 3, 4, 2, -1, -1, -1, -1},               /* 231 */
{ 2, 11, 9, 2, 9, 7, 2, 7, 3, 7, 9, 4, -1, -1, -1, -1},               /* 232 */
{ 9, 7, 11, 9, 4, 7, 11, 7, 2, 8, 0, 7, 2, 7, 0, -1},                 /* 233 */
{ 3, 11, 7, 3, 2, 11, 7, 11, 4, 1, 0, 11, 4, 11, 0, -1},              /* 234 */
{ 1, 2, 11, 8, 4, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 235 */
{ 4, 1, 9, 4, 7, 1, 7, 3, 1, -1, -1, -1, -1, -1, -1, -1},             /* 236 */
{ 4, 1, 9, 4, 7, 1, 0, 1, 8, 8, 1, 7, -1, -1, -1, -1},                /* 237 */
{ 4, 3, 0, 7, 3, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 238 */
{ 4, 7, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 239 */
{ 9, 8, 11, 11, 8, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 240 */
{ 3, 9, 0, 3, 10, 9, 10, 11, 9, -1, -1, -1, -1, -1, -1, -1},          /* 241 */
{ 0, 11, 1, 0, 8, 11, 8, 10, 11, -1, -1, -1, -1, -1, -1, -1},         /* 242 */
{ 3, 11, 1, 10, 11, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 243 */
{ 1, 10, 2, 1, 9, 10, 9, 8, 10, -1, -1, -1, -1, -1, -1, -1},          /* 244 */
{ 3, 9, 0, 3, 10, 9, 1, 9, 2, 2, 9, 10, -1, -1, -1, -1},              /* 245 */
{ 0, 10, 2, 8, 10, 0, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 246 */
{ 3, 10, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},      /* 247 */
{ 2, 8, 3, 2, 11, 8, 11, 9, 8, -1, -1, -1, -1, -1, -1, -1},           /* 248 */
{ 9, 2, 11, 0, 2, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 249 */
{ 2, 8, 3, 2, 11, 8, 0, 8, 1, 1, 8, 11, -1, -1, -1, -1},              /* 250 */
{ 1, 2, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},      /* 251 */
{ 1, 8, 3, 9, 8, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 252 */
{ 0, 1, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 253 */
{ 0, 8, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 254 */
{-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}      /* 255 */
};

static const int HexVerticesFromEdges[12][2] =
{
    { 0, 1 },   /* Edge 0 */
    { 1, 2 },   /* Edge 1 */
    { 2, 3 },   /* Edge 2 */
    { 3, 0 },   /* Edge 3 */
    { 4, 5 },   /* Edge 4 */
    { 5, 6 },   /* Edge 5 */
    { 6, 7 },   /* Edge 6 */
    { 7, 4 },   /* Edge 7 */
    { 0, 4 },   /* Edge 8 */
    { 1, 5 },   /* Edge 9 */
    { 3, 7 },   /* Edge 10 */
    { 2, 6 }    /* Edge 11 */
};

static const int HexQuadFaces[6][4] =
{
    { 0, 4, 7, 3 },
    { 1, 2, 6, 5 },
    { 0, 1, 5, 4 },
    { 3, 7, 6, 2 },
    { 0, 3, 2, 1 },
    { 4, 5, 6, 7 }
};

static const int VoxTriangulationTable[256][16] = {
{-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 0 */
{ 0, 3, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 1 */
{ 0, 9, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 2 */
{ 1, 3, 8, 9, 1, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},            /* 3 */
{ 1, 11, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 4 */
{ 0, 3, 8, 1, 11, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 5 */
{ 9, 11, 2, 0, 9, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 6 */
{ 2, 3, 8, 2, 8, 11, 11, 8, 9, -1, -1, -1, -1, -1, -1, -1},             /* 7 */
{ 3, 2, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 8 */
{ 0, 2, 10, 8, 0, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 9 */
{ 1, 0, 9, 2, 10, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 10 */
{ 1, 2, 10, 1, 10, 9, 9, 10, 8, -1, -1, -1, -1, -1, -1, -1},           /* 11 */
{ 3, 1, 11, 10, 3, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 12 */
{ 0, 1, 11, 0, 11, 8, 8, 11, 10, -1, -1, -1, -1, -1, -1, -1},          /* 13 */
{ 3, 0, 9, 3, 9, 10, 10, 9, 11, -1, -1, -1, -1, -1, -1, -1},           /* 14 */
{ 9, 11, 8, 11, 10, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 15 */
{ 4, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 16 */
{ 4, 0, 3, 7, 4, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 17 */
{ 0, 9, 1, 8, 7, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 18 */
{ 4, 9, 1, 4, 1, 7, 7, 1, 3, -1, -1, -1, -1, -1, -1, -1},              /* 19 */
{ 1, 11, 2, 8, 7, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 20 */
{ 3, 7, 4, 3, 4, 0, 1, 11, 2, -1, -1, -1, -1, -1, -1, -1},             /* 21 */
{ 9, 11, 2, 9, 2, 0, 8, 7, 4, -1, -1, -1, -1, -1, -1, -1},             /* 22 */
{ 2, 9, 11, 2, 7, 9, 2, 3, 7, 7, 4, 9, -1, -1, -1, -1},                /* 23 */
{ 8, 7, 4, 3, 2, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 24 */
{10, 7, 4, 10, 4, 2, 2, 4, 0, -1, -1, -1, -1, -1, -1, -1},             /* 25 */
{ 9, 1, 0, 8, 7, 4, 2, 10, 3, -1, -1, -1, -1, -1, -1, -1},             /* 26 */
{ 4, 10, 7, 9, 10, 4, 9, 2, 10, 9, 1, 2, -1, -1, -1, -1},              /* 27 */
{ 3, 1, 11, 3, 11, 10, 7, 4, 8, -1, -1, -1, -1, -1, -1, -1},           /* 28 */
{ 1, 11, 10, 1, 10, 4, 1, 4, 0, 7, 4, 10, -1, -1, -1, -1},             /* 29 */
{ 4, 8, 7, 9, 10, 0, 9, 11, 10, 10, 3, 0, -1, -1, -1, -1},             /* 30 */
{ 4, 10, 7, 4, 9, 10, 9, 11, 10, -1, -1, -1, -1, -1, -1, -1},          /* 31 */
{ 9, 4, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 32 */
{ 9, 4, 5, 0, 3, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 33 */
{ 0, 4, 5, 1, 0, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 34 */
{ 8, 4, 5, 8, 5, 3, 3, 5, 1, -1, -1, -1, -1, -1, -1, -1},              /* 35 */
{ 1, 11, 2, 9, 4, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 36 */
{ 3, 8, 0, 1, 11, 2, 4, 5, 9, -1, -1, -1, -1, -1, -1, -1},             /* 37 */
{ 5, 11, 2, 5, 2, 4, 4, 2, 0, -1, -1, -1, -1, -1, -1, -1},             /* 38 */
{ 2, 5, 11, 3, 5, 2, 3, 4, 5, 3, 8, 4, -1, -1, -1, -1},                /* 39 */
{ 9, 4, 5, 2, 10, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 40 */
{ 0, 2, 10, 0, 10, 8, 4, 5, 9, -1, -1, -1, -1, -1, -1, -1},            /* 41 */
{ 0, 4, 5, 0, 5, 1, 2, 10, 3, -1, -1, -1, -1, -1, -1, -1},             /* 42 */
{ 2, 5, 1, 2, 8, 5, 2, 10, 8, 4, 5, 8, -1, -1, -1, -1},                /* 43 */
{11, 10, 3, 11, 3, 1, 9, 4, 5, -1, -1, -1, -1, -1, -1, -1},            /* 44 */
{ 4, 5, 9, 0, 1, 8, 8, 1, 11, 8, 11, 10, -1, -1, -1, -1},              /* 45 */
{ 5, 0, 4, 5, 10, 0, 5, 11, 10, 10, 3, 0, -1, -1, -1, -1},             /* 46 */
{ 5, 8, 4, 5, 11, 8, 11, 10, 8, -1, -1, -1, -1, -1, -1, -1},           /* 47 */
{ 9, 8, 7, 5, 9, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 48 */
{ 9, 0, 3, 9, 3, 5, 5, 3, 7, -1, -1, -1, -1, -1, -1, -1},              /* 49 */
{ 0, 8, 7, 0, 7, 1, 1, 7, 5, -1, -1, -1, -1, -1, -1, -1},              /* 50 */
{ 1, 3, 5, 3, 7, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 51 */
{ 9, 8, 7, 9, 7, 5, 11, 2, 1, -1, -1, -1, -1, -1, -1, -1},             /* 52 */
{11, 2, 1, 9, 0, 5, 5, 0, 3, 5, 3, 7, -1, -1, -1, -1},                 /* 53 */
{ 8, 2, 0, 8, 5, 2, 8, 7, 5, 11, 2, 5, -1, -1, -1, -1},                /* 54 */
{ 2, 5, 11, 2, 3, 5, 3, 7, 5, -1, -1, -1, -1, -1, -1, -1},             /* 55 */
{ 7, 5, 9, 7, 9, 8, 3, 2, 10, -1, -1, -1, -1, -1, -1, -1},             /* 56 */
{ 9, 7, 5, 9, 2, 7, 9, 0, 2, 2, 10, 7, -1, -1, -1, -1},                /* 57 */
{ 2, 10, 3, 0, 8, 1, 1, 8, 7, 1, 7, 5, -1, -1, -1, -1},                /* 58 */
{10, 1, 2, 10, 7, 1, 7, 5, 1, -1, -1, -1, -1, -1, -1, -1},             /* 59 */
{ 9, 8, 5, 8, 7, 5, 11, 3, 1, 11, 10, 3, -1, -1, -1, -1},              /* 60 */
{ 5, 0, 7, 5, 9, 0, 7, 0, 10, 1, 11, 0, 10, 0, 11, -1},                /* 61 */
{10, 0, 11, 10, 3, 0, 11, 0, 5, 8, 7, 0, 5, 0, 7, -1},                 /* 62 */
{10, 5, 11, 7, 5, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 63 */
{11, 5, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 64 */
{ 0, 3, 8, 5, 6, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 65 */
{ 9, 1, 0, 5, 6, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 66 */
{ 1, 3, 8, 1, 8, 9, 5, 6, 11, -1, -1, -1, -1, -1, -1, -1},             /* 67 */
{ 1, 5, 6, 2, 1, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},           /* 68 */
{ 1, 5, 6, 1, 6, 2, 3, 8, 0, -1, -1, -1, -1, -1, -1, -1},              /* 69 */
{ 9, 5, 6, 9, 6, 0, 0, 6, 2, -1, -1, -1, -1, -1, -1, -1},              /* 70 */
{ 5, 8, 9, 5, 2, 8, 5, 6, 2, 3, 8, 2, -1, -1, -1, -1},                 /* 71 */
{ 2, 10, 3, 11, 5, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 72 */
{10, 8, 0, 10, 0, 2, 11, 5, 6, -1, -1, -1, -1, -1, -1, -1},            /* 73 */
{ 0, 9, 1, 2, 10, 3, 5, 6, 11, -1, -1, -1, -1, -1, -1, -1},            /* 74 */
{ 5, 6, 11, 1, 2, 9, 9, 2, 10, 9, 10, 8, -1, -1, -1, -1},              /* 75 */
{ 6, 10, 3, 6, 3, 5, 5, 3, 1, -1, -1, -1, -1, -1, -1, -1},             /* 76 */
{ 0, 10, 8, 0, 5, 10, 0, 1, 5, 5, 6, 10, -1, -1, -1, -1},              /* 77 */
{ 3, 6, 10, 0, 6, 3, 0, 5, 6, 0, 9, 5, -1, -1, -1, -1},                /* 78 */
{ 6, 9, 5, 6, 10, 9, 10, 8, 9, -1, -1, -1, -1, -1, -1, -1},            /* 79 */
{ 5, 6, 11, 4, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 80 */
{ 4, 0, 3, 4, 3, 7, 6, 11, 5, -1, -1, -1, -1, -1, -1, -1},             /* 81 */
{ 1, 0, 9, 5, 6, 11, 8, 7, 4, -1, -1, -1, -1, -1, -1, -1},             /* 82 */
{11, 5, 6, 1, 7, 9, 1, 3, 7, 7, 4, 9, -1, -1, -1, -1},                 /* 83 */
{ 6, 2, 1, 6, 1, 5, 4, 8, 7, -1, -1, -1, -1, -1, -1, -1},              /* 84 */
{ 1, 5, 2, 5, 6, 2, 3, 4, 0, 3, 7, 4, -1, -1, -1, -1},                 /* 85 */
{ 8, 7, 4, 9, 5, 0, 0, 5, 6, 0, 6, 2, -1, -1, -1, -1},                 /* 86 */
{ 7, 9, 3, 7, 4, 9, 3, 9, 2, 5, 6, 9, 2, 9, 6, -1},                    /* 87 */
{ 3, 2, 10, 7, 4, 8, 11, 5, 6, -1, -1, -1, -1, -1, -1, -1},            /* 88 */
{ 5, 6, 11, 4, 2, 7, 4, 0, 2, 2, 10, 7, -1, -1, -1, -1},               /* 89 */
{ 0, 9, 1, 4, 8, 7, 2, 10, 3, 5, 6, 11, -1, -1, -1, -1},               /* 90 */
{ 9, 1, 2, 9, 2, 10, 9, 10, 4, 7, 4, 10, 5, 6, 11, -1},                /* 91 */
{ 8, 7, 4, 3, 5, 10, 3, 1, 5, 5, 6, 10, -1, -1, -1, -1},               /* 92 */
{ 5, 10, 1, 5, 6, 10, 1, 10, 0, 7, 4, 10, 0, 10, 4, -1},               /* 93 */
{ 0, 9, 5, 0, 5, 6, 0, 6, 3, 10, 3, 6, 8, 7, 4, -1},                   /* 94 */
{ 6, 9, 5, 6, 10, 9, 4, 9, 7, 7, 9, 10, -1, -1, -1, -1},               /* 95 */
{11, 9, 4, 6, 11, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 96 */
{ 4, 6, 11, 4, 11, 9, 0, 3, 8, -1, -1, -1, -1, -1, -1, -1},            /* 97 */
{11, 1, 0, 11, 0, 6, 6, 0, 4, -1, -1, -1, -1, -1, -1, -1},             /* 98 */
{ 8, 1, 3, 8, 6, 1, 8, 4, 6, 6, 11, 1, -1, -1, -1, -1},                /* 99 */
{ 1, 9, 4, 1, 4, 2, 2, 4, 6, -1, -1, -1, -1, -1, -1, -1},             /* 100 */
{ 3, 8, 0, 1, 9, 2, 2, 9, 4, 2, 4, 6, -1, -1, -1, -1},                /* 101 */
{ 0, 4, 2, 4, 6, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 102 */
{ 8, 2, 3, 8, 4, 2, 4, 6, 2, -1, -1, -1, -1, -1, -1, -1},             /* 103 */
{11, 9, 4, 11, 4, 6, 10, 3, 2, -1, -1, -1, -1, -1, -1, -1},           /* 104 */
{ 0, 2, 8, 2, 10, 8, 4, 11, 9, 4, 6, 11, -1, -1, -1, -1},             /* 105 */
{ 3, 2, 10, 0, 6, 1, 0, 4, 6, 6, 11, 1, -1, -1, -1, -1},              /* 106 */
{ 6, 1, 4, 6, 11, 1, 4, 1, 8, 2, 10, 1, 8, 1, 10, -1},                /* 107 */
{ 9, 4, 6, 9, 6, 3, 9, 3, 1, 10, 3, 6, -1, -1, -1, -1},               /* 108 */
{ 8, 1, 10, 8, 0, 1, 10, 1, 6, 9, 4, 1, 6, 1, 4, -1},                 /* 109 */
{ 3, 6, 10, 3, 0, 6, 0, 4, 6, -1, -1, -1, -1, -1, -1, -1},            /* 110 */
{ 6, 8, 4, 10, 8, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 111 */
{ 7, 6, 11, 7, 11, 8, 8, 11, 9, -1, -1, -1, -1, -1, -1, -1},          /* 112 */
{ 0, 3, 7, 0, 7, 11, 0, 11, 9, 6, 11, 7, -1, -1, -1, -1},             /* 113 */
{11, 7, 6, 1, 7, 11, 1, 8, 7, 1, 0, 8, -1, -1, -1, -1},               /* 114 */
{11, 7, 6, 11, 1, 7, 1, 3, 7, -1, -1, -1, -1, -1, -1, -1},            /* 115 */
{ 1, 6, 2, 1, 8, 6, 1, 9, 8, 8, 7, 6, -1, -1, -1, -1},                /* 116 */
{ 2, 9, 6, 2, 1, 9, 6, 9, 7, 0, 3, 9, 7, 9, 3, -1},                   /* 117 */
{ 7, 0, 8, 7, 6, 0, 6, 2, 0, -1, -1, -1, -1, -1, -1, -1},             /* 118 */
{ 7, 2, 3, 6, 2, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 119 */
{ 2, 10, 3, 11, 8, 6, 11, 9, 8, 8, 7, 6, -1, -1, -1, -1},             /* 120 */
{ 2, 7, 0, 2, 10, 7, 0, 7, 9, 6, 11, 7, 9, 7, 11, -1},                /* 121 */
{ 1, 0, 8, 1, 8, 7, 1, 7, 11, 6, 11, 7, 2, 10, 3, -1},                /* 122 */
{10, 1, 2, 10, 7, 1, 11, 1, 6, 6, 1, 7, -1, -1, -1, -1},              /* 123 */
{ 8, 6, 9, 8, 7, 6, 9, 6, 1, 10, 3, 6, 1, 6, 3, -1},                  /* 124 */
{ 0, 1, 9, 10, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 125 */
{ 7, 0, 8, 7, 6, 0, 3, 0, 10, 10, 0, 6, -1, -1, -1, -1},              /* 126 */
{ 7, 6, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},      /* 127 */
{ 7, 10, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},      /* 128 */
{ 3, 8, 0, 10, 6, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 129 */
{ 0, 9, 1, 10, 6, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 130 */
{ 8, 9, 1, 8, 1, 3, 10, 6, 7, -1, -1, -1, -1, -1, -1, -1},            /* 131 */
{11, 2, 1, 6, 7, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 132 */
{ 1, 11, 2, 3, 8, 0, 6, 7, 10, -1, -1, -1, -1, -1, -1, -1},           /* 133 */
{ 2, 0, 9, 2, 9, 11, 6, 7, 10, -1, -1, -1, -1, -1, -1, -1},           /* 134 */
{ 6, 7, 10, 2, 3, 11, 11, 3, 8, 11, 8, 9, -1, -1, -1, -1},            /* 135 */
{ 7, 3, 2, 6, 7, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 136 */
{ 7, 8, 0, 7, 0, 6, 6, 0, 2, -1, -1, -1, -1, -1, -1, -1},             /* 137 */
{ 2, 6, 7, 2, 7, 3, 0, 9, 1, -1, -1, -1, -1, -1, -1, -1},             /* 138 */
{ 1, 2, 6, 1, 6, 8, 1, 8, 9, 8, 6, 7, -1, -1, -1, -1},                /* 139 */
{11, 6, 7, 11, 7, 1, 1, 7, 3, -1, -1, -1, -1, -1, -1, -1},            /* 140 */
{11, 6, 7, 1, 11, 7, 1, 7, 8, 1, 8, 0, -1, -1, -1, -1},               /* 141 */
{ 0, 7, 3, 0, 11, 7, 0, 9, 11, 6, 7, 11, -1, -1, -1, -1},             /* 142 */
{ 7, 11, 6, 7, 8, 11, 8, 9, 11, -1, -1, -1, -1, -1, -1, -1},          /* 143 */
{ 6, 4, 8, 10, 6, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 144 */
{ 3, 10, 6, 3, 6, 0, 0, 6, 4, -1, -1, -1, -1, -1, -1, -1},            /* 145 */
{ 8, 10, 6, 8, 6, 4, 9, 1, 0, -1, -1, -1, -1, -1, -1, -1},            /* 146 */
{ 9, 6, 4, 9, 3, 6, 9, 1, 3, 10, 6, 3, -1, -1, -1, -1},               /* 147 */
{ 6, 4, 8, 6, 8, 10, 2, 1, 11, -1, -1, -1, -1, -1, -1, -1},           /* 148 */
{ 1, 11, 2, 3, 10, 0, 0, 10, 6, 0, 6, 4, -1, -1, -1, -1},             /* 149 */
{ 4, 8, 10, 4, 10, 6, 0, 9, 2, 2, 9, 11, -1, -1, -1, -1},             /* 150 */
{11, 3, 9, 11, 2, 3, 9, 3, 4, 10, 6, 3, 4, 3, 6, -1},                 /* 151 */
{ 8, 3, 2, 8, 2, 4, 4, 2, 6, -1, -1, -1, -1, -1, -1, -1},             /* 152 */
{ 0, 2, 4, 4, 2, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 153 */
{ 1, 0, 9, 2, 4, 3, 2, 6, 4, 4, 8, 3, -1, -1, -1, -1},                /* 154 */
{ 1, 4, 9, 1, 2, 4, 2, 6, 4, -1, -1, -1, -1, -1, -1, -1},             /* 155 */
{ 8, 3, 1, 8, 1, 6, 8, 6, 4, 6, 1, 11, -1, -1, -1, -1},               /* 156 */
{11, 0, 1, 11, 6, 0, 6, 4, 0, -1, -1, -1, -1, -1, -1, -1},            /* 157 */
{ 4, 3, 6, 4, 8, 3, 6, 3, 11, 0, 9, 3, 11, 3, 9, -1},                 /* 158 */
{11, 4, 9, 6, 4, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 159 */
{ 4, 5, 9, 7, 10, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 160 */
{ 0, 3, 8, 4, 5, 9, 10, 6, 7, -1, -1, -1, -1, -1, -1, -1},            /* 161 */
{ 5, 1, 0, 5, 0, 4, 7, 10, 6, -1, -1, -1, -1, -1, -1, -1},            /* 162 */
{10, 6, 7, 8, 4, 3, 3, 4, 5, 3, 5, 1, -1, -1, -1, -1},                /* 163 */
{ 9, 4, 5, 11, 2, 1, 7, 10, 6, -1, -1, -1, -1, -1, -1, -1},           /* 164 */
{ 6, 7, 10, 1, 11, 2, 0, 3, 8, 4, 5, 9, -1, -1, -1, -1},              /* 165 */
{ 7, 10, 6, 5, 11, 4, 4, 11, 2, 4, 2, 0, -1, -1, -1, -1},             /* 166 */
{ 3, 8, 4, 3, 4, 5, 3, 5, 2, 11, 2, 5, 10, 6, 7, -1},                 /* 167 */
{ 7, 3, 2, 7, 2, 6, 5, 9, 4, -1, -1, -1, -1, -1, -1, -1},             /* 168 */
{ 9, 4, 5, 0, 6, 8, 0, 2, 6, 6, 7, 8, -1, -1, -1, -1},                /* 169 */
{ 3, 2, 6, 3, 6, 7, 1, 0, 5, 5, 0, 4, -1, -1, -1, -1},                /* 170 */
{ 6, 8, 2, 6, 7, 8, 2, 8, 1, 4, 5, 8, 1, 8, 5, -1},                   /* 171 */
{ 9, 4, 5, 11, 6, 1, 1, 6, 7, 1, 7, 3, -1, -1, -1, -1},               /* 172 */
{ 1, 11, 6, 1, 6, 7, 1, 7, 0, 8, 0, 7, 9, 4, 5, -1},                  /* 173 */
{ 4, 11, 0, 4, 5, 11, 0, 11, 3, 6, 7, 11, 3, 11, 7, -1},              /* 174 */
{ 7, 11, 6, 7, 8, 11, 5, 11, 4, 4, 11, 8, -1, -1, -1, -1},            /* 175 */
{ 6, 5, 9, 6, 9, 10, 10, 9, 8, -1, -1, -1, -1, -1, -1, -1},           /* 176 */
{ 3, 10, 6, 0, 3, 6, 0, 6, 5, 0, 5, 9, -1, -1, -1, -1},               /* 177 */
{ 0, 8, 10, 0, 10, 5, 0, 5, 1, 5, 10, 6, -1, -1, -1, -1},             /* 178 */
{ 6, 3, 10, 6, 5, 3, 5, 1, 3, -1, -1, -1, -1, -1, -1, -1},            /* 179 */
{ 1, 11, 2, 9, 10, 5, 9, 8, 10, 10, 6, 5, -1, -1, -1, -1},            /* 180 */
{ 0, 3, 10, 0, 10, 6, 0, 6, 9, 5, 9, 6, 1, 11, 2, -1},                /* 181 */
{10, 5, 8, 10, 6, 5, 8, 5, 0, 11, 2, 5, 0, 5, 2, -1},                 /* 182 */
{ 6, 3, 10, 6, 5, 3, 2, 3, 11, 11, 3, 5, -1, -1, -1, -1},             /* 183 */
{ 5, 9, 8, 5, 8, 2, 5, 2, 6, 3, 2, 8, -1, -1, -1, -1},                /* 184 */
{ 9, 6, 5, 9, 0, 6, 0, 2, 6, -1, -1, -1, -1, -1, -1, -1},             /* 185 */
{ 1, 8, 5, 1, 0, 8, 5, 8, 6, 3, 2, 8, 6, 8, 2, -1},                   /* 186 */
{ 1, 6, 5, 2, 6, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 187 */
{ 1, 6, 3, 1, 11, 6, 3, 6, 8, 5, 9, 6, 8, 6, 9, -1},                  /* 188 */
{11, 0, 1, 11, 6, 0, 9, 0, 5, 5, 0, 6, -1, -1, -1, -1},               /* 189 */
{ 0, 8, 3, 5, 11, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 190 */
{11, 6, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 191 */
{10, 11, 5, 7, 10, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 192 */
{10, 11, 5, 10, 5, 7, 8, 0, 3, -1, -1, -1, -1, -1, -1, -1},           /* 193 */
{ 5, 7, 10, 5, 10, 11, 1, 0, 9, -1, -1, -1, -1, -1, -1, -1},          /* 194 */
{11, 5, 7, 11, 7, 10, 9, 1, 8, 8, 1, 3, -1, -1, -1, -1},              /* 195 */
{10, 2, 1, 10, 1, 7, 7, 1, 5, -1, -1, -1, -1, -1, -1, -1},            /* 196 */
{ 0, 3, 8, 1, 7, 2, 1, 5, 7, 7, 10, 2, -1, -1, -1, -1},               /* 197 */
{ 9, 5, 7, 9, 7, 2, 9, 2, 0, 2, 7, 10, -1, -1, -1, -1},               /* 198 */
{ 7, 2, 5, 7, 10, 2, 5, 2, 9, 3, 8, 2, 9, 2, 8, -1},                  /* 199 */
{ 2, 11, 5, 2, 5, 3, 3, 5, 7, -1, -1, -1, -1, -1, -1, -1},            /* 200 */
{ 8, 0, 2, 8, 2, 5, 8, 5, 7, 11, 5, 2, -1, -1, -1, -1},               /* 201 */
{ 9, 1, 0, 5, 3, 11, 5, 7, 3, 3, 2, 11, -1, -1, -1, -1},              /* 202 */
{ 9, 2, 8, 9, 1, 2, 8, 2, 7, 11, 5, 2, 7, 2, 5, -1},                  /* 203 */
{ 1, 5, 3, 3, 5, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 204 */
{ 0, 7, 8, 0, 1, 7, 1, 5, 7, -1, -1, -1, -1, -1, -1, -1},             /* 205 */
{ 9, 3, 0, 9, 5, 3, 5, 7, 3, -1, -1, -1, -1, -1, -1, -1},             /* 206 */
{ 9, 7, 8, 5, 7, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 207 */
{ 5, 4, 8, 5, 8, 11, 11, 8, 10, -1, -1, -1, -1, -1, -1, -1},          /* 208 */
{ 5, 4, 0, 5, 0, 10, 5, 10, 11, 10, 0, 3, -1, -1, -1, -1},            /* 209 */
{ 0, 9, 1, 8, 11, 4, 8, 10, 11, 11, 5, 4, -1, -1, -1, -1},            /* 210 */
{11, 4, 10, 11, 5, 4, 10, 4, 3, 9, 1, 4, 3, 4, 1, -1},                /* 211 */
{ 2, 1, 5, 2, 5, 8, 2, 8, 10, 4, 8, 5, -1, -1, -1, -1},               /* 212 */
{ 0, 10, 4, 0, 3, 10, 4, 10, 5, 2, 1, 10, 5, 10, 1, -1},              /* 213 */
{ 0, 5, 2, 0, 9, 5, 2, 5, 10, 4, 8, 5, 10, 5, 8, -1},                 /* 214 */
{ 9, 5, 4, 2, 3, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 215 */
{ 2, 11, 5, 3, 2, 5, 3, 5, 4, 3, 4, 8, -1, -1, -1, -1},               /* 216 */
{ 5, 2, 11, 5, 4, 2, 4, 0, 2, -1, -1, -1, -1, -1, -1, -1},            /* 217 */
{ 3, 2, 11, 3, 11, 5, 3, 5, 8, 4, 8, 5, 0, 9, 1, -1},                 /* 218 */
{ 5, 2, 11, 5, 4, 2, 1, 2, 9, 9, 2, 4, -1, -1, -1, -1},               /* 219 */
{ 8, 5, 4, 8, 3, 5, 3, 1, 5, -1, -1, -1, -1, -1, -1, -1},             /* 220 */
{ 0, 5, 4, 1, 5, 0, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 221 */
{ 8, 5, 4, 8, 3, 5, 9, 5, 0, 0, 5, 3, -1, -1, -1, -1},                /* 222 */
{ 9, 5, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 223 */
{ 4, 7, 10, 4, 10, 9, 9, 10, 11, -1, -1, -1, -1, -1, -1, -1},         /* 224 */
{ 0, 3, 8, 4, 7, 9, 9, 7, 10, 9, 10, 11, -1, -1, -1, -1},             /* 225 */
{ 1, 10, 11, 1, 4, 10, 1, 0, 4, 7, 10, 4, -1, -1, -1, -1},            /* 226 */
{ 3, 4, 1, 3, 8, 4, 1, 4, 11, 7, 10, 4, 11, 4, 10, -1},               /* 227 */
{ 4, 7, 10, 9, 4, 10, 9, 10, 2, 9, 2, 1, -1, -1, -1, -1},             /* 228 */
{ 9, 4, 7, 9, 7, 10, 9, 10, 1, 2, 1, 10, 0, 3, 8, -1},                /* 229 */
{10, 4, 7, 10, 2, 4, 2, 0, 4, -1, -1, -1, -1, -1, -1, -1},            /* 230 */
{10, 4, 7, 10, 2, 4, 8, 4, 3, 3, 4, 2, -1, -1, -1, -1},               /* 231 */
{ 2, 11, 9, 2, 9, 7, 2, 7, 3, 7, 9, 4, -1, -1, -1, -1},               /* 232 */
{ 9, 7, 11, 9, 4, 7, 11, 7, 2, 8, 0, 7, 2, 7, 0, -1},                 /* 233 */
{ 3, 11, 7, 3, 2, 11, 7, 11, 4, 1, 0, 11, 4, 11, 0, -1},              /* 234 */
{ 1, 2, 11, 8, 4, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 235 */
{ 4, 1, 9, 4, 7, 1, 7, 3, 1, -1, -1, -1, -1, -1, -1, -1},             /* 236 */
{ 4, 1, 9, 4, 7, 1, 0, 1, 8, 8, 1, 7, -1, -1, -1, -1},                /* 237 */
{ 4, 3, 0, 7, 3, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 238 */
{ 4, 7, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 239 */
{ 9, 8, 11, 11, 8, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 240 */
{ 3, 9, 0, 3, 10, 9, 10, 11, 9, -1, -1, -1, -1, -1, -1, -1},          /* 241 */
{ 0, 11, 1, 0, 8, 11, 8, 10, 11, -1, -1, -1, -1, -1, -1, -1},         /* 242 */
{ 3, 11, 1, 10, 11, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 243 */
{ 1, 10, 2, 1, 9, 10, 9, 8, 10, -1, -1, -1, -1, -1, -1, -1},          /* 244 */
{ 3, 9, 0, 3, 10, 9, 1, 9, 2, 2, 9, 10, -1, -1, -1, -1},              /* 245 */
{ 0, 10, 2, 8, 10, 0, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},        /* 246 */
{ 3, 10, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},      /* 247 */
{ 2, 8, 3, 2, 11, 8, 11, 9, 8, -1, -1, -1, -1, -1, -1, -1},           /* 248 */
{ 9, 2, 11, 0, 2, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},         /* 249 */
{ 2, 8, 3, 2, 11, 8, 0, 8, 1, 1, 8, 11, -1, -1, -1, -1},              /* 250 */
{ 1, 2, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},      /* 251 */
{ 1, 8, 3, 9, 8, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},          /* 252 */
{ 0, 1, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 253 */
{ 0, 8, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1},       /* 254 */
{-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1}      /* 255 */
};

static const int VoxVerticesFromEdges[12][2] =
{
    { 0, 1 },   /* Edge 0 */
    { 1, 3 },   /* Edge 1 */
    { 2, 3 },   /* Edge 2 */
    { 0, 2 },   /* Edge 3 */
    { 4, 5 },   /* Edge 4 */
    { 5, 7 },   /* Edge 5 */
    { 6, 7 },   /* Edge 6 */
    { 4, 6 },   /* Edge 7 */
    { 0, 4 },   /* Edge 8 */
    { 1, 5 },   /* Edge 9 */
    { 2, 6 },   /* Edge 10 */
    { 3, 7 }    /* Edge 11 */
};

static const int VoxQuadFaces[6][4] =
{
    { 2, 0, 6, 4 },
    { 1, 3, 5, 7 },
    { 0, 1, 4, 5 },
    { 3, 2, 7, 6 },
    { 1, 0, 3, 2 },
    { 4, 5, 6, 7 }
};

static const int TriVerticesFromEdges[3][2] =
{
    {0,1},
    {1,2},
    {2,0}
};

static const int QuadVerticesFromEdges[4][2] =
{
    {0,1},
    {1,2},
    {2,3},
    {3,0}
};

static const int PixelVerticesFromEdges[4][2] =
{
    {0,1},
    {1,3},
    {2,3},
    {0,2}
};

static const int LineVerticesFromEdges[1][2] =
{
    {0,1}
};

};

// ============================================================================
// ====================== vtkTriangulationTables ( end ) ======================
// ============================================================================


// ============================================================================
// ============================= ClipCases (begin) ============================
// ============================================================================

namespace vtkTableBasedClipperClipTables
{

// ----------------------------------------------------------------------------
// ---- ClipCases.h (begin)

// Points of original cell (up to 8, for the hex)
// Note: we assume P0 is zero in several places.
// Note: we assume these values are contiguous and monotonic.
#define P0     0
#define P1     1
#define P2     2
#define P3     3
#define P4     4
#define P5     5
#define P6     6
#define P7     7

// Edges of original cell (up to 12, for the hex)
// Note: we assume these values are contiguous and monotonic.
#define EA     20
#define EB     21
#define EC     22
#define ED     23
#define EE     24
#define EF     25
#define EG     26
#define EH     27
#define EI     28
#define EJ     29
#define EK     30
#define EL     31

// New interpolated points (ST_PNT outputs)
// Note: we assume these values are contiguous and monotonic.
#define N0     40
#define N1     41
#define N2     42
#define N3     43

// Shapes
#define ST_TET 100
#define ST_PYR 101
#define ST_WDG 102
#define ST_HEX 103
#define ST_TRI 104
#define ST_QUA 105
#define ST_VTX 106
#define ST_LIN 107
#define ST_PNT 108

// Colors
#define COLOR0  120
#define COLOR1  121
#define NOCOLOR 122

// ---- ClipCases.h ( end )
// ----------------------------------------------------------------------------


// ----------------------------------------------------------------------------
// ---- ClipCasesHex.C (begin)

const int NumClipCasesHex = 256;

const int NumClipShapesHex[256] = {
  1,  10,  10,  3,  10,  18,  3,  15, // cases 0 - 7
  10,  3,  18,  15,  3,  15,  15,  2, // cases 8 - 15
  10,  3,  18,  15,  8,  18,  18,  17, // cases 16 - 23
  18,  15,  11,  10,  18,  17,  16,  15, // cases 24 - 31
  10,  18,  3,  15,  18,  11,  15,  10, // cases 32 - 39
  8,  18,  18,  17,  18,  16,  17,  15, // cases 40 - 47
  3,  15,  15,  2,  18,  16,  17,  15, // cases 48 - 55
  18,  17,  16,  15,  4,  13,  13,  3, // cases 56 - 63
  10,  8,  18,  18,  3,  18,  15,  17, // cases 64 - 71
  18,  18,  11,  16,  15,  17,  10,  15, // cases 72 - 79
  18,  18,  11,  16,  18,  4,  16,  13, // cases 80 - 87
  11,  16,  9,  8,  16,  13,  8,  7, // cases 88 - 95
  3,  18,  15,  17,  15,  16,  2,  15, // cases 96 - 103
  18,  4,  16,  13,  17,  13,  15,  3, // cases 104 - 111
  15,  17,  10,  15,  17,  13,  15,  3, // cases 112 - 119
  16,  13,  8,  7,  13,  8,  7,  10, // cases 120 - 127
  10,  18,  8,  18,  18,  11,  18,  16, // cases 128 - 135
  3,  15,  18,  17,  15,  10,  17,  15, // cases 136 - 143
  3,  15,  18,  17,  18,  16,  4,  13, // cases 144 - 151
  15,  2,  16,  15,  17,  15,  13,  3, // cases 152 - 159
  18,  11,  18,  16,  11,  9,  16,  8, // cases 160 - 167
  18,  16,  4,  13,  16,  8,  13,  7, // cases 168 - 175
  15,  10,  17,  15,  16,  8,  13,  7, // cases 176 - 183
  17,  15,  13,  3,  13,  7,  8,  10, // cases 184 - 191
  3,  18,  18,  4,  15,  16,  17,  13, // cases 192 - 199
  15,  17,  16,  13,  2,  15,  15,  3, // cases 200 - 207
  15,  17,  16,  13,  17,  13,  13,  8, // cases 208 - 215
  10,  15,  8,  7,  15,  3,  7,  10, // cases 216 - 223
  15,  16,  17,  13,  10,  8,  15,  7, // cases 224 - 231
  17,  13,  13,  8,  15,  7,  3,  10, // cases 232 - 239
  2,  15,  15,  3,  15,  7,  3,  10, // cases 240 - 247
  15,  3,  7,  10,  3,  10,  10,  1  // cases 248 - 255
};

const int StartClipShapesHex[256] = {
  0, 10, 80, 150, 176, 246, 361, 387, // cases 0 - 7
  488, 558, 584, 699, 800, 826, 927, 1028, // cases 8 - 15
  1048, 1118, 1144, 1259, 1360, 1412, 1531, 1650, // cases 16 - 23
  1764, 1879, 1980, 2056, 2124, 2243, 2357, 2465, // cases 24 - 31
  2566, 2636, 2751, 2777, 2878, 2993, 3069, 3170, // cases 32 - 39
  3238, 3290, 3409, 3528, 3642, 3761, 3869, 3983, // cases 40 - 47
  4084, 4110, 4211, 4312, 4332, 4451, 4559, 4673, // cases 48 - 55
  4774, 4893, 5007, 5115, 5216, 5252, 5343, 5434, // cases 56 - 63
  5460, 5530, 5582, 5697, 5816, 5842, 5961, 6062, // cases 64 - 71
  6176, 6291, 6410, 6486, 6594, 6695, 6809, 6877, // cases 72 - 79
  6978, 7093, 7212, 7288, 7396, 7515, 7551, 7659, // cases 80 - 87
  7750, 7826, 7934, 7996, 8050, 8158, 8249, 8303, // cases 88 - 95
  8350, 8376, 8495, 8596, 8710, 8811, 8919, 8939, // cases 96 - 103
  9040, 9159, 9195, 9303, 9394, 9508, 9599, 9700, // cases 104 - 111
  9726, 9827, 9941, 10009, 10110, 10224, 10315, 10416, // cases 112 - 119
  10442, 10550, 10641, 10695, 10742, 10833, 10885, 10932, // cases 120 - 127
  11002, 11072, 11187, 11239, 11358, 11473, 11549, 11668, // cases 128 - 135
  11776, 11802, 11903, 12022, 12136, 12237, 12305, 12419, // cases 136 - 143
  12520, 12546, 12647, 12766, 12880, 12999, 13107, 13143, // cases 144 - 151
  13234, 13335, 13355, 13463, 13564, 13678, 13779, 13870, // cases 152 - 159
  13896, 14011, 14087, 14206, 14314, 14390, 14452, 14560, // cases 160 - 167
  14614, 14733, 14841, 14877, 14968, 15076, 15130, 15221, // cases 168 - 175
  15268, 15369, 15437, 15551, 15652, 15760, 15814, 15905, // cases 176 - 183
  15952, 16066, 16167, 16258, 16284, 16375, 16422, 16474, // cases 184 - 191
  16544, 16570, 16689, 16808, 16844, 16945, 17053, 17167, // cases 192 - 199
  17258, 17359, 17473, 17581, 17672, 17692, 17793, 17894, // cases 200 - 207
  17920, 18021, 18135, 18243, 18334, 18448, 18539, 18630, // cases 208 - 215
  18682, 18750, 18851, 18905, 18952, 19053, 19079, 19126, // cases 216 - 223
  19196, 19297, 19405, 19519, 19610, 19678, 19732, 19833, // cases 224 - 231
  19880, 19994, 20085, 20176, 20228, 20329, 20376, 20402, // cases 232 - 239
  20472, 20492, 20593, 20694, 20720, 20821, 20868, 20894, // cases 240 - 247
  20964, 21065, 21091, 21138, 21208, 21234, 21304, 21374  // cases 248 - 255
};

static unsigned char ClipShapesHex[] = {
 // Case #0: Unique case #1
  ST_HEX, COLOR0, P0, P1, P2, P3, P4, P5, P6, P7,
 // Case #1: Unique case #2
  ST_PNT, 0, COLOR0, 7, P1, P2, P3, P4, P5, P6, P7,
  ST_WDG, COLOR0, P1, P3, P4, EA, ED, EI,
  ST_TET, COLOR0, P1, P3, P4, N0,
  ST_TET, COLOR0, P1, P2, P3, N0,
  ST_PYR, COLOR0, P6, P7, P3, P2, N0,
  ST_PYR, COLOR0, P5, P6, P2, P1, N0,
  ST_PYR, COLOR0, P4, P7, P6, P5, N0,
  ST_TET, COLOR0, P3, P7, P4, N0,
  ST_TET, COLOR0, P4, P5, P1, N0,
  ST_TET, COLOR1, P0, EA, ED, EI,
 // Case #2: (cloned #1)
  ST_PNT, 0, COLOR0, 7, P5, P4, P0, P2, P6, P7, P3,
  ST_WDG, COLOR0, EJ, EA, EB, P5, P0, P2,
  ST_TET, COLOR0, P5, P2, P0, N0,
  ST_TET, COLOR0, P5, P0, P4, N0,
  ST_PYR, COLOR0, P7, P4, P0, P3, N0,
  ST_PYR, COLOR0, P6, P5, P4, P7, N0,
  ST_PYR, COLOR0, P2, P6, P7, P3, N0,
  ST_TET, COLOR0, P0, P2, P3, N0,
  ST_TET, COLOR0, P2, P5, P6, N0,
  ST_TET, COLOR1, P1, EA, EJ, EB,
 // Case #3: Unique case #3
  ST_HEX, COLOR0, EB, P2, P3, ED, EJ, P5, P4, EI,
  ST_WDG, COLOR0, P2, P6, P5, P3, P7, P4,
  ST_WDG, COLOR1, P1, EB, EJ, P0, ED, EI,
 // Case #4: (cloned #1)
  ST_PNT, 0, COLOR0, 7, P6, P5, P1, P3, P7, P4, P0,
  ST_WDG, COLOR0, EL, EB, EC, P6, P1, P3,
  ST_TET, COLOR0, P6, P3, P1, N0,
  ST_TET, COLOR0, P6, P1, P5, N0,
  ST_PYR, COLOR0, P4, P5, P1, P0, N0,
  ST_PYR, COLOR0, P7, P6, P5, P4, N0,
  ST_PYR, COLOR0, P3, P7, P4, P0, N0,
  ST_TET, COLOR0, P1, P3, P0, N0,
  ST_TET, COLOR0, P3, P6, P7, N0,
  ST_TET, COLOR1, P2, EB, EL, EC,
 // Case #5: Unique case #4
  ST_PNT, 0, NOCOLOR, 2, EI, EL,
  ST_PYR, COLOR0, P4, P7, P6, P5, N0,
  ST_TET, COLOR0, P5, P6, P1, N0,
  ST_TET, COLOR0, P4, P5, P1, N0,
  ST_TET, COLOR0, P3, P7, P4, N0,
  ST_TET, COLOR0, P6, P7, P3, N0,
  ST_PYR, COLOR0, P6, P3, EC, EL, N0,
  ST_PYR, COLOR0, P1, P6, EL, EB, N0,
  ST_TET, COLOR0, P1, EB, EA, N0,
  ST_PYR, COLOR0, P4, P1, EA, EI, N0,
  ST_PYR, COLOR0, P4, EI, ED, P3, N0,
  ST_TET, COLOR0, P3, ED, EC, N0,
  ST_PYR, COLOR1, P0, P2, EC, ED, N0,
  ST_PYR, COLOR1, EA, EB, P2, P0, N0,
  ST_TET, COLOR1, EB, EL, P2, N0,
  ST_TET, COLOR1, P2, EL, EC, N0,
  ST_TET, COLOR1, EA, N0, P0, EI,
  ST_TET, COLOR1, ED, EI, P0, N0,
 // Case #6: (cloned #3)
  ST_HEX, COLOR0, EC, P3, P0, EA, EL, P6, P5, EJ,
  ST_WDG, COLOR0, P3, P7, P6, P0, P4, P5,
  ST_WDG, COLOR1, P2, EC, EL, P1, EA, EJ,
 // Case #7: Unique case #5
  ST_PNT, 0, NOCOLOR, 5, EI, EJ, ED, EC, EL,
  ST_PYR, COLOR0, P4, P7, P6, P5, N0,
  ST_TET, COLOR0, P6, P3, N0, P7,
  ST_PYR, COLOR0, P5, P6, EL, EJ, N0,
  ST_PYR, COLOR0, EI, P4, P5, EJ, N0,
  ST_TET, COLOR0, P3, P7, P4, N0,
  ST_PYR, COLOR0, P3, P4, EI, ED, N0,
  ST_TET, COLOR0, P3, ED, EC, N0,
  ST_PYR, COLOR0, EL, P6, P3, EC, N0,
  ST_PYR, COLOR1, EJ, EL, P2, P1, N0,
  ST_PYR, COLOR1, EI, EJ, P1, P0, N0,
  ST_TET, COLOR1, ED, EI, P0, N0,
  ST_TET, COLOR1, P0, P1, P2, N0,
  ST_PYR, COLOR1, ED, P0, P2, EC, N0,
  ST_TET, COLOR1, P2, EL, EC, N0,
 // Case #8: (cloned #1)
  ST_PNT, 0, COLOR0, 7, P2, P1, P0, P7, P6, P5, P4,
  ST_WDG, COLOR0, EC, ED, EK, P2, P0, P7,
  ST_TET, COLOR0, P2, P7, P0, N0,
  ST_TET, COLOR0, P2, P0, P1, N0,
  ST_PYR, COLOR0, P5, P1, P0, P4, N0,
  ST_PYR, COLOR0, P6, P2, P1, P5, N0,
  ST_PYR, COLOR0, P7, P6, P5, P4, N0,
  ST_TET, COLOR0, P0, P7, P4, N0,
  ST_TET, COLOR0, P7, P2, P6, N0,
  ST_TET, COLOR1, P3, ED, EC, EK,
 // Case #9: (cloned #3)
  ST_HEX, COLOR0, EK, P7, P4, EI, EC, P2, P1, EA,
  ST_WDG, COLOR0, P7, P6, P2, P4, P5, P1,
  ST_WDG, COLOR1, P3, EK, EC, P0, EI, EA,
 // Case #10: (cloned #5)
  ST_PNT, 0, NOCOLOR, 2, EK, EJ,
  ST_PYR, COLOR0, P7, P6, P5, P4, N0,
  ST_TET, COLOR0, P6, P2, P5, N0,
  ST_TET, COLOR0, P7, P2, P6, N0,
  ST_TET, COLOR0, P0, P7, P4, N0,
  ST_TET, COLOR0, P5, P0, P4, N0,
  ST_PYR, COLOR0, P5, EJ, EA, P0, N0,
  ST_PYR, COLOR0, P2, EB, EJ, P5, N0,
  ST_TET, COLOR0, P2, EC, EB, N0,
  ST_PYR, COLOR0, P7, EK, EC, P2, N0,
  ST_PYR, COLOR0, P7, P0, ED, EK, N0,
  ST_TET, COLOR0, P0, EA, ED, N0,
  ST_PYR, COLOR1, P3, ED, EA, P1, N0,
  ST_PYR, COLOR1, EC, P3, P1, EB, N0,
  ST_TET, COLOR1, EB, P1, EJ, N0,
  ST_TET, COLOR1, P1, EA, EJ, N0,
  ST_TET, COLOR1, EC, P3, N0, EK,
  ST_TET, COLOR1, ED, P3, EK, N0,
 // Case #11: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EJ, EI, EB, EC, EK,
  ST_PYR, COLOR0, P5, P4, P7, P6, N0,
  ST_TET, COLOR0, P7, N0, P2, P6,
  ST_PYR, COLOR0, P4, EI, EK, P7, N0,
  ST_PYR, COLOR0, EJ, EI, P4, P5, N0,
  ST_TET, COLOR0, P2, P5, P6, N0,
  ST_PYR, COLOR0, P2, EB, EJ, P5, N0,
  ST_TET, COLOR0, P2, EC, EB, N0,
  ST_PYR, COLOR0, EK, EC, P2, P7, N0,
  ST_PYR, COLOR1, EI, P0, P3, EK, N0,
  ST_PYR, COLOR1, EJ, P1, P0, EI, N0,
  ST_TET, COLOR1, EB, P1, EJ, N0,
  ST_TET, COLOR1, P1, P3, P0, N0,
  ST_PYR, COLOR1, EB, EC, P3, P1, N0,
  ST_TET, COLOR1, P3, EC, EK, N0,
 // Case #12: (cloned #3)
  ST_HEX, COLOR0, EL, P6, P7, EK, EB, P1, P0, ED,
  ST_WDG, COLOR0, P0, P4, P7, P1, P5, P6,
  ST_WDG, COLOR1, P3, ED, EK, P2, EB, EL,
 // Case #13: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EI, EK, EA, EB, EL,
  ST_PYR, COLOR0, P4, P7, P6, P5, N0,
  ST_TET, COLOR0, P6, N0, P1, P5,
  ST_PYR, COLOR0, P7, EK, EL, P6, N0,
  ST_PYR, COLOR0, EI, EK, P7, P4, N0,
  ST_TET, COLOR0, P1, P4, P5, N0,
  ST_PYR, COLOR0, P1, EA, EI, P4, N0,
  ST_TET, COLOR0, P1, EB, EA, N0,
  ST_PYR, COLOR0, EL, EB, P1, P6, N0,
  ST_PYR, COLOR1, EK, P3, P2, EL, N0,
  ST_PYR, COLOR1, EI, P0, P3, EK, N0,
  ST_TET, COLOR1, EA, P0, EI, N0,
  ST_TET, COLOR1, P0, P2, P3, N0,
  ST_PYR, COLOR1, EA, EB, P2, P0, N0,
  ST_TET, COLOR1, P2, EB, EL, N0,
 // Case #14: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EK, EL, ED, EA, EJ,
  ST_PYR, COLOR0, P7, P6, P5, P4, N0,
  ST_TET, COLOR0, P5, N0, P0, P4,
  ST_PYR, COLOR0, P6, EL, EJ, P5, N0,
  ST_PYR, COLOR0, EK, EL, P6, P7, N0,
  ST_TET, COLOR0, P0, P7, P4, N0,
  ST_PYR, COLOR0, P0, ED, EK, P7, N0,
  ST_TET, COLOR0, P0, EA, ED, N0,
  ST_PYR, COLOR0, EJ, EA, P0, P5, N0,
  ST_PYR, COLOR1, EL, P2, P1, EJ, N0,
  ST_PYR, COLOR1, EK, P3, P2, EL, N0,
  ST_TET, COLOR1, ED, P3, EK, N0,
  ST_TET, COLOR1, P3, P1, P2, N0,
  ST_PYR, COLOR1, ED, EA, P1, P3, N0,
  ST_TET, COLOR1, P1, EA, EJ, N0,
 // Case #15: Unique case #6
  ST_HEX, COLOR0, EI, EJ, EL, EK, P4, P5, P6, P7,
  ST_HEX, COLOR1, P0, P1, P2, P3, EI, EJ, EL, EK,
 // Case #16: (cloned #1)
  ST_PNT, 0, COLOR0, 7, P5, P1, P0, P7, P6, P2, P3,
  ST_WDG, COLOR0, P5, P0, P7, EE, EI, EH,
  ST_TET, COLOR0, P5, P0, P7, N0,
  ST_TET, COLOR0, P5, P1, P0, N0,
  ST_PYR, COLOR0, P2, P3, P0, P1, N0,
  ST_PYR, COLOR0, P6, P2, P1, P5, N0,
  ST_PYR, COLOR0, P7, P3, P2, P6, N0,
  ST_TET, COLOR0, P0, P3, P7, N0,
  ST_TET, COLOR0, P7, P6, P5, N0,
  ST_TET, COLOR1, P4, EE, EI, EH,
 // Case #17: (cloned #3)
  ST_HEX, COLOR0, EE, P5, P1, EA, EH, P7, P3, ED,
  ST_WDG, COLOR0, P3, P2, P1, P7, P6, P5,
  ST_WDG, COLOR1, P0, ED, EA, P4, EH, EE,
 // Case #18: (cloned #5)
  ST_PNT, 0, NOCOLOR, 2, EH, EB,
  ST_PYR, COLOR0, P7, P3, P2, P6, N0,
  ST_TET, COLOR0, P6, P2, P5, N0,
  ST_TET, COLOR0, P7, P6, P5, N0,
  ST_TET, COLOR0, P0, P3, P7, N0,
  ST_TET, COLOR0, P2, P3, P0, N0,
  ST_PYR, COLOR0, P2, P0, EA, EB, N0,
  ST_PYR, COLOR0, P5, P2, EB, EJ, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_PYR, COLOR0, P7, P5, EE, EH, N0,
  ST_PYR, COLOR0, P7, EH, EI, P0, N0,
  ST_TET, COLOR0, P0, EI, EA, N0,
  ST_PYR, COLOR1, P4, P1, EA, EI, N0,
  ST_PYR, COLOR1, EE, EJ, P1, P4, N0,
  ST_TET, COLOR1, EJ, EB, P1, N0,
  ST_TET, COLOR1, P1, EB, EA, N0,
  ST_TET, COLOR1, EE, N0, P4, EH,
  ST_TET, COLOR1, EI, EH, P4, N0,
 // Case #19: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EB, ED, EJ, EE, EH,
  ST_PYR, COLOR0, P2, P6, P7, P3, N0,
  ST_TET, COLOR0, P7, P5, N0, P6,
  ST_PYR, COLOR0, P3, P7, EH, ED, N0,
  ST_PYR, COLOR0, EB, P2, P3, ED, N0,
  ST_TET, COLOR0, P5, P6, P2, N0,
  ST_PYR, COLOR0, P5, P2, EB, EJ, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_PYR, COLOR0, EH, P7, P5, EE, N0,
  ST_PYR, COLOR1, ED, EH, P4, P0, N0,
  ST_PYR, COLOR1, EB, ED, P0, P1, N0,
  ST_TET, COLOR1, EJ, EB, P1, N0,
  ST_TET, COLOR1, P1, P0, P4, N0,
  ST_PYR, COLOR1, EJ, P1, P4, EE, N0,
  ST_TET, COLOR1, P4, EH, EE, N0,
 // Case #20: Unique case #7
  ST_WDG, COLOR0, EB, EC, EL, P1, P3, P6,
  ST_WDG, COLOR0, P0, P7, P5, EI, EH, EE,
  ST_TET, COLOR0, P3, P1, P6, P7,
  ST_TET, COLOR0, P5, P7, P6, P1,
  ST_TET, COLOR0, P0, P5, P1, P7,
  ST_TET, COLOR0, P3, P7, P0, P1,
  ST_TET, COLOR1, P4, EE, EI, EH,
  ST_TET, COLOR1, P2, EC, EB, EL,
 // Case #21: Unique case #8
  ST_PNT, 0, NOCOLOR, 4, EE, EH, EL, EL,
  ST_PYR, COLOR0, P6, P3, EC, EL, N0,
  ST_TET, COLOR0, EC, P3, ED, N0,
  ST_PYR, COLOR0, P7, EH, ED, P3, N0,
  ST_TET, COLOR0, P6, P7, P3, N0,
  ST_TET, COLOR0, P1, EB, EA, N0,
  ST_TET, COLOR0, P5, P6, P1, N0,
  ST_PYR, COLOR0, P1, P6, EL, EB, N0,
  ST_TET, COLOR0, P5, P7, P6, N0,
  ST_PYR, COLOR0, P5, EE, EH, P7, N0,
  ST_PYR, COLOR0, P5, P1, EA, EE, N0,
  ST_PYR, COLOR1, P2, EC, ED, P0, N0,
  ST_PYR, COLOR1, EA, EB, P2, P0, N0,
  ST_TET, COLOR1, P2, EL, EC, N0,
  ST_TET, COLOR1, EB, EL, P2, N0,
  ST_PYR, COLOR1, ED, EH, P4, P0, N0,
  ST_PYR, COLOR1, P0, P4, EE, EA, N0,
  ST_TET, COLOR1, EE, P4, EH, N0,
 // Case #22: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EL, EC, EH, EH,
  ST_PYR, COLOR0, P7, EH, EI, P0, N0,
  ST_TET, COLOR0, EI, EA, P0, N0,
  ST_PYR, COLOR0, P3, P0, EA, EC, N0,
  ST_TET, COLOR0, P7, P0, P3, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_TET, COLOR0, P6, P5, P7, N0,
  ST_PYR, COLOR0, P5, EE, EH, P7, N0,
  ST_TET, COLOR0, P6, P7, P3, N0,
  ST_PYR, COLOR0, P6, P3, EC, EL, N0,
  ST_PYR, COLOR0, P6, EL, EJ, P5, N0,
  ST_PYR, COLOR1, P4, P1, EA, EI, N0,
  ST_PYR, COLOR1, EJ, P1, P4, EE, N0,
  ST_TET, COLOR1, P4, EI, EH, N0,
  ST_TET, COLOR1, EE, P4, EH, N0,
  ST_PYR, COLOR1, EA, P1, P2, EC, N0,
  ST_PYR, COLOR1, P1, EJ, EL, P2, N0,
  ST_TET, COLOR1, EL, EC, P2, N0,
 // Case #23: Unique case #9
  ST_PNT, 0, NOCOLOR, 6, ED, EC, EL, EJ, EE, EH,
  ST_TET, COLOR0, P6, P5, P7, N0,
  ST_PYR, COLOR0, P6, EL, EJ, P5, N0,
  ST_TET, COLOR0, P3, P6, P7, N0,
  ST_PYR, COLOR0, P3, EC, EL, P6, N0,
  ST_TET, COLOR0, ED, EC, P3, N0,
  ST_PYR, COLOR0, P3, P7, EH, ED, N0,
  ST_PYR, COLOR0, EH, P7, P5, EE, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_TET, COLOR1, P0, P1, P2, N0,
  ST_PYR, COLOR1, ED, P0, P2, EC, N0,
  ST_PYR, COLOR1, ED, EH, P4, P0, N0,
  ST_TET, COLOR1, P4, P1, P0, N0,
  ST_TET, COLOR1, P4, EH, EE, N0,
  ST_PYR, COLOR1, P4, EE, EJ, P1, N0,
  ST_PYR, COLOR1, EJ, EL, P2, P1, N0,
  ST_TET, COLOR1, EL, EC, P2, N0,
 // Case #24: (cloned #5)
  ST_PNT, 0, NOCOLOR, 2, EC, EE,
  ST_PYR, COLOR0, P2, P1, P5, P6, N0,
  ST_TET, COLOR0, P6, P5, P7, N0,
  ST_TET, COLOR0, P2, P6, P7, N0,
  ST_TET, COLOR0, P0, P1, P2, N0,
  ST_TET, COLOR0, P5, P1, P0, N0,
  ST_PYR, COLOR0, P5, P0, EI, EE, N0,
  ST_PYR, COLOR0, P7, P5, EE, EH, N0,
  ST_TET, COLOR0, P7, EH, EK, N0,
  ST_PYR, COLOR0, P2, P7, EK, EC, N0,
  ST_PYR, COLOR0, P2, EC, ED, P0, N0,
  ST_TET, COLOR0, P0, ED, EI, N0,
  ST_PYR, COLOR1, P3, P4, EI, ED, N0,
  ST_PYR, COLOR1, EK, EH, P4, P3, N0,
  ST_TET, COLOR1, EH, EE, P4, N0,
  ST_TET, COLOR1, P4, EE, EI, N0,
  ST_TET, COLOR1, EK, N0, P3, EC,
  ST_TET, COLOR1, ED, EC, P3, N0,
 // Case #25: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EE, EA, EH, EK, EC,
  ST_PYR, COLOR0, P5, P6, P2, P1, N0,
  ST_TET, COLOR0, P2, P7, N0, P6,
  ST_PYR, COLOR0, P1, P2, EC, EA, N0,
  ST_PYR, COLOR0, EE, P5, P1, EA, N0,
  ST_TET, COLOR0, P7, P6, P5, N0,
  ST_PYR, COLOR0, P7, P5, EE, EH, N0,
  ST_TET, COLOR0, P7, EH, EK, N0,
  ST_PYR, COLOR0, EC, P2, P7, EK, N0,
  ST_PYR, COLOR1, EA, EC, P3, P0, N0,
  ST_PYR, COLOR1, EE, EA, P0, P4, N0,
  ST_TET, COLOR1, EH, EE, P4, N0,
  ST_TET, COLOR1, P4, P0, P3, N0,
  ST_PYR, COLOR1, EH, P4, P3, EK, N0,
  ST_TET, COLOR1, P3, EC, EK, N0,
 // Case #26: Unique case #10
  ST_TET, COLOR0, P0, EA, ED, EI,
  ST_TET, COLOR0, P5, P7, P6, P2,
  ST_PYR, COLOR0, EC, P2, P7, EK, EH,
  ST_PYR, COLOR0, EB, EJ, P5, P2, EE,
  ST_PYR, COLOR0, P7, P5, EE, EH, P2,
  ST_PYR, COLOR0, EH, EE, EB, EC, P2,
  ST_WDG, COLOR1, ED, EA, EI, P3, P1, P4,
  ST_PYR, COLOR1, P3, EK, EH, P4, EC,
  ST_PYR, COLOR1, EE, EJ, P1, P4, EB,
  ST_PYR, COLOR1, EC, P3, P1, EB, P4,
  ST_PYR, COLOR1, EC, EB, EE, EH, P4,
 // Case #27: Unique case #11
  ST_TET, COLOR0, P5, P7, P6, P2,
  ST_PYR, COLOR0, EC, P2, P7, EK, EH,
  ST_PYR, COLOR0, EB, EJ, P5, P2, EE,
  ST_PYR, COLOR0, P7, P5, EE, EH, P2,
  ST_PYR, COLOR0, EH, EE, EB, EC, P2,
  ST_TET, COLOR1, P0, P1, P3, P4,
  ST_PYR, COLOR1, EH, P4, P3, EK, EC,
  ST_PYR, COLOR1, EE, EJ, P1, P4, EB,
  ST_PYR, COLOR1, P3, P1, EB, EC, P4,
  ST_PYR, COLOR1, EH, EC, EB, EE, P4,
 // Case #28: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EL, EB, EE, EE,
  ST_PYR, COLOR0, P5, P0, EI, EE, N0,
  ST_TET, COLOR0, EI, P0, ED, N0,
  ST_PYR, COLOR0, P1, EB, ED, P0, N0,
  ST_TET, COLOR0, P5, P1, P0, N0,
  ST_TET, COLOR0, P7, EH, EK, N0,
  ST_TET, COLOR0, P6, P5, P7, N0,
  ST_PYR, COLOR0, P7, P5, EE, EH, N0,
  ST_TET, COLOR0, P6, P1, P5, N0,
  ST_PYR, COLOR0, P6, EL, EB, P1, N0,
  ST_PYR, COLOR0, P6, P7, EK, EL, N0,
  ST_PYR, COLOR1, P4, EI, ED, P3, N0,
  ST_PYR, COLOR1, EK, EH, P4, P3, N0,
  ST_TET, COLOR1, P4, EE, EI, N0,
  ST_TET, COLOR1, EH, EE, P4, N0,
  ST_PYR, COLOR1, ED, EB, P2, P3, N0,
  ST_PYR, COLOR1, P3, P2, EL, EK, N0,
  ST_TET, COLOR1, EL, P2, EB, N0,
 // Case #29: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EA, EB, EL, EK, EH, EE,
  ST_TET, COLOR0, P6, P5, P7, N0,
  ST_PYR, COLOR0, P6, P7, EK, EL, N0,
  ST_TET, COLOR0, P1, P5, P6, N0,
  ST_PYR, COLOR0, P1, P6, EL, EB, N0,
  ST_TET, COLOR0, EA, P1, EB, N0,
  ST_PYR, COLOR0, P1, EA, EE, P5, N0,
  ST_PYR, COLOR0, EE, EH, P7, P5, N0,
  ST_TET, COLOR0, P7, EH, EK, N0,
  ST_TET, COLOR1, P0, P2, P3, N0,
  ST_PYR, COLOR1, EA, EB, P2, P0, N0,
  ST_PYR, COLOR1, EA, P0, P4, EE, N0,
  ST_TET, COLOR1, P4, P0, P3, N0,
  ST_TET, COLOR1, P4, EH, EE, N0,
  ST_PYR, COLOR1, P4, P3, EK, EH, N0,
  ST_PYR, COLOR1, EK, P3, P2, EL, N0,
  ST_TET, COLOR1, EL, P2, EB, N0,
 // Case #30: Unique case #12
  ST_PNT, 0, NOCOLOR, 5, EL, EJ, EK, EH, EE,
  ST_TET, COLOR0, P0, EA, ED, EI,
  ST_PYR, COLOR0, P5, P6, EL, EJ, N0,
  ST_PYR, COLOR0, P6, P7, EK, EL, N0,
  ST_TET, COLOR0, P7, EH, EK, N0,
  ST_TET, COLOR0, P6, P5, P7, N0,
  ST_PYR, COLOR0, P7, P5, EE, EH, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_WDG, COLOR1, ED, EA, EI, P3, P1, P4,
  ST_TET, COLOR1, P1, P3, P4, N0,
  ST_PYR, COLOR1, P3, EK, EH, P4, N0,
  ST_TET, COLOR1, P2, P3, P1, N0,
  ST_PYR, COLOR1, EJ, EL, P2, P1, N0,
  ST_PYR, COLOR1, EL, EK, P3, P2, N0,
  ST_PYR, COLOR1, P4, EE, EJ, P1, N0,
  ST_TET, COLOR1, EH, EE, P4, N0,
 // Case #31: Unique case #13
  ST_PNT, 0, NOCOLOR, 5, EJ, EL, EK, EE, EH,
  ST_PYR, COLOR0, P6, P7, EK, EL, N0,
  ST_TET, COLOR0, P7, EH, EK, N0,
  ST_PYR, COLOR0, P5, P6, EL, EJ, N0,
  ST_TET, COLOR0, EE, P5, EJ, N0,
  ST_PYR, COLOR0, EH, P7, P5, EE, N0,
  ST_TET, COLOR0, P7, P6, P5, N0,
  ST_PYR, COLOR1, P0, P1, P2, P3, N0,
  ST_TET, COLOR1, P3, P4, P0, N0,
  ST_TET, COLOR1, P4, P1, P0, N0,
  ST_PYR, COLOR1, P4, EE, EJ, P1, N0,
  ST_PYR, COLOR1, EJ, EL, P2, P1, N0,
  ST_PYR, COLOR1, EL, EK, P3, P2, N0,
  ST_PYR, COLOR1, EK, EH, P4, P3, N0,
  ST_TET, COLOR1, EE, P4, EH, N0,
 // Case #32: (cloned #1)
  ST_PNT, 0, COLOR0, 7, P6, P2, P1, P4, P7, P3, P0,
  ST_WDG, COLOR0, P6, P1, P4, EF, EJ, EE,
  ST_TET, COLOR0, P6, P1, P4, N0,
  ST_TET, COLOR0, P6, P2, P1, N0,
  ST_PYR, COLOR0, P3, P0, P1, P2, N0,
  ST_PYR, COLOR0, P7, P3, P2, P6, N0,
  ST_PYR, COLOR0, P4, P0, P3, P7, N0,
  ST_TET, COLOR0, P1, P0, P4, N0,
  ST_TET, COLOR0, P4, P7, P6, N0,
  ST_TET, COLOR1, P5, EF, EJ, EE,
 // Case #33: (cloned #5)
  ST_PNT, 0, NOCOLOR, 2, ED, EF,
  ST_PYR, COLOR0, P3, P2, P6, P7, N0,
  ST_TET, COLOR0, P2, P1, P6, N0,
  ST_TET, COLOR0, P3, P1, P2, N0,
  ST_TET, COLOR0, P4, P3, P7, N0,
  ST_TET, COLOR0, P6, P4, P7, N0,
  ST_PYR, COLOR0, P6, EF, EE, P4, N0,
  ST_PYR, COLOR0, P1, EJ, EF, P6, N0,
  ST_TET, COLOR0, P1, EA, EJ, N0,
  ST_PYR, COLOR0, P3, ED, EA, P1, N0,
  ST_PYR, COLOR0, P3, P4, EI, ED, N0,
  ST_TET, COLOR0, P4, EE, EI, N0,
  ST_PYR, COLOR1, P0, EI, EE, P5, N0,
  ST_PYR, COLOR1, EA, P0, P5, EJ, N0,
  ST_TET, COLOR1, EJ, P5, EF, N0,
  ST_TET, COLOR1, P5, EE, EF, N0,
  ST_TET, COLOR1, EA, P0, N0, ED,
  ST_TET, COLOR1, EI, P0, ED, N0,
 // Case #34: (cloned #3)
  ST_HEX, COLOR0, EF, P6, P2, EB, EE, P4, P0, EA,
  ST_WDG, COLOR0, P0, P3, P2, P4, P7, P6,
  ST_WDG, COLOR1, P1, EA, EB, P5, EE, EF,
 // Case #35: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, ED, EB, EI, EE, EF,
  ST_PYR, COLOR0, P3, P2, P6, P7, N0,
  ST_TET, COLOR0, P6, N0, P4, P7,
  ST_PYR, COLOR0, P2, EB, EF, P6, N0,
  ST_PYR, COLOR0, ED, EB, P2, P3, N0,
  ST_TET, COLOR0, P4, P3, P7, N0,
  ST_PYR, COLOR0, P4, EI, ED, P3, N0,
  ST_TET, COLOR0, P4, EE, EI, N0,
  ST_PYR, COLOR0, EF, EE, P4, P6, N0,
  ST_PYR, COLOR1, EB, P1, P5, EF, N0,
  ST_PYR, COLOR1, ED, P0, P1, EB, N0,
  ST_TET, COLOR1, EI, P0, ED, N0,
  ST_TET, COLOR1, P0, P5, P1, N0,
  ST_PYR, COLOR1, EI, EE, P5, P0, N0,
  ST_TET, COLOR1, P5, EE, EF, N0,
 // Case #36: (cloned #5)
  ST_PNT, 0, NOCOLOR, 2, EC, EE,
  ST_PYR, COLOR0, P3, P7, P4, P0, N0,
  ST_TET, COLOR0, P7, P6, P4, N0,
  ST_TET, COLOR0, P3, P6, P7, N0,
  ST_TET, COLOR0, P1, P3, P0, N0,
  ST_TET, COLOR0, P4, P1, P0, N0,
  ST_PYR, COLOR0, P4, EE, EJ, P1, N0,
  ST_PYR, COLOR0, P6, EF, EE, P4, N0,
  ST_TET, COLOR0, P6, EL, EF, N0,
  ST_PYR, COLOR0, P3, EC, EL, P6, N0,
  ST_PYR, COLOR0, P3, P1, EB, EC, N0,
  ST_TET, COLOR0, P1, EJ, EB, N0,
  ST_PYR, COLOR1, P2, EB, EJ, P5, N0,
  ST_PYR, COLOR1, EL, P2, P5, EF, N0,
  ST_TET, COLOR1, EF, P5, EE, N0,
  ST_TET, COLOR1, P5, EJ, EE, N0,
  ST_TET, COLOR1, EL, P2, N0, EC,
  ST_TET, COLOR1, EB, P2, EC, N0,
 // Case #37: (cloned #26)
  ST_TET, COLOR0, P1, EA, EJ, EB,
  ST_TET, COLOR0, P6, P7, P3, P4,
  ST_PYR, COLOR0, EI, ED, P3, P4, EC,
  ST_PYR, COLOR0, EE, P4, P6, EF, EL,
  ST_PYR, COLOR0, P3, EC, EL, P6, P4,
  ST_PYR, COLOR0, EC, EI, EE, EL, P4,
  ST_WDG, COLOR1, P0, P5, P2, EA, EJ, EB,
  ST_PYR, COLOR1, P0, P2, EC, ED, EI,
  ST_PYR, COLOR1, EL, P2, P5, EF, EE,
  ST_PYR, COLOR1, EI, EE, P5, P0, P2,
  ST_PYR, COLOR1, EI, EC, EL, EE, P2,
 // Case #38: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EE, EA, EF, EL, EC,
  ST_PYR, COLOR0, P4, P0, P3, P7, N0,
  ST_TET, COLOR0, P3, N0, P6, P7,
  ST_PYR, COLOR0, P0, EA, EC, P3, N0,
  ST_PYR, COLOR0, EE, EA, P0, P4, N0,
  ST_TET, COLOR0, P6, P4, P7, N0,
  ST_PYR, COLOR0, P6, EF, EE, P4, N0,
  ST_TET, COLOR0, P6, EL, EF, N0,
  ST_PYR, COLOR0, EC, EL, P6, P3, N0,
  ST_PYR, COLOR1, EA, P1, P2, EC, N0,
  ST_PYR, COLOR1, EE, P5, P1, EA, N0,
  ST_TET, COLOR1, EF, P5, EE, N0,
  ST_TET, COLOR1, P5, P2, P1, N0,
  ST_PYR, COLOR1, EF, EL, P2, P5, N0,
  ST_TET, COLOR1, P2, EL, EC, N0,
 // Case #39: (cloned #27)
  ST_TET, COLOR0, P6, P7, P3, P4,
  ST_PYR, COLOR0, EI, ED, P3, P4, EC,
  ST_PYR, COLOR0, EE, P4, P6, EF, EL,
  ST_PYR, COLOR0, P3, EC, EL, P6, P4,
  ST_PYR, COLOR0, EC, EI, EE, EL, P4,
  ST_TET, COLOR1, P1, P0, P5, P2,
  ST_PYR, COLOR1, EC, ED, P0, P2, EI,
  ST_PYR, COLOR1, EL, P2, P5, EF, EE,
  ST_PYR, COLOR1, P0, EI, EE, P5, P2,
  ST_PYR, COLOR1, EC, EL, EE, EI, P2,
 // Case #40: (cloned #20)
  ST_WDG, COLOR0, P1, P4, P6, EJ, EE, EF,
  ST_WDG, COLOR0, ED, EK, EC, P0, P7, P2,
  ST_TET, COLOR0, P4, P6, P1, P7,
  ST_TET, COLOR0, P2, P6, P7, P1,
  ST_TET, COLOR0, P0, P1, P2, P7,
  ST_TET, COLOR0, P4, P0, P7, P1,
  ST_TET, COLOR1, P3, ED, EC, EK,
  ST_TET, COLOR1, P5, EJ, EE, EF,
 // Case #41: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EC, EK, EF, EF,
  ST_PYR, COLOR0, P6, EF, EE, P4, N0,
  ST_TET, COLOR0, EE, EI, P4, N0,
  ST_PYR, COLOR0, P7, P4, EI, EK, N0,
  ST_TET, COLOR0, P6, P4, P7, N0,
  ST_TET, COLOR0, P1, EA, EJ, N0,
  ST_TET, COLOR0, P2, P1, P6, N0,
  ST_PYR, COLOR0, P1, EJ, EF, P6, N0,
  ST_TET, COLOR0, P2, P6, P7, N0,
  ST_PYR, COLOR0, P2, P7, EK, EC, N0,
  ST_PYR, COLOR0, P2, EC, EA, P1, N0,
  ST_PYR, COLOR1, P5, P0, EI, EE, N0,
  ST_PYR, COLOR1, EA, P0, P5, EJ, N0,
  ST_TET, COLOR1, P5, EE, EF, N0,
  ST_TET, COLOR1, EJ, P5, EF, N0,
  ST_PYR, COLOR1, EI, P0, P3, EK, N0,
  ST_PYR, COLOR1, P0, EA, EC, P3, N0,
  ST_TET, COLOR1, EC, EK, P3, N0,
 // Case #42: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EF, EE, EK, EK,
  ST_PYR, COLOR0, P7, P0, ED, EK, N0,
  ST_TET, COLOR0, ED, P0, EA, N0,
  ST_PYR, COLOR0, P4, EE, EA, P0, N0,
  ST_TET, COLOR0, P7, P4, P0, N0,
  ST_TET, COLOR0, P2, EC, EB, N0,
  ST_TET, COLOR0, P6, P7, P2, N0,
  ST_PYR, COLOR0, P2, P7, EK, EC, N0,
  ST_TET, COLOR0, P6, P4, P7, N0,
  ST_PYR, COLOR0, P6, EF, EE, P4, N0,
  ST_PYR, COLOR0, P6, P2, EB, EF, N0,
  ST_PYR, COLOR1, P3, ED, EA, P1, N0,
  ST_PYR, COLOR1, EB, EC, P3, P1, N0,
  ST_TET, COLOR1, P3, EK, ED, N0,
  ST_TET, COLOR1, EC, EK, P3, N0,
  ST_PYR, COLOR1, EA, EE, P5, P1, N0,
  ST_PYR, COLOR1, P1, P5, EF, EB, N0,
  ST_TET, COLOR1, EF, P5, EE, N0,
 // Case #43: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EI, EE, EF, EB, EC, EK,
  ST_TET, COLOR0, P6, P7, P2, N0,
  ST_PYR, COLOR0, P6, P2, EB, EF, N0,
  ST_TET, COLOR0, P4, P7, P6, N0,
  ST_PYR, COLOR0, P4, P6, EF, EE, N0,
  ST_TET, COLOR0, EI, P4, EE, N0,
  ST_PYR, COLOR0, P4, EI, EK, P7, N0,
  ST_PYR, COLOR0, EK, EC, P2, P7, N0,
  ST_TET, COLOR0, P2, EC, EB, N0,
  ST_TET, COLOR1, P0, P5, P1, N0,
  ST_PYR, COLOR1, EI, EE, P5, P0, N0,
  ST_PYR, COLOR1, EI, P0, P3, EK, N0,
  ST_TET, COLOR1, P3, P0, P1, N0,
  ST_TET, COLOR1, P3, EC, EK, N0,
  ST_PYR, COLOR1, P3, P1, EB, EC, N0,
  ST_PYR, COLOR1, EB, P1, P5, EF, N0,
  ST_TET, COLOR1, EF, P5, EE, N0,
 // Case #44: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EK, ED, EE, EE,
  ST_PYR, COLOR0, P4, EE, EJ, P1, N0,
  ST_TET, COLOR0, EJ, EB, P1, N0,
  ST_PYR, COLOR0, P0, P1, EB, ED, N0,
  ST_TET, COLOR0, P4, P1, P0, N0,
  ST_TET, COLOR0, P6, EL, EF, N0,
  ST_TET, COLOR0, P7, P6, P4, N0,
  ST_PYR, COLOR0, P6, EF, EE, P4, N0,
  ST_TET, COLOR0, P7, P4, P0, N0,
  ST_PYR, COLOR0, P7, P0, ED, EK, N0,
  ST_PYR, COLOR0, P7, EK, EL, P6, N0,
  ST_PYR, COLOR1, P5, P2, EB, EJ, N0,
  ST_PYR, COLOR1, EL, P2, P5, EF, N0,
  ST_TET, COLOR1, P5, EJ, EE, N0,
  ST_TET, COLOR1, EF, P5, EE, N0,
  ST_PYR, COLOR1, EB, P2, P3, ED, N0,
  ST_PYR, COLOR1, P2, EL, EK, P3, N0,
  ST_TET, COLOR1, EK, ED, P3, N0,
 // Case #45: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EK, EL, EI, EE, EF,
  ST_TET, COLOR0, P1, EB, EA, EJ,
  ST_PYR, COLOR0, P6, P7, EK, EL, N0,
  ST_PYR, COLOR0, P7, P4, EI, EK, N0,
  ST_TET, COLOR0, P4, EE, EI, N0,
  ST_TET, COLOR0, P7, P6, P4, N0,
  ST_PYR, COLOR0, P4, P6, EF, EE, N0,
  ST_TET, COLOR0, P6, EL, EF, N0,
  ST_WDG, COLOR1, EA, EB, EJ, P0, P2, P5,
  ST_TET, COLOR1, P2, P0, P5, N0,
  ST_PYR, COLOR1, P0, EI, EE, P5, N0,
  ST_TET, COLOR1, P3, P0, P2, N0,
  ST_PYR, COLOR1, EL, EK, P3, P2, N0,
  ST_PYR, COLOR1, EK, EI, P0, P3, N0,
  ST_PYR, COLOR1, P5, EF, EL, P2, N0,
  ST_TET, COLOR1, EE, EF, P5, N0,
 // Case #46: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EA, ED, EK, EL, EF, EE,
  ST_TET, COLOR0, P7, P6, P4, N0,
  ST_PYR, COLOR0, P7, EK, EL, P6, N0,
  ST_TET, COLOR0, P0, P7, P4, N0,
  ST_PYR, COLOR0, P0, ED, EK, P7, N0,
  ST_TET, COLOR0, EA, ED, P0, N0,
  ST_PYR, COLOR0, P0, P4, EE, EA, N0,
  ST_PYR, COLOR0, EE, P4, P6, EF, N0,
  ST_TET, COLOR0, P6, EL, EF, N0,
  ST_TET, COLOR1, P1, P2, P3, N0,
  ST_PYR, COLOR1, EA, P1, P3, ED, N0,
  ST_PYR, COLOR1, EA, EE, P5, P1, N0,
  ST_TET, COLOR1, P5, P2, P1, N0,
  ST_TET, COLOR1, P5, EE, EF, N0,
  ST_PYR, COLOR1, P5, EF, EL, P2, N0,
  ST_PYR, COLOR1, EL, EK, P3, P2, N0,
  ST_TET, COLOR1, EK, ED, P3, N0,
 // Case #47: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EL, EK, EI, EF, EE,
  ST_PYR, COLOR0, P7, P4, EI, EK, N0,
  ST_TET, COLOR0, P4, EE, EI, N0,
  ST_PYR, COLOR0, P6, P7, EK, EL, N0,
  ST_TET, COLOR0, EF, P6, EL, N0,
  ST_PYR, COLOR0, EE, P4, P6, EF, N0,
  ST_TET, COLOR0, P4, P7, P6, N0,
  ST_PYR, COLOR1, P1, P2, P3, P0, N0,
  ST_TET, COLOR1, P0, P5, P1, N0,
  ST_TET, COLOR1, P5, P2, P1, N0,
  ST_PYR, COLOR1, P5, EF, EL, P2, N0,
  ST_PYR, COLOR1, EL, EK, P3, P2, N0,
  ST_PYR, COLOR1, EK, EI, P0, P3, N0,
  ST_PYR, COLOR1, EI, EE, P5, P0, N0,
  ST_TET, COLOR1, EF, P5, EE, N0,
 // Case #48: (cloned #3)
  ST_HEX, COLOR0, EJ, P1, P0, EI, EF, P6, P7, EH,
  ST_WDG, COLOR0, P1, P2, P6, P0, P3, P7,
  ST_WDG, COLOR1, P5, EJ, EF, P4, EI, EH,
 // Case #49: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, ED, EH, EA, EJ, EF,
  ST_PYR, COLOR0, P3, P2, P6, P7, N0,
  ST_TET, COLOR0, P6, P1, N0, P2,
  ST_PYR, COLOR0, P7, P6, EF, EH, N0,
  ST_PYR, COLOR0, ED, P3, P7, EH, N0,
  ST_TET, COLOR0, P1, P2, P3, N0,
  ST_PYR, COLOR0, P1, P3, ED, EA, N0,
  ST_TET, COLOR0, P1, EA, EJ, N0,
  ST_PYR, COLOR0, EF, P6, P1, EJ, N0,
  ST_PYR, COLOR1, EH, EF, P5, P4, N0,
  ST_PYR, COLOR1, ED, EH, P4, P0, N0,
  ST_TET, COLOR1, EA, ED, P0, N0,
  ST_TET, COLOR1, P0, P4, P5, N0,
  ST_PYR, COLOR1, EA, P0, P5, EJ, N0,
  ST_TET, COLOR1, P5, EF, EJ, N0,
 // Case #50: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EH, EF, EI, EA, EB,
  ST_PYR, COLOR0, P7, P3, P2, P6, N0,
  ST_TET, COLOR0, P2, P0, N0, P3,
  ST_PYR, COLOR0, P6, P2, EB, EF, N0,
  ST_PYR, COLOR0, EH, P7, P6, EF, N0,
  ST_TET, COLOR0, P0, P3, P7, N0,
  ST_PYR, COLOR0, P0, P7, EH, EI, N0,
  ST_TET, COLOR0, P0, EI, EA, N0,
  ST_PYR, COLOR0, EB, P2, P0, EA, N0,
  ST_PYR, COLOR1, EF, EB, P1, P5, N0,
  ST_PYR, COLOR1, EH, EF, P5, P4, N0,
  ST_TET, COLOR1, EI, EH, P4, N0,
  ST_TET, COLOR1, P4, P5, P1, N0,
  ST_PYR, COLOR1, EI, P4, P1, EA, N0,
  ST_TET, COLOR1, P1, EB, EA, N0,
 // Case #51: (cloned #15)
  ST_HEX, COLOR0, P3, P2, P6, P7, ED, EB, EF, EH,
  ST_HEX, COLOR1, ED, EB, EF, EH, P0, P1, P5, P4,
 // Case #52: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EH, EI, EC, EC,
  ST_PYR, COLOR0, P3, P1, EB, EC, N0,
  ST_TET, COLOR0, EB, P1, EJ, N0,
  ST_PYR, COLOR0, P0, EI, EJ, P1, N0,
  ST_TET, COLOR0, P3, P0, P1, N0,
  ST_TET, COLOR0, P6, EL, EF, N0,
  ST_TET, COLOR0, P7, P3, P6, N0,
  ST_PYR, COLOR0, P6, P3, EC, EL, N0,
  ST_TET, COLOR0, P7, P0, P3, N0,
  ST_PYR, COLOR0, P7, EH, EI, P0, N0,
  ST_PYR, COLOR0, P7, P6, EF, EH, N0,
  ST_PYR, COLOR1, P2, EB, EJ, P5, N0,
  ST_PYR, COLOR1, EF, EL, P2, P5, N0,
  ST_TET, COLOR1, P2, EC, EB, N0,
  ST_TET, COLOR1, EL, EC, P2, N0,
  ST_PYR, COLOR1, EJ, EI, P4, P5, N0,
  ST_PYR, COLOR1, P5, P4, EH, EF, N0,
  ST_TET, COLOR1, EH, P4, EI, N0,
 // Case #53: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EH, EF, ED, EC, EL,
  ST_TET, COLOR0, P1, EA, EJ, EB,
  ST_PYR, COLOR0, P6, EF, EH, P7, N0,
  ST_PYR, COLOR0, P7, EH, ED, P3, N0,
  ST_TET, COLOR0, P3, ED, EC, N0,
  ST_TET, COLOR0, P7, P3, P6, N0,
  ST_PYR, COLOR0, P3, EC, EL, P6, N0,
  ST_TET, COLOR0, P6, EL, EF, N0,
  ST_WDG, COLOR1, P0, P5, P2, EA, EJ, EB,
  ST_TET, COLOR1, P5, P2, P0, N0,
  ST_PYR, COLOR1, P0, P2, EC, ED, N0,
  ST_TET, COLOR1, P4, P5, P0, N0,
  ST_PYR, COLOR1, EF, P5, P4, EH, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_PYR, COLOR1, P2, P5, EF, EL, N0,
  ST_TET, COLOR1, EC, P2, EL, N0,
 // Case #54: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EA, EI, EH, EF, EL, EC,
  ST_TET, COLOR0, P7, P3, P6, N0,
  ST_PYR, COLOR0, P7, P6, EF, EH, N0,
  ST_TET, COLOR0, P0, P3, P7, N0,
  ST_PYR, COLOR0, P0, P7, EH, EI, N0,
  ST_TET, COLOR0, EA, P0, EI, N0,
  ST_PYR, COLOR0, P0, EA, EC, P3, N0,
  ST_PYR, COLOR0, EC, EL, P6, P3, N0,
  ST_TET, COLOR0, P6, EL, EF, N0,
  ST_TET, COLOR1, P1, P4, P5, N0,
  ST_PYR, COLOR1, EA, EI, P4, P1, N0,
  ST_PYR, COLOR1, EA, P1, P2, EC, N0,
  ST_TET, COLOR1, P2, P1, P5, N0,
  ST_TET, COLOR1, P2, EL, EC, N0,
  ST_PYR, COLOR1, P2, P5, EF, EL, N0,
  ST_PYR, COLOR1, EF, P5, P4, EH, N0,
  ST_TET, COLOR1, EH, P4, EI, N0,
 // Case #55: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EF, EH, ED, EL, EC,
  ST_PYR, COLOR0, P7, EH, ED, P3, N0,
  ST_TET, COLOR0, P3, ED, EC, N0,
  ST_PYR, COLOR0, P6, EF, EH, P7, N0,
  ST_TET, COLOR0, EL, EF, P6, N0,
  ST_PYR, COLOR0, EC, EL, P6, P3, N0,
  ST_TET, COLOR0, P3, P6, P7, N0,
  ST_PYR, COLOR1, P1, P0, P4, P5, N0,
  ST_TET, COLOR1, P0, P1, P2, N0,
  ST_TET, COLOR1, P2, P1, P5, N0,
  ST_PYR, COLOR1, P2, P5, EF, EL, N0,
  ST_PYR, COLOR1, EF, P5, P4, EH, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_PYR, COLOR1, ED, P0, P2, EC, N0,
  ST_TET, COLOR1, EL, EC, P2, N0,
 // Case #56: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EF, EJ, EC, EC,
  ST_PYR, COLOR0, P2, EC, ED, P0, N0,
  ST_TET, COLOR0, ED, EI, P0, N0,
  ST_PYR, COLOR0, P1, P0, EI, EJ, N0,
  ST_TET, COLOR0, P2, P0, P1, N0,
  ST_TET, COLOR0, P7, EH, EK, N0,
  ST_TET, COLOR0, P6, P7, P2, N0,
  ST_PYR, COLOR0, P7, EK, EC, P2, N0,
  ST_TET, COLOR0, P6, P2, P1, N0,
  ST_PYR, COLOR0, P6, P1, EJ, EF, N0,
  ST_PYR, COLOR0, P6, EF, EH, P7, N0,
  ST_PYR, COLOR1, P3, P4, EI, ED, N0,
  ST_PYR, COLOR1, EH, P4, P3, EK, N0,
  ST_TET, COLOR1, P3, ED, EC, N0,
  ST_TET, COLOR1, EK, P3, EC, N0,
  ST_PYR, COLOR1, EI, P4, P5, EJ, N0,
  ST_PYR, COLOR1, P4, EH, EF, P5, N0,
  ST_TET, COLOR1, EF, EJ, P5, N0,
 // Case #57: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EA, EJ, EF, EH, EK, EC,
  ST_TET, COLOR0, P6, P7, P2, N0,
  ST_PYR, COLOR0, P6, EF, EH, P7, N0,
  ST_TET, COLOR0, P1, P6, P2, N0,
  ST_PYR, COLOR0, P1, EJ, EF, P6, N0,
  ST_TET, COLOR0, EA, EJ, P1, N0,
  ST_PYR, COLOR0, P1, P2, EC, EA, N0,
  ST_PYR, COLOR0, EC, P2, P7, EK, N0,
  ST_TET, COLOR0, P7, EH, EK, N0,
  ST_TET, COLOR1, P0, P4, P5, N0,
  ST_PYR, COLOR1, EA, P0, P5, EJ, N0,
  ST_PYR, COLOR1, EA, EC, P3, P0, N0,
  ST_TET, COLOR1, P3, P4, P0, N0,
  ST_TET, COLOR1, P3, EC, EK, N0,
  ST_PYR, COLOR1, P3, EK, EH, P4, N0,
  ST_PYR, COLOR1, EH, EF, P5, P4, N0,
  ST_TET, COLOR1, EF, EJ, P5, N0,
 // Case #58: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EF, EB, EH, EK, EC,
  ST_TET, COLOR0, P0, EI, EA, ED,
  ST_PYR, COLOR0, P2, EB, EF, P6, N0,
  ST_PYR, COLOR0, P6, EF, EH, P7, N0,
  ST_TET, COLOR0, P7, EH, EK, N0,
  ST_TET, COLOR0, P6, P7, P2, N0,
  ST_PYR, COLOR0, P7, EK, EC, P2, N0,
  ST_TET, COLOR0, P2, EC, EB, N0,
  ST_WDG, COLOR1, P4, P1, P3, EI, EA, ED,
  ST_TET, COLOR1, P1, P3, P4, N0,
  ST_PYR, COLOR1, P4, P3, EK, EH, N0,
  ST_TET, COLOR1, P5, P1, P4, N0,
  ST_PYR, COLOR1, EB, P1, P5, EF, N0,
  ST_PYR, COLOR1, EF, P5, P4, EH, N0,
  ST_PYR, COLOR1, P3, P1, EB, EC, N0,
  ST_TET, COLOR1, EK, P3, EC, N0,
 // Case #59: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EB, EF, EH, EC, EK,
  ST_PYR, COLOR0, P6, EF, EH, P7, N0,
  ST_TET, COLOR0, P7, EH, EK, N0,
  ST_PYR, COLOR0, P2, EB, EF, P6, N0,
  ST_TET, COLOR0, EC, EB, P2, N0,
  ST_PYR, COLOR0, EK, EC, P2, P7, N0,
  ST_TET, COLOR0, P7, P2, P6, N0,
  ST_PYR, COLOR1, P0, P4, P5, P1, N0,
  ST_TET, COLOR1, P4, P0, P3, N0,
  ST_TET, COLOR1, P3, P0, P1, N0,
  ST_PYR, COLOR1, P3, P1, EB, EC, N0,
  ST_PYR, COLOR1, EB, P1, P5, EF, N0,
  ST_PYR, COLOR1, EF, P5, P4, EH, N0,
  ST_PYR, COLOR1, EH, P4, P3, EK, N0,
  ST_TET, COLOR1, EC, EK, P3, N0,
 // Case #60: Unique case #14
  ST_WDG, COLOR0, P1, EB, EJ, P0, ED, EI,
  ST_WDG, COLOR0, P6, EF, EL, P7, EH, EK,
  ST_HEX, COLOR1, P3, P4, P5, P2, EK, EH, EF, EL,
  ST_HEX, COLOR1, ED, EI, EJ, EB, P3, P4, P5, P2,
 // Case #61: Unique case #15
  ST_PNT, 0, COLOR1, 6, P0, P2, P3, P4, EF, EH,
  ST_WDG, COLOR0, EH, P7, EK, EF, P6, EL,
  ST_TET, COLOR0, EA, P1, EB, EJ,
  ST_WDG, COLOR1, P0, P5, P2, EA, EJ, EB,
  ST_PYR, COLOR1, EH, EF, P5, P4, N0,
  ST_TET, COLOR1, P4, P5, P0, N0,
  ST_TET, COLOR1, P4, P0, P3, N0,
  ST_PYR, COLOR1, EK, EH, P4, P3, N0,
  ST_PYR, COLOR1, EL, EK, P3, P2, N0,
  ST_TET, COLOR1, P3, P0, P2, N0,
  ST_PYR, COLOR1, EF, EH, EK, EL, N0,
  ST_TET, COLOR1, P2, P0, P5, N0,
  ST_PYR, COLOR1, EF, EL, P2, P5, N0,
 // Case #62: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P1, P3, P2, P5, EH, EF,
  ST_WDG, COLOR0, EH, P7, EK, EF, P6, EL,
  ST_TET, COLOR0, EA, ED, P0, EI,
  ST_WDG, COLOR1, EA, EI, ED, P1, P4, P3,
  ST_PYR, COLOR1, EF, P5, P4, EH, N0,
  ST_TET, COLOR1, P5, P1, P4, N0,
  ST_TET, COLOR1, P5, P2, P1, N0,
  ST_PYR, COLOR1, EL, P2, P5, EF, N0,
  ST_PYR, COLOR1, EK, P3, P2, EL, N0,
  ST_TET, COLOR1, P2, P3, P1, N0,
  ST_PYR, COLOR1, EH, EK, EL, EF, N0,
  ST_TET, COLOR1, P3, P4, P1, N0,
  ST_PYR, COLOR1, EH, P4, P3, EK, N0,
 // Case #63: Unique case #16
  ST_WDG, COLOR0, P7, EK, EH, P6, EL, EF,
  ST_HEX, COLOR1, P3, P4, P5, P2, EK, EH, EF, EL,
  ST_WDG, COLOR1, P1, P2, P5, P0, P3, P4,
 // Case #64: (cloned #1)
  ST_PNT, 0, COLOR0, 7, P7, P4, P5, P2, P3, P0, P1,
  ST_WDG, COLOR0, EG, EF, EL, P7, P5, P2,
  ST_TET, COLOR0, P7, P2, P5, N0,
  ST_TET, COLOR0, P7, P5, P4, N0,
  ST_PYR, COLOR0, P0, P4, P5, P1, N0,
  ST_PYR, COLOR0, P3, P7, P4, P0, N0,
  ST_PYR, COLOR0, P2, P3, P0, P1, N0,
  ST_TET, COLOR0, P5, P2, P1, N0,
  ST_TET, COLOR0, P2, P7, P3, N0,
  ST_TET, COLOR1, P6, EF, EG, EL,
 // Case #65: (cloned #20)
  ST_WDG, COLOR0, P5, P7, P2, EF, EG, EL,
  ST_WDG, COLOR0, EI, ED, EA, P4, P3, P1,
  ST_TET, COLOR0, P7, P2, P5, P3,
  ST_TET, COLOR0, P1, P2, P3, P5,
  ST_TET, COLOR0, P4, P5, P1, P3,
  ST_TET, COLOR0, P7, P4, P3, P5,
  ST_TET, COLOR1, P0, EI, EA, ED,
  ST_TET, COLOR1, P6, EF, EG, EL,
 // Case #66: (cloned #5)
  ST_PNT, 0, NOCOLOR, 2, EA, EG,
  ST_PYR, COLOR0, P0, P3, P7, P4, N0,
  ST_TET, COLOR0, P4, P7, P5, N0,
  ST_TET, COLOR0, P0, P4, P5, N0,
  ST_TET, COLOR0, P2, P3, P0, N0,
  ST_TET, COLOR0, P7, P3, P2, N0,
  ST_PYR, COLOR0, P7, P2, EL, EG, N0,
  ST_PYR, COLOR0, P5, P7, EG, EF, N0,
  ST_TET, COLOR0, P5, EF, EJ, N0,
  ST_PYR, COLOR0, P0, P5, EJ, EA, N0,
  ST_PYR, COLOR0, P0, EA, EB, P2, N0,
  ST_TET, COLOR0, P2, EB, EL, N0,
  ST_PYR, COLOR1, P1, P6, EL, EB, N0,
  ST_PYR, COLOR1, EJ, EF, P6, P1, N0,
  ST_TET, COLOR1, EF, EG, P6, N0,
  ST_TET, COLOR1, P6, EG, EL, N0,
  ST_TET, COLOR1, EJ, N0, P1, EA,
  ST_TET, COLOR1, EB, EA, P1, N0,
 // Case #67: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EI, ED, EG, EG,
  ST_PYR, COLOR0, P7, P2, EL, EG, N0,
  ST_TET, COLOR0, EL, P2, EB, N0,
  ST_PYR, COLOR0, P3, ED, EB, P2, N0,
  ST_TET, COLOR0, P7, P3, P2, N0,
  ST_TET, COLOR0, P5, EF, EJ, N0,
  ST_TET, COLOR0, P4, P7, P5, N0,
  ST_PYR, COLOR0, P5, P7, EG, EF, N0,
  ST_TET, COLOR0, P4, P3, P7, N0,
  ST_PYR, COLOR0, P4, EI, ED, P3, N0,
  ST_PYR, COLOR0, P4, P5, EJ, EI, N0,
  ST_PYR, COLOR1, P6, EL, EB, P1, N0,
  ST_PYR, COLOR1, EJ, EF, P6, P1, N0,
  ST_TET, COLOR1, P6, EG, EL, N0,
  ST_TET, COLOR1, EF, EG, P6, N0,
  ST_PYR, COLOR1, EB, ED, P0, P1, N0,
  ST_PYR, COLOR1, P1, P0, EI, EJ, N0,
  ST_TET, COLOR1, EI, P0, ED, N0,
 // Case #68: (cloned #3)
  ST_HEX, COLOR0, EG, P7, P3, EC, EF, P5, P1, EB,
  ST_WDG, COLOR0, P1, P0, P3, P5, P4, P7,
  ST_WDG, COLOR1, P2, EB, EC, P6, EF, EG,
 // Case #69: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EG, EF, EI, EI,
  ST_PYR, COLOR0, P4, P1, EA, EI, N0,
  ST_TET, COLOR0, EA, P1, EB, N0,
  ST_PYR, COLOR0, P5, EF, EB, P1, N0,
  ST_TET, COLOR0, P4, P5, P1, N0,
  ST_TET, COLOR0, P3, ED, EC, N0,
  ST_TET, COLOR0, P7, P4, P3, N0,
  ST_PYR, COLOR0, P3, P4, EI, ED, N0,
  ST_TET, COLOR0, P7, P5, P4, N0,
  ST_PYR, COLOR0, P7, EG, EF, P5, N0,
  ST_PYR, COLOR0, P7, P3, EC, EG, N0,
  ST_PYR, COLOR1, P0, EA, EB, P2, N0,
  ST_PYR, COLOR1, EC, ED, P0, P2, N0,
  ST_TET, COLOR1, P0, EI, EA, N0,
  ST_TET, COLOR1, ED, EI, P0, N0,
  ST_PYR, COLOR1, EB, EF, P6, P2, N0,
  ST_PYR, COLOR1, P2, P6, EG, EC, N0,
  ST_TET, COLOR1, EG, P6, EF, N0,
 // Case #70: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EA, EC, EJ, EF, EG,
  ST_PYR, COLOR0, P0, P3, P7, P4, N0,
  ST_TET, COLOR0, P7, N0, P5, P4,
  ST_PYR, COLOR0, P3, EC, EG, P7, N0,
  ST_PYR, COLOR0, EA, EC, P3, P0, N0,
  ST_TET, COLOR0, P5, P0, P4, N0,
  ST_PYR, COLOR0, P5, EJ, EA, P0, N0,
  ST_TET, COLOR0, P5, EF, EJ, N0,
  ST_PYR, COLOR0, EG, EF, P5, P7, N0,
  ST_PYR, COLOR1, EC, P2, P6, EG, N0,
  ST_PYR, COLOR1, EA, P1, P2, EC, N0,
  ST_TET, COLOR1, EJ, P1, EA, N0,
  ST_TET, COLOR1, P1, P6, P2, N0,
  ST_PYR, COLOR1, EJ, EF, P6, P1, N0,
  ST_TET, COLOR1, P6, EF, EG, N0,
 // Case #71: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EJ, EF, EG, EC, ED, EI,
  ST_TET, COLOR0, P7, P4, P3, N0,
  ST_PYR, COLOR0, P7, P3, EC, EG, N0,
  ST_TET, COLOR0, P5, P4, P7, N0,
  ST_PYR, COLOR0, P5, P7, EG, EF, N0,
  ST_TET, COLOR0, EJ, P5, EF, N0,
  ST_PYR, COLOR0, P5, EJ, EI, P4, N0,
  ST_PYR, COLOR0, EI, ED, P3, P4, N0,
  ST_TET, COLOR0, P3, ED, EC, N0,
  ST_TET, COLOR1, P1, P6, P2, N0,
  ST_PYR, COLOR1, EJ, EF, P6, P1, N0,
  ST_PYR, COLOR1, EJ, P1, P0, EI, N0,
  ST_TET, COLOR1, P0, P1, P2, N0,
  ST_TET, COLOR1, P0, ED, EI, N0,
  ST_PYR, COLOR1, P0, P2, EC, ED, N0,
  ST_PYR, COLOR1, EC, P2, P6, EG, N0,
  ST_TET, COLOR1, EG, P6, EF, N0,
 // Case #72: (cloned #5)
  ST_PNT, 0, NOCOLOR, 2, ED, EF,
  ST_PYR, COLOR0, P0, P4, P5, P1, N0,
  ST_TET, COLOR0, P1, P5, P2, N0,
  ST_TET, COLOR0, P0, P1, P2, N0,
  ST_TET, COLOR0, P7, P4, P0, N0,
  ST_TET, COLOR0, P5, P4, P7, N0,
  ST_PYR, COLOR0, P5, P7, EG, EF, N0,
  ST_PYR, COLOR0, P2, P5, EF, EL, N0,
  ST_TET, COLOR0, P2, EL, EC, N0,
  ST_PYR, COLOR0, P0, P2, EC, ED, N0,
  ST_PYR, COLOR0, P0, ED, EK, P7, N0,
  ST_TET, COLOR0, P7, EK, EG, N0,
  ST_PYR, COLOR1, P3, P6, EG, EK, N0,
  ST_PYR, COLOR1, EC, EL, P6, P3, N0,
  ST_TET, COLOR1, EL, EF, P6, N0,
  ST_TET, COLOR1, P6, EF, EG, N0,
  ST_TET, COLOR1, EC, N0, P3, ED,
  ST_TET, COLOR1, EK, ED, P3, N0,
 // Case #73: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EA, EI, EF, EF,
  ST_PYR, COLOR0, P5, P7, EG, EF, N0,
  ST_TET, COLOR0, EG, P7, EK, N0,
  ST_PYR, COLOR0, P4, EI, EK, P7, N0,
  ST_TET, COLOR0, P5, P4, P7, N0,
  ST_TET, COLOR0, P2, EL, EC, N0,
  ST_TET, COLOR0, P1, P5, P2, N0,
  ST_PYR, COLOR0, P2, P5, EF, EL, N0,
  ST_TET, COLOR0, P1, P4, P5, N0,
  ST_PYR, COLOR0, P1, EA, EI, P4, N0,
  ST_PYR, COLOR0, P1, P2, EC, EA, N0,
  ST_PYR, COLOR1, P6, EG, EK, P3, N0,
  ST_PYR, COLOR1, EC, EL, P6, P3, N0,
  ST_TET, COLOR1, P6, EF, EG, N0,
  ST_TET, COLOR1, EL, EF, P6, N0,
  ST_PYR, COLOR1, EK, EI, P0, P3, N0,
  ST_PYR, COLOR1, P3, P0, EA, EC, N0,
  ST_TET, COLOR1, EA, P0, EI, N0,
 // Case #74: (cloned #26)
  ST_TET, COLOR0, P2, EB, EL, EC,
  ST_TET, COLOR0, P7, P4, P0, P5,
  ST_PYR, COLOR0, EJ, EA, P0, P5, ED,
  ST_PYR, COLOR0, EF, P5, P7, EG, EK,
  ST_PYR, COLOR0, P0, ED, EK, P7, P5,
  ST_PYR, COLOR0, ED, EJ, EF, EK, P5,
  ST_WDG, COLOR1, P1, P6, P3, EB, EL, EC,
  ST_PYR, COLOR1, P1, P3, ED, EA, EJ,
  ST_PYR, COLOR1, EK, P3, P6, EG, EF,
  ST_PYR, COLOR1, EJ, EF, P6, P1, P3,
  ST_PYR, COLOR1, EJ, ED, EK, EF, P3,
 // Case #75: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EI, EK, EJ, EF, EG,
  ST_TET, COLOR0, P2, EC, EB, EL,
  ST_PYR, COLOR0, P7, P4, EI, EK, N0,
  ST_PYR, COLOR0, P4, P5, EJ, EI, N0,
  ST_TET, COLOR0, P5, EF, EJ, N0,
  ST_TET, COLOR0, P4, P7, P5, N0,
  ST_PYR, COLOR0, P5, P7, EG, EF, N0,
  ST_TET, COLOR0, P7, EK, EG, N0,
  ST_WDG, COLOR1, EB, EC, EL, P1, P3, P6,
  ST_TET, COLOR1, P3, P1, P6, N0,
  ST_PYR, COLOR1, P1, EJ, EF, P6, N0,
  ST_TET, COLOR1, P0, P1, P3, N0,
  ST_PYR, COLOR1, EK, EI, P0, P3, N0,
  ST_PYR, COLOR1, EI, EJ, P1, P0, N0,
  ST_PYR, COLOR1, P6, EG, EK, P3, N0,
  ST_TET, COLOR1, EF, EG, P6, N0,
 // Case #76: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, ED, EB, EK, EG, EF,
  ST_PYR, COLOR0, P0, P4, P5, P1, N0,
  ST_TET, COLOR0, P5, P7, N0, P4,
  ST_PYR, COLOR0, P1, P5, EF, EB, N0,
  ST_PYR, COLOR0, ED, P0, P1, EB, N0,
  ST_TET, COLOR0, P7, P4, P0, N0,
  ST_PYR, COLOR0, P7, P0, ED, EK, N0,
  ST_TET, COLOR0, P7, EK, EG, N0,
  ST_PYR, COLOR0, EF, P5, P7, EG, N0,
  ST_PYR, COLOR1, EB, EF, P6, P2, N0,
  ST_PYR, COLOR1, ED, EB, P2, P3, N0,
  ST_TET, COLOR1, EK, ED, P3, N0,
  ST_TET, COLOR1, P3, P2, P6, N0,
  ST_PYR, COLOR1, EK, P3, P6, EG, N0,
  ST_TET, COLOR1, P6, EF, EG, N0,
 // Case #77: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EK, EG, EF, EB, EA, EI,
  ST_TET, COLOR0, P5, P1, P4, N0,
  ST_PYR, COLOR0, P5, EF, EB, P1, N0,
  ST_TET, COLOR0, P7, P5, P4, N0,
  ST_PYR, COLOR0, P7, EG, EF, P5, N0,
  ST_TET, COLOR0, EK, EG, P7, N0,
  ST_PYR, COLOR0, P7, P4, EI, EK, N0,
  ST_PYR, COLOR0, EI, P4, P1, EA, N0,
  ST_TET, COLOR0, P1, EB, EA, N0,
  ST_TET, COLOR1, P3, P2, P6, N0,
  ST_PYR, COLOR1, EK, P3, P6, EG, N0,
  ST_PYR, COLOR1, EK, EI, P0, P3, N0,
  ST_TET, COLOR1, P0, P2, P3, N0,
  ST_TET, COLOR1, P0, EI, EA, N0,
  ST_PYR, COLOR1, P0, EA, EB, P2, N0,
  ST_PYR, COLOR1, EB, EF, P6, P2, N0,
  ST_TET, COLOR1, EF, EG, P6, N0,
 // Case #78: (cloned #27)
  ST_TET, COLOR0, P7, P4, P0, P5,
  ST_PYR, COLOR0, EJ, EA, P0, P5, ED,
  ST_PYR, COLOR0, EF, P5, P7, EG, EK,
  ST_PYR, COLOR0, P0, ED, EK, P7, P5,
  ST_PYR, COLOR0, ED, EJ, EF, EK, P5,
  ST_TET, COLOR1, P2, P1, P6, P3,
  ST_PYR, COLOR1, ED, EA, P1, P3, EJ,
  ST_PYR, COLOR1, EK, P3, P6, EG, EF,
  ST_PYR, COLOR1, P1, EJ, EF, P6, P3,
  ST_PYR, COLOR1, ED, EK, EF, EJ, P3,
 // Case #79: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EK, EI, EJ, EG, EF,
  ST_PYR, COLOR0, P4, P5, EJ, EI, N0,
  ST_TET, COLOR0, P5, EF, EJ, N0,
  ST_PYR, COLOR0, P7, P4, EI, EK, N0,
  ST_TET, COLOR0, EG, P7, EK, N0,
  ST_PYR, COLOR0, EF, P5, P7, EG, N0,
  ST_TET, COLOR0, P5, P4, P7, N0,
  ST_PYR, COLOR1, P2, P3, P0, P1, N0,
  ST_TET, COLOR1, P1, P6, P2, N0,
  ST_TET, COLOR1, P6, P3, P2, N0,
  ST_PYR, COLOR1, P6, EG, EK, P3, N0,
  ST_PYR, COLOR1, EK, EI, P0, P3, N0,
  ST_PYR, COLOR1, EI, EJ, P1, P0, N0,
  ST_PYR, COLOR1, EJ, EF, P6, P1, N0,
  ST_TET, COLOR1, EG, P6, EF, N0,
 // Case #80: (cloned #5)
  ST_PNT, 0, NOCOLOR, 2, EI, EL,
  ST_PYR, COLOR0, P0, P1, P2, P3, N0,
  ST_TET, COLOR0, P1, P5, P2, N0,
  ST_TET, COLOR0, P0, P5, P1, N0,
  ST_TET, COLOR0, P7, P0, P3, N0,
  ST_TET, COLOR0, P2, P7, P3, N0,
  ST_PYR, COLOR0, P2, EL, EG, P7, N0,
  ST_PYR, COLOR0, P5, EF, EL, P2, N0,
  ST_TET, COLOR0, P5, EE, EF, N0,
  ST_PYR, COLOR0, P0, EI, EE, P5, N0,
  ST_PYR, COLOR0, P0, P7, EH, EI, N0,
  ST_TET, COLOR0, P7, EG, EH, N0,
  ST_PYR, COLOR1, P4, EH, EG, P6, N0,
  ST_PYR, COLOR1, EE, P4, P6, EF, N0,
  ST_TET, COLOR1, EF, P6, EL, N0,
  ST_TET, COLOR1, P6, EG, EL, N0,
  ST_TET, COLOR1, EE, P4, N0, EI,
  ST_TET, COLOR1, EH, P4, EI, N0,
 // Case #81: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EA, ED, EL, EL,
  ST_PYR, COLOR0, P2, EL, EG, P7, N0,
  ST_TET, COLOR0, EG, EH, P7, N0,
  ST_PYR, COLOR0, P3, P7, EH, ED, N0,
  ST_TET, COLOR0, P2, P7, P3, N0,
  ST_TET, COLOR0, P5, EE, EF, N0,
  ST_TET, COLOR0, P1, P5, P2, N0,
  ST_PYR, COLOR0, P5, EF, EL, P2, N0,
  ST_TET, COLOR0, P1, P2, P3, N0,
  ST_PYR, COLOR0, P1, P3, ED, EA, N0,
  ST_PYR, COLOR0, P1, EA, EE, P5, N0,
  ST_PYR, COLOR1, P6, P4, EH, EG, N0,
  ST_PYR, COLOR1, EE, P4, P6, EF, N0,
  ST_TET, COLOR1, P6, EG, EL, N0,
  ST_TET, COLOR1, EF, P6, EL, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_PYR, COLOR1, P4, EE, EA, P0, N0,
  ST_TET, COLOR1, EA, ED, P0, N0,
 // Case #82: (cloned #26)
  ST_TET, COLOR0, P5, EF, EJ, EE,
  ST_TET, COLOR0, P7, P0, P3, P2,
  ST_PYR, COLOR0, EB, P2, P0, EA, EI,
  ST_PYR, COLOR0, EL, EG, P7, P2, EH,
  ST_PYR, COLOR0, P0, P7, EH, EI, P2,
  ST_PYR, COLOR0, EI, EH, EL, EB, P2,
  ST_WDG, COLOR1, EJ, EF, EE, P1, P6, P4,
  ST_PYR, COLOR1, P1, EA, EI, P4, EB,
  ST_PYR, COLOR1, EH, EG, P6, P4, EL,
  ST_PYR, COLOR1, EB, P1, P6, EL, P4,
  ST_PYR, COLOR1, EB, EL, EH, EI, P4,
 // Case #83: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, ED, EH, EB, EL, EG,
  ST_TET, COLOR0, P5, EJ, EE, EF,
  ST_PYR, COLOR0, P7, EH, ED, P3, N0,
  ST_PYR, COLOR0, P3, ED, EB, P2, N0,
  ST_TET, COLOR0, P2, EB, EL, N0,
  ST_TET, COLOR0, P3, P2, P7, N0,
  ST_PYR, COLOR0, P2, EL, EG, P7, N0,
  ST_TET, COLOR0, P7, EG, EH, N0,
  ST_WDG, COLOR1, P1, P4, P6, EJ, EE, EF,
  ST_TET, COLOR1, P4, P6, P1, N0,
  ST_PYR, COLOR1, P1, P6, EL, EB, N0,
  ST_TET, COLOR1, P0, P4, P1, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_PYR, COLOR1, ED, P0, P1, EB, N0,
  ST_PYR, COLOR1, P6, P4, EH, EG, N0,
  ST_TET, COLOR1, EL, P6, EG, N0,
 // Case #84: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EC, EB, EI, EI,
  ST_PYR, COLOR0, P0, EI, EE, P5, N0,
  ST_TET, COLOR0, EE, EF, P5, N0,
  ST_PYR, COLOR0, P1, P5, EF, EB, N0,
  ST_TET, COLOR0, P0, P5, P1, N0,
  ST_TET, COLOR0, P7, EG, EH, N0,
  ST_TET, COLOR0, P3, P7, P0, N0,
  ST_PYR, COLOR0, P7, EH, EI, P0, N0,
  ST_TET, COLOR0, P3, P0, P1, N0,
  ST_PYR, COLOR0, P3, P1, EB, EC, N0,
  ST_PYR, COLOR0, P3, EC, EG, P7, N0,
  ST_PYR, COLOR1, P4, P6, EF, EE, N0,
  ST_PYR, COLOR1, EG, P6, P4, EH, N0,
  ST_TET, COLOR1, P4, EE, EI, N0,
  ST_TET, COLOR1, EH, P4, EI, N0,
  ST_PYR, COLOR1, EF, P6, P2, EB, N0,
  ST_PYR, COLOR1, P6, EG, EC, P2, N0,
  ST_TET, COLOR1, EC, EB, P2, N0,
 // Case #85: (cloned #60)
  ST_WDG, COLOR0, P7, EH, EG, P3, ED, EC,
  ST_WDG, COLOR0, P5, EF, EE, P1, EB, EA,
  ST_HEX, COLOR1, P0, P2, P6, P4, EA, EB, EF, EE,
  ST_HEX, COLOR1, ED, EC, EG, EH, P0, P2, P6, P4,
 // Case #86: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EC, EG, EA, EI, EH,
  ST_TET, COLOR0, P5, EF, EJ, EE,
  ST_PYR, COLOR0, P7, P3, EC, EG, N0,
  ST_PYR, COLOR0, P3, P0, EA, EC, N0,
  ST_TET, COLOR0, P0, EI, EA, N0,
  ST_TET, COLOR0, P3, P7, P0, N0,
  ST_PYR, COLOR0, P0, P7, EH, EI, N0,
  ST_TET, COLOR0, P7, EG, EH, N0,
  ST_WDG, COLOR1, EJ, EF, EE, P1, P6, P4,
  ST_TET, COLOR1, P6, P1, P4, N0,
  ST_PYR, COLOR1, P1, EA, EI, P4, N0,
  ST_TET, COLOR1, P2, P1, P6, N0,
  ST_PYR, COLOR1, EG, EC, P2, P6, N0,
  ST_PYR, COLOR1, EC, EA, P1, P2, N0,
  ST_PYR, COLOR1, P4, EH, EG, P6, N0,
  ST_TET, COLOR1, EI, EH, P4, N0,
 // Case #87: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P1, P4, P0, P2, EG, EC,
  ST_WDG, COLOR0, EG, P7, EH, EC, P3, ED,
  ST_TET, COLOR0, EJ, EE, P5, EF,
  ST_WDG, COLOR1, EJ, EF, EE, P1, P6, P4,
  ST_PYR, COLOR1, EC, P2, P6, EG, N0,
  ST_TET, COLOR1, P2, P1, P6, N0,
  ST_TET, COLOR1, P2, P0, P1, N0,
  ST_PYR, COLOR1, ED, P0, P2, EC, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_TET, COLOR1, P0, P4, P1, N0,
  ST_PYR, COLOR1, EG, EH, ED, EC, N0,
  ST_TET, COLOR1, P4, P6, P1, N0,
  ST_PYR, COLOR1, EG, P6, P4, EH, N0,
 // Case #88: (cloned #26)
  ST_TET, COLOR0, P7, EG, EH, EK,
  ST_TET, COLOR0, P2, P0, P1, P5,
  ST_PYR, COLOR0, EE, P5, P0, EI, ED,
  ST_PYR, COLOR0, EF, EL, P2, P5, EC,
  ST_PYR, COLOR0, P0, P2, EC, ED, P5,
  ST_PYR, COLOR0, ED, EC, EF, EE, P5,
  ST_WDG, COLOR1, EH, EG, EK, P4, P6, P3,
  ST_PYR, COLOR1, P4, EI, ED, P3, EE,
  ST_PYR, COLOR1, EC, EL, P6, P3, EF,
  ST_PYR, COLOR1, EE, P4, P6, EF, P3,
  ST_PYR, COLOR1, EE, EF, EC, ED, P3,
 // Case #89: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EA, EC, EE, EF, EL,
  ST_TET, COLOR0, P7, EH, EK, EG,
  ST_PYR, COLOR0, P2, EC, EA, P1, N0,
  ST_PYR, COLOR0, P1, EA, EE, P5, N0,
  ST_TET, COLOR0, P5, EE, EF, N0,
  ST_TET, COLOR0, P1, P5, P2, N0,
  ST_PYR, COLOR0, P5, EF, EL, P2, N0,
  ST_TET, COLOR0, P2, EL, EC, N0,
  ST_WDG, COLOR1, P4, P3, P6, EH, EK, EG,
  ST_TET, COLOR1, P3, P6, P4, N0,
  ST_PYR, COLOR1, P4, P6, EF, EE, N0,
  ST_TET, COLOR1, P0, P3, P4, N0,
  ST_PYR, COLOR1, EC, P3, P0, EA, N0,
  ST_PYR, COLOR1, EA, P0, P4, EE, N0,
  ST_PYR, COLOR1, P6, P3, EC, EL, N0,
  ST_TET, COLOR1, EF, P6, EL, N0,
 // Case #90: Unique case #17
  ST_TET, COLOR0, EH, EG, EK, P7,
  ST_TET, COLOR0, EI, ED, EA, P0,
  ST_TET, COLOR0, EE, EJ, EF, P5,
  ST_TET, COLOR0, EB, EC, EL, P2,
  ST_WDG, COLOR1, EB, EC, EL, P1, P3, P6,
  ST_TET, COLOR1, P1, P6, P3, P4,
  ST_WDG, COLOR1, P4, P6, P1, EE, EF, EJ,
  ST_WDG, COLOR1, P3, P4, P1, ED, EI, EA,
  ST_WDG, COLOR1, P6, P4, P3, EG, EH, EK,
 // Case #91: Unique case #18
  ST_TET, COLOR0, EE, EJ, EF, P5,
  ST_TET, COLOR0, EH, EK, P7, EG,
  ST_TET, COLOR0, EB, P2, EC, EL,
  ST_WDG, COLOR1, P6, P3, P1, EL, EC, EB,
  ST_WDG, COLOR1, EH, EG, EK, P4, P6, P3,
  ST_WDG, COLOR1, P4, P6, P1, EE, EF, EJ,
  ST_TET, COLOR1, P6, P3, P1, P4,
  ST_TET, COLOR1, P4, P1, P0, P3,
 // Case #92: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EB, EF, ED, EI, EE,
  ST_TET, COLOR0, P7, EK, EG, EH,
  ST_PYR, COLOR0, P5, EF, EB, P1, N0,
  ST_PYR, COLOR0, P1, EB, ED, P0, N0,
  ST_TET, COLOR0, P0, ED, EI, N0,
  ST_TET, COLOR0, P1, P0, P5, N0,
  ST_PYR, COLOR0, P0, EI, EE, P5, N0,
  ST_TET, COLOR0, P5, EE, EF, N0,
  ST_WDG, COLOR1, P3, P6, P4, EK, EG, EH,
  ST_TET, COLOR1, P6, P4, P3, N0,
  ST_PYR, COLOR1, P3, P4, EI, ED, N0,
  ST_TET, COLOR1, P2, P6, P3, N0,
  ST_PYR, COLOR1, EF, P6, P2, EB, N0,
  ST_PYR, COLOR1, EB, P2, P3, ED, N0,
  ST_PYR, COLOR1, P4, P6, EF, EE, N0,
  ST_TET, COLOR1, EI, P4, EE, N0,
 // Case #93: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P3, P4, P0, P2, EF, EB,
  ST_WDG, COLOR0, EB, P1, EA, EF, P5, EE,
  ST_TET, COLOR0, EK, P7, EH, EG,
  ST_WDG, COLOR1, P3, P6, P4, EK, EG, EH,
  ST_PYR, COLOR1, EB, EF, P6, P2, N0,
  ST_TET, COLOR1, P2, P6, P3, N0,
  ST_TET, COLOR1, P2, P3, P0, N0,
  ST_PYR, COLOR1, EA, EB, P2, P0, N0,
  ST_PYR, COLOR1, EE, EA, P0, P4, N0,
  ST_TET, COLOR1, P0, P3, P4, N0,
  ST_PYR, COLOR1, EF, EB, EA, EE, N0,
  ST_TET, COLOR1, P4, P3, P6, N0,
  ST_PYR, COLOR1, EF, EE, P4, P6, N0,
 // Case #94: (cloned #91)
  ST_TET, COLOR0, EK, EH, EG, P7,
  ST_TET, COLOR0, ED, P0, EA, EI,
  ST_TET, COLOR0, EF, EJ, P5, EE,
  ST_WDG, COLOR1, EE, EJ, EF, P4, P1, P6,
  ST_WDG, COLOR1, P3, P4, P1, ED, EI, EA,
  ST_WDG, COLOR1, EK, EH, EG, P3, P4, P6,
  ST_TET, COLOR1, P4, P6, P1, P3,
  ST_TET, COLOR1, P3, P2, P6, P1,
 // Case #95: Unique case #19
  ST_TET, COLOR0, EG, EK, EH, P7,
  ST_TET, COLOR0, EF, EE, EJ, P5,
  ST_WDG, COLOR1, P4, P6, P1, EE, EF, EJ,
  ST_WDG, COLOR1, EH, EG, EK, P4, P6, P3,
  ST_PYR, COLOR1, P0, P1, P2, P3, P4,
  ST_TET, COLOR1, P6, P3, P2, P4,
  ST_TET, COLOR1, P6, P2, P1, P4,
 // Case #96: (cloned #3)
  ST_HEX, COLOR0, EL, P2, P1, EJ, EG, P7, P4, EE,
  ST_WDG, COLOR0, P2, P3, P7, P1, P0, P4,
  ST_WDG, COLOR1, P6, EL, EG, P5, EJ, EE,
 // Case #97: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EG, EL, ED, ED,
  ST_PYR, COLOR0, P3, ED, EA, P1, N0,
  ST_TET, COLOR0, EA, EJ, P1, N0,
  ST_PYR, COLOR0, P2, P1, EJ, EL, N0,
  ST_TET, COLOR0, P3, P1, P2, N0,
  ST_TET, COLOR0, P4, EE, EI, N0,
  ST_TET, COLOR0, P7, P4, P3, N0,
  ST_PYR, COLOR0, P4, EI, ED, P3, N0,
  ST_TET, COLOR0, P7, P3, P2, N0,
  ST_PYR, COLOR0, P7, P2, EL, EG, N0,
  ST_PYR, COLOR0, P7, EG, EE, P4, N0,
  ST_PYR, COLOR1, P0, P5, EJ, EA, N0,
  ST_PYR, COLOR1, EE, P5, P0, EI, N0,
  ST_TET, COLOR1, P0, EA, ED, N0,
  ST_TET, COLOR1, EI, P0, ED, N0,
  ST_PYR, COLOR1, EJ, P5, P6, EL, N0,
  ST_PYR, COLOR1, P5, EE, EG, P6, N0,
  ST_TET, COLOR1, EG, EL, P6, N0,
 // Case #98: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EA, EE, EB, EL, EG,
  ST_PYR, COLOR0, P0, P3, P7, P4, N0,
  ST_TET, COLOR0, P7, P2, N0, P3,
  ST_PYR, COLOR0, P4, P7, EG, EE, N0,
  ST_PYR, COLOR0, EA, P0, P4, EE, N0,
  ST_TET, COLOR0, P2, P3, P0, N0,
  ST_PYR, COLOR0, P2, P0, EA, EB, N0,
  ST_TET, COLOR0, P2, EB, EL, N0,
  ST_PYR, COLOR0, EG, P7, P2, EL, N0,
  ST_PYR, COLOR1, EE, EG, P6, P5, N0,
  ST_PYR, COLOR1, EA, EE, P5, P1, N0,
  ST_TET, COLOR1, EB, EA, P1, N0,
  ST_TET, COLOR1, P1, P5, P6, N0,
  ST_PYR, COLOR1, EB, P1, P6, EL, N0,
  ST_TET, COLOR1, P6, EG, EL, N0,
 // Case #99: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EB, EL, EG, EE, EI, ED,
  ST_TET, COLOR0, P7, P4, P3, N0,
  ST_PYR, COLOR0, P7, EG, EE, P4, N0,
  ST_TET, COLOR0, P2, P7, P3, N0,
  ST_PYR, COLOR0, P2, EL, EG, P7, N0,
  ST_TET, COLOR0, EB, EL, P2, N0,
  ST_PYR, COLOR0, P2, P3, ED, EB, N0,
  ST_PYR, COLOR0, ED, P3, P4, EI, N0,
  ST_TET, COLOR0, P4, EE, EI, N0,
  ST_TET, COLOR1, P1, P5, P6, N0,
  ST_PYR, COLOR1, EB, P1, P6, EL, N0,
  ST_PYR, COLOR1, EB, ED, P0, P1, N0,
  ST_TET, COLOR1, P0, P5, P1, N0,
  ST_TET, COLOR1, P0, ED, EI, N0,
  ST_PYR, COLOR1, P0, EI, EE, P5, N0,
  ST_PYR, COLOR1, EE, EG, P6, P5, N0,
  ST_TET, COLOR1, EG, EL, P6, N0,
 // Case #100: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EC, EG, EB, EJ, EE,
  ST_PYR, COLOR0, P3, P7, P4, P0, N0,
  ST_TET, COLOR0, P4, N0, P1, P0,
  ST_PYR, COLOR0, P7, EG, EE, P4, N0,
  ST_PYR, COLOR0, EC, EG, P7, P3, N0,
  ST_TET, COLOR0, P1, P3, P0, N0,
  ST_PYR, COLOR0, P1, EB, EC, P3, N0,
  ST_TET, COLOR0, P1, EJ, EB, N0,
  ST_PYR, COLOR0, EE, EJ, P1, P4, N0,
  ST_PYR, COLOR1, EG, P6, P5, EE, N0,
  ST_PYR, COLOR1, EC, P2, P6, EG, N0,
  ST_TET, COLOR1, EB, P2, EC, N0,
  ST_TET, COLOR1, P2, P5, P6, N0,
  ST_PYR, COLOR1, EB, EJ, P5, P2, N0,
  ST_TET, COLOR1, P5, EJ, EE, N0,
 // Case #101: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EG, EE, EC, ED, EI,
  ST_TET, COLOR0, P1, EJ, EB, EA,
  ST_PYR, COLOR0, P4, P7, EG, EE, N0,
  ST_PYR, COLOR0, P7, P3, EC, EG, N0,
  ST_TET, COLOR0, P3, ED, EC, N0,
  ST_TET, COLOR0, P7, P4, P3, N0,
  ST_PYR, COLOR0, P3, P4, EI, ED, N0,
  ST_TET, COLOR0, P4, EE, EI, N0,
  ST_WDG, COLOR1, EB, EJ, EA, P2, P5, P0,
  ST_TET, COLOR1, P5, P2, P0, N0,
  ST_PYR, COLOR1, P2, EC, ED, P0, N0,
  ST_TET, COLOR1, P6, P2, P5, N0,
  ST_PYR, COLOR1, EE, EG, P6, P5, N0,
  ST_PYR, COLOR1, EG, EC, P2, P6, N0,
  ST_PYR, COLOR1, P0, EI, EE, P5, N0,
  ST_TET, COLOR1, ED, EI, P0, N0,
 // Case #102: (cloned #15)
  ST_HEX, COLOR0, EA, EE, EG, EC, P0, P4, P7, P3,
  ST_HEX, COLOR1, P1, P5, P6, P2, EA, EE, EG, EC,
 // Case #103: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EE, EG, EC, EI, ED,
  ST_PYR, COLOR0, P7, P3, EC, EG, N0,
  ST_TET, COLOR0, P3, ED, EC, N0,
  ST_PYR, COLOR0, P4, P7, EG, EE, N0,
  ST_TET, COLOR0, EI, P4, EE, N0,
  ST_PYR, COLOR0, ED, P3, P4, EI, N0,
  ST_TET, COLOR0, P3, P7, P4, N0,
  ST_PYR, COLOR1, P1, P5, P6, P2, N0,
  ST_TET, COLOR1, P2, P0, P1, N0,
  ST_TET, COLOR1, P0, P5, P1, N0,
  ST_PYR, COLOR1, P0, EI, EE, P5, N0,
  ST_PYR, COLOR1, EE, EG, P6, P5, N0,
  ST_PYR, COLOR1, EG, EC, P2, P6, N0,
  ST_PYR, COLOR1, EC, ED, P0, P2, N0,
  ST_TET, COLOR1, EI, P0, ED, N0,
 // Case #104: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EE, EJ, ED, ED,
  ST_PYR, COLOR0, P0, P2, EC, ED, N0,
  ST_TET, COLOR0, EC, P2, EL, N0,
  ST_PYR, COLOR0, P1, EJ, EL, P2, N0,
  ST_TET, COLOR0, P0, P1, P2, N0,
  ST_TET, COLOR0, P7, EK, EG, N0,
  ST_TET, COLOR0, P4, P0, P7, N0,
  ST_PYR, COLOR0, P7, P0, ED, EK, N0,
  ST_TET, COLOR0, P4, P1, P0, N0,
  ST_PYR, COLOR0, P4, EE, EJ, P1, N0,
  ST_PYR, COLOR0, P4, P7, EG, EE, N0,
  ST_PYR, COLOR1, P3, EC, EL, P6, N0,
  ST_PYR, COLOR1, EG, EK, P3, P6, N0,
  ST_TET, COLOR1, P3, ED, EC, N0,
  ST_TET, COLOR1, EK, ED, P3, N0,
  ST_PYR, COLOR1, EL, EJ, P5, P6, N0,
  ST_PYR, COLOR1, P6, P5, EE, EG, N0,
  ST_TET, COLOR1, EE, P5, EJ, N0,
 // Case #105: (cloned #60)
  ST_WDG, COLOR0, P4, EI, EE, P7, EK, EG,
  ST_WDG, COLOR0, P1, EJ, EA, P2, EL, EC,
  ST_HEX, COLOR1, EA, EJ, EL, EC, P0, P5, P6, P3,
  ST_HEX, COLOR1, P0, P5, P6, P3, EI, EE, EG, EK,
 // Case #106: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EE, EG, EA, ED, EK,
  ST_TET, COLOR0, P2, EB, EL, EC,
  ST_PYR, COLOR0, P7, EG, EE, P4, N0,
  ST_PYR, COLOR0, P4, EE, EA, P0, N0,
  ST_TET, COLOR0, P0, EA, ED, N0,
  ST_TET, COLOR0, P4, P0, P7, N0,
  ST_PYR, COLOR0, P0, ED, EK, P7, N0,
  ST_TET, COLOR0, P7, EK, EG, N0,
  ST_WDG, COLOR1, P1, P6, P3, EB, EL, EC,
  ST_TET, COLOR1, P6, P3, P1, N0,
  ST_PYR, COLOR1, P1, P3, ED, EA, N0,
  ST_TET, COLOR1, P5, P6, P1, N0,
  ST_PYR, COLOR1, EG, P6, P5, EE, N0,
  ST_PYR, COLOR1, EE, P5, P1, EA, N0,
  ST_PYR, COLOR1, P3, P6, EG, EK, N0,
  ST_TET, COLOR1, ED, P3, EK, N0,
 // Case #107: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P1, P3, P0, P5, EG, EE,
  ST_WDG, COLOR0, EE, P4, EI, EG, P7, EK,
  ST_TET, COLOR0, EB, P2, EC, EL,
  ST_WDG, COLOR1, P1, P6, P3, EB, EL, EC,
  ST_PYR, COLOR1, EE, EG, P6, P5, N0,
  ST_TET, COLOR1, P5, P6, P1, N0,
  ST_TET, COLOR1, P5, P1, P0, N0,
  ST_PYR, COLOR1, EI, EE, P5, P0, N0,
  ST_PYR, COLOR1, EK, EI, P0, P3, N0,
  ST_TET, COLOR1, P0, P1, P3, N0,
  ST_PYR, COLOR1, EG, EE, EI, EK, N0,
  ST_TET, COLOR1, P3, P1, P6, N0,
  ST_PYR, COLOR1, EG, EK, P3, P6, N0,
 // Case #108: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EB, EJ, EE, EG, EK, ED,
  ST_TET, COLOR0, P4, P0, P7, N0,
  ST_PYR, COLOR0, P4, P7, EG, EE, N0,
  ST_TET, COLOR0, P1, P0, P4, N0,
  ST_PYR, COLOR0, P1, P4, EE, EJ, N0,
  ST_TET, COLOR0, EB, P1, EJ, N0,
  ST_PYR, COLOR0, P1, EB, ED, P0, N0,
  ST_PYR, COLOR0, ED, EK, P7, P0, N0,
  ST_TET, COLOR0, P7, EK, EG, N0,
  ST_TET, COLOR1, P2, P5, P6, N0,
  ST_PYR, COLOR1, EB, EJ, P5, P2, N0,
  ST_PYR, COLOR1, EB, P2, P3, ED, N0,
  ST_TET, COLOR1, P3, P2, P6, N0,
  ST_TET, COLOR1, P3, EK, ED, N0,
  ST_PYR, COLOR1, P3, P6, EG, EK, N0,
  ST_PYR, COLOR1, EG, P6, P5, EE, N0,
  ST_TET, COLOR1, EE, P5, EJ, N0,
 // Case #109: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P2, P0, P3, P6, EE, EG,
  ST_WDG, COLOR0, EE, P4, EI, EG, P7, EK,
  ST_TET, COLOR0, EB, EA, P1, EJ,
  ST_WDG, COLOR1, EB, EJ, EA, P2, P5, P0,
  ST_PYR, COLOR1, EG, P6, P5, EE, N0,
  ST_TET, COLOR1, P6, P2, P5, N0,
  ST_TET, COLOR1, P6, P3, P2, N0,
  ST_PYR, COLOR1, EK, P3, P6, EG, N0,
  ST_PYR, COLOR1, EI, P0, P3, EK, N0,
  ST_TET, COLOR1, P3, P0, P2, N0,
  ST_PYR, COLOR1, EE, EI, EK, EG, N0,
  ST_TET, COLOR1, P0, P5, P2, N0,
  ST_PYR, COLOR1, EE, P5, P0, EI, N0,
 // Case #110: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EG, EE, EA, EK, ED,
  ST_PYR, COLOR0, P4, EE, EA, P0, N0,
  ST_TET, COLOR0, P0, EA, ED, N0,
  ST_PYR, COLOR0, P7, EG, EE, P4, N0,
  ST_TET, COLOR0, EK, EG, P7, N0,
  ST_PYR, COLOR0, ED, EK, P7, P0, N0,
  ST_TET, COLOR0, P0, P7, P4, N0,
  ST_PYR, COLOR1, P2, P1, P5, P6, N0,
  ST_TET, COLOR1, P1, P2, P3, N0,
  ST_TET, COLOR1, P3, P2, P6, N0,
  ST_PYR, COLOR1, P3, P6, EG, EK, N0,
  ST_PYR, COLOR1, EG, P6, P5, EE, N0,
  ST_PYR, COLOR1, EE, P5, P1, EA, N0,
  ST_PYR, COLOR1, EA, P1, P3, ED, N0,
  ST_TET, COLOR1, EK, ED, P3, N0,
 // Case #111: (cloned #63)
  ST_WDG, COLOR0, P4, EI, EE, P7, EK, EG,
  ST_HEX, COLOR1, P0, P5, P6, P3, EI, EE, EG, EK,
  ST_WDG, COLOR1, P2, P3, P6, P1, P0, P5,
 // Case #112: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EI, EJ, EH, EG, EL,
  ST_PYR, COLOR0, P0, P1, P2, P3, N0,
  ST_TET, COLOR0, P2, N0, P7, P3,
  ST_PYR, COLOR0, P1, EJ, EL, P2, N0,
  ST_PYR, COLOR0, EI, EJ, P1, P0, N0,
  ST_TET, COLOR0, P7, P0, P3, N0,
  ST_PYR, COLOR0, P7, EH, EI, P0, N0,
  ST_TET, COLOR0, P7, EG, EH, N0,
  ST_PYR, COLOR0, EL, EG, P7, P2, N0,
  ST_PYR, COLOR1, EJ, P5, P6, EL, N0,
  ST_PYR, COLOR1, EI, P4, P5, EJ, N0,
  ST_TET, COLOR1, EH, P4, EI, N0,
  ST_TET, COLOR1, P4, P6, P5, N0,
  ST_PYR, COLOR1, EH, EG, P6, P4, N0,
  ST_TET, COLOR1, P6, EG, EL, N0,
 // Case #113: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EH, EG, EL, EJ, EA, ED,
  ST_TET, COLOR0, P2, P3, P1, N0,
  ST_PYR, COLOR0, P2, P1, EJ, EL, N0,
  ST_TET, COLOR0, P7, P3, P2, N0,
  ST_PYR, COLOR0, P7, P2, EL, EG, N0,
  ST_TET, COLOR0, EH, P7, EG, N0,
  ST_PYR, COLOR0, P7, EH, ED, P3, N0,
  ST_PYR, COLOR0, ED, EA, P1, P3, N0,
  ST_TET, COLOR0, P1, EA, EJ, N0,
  ST_TET, COLOR1, P4, P6, P5, N0,
  ST_PYR, COLOR1, EH, EG, P6, P4, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_TET, COLOR1, P0, P4, P5, N0,
  ST_TET, COLOR1, P0, EA, ED, N0,
  ST_PYR, COLOR1, P0, P5, EJ, EA, N0,
  ST_PYR, COLOR1, EJ, P5, P6, EL, N0,
  ST_TET, COLOR1, EL, P6, EG, N0,
 // Case #114: (cloned #27)
  ST_TET, COLOR0, P7, P0, P3, P2,
  ST_PYR, COLOR0, EB, P2, P0, EA, EI,
  ST_PYR, COLOR0, EL, EG, P7, P2, EH,
  ST_PYR, COLOR0, P0, P7, EH, EI, P2,
  ST_PYR, COLOR0, EI, EH, EL, EB, P2,
  ST_TET, COLOR1, P5, P6, P1, P4,
  ST_PYR, COLOR1, EI, P4, P1, EA, EB,
  ST_PYR, COLOR1, EH, EG, P6, P4, EL,
  ST_PYR, COLOR1, P1, P6, EL, EB, P4,
  ST_PYR, COLOR1, EI, EB, EL, EH, P4,
 // Case #115: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EH, ED, EB, EG, EL,
  ST_PYR, COLOR0, P3, ED, EB, P2, N0,
  ST_TET, COLOR0, P2, EB, EL, N0,
  ST_PYR, COLOR0, P7, EH, ED, P3, N0,
  ST_TET, COLOR0, EG, EH, P7, N0,
  ST_PYR, COLOR0, EL, EG, P7, P2, N0,
  ST_TET, COLOR0, P2, P7, P3, N0,
  ST_PYR, COLOR1, P5, P1, P0, P4, N0,
  ST_TET, COLOR1, P1, P5, P6, N0,
  ST_TET, COLOR1, P6, P5, P4, N0,
  ST_PYR, COLOR1, P6, P4, EH, EG, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_PYR, COLOR1, ED, P0, P1, EB, N0,
  ST_PYR, COLOR1, EB, P1, P6, EL, N0,
  ST_TET, COLOR1, EG, EL, P6, N0,
 // Case #116: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EJ, EB, EC, EG, EH, EI,
  ST_TET, COLOR0, P3, P7, P0, N0,
  ST_PYR, COLOR0, P3, EC, EG, P7, N0,
  ST_TET, COLOR0, P1, P3, P0, N0,
  ST_PYR, COLOR0, P1, EB, EC, P3, N0,
  ST_TET, COLOR0, EJ, EB, P1, N0,
  ST_PYR, COLOR0, P1, P0, EI, EJ, N0,
  ST_PYR, COLOR0, EI, P0, P7, EH, N0,
  ST_TET, COLOR0, P7, EG, EH, N0,
  ST_TET, COLOR1, P5, P6, P2, N0,
  ST_PYR, COLOR1, EJ, P5, P2, EB, N0,
  ST_PYR, COLOR1, EJ, EI, P4, P5, N0,
  ST_TET, COLOR1, P4, P6, P5, N0,
  ST_TET, COLOR1, P4, EI, EH, N0,
  ST_PYR, COLOR1, P4, EH, EG, P6, N0,
  ST_PYR, COLOR1, EG, EC, P2, P6, N0,
  ST_TET, COLOR1, EC, EB, P2, N0,
 // Case #117: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P5, P0, P4, P6, EC, EG,
  ST_WDG, COLOR0, EG, P7, EH, EC, P3, ED,
  ST_TET, COLOR0, EJ, P1, EA, EB,
  ST_WDG, COLOR1, P5, P2, P0, EJ, EB, EA,
  ST_PYR, COLOR1, EG, EC, P2, P6, N0,
  ST_TET, COLOR1, P6, P2, P5, N0,
  ST_TET, COLOR1, P6, P5, P4, N0,
  ST_PYR, COLOR1, EH, EG, P6, P4, N0,
  ST_PYR, COLOR1, ED, EH, P4, P0, N0,
  ST_TET, COLOR1, P4, P5, P0, N0,
  ST_PYR, COLOR1, EC, EG, EH, ED, N0,
  ST_TET, COLOR1, P0, P5, P2, N0,
  ST_PYR, COLOR1, EC, ED, P0, P2, N0,
 // Case #118: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EG, EC, EA, EH, EI,
  ST_PYR, COLOR0, P3, P0, EA, EC, N0,
  ST_TET, COLOR0, P0, EI, EA, N0,
  ST_PYR, COLOR0, P7, P3, EC, EG, N0,
  ST_TET, COLOR0, EH, P7, EG, N0,
  ST_PYR, COLOR0, EI, P0, P7, EH, N0,
  ST_TET, COLOR0, P0, P3, P7, N0,
  ST_PYR, COLOR1, P5, P6, P2, P1, N0,
  ST_TET, COLOR1, P1, P4, P5, N0,
  ST_TET, COLOR1, P4, P6, P5, N0,
  ST_PYR, COLOR1, P4, EH, EG, P6, N0,
  ST_PYR, COLOR1, EG, EC, P2, P6, N0,
  ST_PYR, COLOR1, EC, EA, P1, P2, N0,
  ST_PYR, COLOR1, EA, EI, P4, P1, N0,
  ST_TET, COLOR1, EH, P4, EI, N0,
 // Case #119: (cloned #63)
  ST_WDG, COLOR0, P7, EH, EG, P3, ED, EC,
  ST_HEX, COLOR1, ED, EC, EG, EH, P0, P2, P6, P4,
  ST_WDG, COLOR1, P1, P0, P2, P5, P4, P6,
 // Case #120: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EJ, EL, EI, ED, EC,
  ST_TET, COLOR0, P7, EG, EH, EK,
  ST_PYR, COLOR0, P2, P1, EJ, EL, N0,
  ST_PYR, COLOR0, P1, P0, EI, EJ, N0,
  ST_TET, COLOR0, P0, ED, EI, N0,
  ST_TET, COLOR0, P1, P2, P0, N0,
  ST_PYR, COLOR0, P0, P2, EC, ED, N0,
  ST_TET, COLOR0, P2, EL, EC, N0,
  ST_WDG, COLOR1, EH, EG, EK, P4, P6, P3,
  ST_TET, COLOR1, P6, P4, P3, N0,
  ST_PYR, COLOR1, P4, EI, ED, P3, N0,
  ST_TET, COLOR1, P5, P4, P6, N0,
  ST_PYR, COLOR1, EL, EJ, P5, P6, N0,
  ST_PYR, COLOR1, EJ, EI, P4, P5, N0,
  ST_PYR, COLOR1, P3, EC, EL, P6, N0,
  ST_TET, COLOR1, ED, EC, P3, N0,
 // Case #121: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P4, P3, P0, P5, EL, EJ,
  ST_WDG, COLOR0, EL, P2, EC, EJ, P1, EA,
  ST_TET, COLOR0, EH, EK, P7, EG,
  ST_WDG, COLOR1, EH, EG, EK, P4, P6, P3,
  ST_PYR, COLOR1, EJ, P5, P6, EL, N0,
  ST_TET, COLOR1, P5, P4, P6, N0,
  ST_TET, COLOR1, P5, P0, P4, N0,
  ST_PYR, COLOR1, EA, P0, P5, EJ, N0,
  ST_PYR, COLOR1, EC, P3, P0, EA, N0,
  ST_TET, COLOR1, P0, P3, P4, N0,
  ST_PYR, COLOR1, EL, EC, EA, EJ, N0,
  ST_TET, COLOR1, P3, P6, P4, N0,
  ST_PYR, COLOR1, EL, P6, P3, EC, N0,
 // Case #122: (cloned #91)
  ST_TET, COLOR0, EH, EG, EK, P7,
  ST_TET, COLOR0, EI, EA, P0, ED,
  ST_TET, COLOR0, EL, P2, EB, EC,
  ST_WDG, COLOR1, P3, P1, P6, EC, EB, EL,
  ST_WDG, COLOR1, EI, ED, EA, P4, P3, P1,
  ST_WDG, COLOR1, P4, P3, P6, EH, EK, EG,
  ST_TET, COLOR1, P3, P1, P6, P4,
  ST_TET, COLOR1, P4, P6, P5, P1,
 // Case #123: (cloned #95)
  ST_TET, COLOR0, EG, EK, EH, P7,
  ST_TET, COLOR0, EL, EB, EC, P2,
  ST_WDG, COLOR1, EC, EL, EB, P3, P6, P1,
  ST_WDG, COLOR1, P3, P6, P4, EK, EG, EH,
  ST_PYR, COLOR1, P0, P4, P5, P1, P3,
  ST_TET, COLOR1, P6, P5, P4, P3,
  ST_TET, COLOR1, P6, P1, P5, P3,
 // Case #124: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P6, P4, P5, P2, ED, EB,
  ST_WDG, COLOR0, ED, P0, EI, EB, P1, EJ,
  ST_TET, COLOR0, EG, EH, P7, EK,
  ST_WDG, COLOR1, EG, EK, EH, P6, P3, P4,
  ST_PYR, COLOR1, EB, P2, P3, ED, N0,
  ST_TET, COLOR1, P2, P6, P3, N0,
  ST_TET, COLOR1, P2, P5, P6, N0,
  ST_PYR, COLOR1, EJ, P5, P2, EB, N0,
  ST_PYR, COLOR1, EI, P4, P5, EJ, N0,
  ST_TET, COLOR1, P5, P4, P6, N0,
  ST_PYR, COLOR1, ED, EI, EJ, EB, N0,
  ST_TET, COLOR1, P4, P3, P6, N0,
  ST_PYR, COLOR1, ED, P3, P4, EI, N0,
 // Case #125: Unique case #20
  ST_TET, COLOR0, EG, EK, EH, P7,
  ST_TET, COLOR0, EJ, EA, EB, P1,
  ST_WDG, COLOR1, EH, EG, EK, P4, P6, P3,
  ST_WDG, COLOR1, EJ, EA, EB, P5, P0, P2,
  ST_TET, COLOR1, P2, P3, P0, P5,
  ST_TET, COLOR1, P5, P4, P6, P3,
  ST_TET, COLOR1, P4, P5, P0, P3,
  ST_TET, COLOR1, P5, P6, P2, P3,
 // Case #126: (cloned #95)
  ST_TET, COLOR0, EI, ED, EA, P0,
  ST_TET, COLOR0, EH, EG, EK, P7,
  ST_WDG, COLOR1, EK, EH, EG, P3, P4, P6,
  ST_WDG, COLOR1, P3, P4, P1, ED, EI, EA,
  ST_PYR, COLOR1, P2, P1, P5, P6, P3,
  ST_TET, COLOR1, P4, P5, P1, P3,
  ST_TET, COLOR1, P4, P6, P5, P3,
 // Case #127: Unique case #21
  ST_PNT, 0, COLOR1, 7, P0, P1, P2, P3, P4, P5, P6,
  ST_TET, COLOR0, EH, EG, EK, P7,
  ST_WDG, COLOR1, EH, EG, EK, P4, P6, P3,
  ST_TET, COLOR1, P4, P3, P6, N0,
  ST_PYR, COLOR1, P5, P6, P2, P1, N0,
  ST_TET, COLOR1, P6, P3, P2, N0,
  ST_PYR, COLOR1, P0, P1, P2, P3, N0,
  ST_TET, COLOR1, P0, P3, P4, N0,
  ST_PYR, COLOR1, P0, P4, P5, P1, N0,
  ST_TET, COLOR1, P4, P6, P5, N0,
 // Case #128: (cloned #1)
  ST_PNT, 0, COLOR0, 7, P6, P5, P4, P3, P2, P1, P0,
  ST_WDG, COLOR0, P6, P4, P3, EG, EH, EK,
  ST_TET, COLOR0, P6, P4, P3, N0,
  ST_TET, COLOR0, P6, P5, P4, N0,
  ST_PYR, COLOR0, P1, P0, P4, P5, N0,
  ST_PYR, COLOR0, P2, P1, P5, P6, N0,
  ST_PYR, COLOR0, P3, P0, P1, P2, N0,
  ST_TET, COLOR0, P4, P0, P3, N0,
  ST_TET, COLOR0, P3, P2, P6, N0,
  ST_TET, COLOR1, P7, EG, EH, EK,
 // Case #129: (cloned #5)
  ST_PNT, 0, NOCOLOR, 2, EA, EG,
  ST_PYR, COLOR0, P1, P5, P6, P2, N0,
  ST_TET, COLOR0, P5, P4, P6, N0,
  ST_TET, COLOR0, P1, P4, P5, N0,
  ST_TET, COLOR0, P3, P1, P2, N0,
  ST_TET, COLOR0, P6, P3, P2, N0,
  ST_PYR, COLOR0, P6, EG, EK, P3, N0,
  ST_PYR, COLOR0, P4, EH, EG, P6, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_PYR, COLOR0, P1, EA, EI, P4, N0,
  ST_PYR, COLOR0, P1, P3, ED, EA, N0,
  ST_TET, COLOR0, P3, EK, ED, N0,
  ST_PYR, COLOR1, P0, ED, EK, P7, N0,
  ST_PYR, COLOR1, EI, P0, P7, EH, N0,
  ST_TET, COLOR1, EH, P7, EG, N0,
  ST_TET, COLOR1, P7, EK, EG, N0,
  ST_TET, COLOR1, EI, P0, N0, EA,
  ST_TET, COLOR1, ED, P0, EA, N0,
 // Case #130: (cloned #20)
  ST_WDG, COLOR0, P4, P3, P6, EH, EK, EG,
  ST_WDG, COLOR0, EA, EB, EJ, P0, P2, P5,
  ST_TET, COLOR0, P3, P6, P4, P2,
  ST_TET, COLOR0, P5, P6, P2, P4,
  ST_TET, COLOR0, P0, P4, P5, P2,
  ST_TET, COLOR0, P3, P0, P2, P4,
  ST_TET, COLOR1, P1, EA, EJ, EB,
  ST_TET, COLOR1, P7, EH, EK, EG,
 // Case #131: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EJ, EB, EG, EG,
  ST_PYR, COLOR0, P6, EG, EK, P3, N0,
  ST_TET, COLOR0, EK, ED, P3, N0,
  ST_PYR, COLOR0, P2, P3, ED, EB, N0,
  ST_TET, COLOR0, P6, P3, P2, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_TET, COLOR0, P5, P4, P6, N0,
  ST_PYR, COLOR0, P4, EH, EG, P6, N0,
  ST_TET, COLOR0, P5, P6, P2, N0,
  ST_PYR, COLOR0, P5, P2, EB, EJ, N0,
  ST_PYR, COLOR0, P5, EJ, EI, P4, N0,
  ST_PYR, COLOR1, P7, P0, ED, EK, N0,
  ST_PYR, COLOR1, EI, P0, P7, EH, N0,
  ST_TET, COLOR1, P7, EK, EG, N0,
  ST_TET, COLOR1, EH, P7, EG, N0,
  ST_PYR, COLOR1, ED, P0, P1, EB, N0,
  ST_PYR, COLOR1, P0, EI, EJ, P1, N0,
  ST_TET, COLOR1, EJ, EB, P1, N0,
 // Case #132: (cloned #5)
  ST_PNT, 0, NOCOLOR, 2, EH, EB,
  ST_PYR, COLOR0, P4, P5, P1, P0, N0,
  ST_TET, COLOR0, P5, P6, P1, N0,
  ST_TET, COLOR0, P4, P6, P5, N0,
  ST_TET, COLOR0, P3, P4, P0, N0,
  ST_TET, COLOR0, P1, P3, P0, N0,
  ST_PYR, COLOR0, P1, EB, EC, P3, N0,
  ST_PYR, COLOR0, P6, EL, EB, P1, N0,
  ST_TET, COLOR0, P6, EG, EL, N0,
  ST_PYR, COLOR0, P4, EH, EG, P6, N0,
  ST_PYR, COLOR0, P4, P3, EK, EH, N0,
  ST_TET, COLOR0, P3, EC, EK, N0,
  ST_PYR, COLOR1, P7, EK, EC, P2, N0,
  ST_PYR, COLOR1, EG, P7, P2, EL, N0,
  ST_TET, COLOR1, EL, P2, EB, N0,
  ST_TET, COLOR1, P2, EC, EB, N0,
  ST_TET, COLOR1, EG, P7, N0, EH,
  ST_TET, COLOR1, EK, P7, EH, N0,
 // Case #133: (cloned #26)
  ST_TET, COLOR0, P3, ED, EC, EK,
  ST_TET, COLOR0, P6, P5, P4, P1,
  ST_PYR, COLOR0, EA, EI, P4, P1, EH,
  ST_PYR, COLOR0, EB, P1, P6, EL, EG,
  ST_PYR, COLOR0, P4, EH, EG, P6, P1,
  ST_PYR, COLOR0, EH, EA, EB, EG, P1,
  ST_WDG, COLOR1, P0, P2, P7, ED, EC, EK,
  ST_PYR, COLOR1, P0, P7, EH, EI, EA,
  ST_PYR, COLOR1, EG, P7, P2, EL, EB,
  ST_PYR, COLOR1, EA, EB, P2, P0, P7,
  ST_PYR, COLOR1, EA, EH, EG, EB, P7,
 // Case #134: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EJ, EA, EH, EH,
  ST_PYR, COLOR0, P4, P3, EK, EH, N0,
  ST_TET, COLOR0, EK, P3, EC, N0,
  ST_PYR, COLOR0, P0, EA, EC, P3, N0,
  ST_TET, COLOR0, P4, P0, P3, N0,
  ST_TET, COLOR0, P6, EG, EL, N0,
  ST_TET, COLOR0, P5, P4, P6, N0,
  ST_PYR, COLOR0, P6, P4, EH, EG, N0,
  ST_TET, COLOR0, P5, P0, P4, N0,
  ST_PYR, COLOR0, P5, EJ, EA, P0, N0,
  ST_PYR, COLOR0, P5, P6, EL, EJ, N0,
  ST_PYR, COLOR1, P7, EK, EC, P2, N0,
  ST_PYR, COLOR1, EL, EG, P7, P2, N0,
  ST_TET, COLOR1, P7, EH, EK, N0,
  ST_TET, COLOR1, EG, EH, P7, N0,
  ST_PYR, COLOR1, EC, EA, P1, P2, N0,
  ST_PYR, COLOR1, P2, P1, EJ, EL, N0,
  ST_TET, COLOR1, EJ, P1, EA, N0,
 // Case #135: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EJ, EL, EI, EH, EG,
  ST_TET, COLOR0, P3, ED, EC, EK,
  ST_PYR, COLOR0, P6, EL, EJ, P5, N0,
  ST_PYR, COLOR0, P5, EJ, EI, P4, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_TET, COLOR0, P5, P4, P6, N0,
  ST_PYR, COLOR0, P4, EH, EG, P6, N0,
  ST_TET, COLOR0, P6, EG, EL, N0,
  ST_WDG, COLOR1, P0, P2, P7, ED, EC, EK,
  ST_TET, COLOR1, P2, P7, P0, N0,
  ST_PYR, COLOR1, P0, P7, EH, EI, N0,
  ST_TET, COLOR1, P1, P2, P0, N0,
  ST_PYR, COLOR1, EL, P2, P1, EJ, N0,
  ST_PYR, COLOR1, EJ, P1, P0, EI, N0,
  ST_PYR, COLOR1, P7, P2, EL, EG, N0,
  ST_TET, COLOR1, EH, P7, EG, N0,
 // Case #136: (cloned #3)
  ST_HEX, COLOR0, EH, P4, P0, ED, EG, P6, P2, EC,
  ST_WDG, COLOR0, P4, P5, P6, P0, P1, P2,
  ST_WDG, COLOR1, P7, EH, EG, P3, ED, EC,
 // Case #137: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EA, EC, EI, EH, EG,
  ST_PYR, COLOR0, P1, P5, P6, P2, N0,
  ST_TET, COLOR0, P6, P4, N0, P5,
  ST_PYR, COLOR0, P2, P6, EG, EC, N0,
  ST_PYR, COLOR0, EA, P1, P2, EC, N0,
  ST_TET, COLOR0, P4, P5, P1, N0,
  ST_PYR, COLOR0, P4, P1, EA, EI, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_PYR, COLOR0, EG, P6, P4, EH, N0,
  ST_PYR, COLOR1, EC, EG, P7, P3, N0,
  ST_PYR, COLOR1, EA, EC, P3, P0, N0,
  ST_TET, COLOR1, EI, EA, P0, N0,
  ST_TET, COLOR1, P0, P3, P7, N0,
  ST_PYR, COLOR1, EI, P0, P7, EH, N0,
  ST_TET, COLOR1, P7, EG, EH, N0,
 // Case #138: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EG, EH, EJ, EJ,
  ST_PYR, COLOR0, P5, EJ, EA, P0, N0,
  ST_TET, COLOR0, EA, ED, P0, N0,
  ST_PYR, COLOR0, P4, P0, ED, EH, N0,
  ST_TET, COLOR0, P5, P0, P4, N0,
  ST_TET, COLOR0, P2, EC, EB, N0,
  ST_TET, COLOR0, P6, P2, P5, N0,
  ST_PYR, COLOR0, P2, EB, EJ, P5, N0,
  ST_TET, COLOR0, P6, P5, P4, N0,
  ST_PYR, COLOR0, P6, P4, EH, EG, N0,
  ST_PYR, COLOR0, P6, EG, EC, P2, N0,
  ST_PYR, COLOR1, P1, P3, ED, EA, N0,
  ST_PYR, COLOR1, EC, P3, P1, EB, N0,
  ST_TET, COLOR1, P1, EA, EJ, N0,
  ST_TET, COLOR1, EB, P1, EJ, N0,
  ST_PYR, COLOR1, ED, P3, P7, EH, N0,
  ST_PYR, COLOR1, P3, EC, EG, P7, N0,
  ST_TET, COLOR1, EG, EH, P7, N0,
 // Case #139: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EI, EH, EG, EC, EB, EJ,
  ST_TET, COLOR0, P6, P2, P5, N0,
  ST_PYR, COLOR0, P6, EG, EC, P2, N0,
  ST_TET, COLOR0, P4, P6, P5, N0,
  ST_PYR, COLOR0, P4, EH, EG, P6, N0,
  ST_TET, COLOR0, EI, EH, P4, N0,
  ST_PYR, COLOR0, P4, P5, EJ, EI, N0,
  ST_PYR, COLOR0, EJ, P5, P2, EB, N0,
  ST_TET, COLOR0, P2, EC, EB, N0,
  ST_TET, COLOR1, P0, P3, P7, N0,
  ST_PYR, COLOR1, EI, P0, P7, EH, N0,
  ST_PYR, COLOR1, EI, EJ, P1, P0, N0,
  ST_TET, COLOR1, P1, P3, P0, N0,
  ST_TET, COLOR1, P1, EJ, EB, N0,
  ST_PYR, COLOR1, P1, EB, EC, P3, N0,
  ST_PYR, COLOR1, EC, EG, P7, P3, N0,
  ST_TET, COLOR1, EG, EH, P7, N0,
 // Case #140: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EB, ED, EL, EG, EH,
  ST_PYR, COLOR0, P1, P0, P4, P5, N0,
  ST_TET, COLOR0, P4, N0, P6, P5,
  ST_PYR, COLOR0, P0, ED, EH, P4, N0,
  ST_PYR, COLOR0, EB, ED, P0, P1, N0,
  ST_TET, COLOR0, P6, P1, P5, N0,
  ST_PYR, COLOR0, P6, EL, EB, P1, N0,
  ST_TET, COLOR0, P6, EG, EL, N0,
  ST_PYR, COLOR0, EH, EG, P6, P4, N0,
  ST_PYR, COLOR1, ED, P3, P7, EH, N0,
  ST_PYR, COLOR1, EB, P2, P3, ED, N0,
  ST_TET, COLOR1, EL, P2, EB, N0,
  ST_TET, COLOR1, P2, P7, P3, N0,
  ST_PYR, COLOR1, EL, EG, P7, P2, N0,
  ST_TET, COLOR1, P7, EG, EH, N0,
 // Case #141: (cloned #27)
  ST_TET, COLOR0, P6, P5, P4, P1,
  ST_PYR, COLOR0, EA, EI, P4, P1, EH,
  ST_PYR, COLOR0, EB, P1, P6, EL, EG,
  ST_PYR, COLOR0, P4, EH, EG, P6, P1,
  ST_PYR, COLOR0, EH, EA, EB, EG, P1,
  ST_TET, COLOR1, P3, P0, P2, P7,
  ST_PYR, COLOR1, EH, EI, P0, P7, EA,
  ST_PYR, COLOR1, EG, P7, P2, EL, EB,
  ST_PYR, COLOR1, P0, EA, EB, P2, P7,
  ST_PYR, COLOR1, EH, EG, EB, EA, P7,
 // Case #142: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, ED, EA, EJ, EL, EG, EH,
  ST_TET, COLOR0, P5, P4, P6, N0,
  ST_PYR, COLOR0, P5, P6, EL, EJ, N0,
  ST_TET, COLOR0, P0, P4, P5, N0,
  ST_PYR, COLOR0, P0, P5, EJ, EA, N0,
  ST_TET, COLOR0, ED, P0, EA, N0,
  ST_PYR, COLOR0, P0, ED, EH, P4, N0,
  ST_PYR, COLOR0, EH, EG, P6, P4, N0,
  ST_TET, COLOR0, P6, EG, EL, N0,
  ST_TET, COLOR1, P3, P1, P2, N0,
  ST_PYR, COLOR1, ED, EA, P1, P3, N0,
  ST_PYR, COLOR1, ED, P3, P7, EH, N0,
  ST_TET, COLOR1, P7, P3, P2, N0,
  ST_TET, COLOR1, P7, EG, EH, N0,
  ST_PYR, COLOR1, P7, P2, EL, EG, N0,
  ST_PYR, COLOR1, EL, P2, P1, EJ, N0,
  ST_TET, COLOR1, EJ, P1, EA, N0,
 // Case #143: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EL, EJ, EI, EG, EH,
  ST_PYR, COLOR0, P5, EJ, EI, P4, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_PYR, COLOR0, P6, EL, EJ, P5, N0,
  ST_TET, COLOR0, EG, EL, P6, N0,
  ST_PYR, COLOR0, EH, EG, P6, P4, N0,
  ST_TET, COLOR0, P4, P6, P5, N0,
  ST_PYR, COLOR1, P3, P0, P1, P2, N0,
  ST_TET, COLOR1, P0, P3, P7, N0,
  ST_TET, COLOR1, P7, P3, P2, N0,
  ST_PYR, COLOR1, P7, P2, EL, EG, N0,
  ST_PYR, COLOR1, EL, P2, P1, EJ, N0,
  ST_PYR, COLOR1, EJ, P1, P0, EI, N0,
  ST_PYR, COLOR1, EI, P0, P7, EH, N0,
  ST_TET, COLOR1, EG, EH, P7, N0,
 // Case #144: (cloned #3)
  ST_HEX, COLOR0, EG, P6, P5, EE, EK, P3, P0, EI,
  ST_WDG, COLOR0, P0, P1, P5, P3, P2, P6,
  ST_WDG, COLOR1, P4, EI, EE, P7, EK, EG,
 // Case #145: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EA, EE, ED, EK, EG,
  ST_PYR, COLOR0, P1, P5, P6, P2, N0,
  ST_TET, COLOR0, P6, N0, P3, P2,
  ST_PYR, COLOR0, P5, EE, EG, P6, N0,
  ST_PYR, COLOR0, EA, EE, P5, P1, N0,
  ST_TET, COLOR0, P3, P1, P2, N0,
  ST_PYR, COLOR0, P3, ED, EA, P1, N0,
  ST_TET, COLOR0, P3, EK, ED, N0,
  ST_PYR, COLOR0, EG, EK, P3, P6, N0,
  ST_PYR, COLOR1, EE, P4, P7, EG, N0,
  ST_PYR, COLOR1, EA, P0, P4, EE, N0,
  ST_TET, COLOR1, ED, P0, EA, N0,
  ST_TET, COLOR1, P0, P7, P4, N0,
  ST_PYR, COLOR1, ED, EK, P7, P0, N0,
  ST_TET, COLOR1, P7, EK, EG, N0,
 // Case #146: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EG, EK, EB, EB,
  ST_PYR, COLOR0, P2, P0, EA, EB, N0,
  ST_TET, COLOR0, EA, P0, EI, N0,
  ST_PYR, COLOR0, P3, EK, EI, P0, N0,
  ST_TET, COLOR0, P2, P3, P0, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_TET, COLOR0, P6, P2, P5, N0,
  ST_PYR, COLOR0, P5, P2, EB, EJ, N0,
  ST_TET, COLOR0, P6, P3, P2, N0,
  ST_PYR, COLOR0, P6, EG, EK, P3, N0,
  ST_PYR, COLOR0, P6, P5, EE, EG, N0,
  ST_PYR, COLOR1, P1, EA, EI, P4, N0,
  ST_PYR, COLOR1, EE, EJ, P1, P4, N0,
  ST_TET, COLOR1, P1, EB, EA, N0,
  ST_TET, COLOR1, EJ, EB, P1, N0,
  ST_PYR, COLOR1, EI, EK, P7, P4, N0,
  ST_PYR, COLOR1, P4, P7, EG, EE, N0,
  ST_TET, COLOR1, EG, P7, EK, N0,
 // Case #147: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, ED, EK, EG, EE, EJ, EB,
  ST_TET, COLOR0, P6, P2, P5, N0,
  ST_PYR, COLOR0, P6, P5, EE, EG, N0,
  ST_TET, COLOR0, P3, P2, P6, N0,
  ST_PYR, COLOR0, P3, P6, EG, EK, N0,
  ST_TET, COLOR0, ED, P3, EK, N0,
  ST_PYR, COLOR0, P3, ED, EB, P2, N0,
  ST_PYR, COLOR0, EB, EJ, P5, P2, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_TET, COLOR1, P0, P7, P4, N0,
  ST_PYR, COLOR1, ED, EK, P7, P0, N0,
  ST_PYR, COLOR1, ED, P0, P1, EB, N0,
  ST_TET, COLOR1, P1, P0, P4, N0,
  ST_TET, COLOR1, P1, EJ, EB, N0,
  ST_PYR, COLOR1, P1, P4, EE, EJ, N0,
  ST_PYR, COLOR1, EE, P4, P7, EG, N0,
  ST_TET, COLOR1, EG, P7, EK, N0,
 // Case #148: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EE, EI, EB, EB,
  ST_PYR, COLOR0, P1, EB, EC, P3, N0,
  ST_TET, COLOR0, EC, EK, P3, N0,
  ST_PYR, COLOR0, P0, P3, EK, EI, N0,
  ST_TET, COLOR0, P1, P3, P0, N0,
  ST_TET, COLOR0, P6, EG, EL, N0,
  ST_TET, COLOR0, P5, P6, P1, N0,
  ST_PYR, COLOR0, P6, EL, EB, P1, N0,
  ST_TET, COLOR0, P5, P1, P0, N0,
  ST_PYR, COLOR0, P5, P0, EI, EE, N0,
  ST_PYR, COLOR0, P5, EE, EG, P6, N0,
  ST_PYR, COLOR1, P2, P7, EK, EC, N0,
  ST_PYR, COLOR1, EG, P7, P2, EL, N0,
  ST_TET, COLOR1, P2, EC, EB, N0,
  ST_TET, COLOR1, EL, P2, EB, N0,
  ST_PYR, COLOR1, EK, P7, P4, EI, N0,
  ST_PYR, COLOR1, P7, EG, EE, P4, N0,
  ST_TET, COLOR1, EE, EI, P4, N0,
 // Case #149: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EE, EG, EA, EB, EL,
  ST_TET, COLOR0, P3, EK, ED, EC,
  ST_PYR, COLOR0, P6, P5, EE, EG, N0,
  ST_PYR, COLOR0, P5, P1, EA, EE, N0,
  ST_TET, COLOR0, P1, EB, EA, N0,
  ST_TET, COLOR0, P5, P6, P1, N0,
  ST_PYR, COLOR0, P1, P6, EL, EB, N0,
  ST_TET, COLOR0, P6, EG, EL, N0,
  ST_WDG, COLOR1, ED, EK, EC, P0, P7, P2,
  ST_TET, COLOR1, P7, P0, P2, N0,
  ST_PYR, COLOR1, P0, EA, EB, P2, N0,
  ST_TET, COLOR1, P4, P0, P7, N0,
  ST_PYR, COLOR1, EG, EE, P4, P7, N0,
  ST_PYR, COLOR1, EE, EA, P0, P4, N0,
  ST_PYR, COLOR1, P2, EL, EG, P7, N0,
  ST_TET, COLOR1, EB, EL, P2, N0,
 // Case #150: (cloned #60)
  ST_WDG, COLOR0, P3, EK, EC, P0, EI, EA,
  ST_WDG, COLOR0, P6, EL, EG, P5, EJ, EE,
  ST_HEX, COLOR1, P4, P1, P2, P7, EE, EJ, EL, EG,
  ST_HEX, COLOR1, EI, EA, EC, EK, P4, P1, P2, P7,
 // Case #151: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P0, P7, P4, P1, EL, EJ,
  ST_WDG, COLOR0, EJ, P5, EE, EL, P6, EG,
  ST_TET, COLOR0, ED, P3, EK, EC,
  ST_WDG, COLOR1, P0, P2, P7, ED, EC, EK,
  ST_PYR, COLOR1, EJ, EL, P2, P1, N0,
  ST_TET, COLOR1, P1, P2, P0, N0,
  ST_TET, COLOR1, P1, P0, P4, N0,
  ST_PYR, COLOR1, EE, EJ, P1, P4, N0,
  ST_PYR, COLOR1, EG, EE, P4, P7, N0,
  ST_TET, COLOR1, P4, P0, P7, N0,
  ST_PYR, COLOR1, EL, EJ, EE, EG, N0,
  ST_TET, COLOR1, P7, P0, P2, N0,
  ST_PYR, COLOR1, EL, EG, P7, P2, N0,
 // Case #152: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EC, EG, ED, EI, EE,
  ST_PYR, COLOR0, P2, P1, P5, P6, N0,
  ST_TET, COLOR0, P5, P0, N0, P1,
  ST_PYR, COLOR0, P6, P5, EE, EG, N0,
  ST_PYR, COLOR0, EC, P2, P6, EG, N0,
  ST_TET, COLOR0, P0, P1, P2, N0,
  ST_PYR, COLOR0, P0, P2, EC, ED, N0,
  ST_TET, COLOR0, P0, ED, EI, N0,
  ST_PYR, COLOR0, EE, P5, P0, EI, N0,
  ST_PYR, COLOR1, EG, EE, P4, P7, N0,
  ST_PYR, COLOR1, EC, EG, P7, P3, N0,
  ST_TET, COLOR1, ED, EC, P3, N0,
  ST_TET, COLOR1, P3, P7, P4, N0,
  ST_PYR, COLOR1, ED, P3, P4, EI, N0,
  ST_TET, COLOR1, P4, EE, EI, N0,
 // Case #153: (cloned #15)
  ST_HEX, COLOR0, P1, P5, P6, P2, EA, EE, EG, EC,
  ST_HEX, COLOR1, EA, EE, EG, EC, P0, P4, P7, P3,
 // Case #154: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EG, EE, EC, EB, EJ,
  ST_TET, COLOR0, P0, ED, EI, EA,
  ST_PYR, COLOR0, P5, EE, EG, P6, N0,
  ST_PYR, COLOR0, P6, EG, EC, P2, N0,
  ST_TET, COLOR0, P2, EC, EB, N0,
  ST_TET, COLOR0, P6, P2, P5, N0,
  ST_PYR, COLOR0, P2, EB, EJ, P5, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_WDG, COLOR1, P3, P4, P1, ED, EI, EA,
  ST_TET, COLOR1, P4, P1, P3, N0,
  ST_PYR, COLOR1, P3, P1, EB, EC, N0,
  ST_TET, COLOR1, P7, P4, P3, N0,
  ST_PYR, COLOR1, EE, P4, P7, EG, N0,
  ST_PYR, COLOR1, EG, P7, P3, EC, N0,
  ST_PYR, COLOR1, P1, P4, EE, EJ, N0,
  ST_TET, COLOR1, EB, P1, EJ, N0,
 // Case #155: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EE, EG, EC, EJ, EB,
  ST_PYR, COLOR0, P6, EG, EC, P2, N0,
  ST_TET, COLOR0, P2, EC, EB, N0,
  ST_PYR, COLOR0, P5, EE, EG, P6, N0,
  ST_TET, COLOR0, EJ, EE, P5, N0,
  ST_PYR, COLOR0, EB, EJ, P5, P2, N0,
  ST_TET, COLOR0, P2, P5, P6, N0,
  ST_PYR, COLOR1, P0, P3, P7, P4, N0,
  ST_TET, COLOR1, P3, P0, P1, N0,
  ST_TET, COLOR1, P1, P0, P4, N0,
  ST_PYR, COLOR1, P1, P4, EE, EJ, N0,
  ST_PYR, COLOR1, EE, P4, P7, EG, N0,
  ST_PYR, COLOR1, EG, P7, P3, EC, N0,
  ST_PYR, COLOR1, EC, P3, P1, EB, N0,
  ST_TET, COLOR1, EJ, EB, P1, N0,
 // Case #156: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, ED, EI, EE, EG, EL, EB,
  ST_TET, COLOR0, P5, P6, P1, N0,
  ST_PYR, COLOR0, P5, EE, EG, P6, N0,
  ST_TET, COLOR0, P0, P5, P1, N0,
  ST_PYR, COLOR0, P0, EI, EE, P5, N0,
  ST_TET, COLOR0, ED, EI, P0, N0,
  ST_PYR, COLOR0, P0, P1, EB, ED, N0,
  ST_PYR, COLOR0, EB, P1, P6, EL, N0,
  ST_TET, COLOR0, P6, EG, EL, N0,
  ST_TET, COLOR1, P3, P7, P4, N0,
  ST_PYR, COLOR1, ED, P3, P4, EI, N0,
  ST_PYR, COLOR1, ED, EB, P2, P3, N0,
  ST_TET, COLOR1, P2, P7, P3, N0,
  ST_TET, COLOR1, P2, EB, EL, N0,
  ST_PYR, COLOR1, P2, EL, EG, P7, N0,
  ST_PYR, COLOR1, EG, EE, P4, P7, N0,
  ST_TET, COLOR1, EE, EI, P4, N0,
 // Case #157: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EG, EE, EA, EL, EB,
  ST_PYR, COLOR0, P5, P1, EA, EE, N0,
  ST_TET, COLOR0, P1, EB, EA, N0,
  ST_PYR, COLOR0, P6, P5, EE, EG, N0,
  ST_TET, COLOR0, EL, P6, EG, N0,
  ST_PYR, COLOR0, EB, P1, P6, EL, N0,
  ST_TET, COLOR0, P1, P5, P6, N0,
  ST_PYR, COLOR1, P3, P7, P4, P0, N0,
  ST_TET, COLOR1, P0, P2, P3, N0,
  ST_TET, COLOR1, P2, P7, P3, N0,
  ST_PYR, COLOR1, P2, EL, EG, P7, N0,
  ST_PYR, COLOR1, EG, EE, P4, P7, N0,
  ST_PYR, COLOR1, EE, EA, P0, P4, N0,
  ST_PYR, COLOR1, EA, EB, P2, P0, N0,
  ST_TET, COLOR1, EL, P2, EB, N0,
 // Case #158: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P3, P4, P7, P2, EJ, EL,
  ST_WDG, COLOR0, EJ, P5, EE, EL, P6, EG,
  ST_TET, COLOR0, ED, EI, P0, EA,
  ST_WDG, COLOR1, ED, EA, EI, P3, P1, P4,
  ST_PYR, COLOR1, EL, P2, P1, EJ, N0,
  ST_TET, COLOR1, P2, P3, P1, N0,
  ST_TET, COLOR1, P2, P7, P3, N0,
  ST_PYR, COLOR1, EG, P7, P2, EL, N0,
  ST_PYR, COLOR1, EE, P4, P7, EG, N0,
  ST_TET, COLOR1, P7, P4, P3, N0,
  ST_PYR, COLOR1, EJ, EE, EG, EL, N0,
  ST_TET, COLOR1, P4, P1, P3, N0,
  ST_PYR, COLOR1, EJ, P1, P4, EE, N0,
 // Case #159: (cloned #63)
  ST_WDG, COLOR0, P5, EE, EJ, P6, EG, EL,
  ST_HEX, COLOR1, P4, P1, P2, P7, EE, EJ, EL, EG,
  ST_WDG, COLOR1, P3, P7, P2, P0, P4, P1,
 // Case #160: (cloned #5)
  ST_PNT, 0, NOCOLOR, 2, EK, EJ,
  ST_PYR, COLOR0, P3, P0, P1, P2, N0,
  ST_TET, COLOR0, P2, P1, P6, N0,
  ST_TET, COLOR0, P3, P2, P6, N0,
  ST_TET, COLOR0, P4, P0, P3, N0,
  ST_TET, COLOR0, P1, P0, P4, N0,
  ST_PYR, COLOR0, P1, P4, EE, EJ, N0,
  ST_PYR, COLOR0, P6, P1, EJ, EF, N0,
  ST_TET, COLOR0, P6, EF, EG, N0,
  ST_PYR, COLOR0, P3, P6, EG, EK, N0,
  ST_PYR, COLOR0, P3, EK, EH, P4, N0,
  ST_TET, COLOR0, P4, EH, EE, N0,
  ST_PYR, COLOR1, P7, P5, EE, EH, N0,
  ST_PYR, COLOR1, EG, EF, P5, P7, N0,
  ST_TET, COLOR1, EF, EJ, P5, N0,
  ST_TET, COLOR1, P5, EJ, EE, N0,
  ST_TET, COLOR1, EG, N0, P7, EK,
  ST_TET, COLOR1, EH, EK, P7, N0,
 // Case #161: (cloned #26)
  ST_TET, COLOR0, P4, EE, EI, EH,
  ST_TET, COLOR0, P6, P3, P2, P1,
  ST_PYR, COLOR0, EA, P1, P3, ED, EK,
  ST_PYR, COLOR0, EJ, EF, P6, P1, EG,
  ST_PYR, COLOR0, P3, P6, EG, EK, P1,
  ST_PYR, COLOR0, EK, EG, EJ, EA, P1,
  ST_WDG, COLOR1, EI, EE, EH, P0, P5, P7,
  ST_PYR, COLOR1, P0, ED, EK, P7, EA,
  ST_PYR, COLOR1, EG, EF, P5, P7, EJ,
  ST_PYR, COLOR1, EA, P0, P5, EJ, P7,
  ST_PYR, COLOR1, EA, EJ, EG, EK, P7,
 // Case #162: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EB, EA, EK, EK,
  ST_PYR, COLOR0, P3, EK, EH, P4, N0,
  ST_TET, COLOR0, EH, EE, P4, N0,
  ST_PYR, COLOR0, P0, P4, EE, EA, N0,
  ST_TET, COLOR0, P3, P4, P0, N0,
  ST_TET, COLOR0, P6, EF, EG, N0,
  ST_TET, COLOR0, P2, P6, P3, N0,
  ST_PYR, COLOR0, P6, EG, EK, P3, N0,
  ST_TET, COLOR0, P2, P3, P0, N0,
  ST_PYR, COLOR0, P2, P0, EA, EB, N0,
  ST_PYR, COLOR0, P2, EB, EF, P6, N0,
  ST_PYR, COLOR1, P7, P5, EE, EH, N0,
  ST_PYR, COLOR1, EF, P5, P7, EG, N0,
  ST_TET, COLOR1, P7, EH, EK, N0,
  ST_TET, COLOR1, EG, P7, EK, N0,
  ST_PYR, COLOR1, EE, P5, P1, EA, N0,
  ST_PYR, COLOR1, P5, EF, EB, P1, N0,
  ST_TET, COLOR1, EB, EA, P1, N0,
 // Case #163: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EB, EF, ED, EK, EG,
  ST_TET, COLOR0, P4, EE, EI, EH,
  ST_PYR, COLOR0, P6, P2, EB, EF, N0,
  ST_PYR, COLOR0, P2, P3, ED, EB, N0,
  ST_TET, COLOR0, P3, EK, ED, N0,
  ST_TET, COLOR0, P2, P6, P3, N0,
  ST_PYR, COLOR0, P3, P6, EG, EK, N0,
  ST_TET, COLOR0, P6, EF, EG, N0,
  ST_WDG, COLOR1, EI, EE, EH, P0, P5, P7,
  ST_TET, COLOR1, P5, P0, P7, N0,
  ST_PYR, COLOR1, P0, ED, EK, P7, N0,
  ST_TET, COLOR1, P1, P0, P5, N0,
  ST_PYR, COLOR1, EF, EB, P1, P5, N0,
  ST_PYR, COLOR1, EB, ED, P0, P1, N0,
  ST_PYR, COLOR1, P7, EG, EF, P5, N0,
  ST_TET, COLOR1, EK, EG, P7, N0,
 // Case #164: (cloned #26)
  ST_TET, COLOR0, P6, EF, EG, EL,
  ST_TET, COLOR0, P3, P0, P1, P4,
  ST_PYR, COLOR0, EE, EJ, P1, P4, EB,
  ST_PYR, COLOR0, EH, P4, P3, EK, EC,
  ST_PYR, COLOR0, P1, EB, EC, P3, P4,
  ST_PYR, COLOR0, EB, EE, EH, EC, P4,
  ST_WDG, COLOR1, P5, P7, P2, EF, EG, EL,
  ST_PYR, COLOR1, P5, P2, EB, EJ, EE,
  ST_PYR, COLOR1, EC, P2, P7, EK, EH,
  ST_PYR, COLOR1, EE, EH, P7, P5, P2,
  ST_PYR, COLOR1, EE, EB, EC, EH, P2,
 // Case #165: (cloned #90)
  ST_TET, COLOR0, EH, EI, EE, P4,
  ST_TET, COLOR0, EK, EC, ED, P3,
  ST_TET, COLOR0, EG, EF, EL, P6,
  ST_TET, COLOR0, EB, EJ, EA, P1,
  ST_WDG, COLOR1, P2, P0, P5, EB, EA, EJ,
  ST_TET, COLOR1, P2, P0, P5, P7,
  ST_WDG, COLOR1, EG, EF, EL, P7, P5, P2,
  ST_WDG, COLOR1, ED, EK, EC, P0, P7, P2,
  ST_WDG, COLOR1, EE, EH, EI, P5, P7, P0,
 // Case #166: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EA, EC, EE, EH, EK,
  ST_TET, COLOR0, P6, EL, EF, EG,
  ST_PYR, COLOR0, P3, P0, EA, EC, N0,
  ST_PYR, COLOR0, P0, P4, EE, EA, N0,
  ST_TET, COLOR0, P4, EH, EE, N0,
  ST_TET, COLOR0, P0, P3, P4, N0,
  ST_PYR, COLOR0, P4, P3, EK, EH, N0,
  ST_TET, COLOR0, P3, EC, EK, N0,
  ST_WDG, COLOR1, EF, EL, EG, P5, P2, P7,
  ST_TET, COLOR1, P2, P5, P7, N0,
  ST_PYR, COLOR1, P5, EE, EH, P7, N0,
  ST_TET, COLOR1, P1, P5, P2, N0,
  ST_PYR, COLOR1, EC, EA, P1, P2, N0,
  ST_PYR, COLOR1, EA, EE, P5, P1, N0,
  ST_PYR, COLOR1, P7, EK, EC, P2, N0,
  ST_TET, COLOR1, EH, EK, P7, N0,
 // Case #167: (cloned #91)
  ST_TET, COLOR0, EL, EG, EF, P6,
  ST_TET, COLOR0, EC, P3, ED, EK,
  ST_TET, COLOR0, EE, EI, P4, EH,
  ST_WDG, COLOR1, EH, EI, EE, P7, P0, P5,
  ST_WDG, COLOR1, P2, P7, P0, EC, EK, ED,
  ST_WDG, COLOR1, EL, EG, EF, P2, P7, P5,
  ST_TET, COLOR1, P7, P5, P0, P2,
  ST_TET, COLOR1, P2, P1, P5, P0,
 // Case #168: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EC, ED, EJ, EJ,
  ST_PYR, COLOR0, P1, P4, EE, EJ, N0,
  ST_TET, COLOR0, EE, P4, EH, N0,
  ST_PYR, COLOR0, P0, ED, EH, P4, N0,
  ST_TET, COLOR0, P1, P0, P4, N0,
  ST_TET, COLOR0, P6, EF, EG, N0,
  ST_TET, COLOR0, P2, P1, P6, N0,
  ST_PYR, COLOR0, P6, P1, EJ, EF, N0,
  ST_TET, COLOR0, P2, P0, P1, N0,
  ST_PYR, COLOR0, P2, EC, ED, P0, N0,
  ST_PYR, COLOR0, P2, P6, EG, EC, N0,
  ST_PYR, COLOR1, P5, EE, EH, P7, N0,
  ST_PYR, COLOR1, EG, EF, P5, P7, N0,
  ST_TET, COLOR1, P5, EJ, EE, N0,
  ST_TET, COLOR1, EF, EJ, P5, N0,
  ST_PYR, COLOR1, EH, ED, P3, P7, N0,
  ST_PYR, COLOR1, P7, P3, EC, EG, N0,
  ST_TET, COLOR1, EC, P3, ED, N0,
 // Case #169: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EC, EG, EA, EJ, EF,
  ST_TET, COLOR0, P4, EI, EH, EE,
  ST_PYR, COLOR0, P6, EG, EC, P2, N0,
  ST_PYR, COLOR0, P2, EC, EA, P1, N0,
  ST_TET, COLOR0, P1, EA, EJ, N0,
  ST_TET, COLOR0, P2, P1, P6, N0,
  ST_PYR, COLOR0, P1, EJ, EF, P6, N0,
  ST_TET, COLOR0, P6, EF, EG, N0,
  ST_WDG, COLOR1, P0, P7, P5, EI, EH, EE,
  ST_TET, COLOR1, P7, P5, P0, N0,
  ST_PYR, COLOR1, P0, P5, EJ, EA, N0,
  ST_TET, COLOR1, P3, P7, P0, N0,
  ST_PYR, COLOR1, EG, P7, P3, EC, N0,
  ST_PYR, COLOR1, EC, P3, P0, EA, N0,
  ST_PYR, COLOR1, P5, P7, EG, EF, N0,
  ST_TET, COLOR1, EJ, P5, EF, N0,
 // Case #170: (cloned #60)
  ST_WDG, COLOR0, P0, ED, EA, P4, EH, EE,
  ST_WDG, COLOR0, P2, EB, EC, P6, EF, EG,
  ST_HEX, COLOR1, EC, EB, EF, EG, P3, P1, P5, P7,
  ST_HEX, COLOR1, P3, P1, P5, P7, ED, EA, EE, EH,
 // Case #171: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P0, P7, P3, P1, EF, EB,
  ST_WDG, COLOR0, EF, P6, EG, EB, P2, EC,
  ST_TET, COLOR0, EI, EH, P4, EE,
  ST_WDG, COLOR1, EI, EE, EH, P0, P5, P7,
  ST_PYR, COLOR1, EB, P1, P5, EF, N0,
  ST_TET, COLOR1, P1, P0, P5, N0,
  ST_TET, COLOR1, P1, P3, P0, N0,
  ST_PYR, COLOR1, EC, P3, P1, EB, N0,
  ST_PYR, COLOR1, EG, P7, P3, EC, N0,
  ST_TET, COLOR1, P3, P7, P0, N0,
  ST_PYR, COLOR1, EF, EG, EC, EB, N0,
  ST_TET, COLOR1, P7, P5, P0, N0,
  ST_PYR, COLOR1, EF, P5, P7, EG, N0,
 // Case #172: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, ED, EH, EB, EJ, EE,
  ST_TET, COLOR0, P6, EG, EL, EF,
  ST_PYR, COLOR0, P4, P0, ED, EH, N0,
  ST_PYR, COLOR0, P0, P1, EB, ED, N0,
  ST_TET, COLOR0, P1, EJ, EB, N0,
  ST_TET, COLOR0, P0, P4, P1, N0,
  ST_PYR, COLOR0, P1, P4, EE, EJ, N0,
  ST_TET, COLOR0, P4, EH, EE, N0,
  ST_WDG, COLOR1, EL, EG, EF, P2, P7, P5,
  ST_TET, COLOR1, P7, P2, P5, N0,
  ST_PYR, COLOR1, P2, EB, EJ, P5, N0,
  ST_TET, COLOR1, P3, P2, P7, N0,
  ST_PYR, COLOR1, EH, ED, P3, P7, N0,
  ST_PYR, COLOR1, ED, EB, P2, P3, N0,
  ST_PYR, COLOR1, P5, EE, EH, P7, N0,
  ST_TET, COLOR1, EJ, EE, P5, N0,
 // Case #173: (cloned #91)
  ST_TET, COLOR0, EG, EF, EL, P6,
  ST_TET, COLOR0, EH, P4, EI, EE,
  ST_TET, COLOR0, EB, EA, P1, EJ,
  ST_WDG, COLOR1, EJ, EA, EB, P5, P0, P2,
  ST_WDG, COLOR1, P7, P5, P0, EH, EE, EI,
  ST_WDG, COLOR1, EG, EF, EL, P7, P5, P2,
  ST_TET, COLOR1, P5, P2, P0, P7,
  ST_TET, COLOR1, P7, P3, P2, P0,
 // Case #174: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P2, P5, P1, P3, EH, ED,
  ST_WDG, COLOR0, EH, P4, EE, ED, P0, EA,
  ST_TET, COLOR0, EL, EF, P6, EG,
  ST_WDG, COLOR1, EL, EG, EF, P2, P7, P5,
  ST_PYR, COLOR1, ED, P3, P7, EH, N0,
  ST_TET, COLOR1, P3, P2, P7, N0,
  ST_TET, COLOR1, P3, P1, P2, N0,
  ST_PYR, COLOR1, EA, P1, P3, ED, N0,
  ST_PYR, COLOR1, EE, P5, P1, EA, N0,
  ST_TET, COLOR1, P1, P5, P2, N0,
  ST_PYR, COLOR1, EH, EE, EA, ED, N0,
  ST_TET, COLOR1, P5, P7, P2, N0,
  ST_PYR, COLOR1, EH, P7, P5, EE, N0,
 // Case #175: (cloned #95)
  ST_TET, COLOR0, EE, EH, EI, P4,
  ST_TET, COLOR0, EF, EL, EG, P6,
  ST_WDG, COLOR1, EG, EF, EL, P7, P5, P2,
  ST_WDG, COLOR1, P7, P5, P0, EH, EE, EI,
  ST_PYR, COLOR1, P3, P0, P1, P2, P7,
  ST_TET, COLOR1, P5, P1, P0, P7,
  ST_TET, COLOR1, P5, P2, P1, P7,
 // Case #176: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EJ, EI, EF, EG, EK,
  ST_PYR, COLOR0, P1, P2, P3, P0, N0,
  ST_TET, COLOR0, P3, P6, N0, P2,
  ST_PYR, COLOR0, P0, P3, EK, EI, N0,
  ST_PYR, COLOR0, EJ, P1, P0, EI, N0,
  ST_TET, COLOR0, P6, P2, P1, N0,
  ST_PYR, COLOR0, P6, P1, EJ, EF, N0,
  ST_TET, COLOR0, P6, EF, EG, N0,
  ST_PYR, COLOR0, EK, P3, P6, EG, N0,
  ST_PYR, COLOR1, EI, EK, P7, P4, N0,
  ST_PYR, COLOR1, EJ, EI, P4, P5, N0,
  ST_TET, COLOR1, EF, EJ, P5, N0,
  ST_TET, COLOR1, P5, P4, P7, N0,
  ST_PYR, COLOR1, EF, P5, P7, EG, N0,
  ST_TET, COLOR1, P7, EK, EG, N0,
 // Case #177: (cloned #27)
  ST_TET, COLOR0, P6, P3, P2, P1,
  ST_PYR, COLOR0, EA, P1, P3, ED, EK,
  ST_PYR, COLOR0, EJ, EF, P6, P1, EG,
  ST_PYR, COLOR0, P3, P6, EG, EK, P1,
  ST_PYR, COLOR0, EK, EG, EJ, EA, P1,
  ST_TET, COLOR1, P4, P5, P0, P7,
  ST_PYR, COLOR1, EK, P7, P0, ED, EA,
  ST_PYR, COLOR1, EG, EF, P5, P7, EJ,
  ST_PYR, COLOR1, P0, P5, EJ, EA, P7,
  ST_PYR, COLOR1, EK, EA, EJ, EG, P7,
 // Case #178: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EI, EA, EB, EF, EG, EK,
  ST_TET, COLOR0, P2, P6, P3, N0,
  ST_PYR, COLOR0, P2, EB, EF, P6, N0,
  ST_TET, COLOR0, P0, P2, P3, N0,
  ST_PYR, COLOR0, P0, EA, EB, P2, N0,
  ST_TET, COLOR0, EI, EA, P0, N0,
  ST_PYR, COLOR0, P0, P3, EK, EI, N0,
  ST_PYR, COLOR0, EK, P3, P6, EG, N0,
  ST_TET, COLOR0, P6, EF, EG, N0,
  ST_TET, COLOR1, P4, P5, P1, N0,
  ST_PYR, COLOR1, EI, P4, P1, EA, N0,
  ST_PYR, COLOR1, EI, EK, P7, P4, N0,
  ST_TET, COLOR1, P7, P5, P4, N0,
  ST_TET, COLOR1, P7, EK, EG, N0,
  ST_PYR, COLOR1, P7, EG, EF, P5, N0,
  ST_PYR, COLOR1, EF, EB, P1, P5, N0,
  ST_TET, COLOR1, EB, EA, P1, N0,
 // Case #179: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EF, EB, ED, EG, EK,
  ST_PYR, COLOR0, P2, P3, ED, EB, N0,
  ST_TET, COLOR0, P3, EK, ED, N0,
  ST_PYR, COLOR0, P6, P2, EB, EF, N0,
  ST_TET, COLOR0, EG, P6, EF, N0,
  ST_PYR, COLOR0, EK, P3, P6, EG, N0,
  ST_TET, COLOR0, P3, P2, P6, N0,
  ST_PYR, COLOR1, P4, P5, P1, P0, N0,
  ST_TET, COLOR1, P0, P7, P4, N0,
  ST_TET, COLOR1, P7, P5, P4, N0,
  ST_PYR, COLOR1, P7, EG, EF, P5, N0,
  ST_PYR, COLOR1, EF, EB, P1, P5, N0,
  ST_PYR, COLOR1, EB, ED, P0, P1, N0,
  ST_PYR, COLOR1, ED, EK, P7, P0, N0,
  ST_TET, COLOR1, EG, P7, EK, N0,
 // Case #180: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EI, EK, EJ, EB, EC,
  ST_TET, COLOR0, P6, EF, EG, EL,
  ST_PYR, COLOR0, P3, EK, EI, P0, N0,
  ST_PYR, COLOR0, P0, EI, EJ, P1, N0,
  ST_TET, COLOR0, P1, EJ, EB, N0,
  ST_TET, COLOR0, P0, P1, P3, N0,
  ST_PYR, COLOR0, P1, EB, EC, P3, N0,
  ST_TET, COLOR0, P3, EC, EK, N0,
  ST_WDG, COLOR1, P5, P7, P2, EF, EG, EL,
  ST_TET, COLOR1, P7, P2, P5, N0,
  ST_PYR, COLOR1, P5, P2, EB, EJ, N0,
  ST_TET, COLOR1, P4, P7, P5, N0,
  ST_PYR, COLOR1, EK, P7, P4, EI, N0,
  ST_PYR, COLOR1, EI, P4, P5, EJ, N0,
  ST_PYR, COLOR1, P2, P7, EK, EC, N0,
  ST_TET, COLOR1, EB, P2, EC, N0,
 // Case #181: (cloned #91)
  ST_TET, COLOR0, EG, EF, EL, P6,
  ST_TET, COLOR0, EK, ED, P3, EC,
  ST_TET, COLOR0, EJ, P1, EA, EB,
  ST_WDG, COLOR1, P2, P0, P5, EB, EA, EJ,
  ST_WDG, COLOR1, EK, EC, ED, P7, P2, P0,
  ST_WDG, COLOR1, P7, P2, P5, EG, EL, EF,
  ST_TET, COLOR1, P2, P0, P5, P7,
  ST_TET, COLOR1, P7, P5, P4, P0,
 // Case #182: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P5, P2, P1, P4, EK, EI,
  ST_WDG, COLOR0, EI, P0, EA, EK, P3, EC,
  ST_TET, COLOR0, EF, P6, EL, EG,
  ST_WDG, COLOR1, P5, P7, P2, EF, EG, EL,
  ST_PYR, COLOR1, EI, EK, P7, P4, N0,
  ST_TET, COLOR1, P4, P7, P5, N0,
  ST_TET, COLOR1, P4, P5, P1, N0,
  ST_PYR, COLOR1, EA, EI, P4, P1, N0,
  ST_PYR, COLOR1, EC, EA, P1, P2, N0,
  ST_TET, COLOR1, P1, P5, P2, N0,
  ST_PYR, COLOR1, EK, EI, EA, EC, N0,
  ST_TET, COLOR1, P2, P5, P7, N0,
  ST_PYR, COLOR1, EK, EC, P2, P7, N0,
 // Case #183: (cloned #95)
  ST_TET, COLOR0, EC, ED, EK, P3,
  ST_TET, COLOR0, EL, EG, EF, P6,
  ST_WDG, COLOR1, P7, P2, P5, EG, EL, EF,
  ST_WDG, COLOR1, EK, EC, ED, P7, P2, P0,
  ST_PYR, COLOR1, P4, P5, P1, P0, P7,
  ST_TET, COLOR1, P2, P0, P1, P7,
  ST_TET, COLOR1, P2, P1, P5, P7,
 // Case #184: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EI, ED, EC, EG, EF, EJ,
  ST_TET, COLOR0, P2, P1, P6, N0,
  ST_PYR, COLOR0, P2, P6, EG, EC, N0,
  ST_TET, COLOR0, P0, P1, P2, N0,
  ST_PYR, COLOR0, P0, P2, EC, ED, N0,
  ST_TET, COLOR0, EI, P0, ED, N0,
  ST_PYR, COLOR0, P0, EI, EJ, P1, N0,
  ST_PYR, COLOR0, EJ, EF, P6, P1, N0,
  ST_TET, COLOR0, P6, EF, EG, N0,
  ST_TET, COLOR1, P4, P3, P7, N0,
  ST_PYR, COLOR1, EI, ED, P3, P4, N0,
  ST_PYR, COLOR1, EI, P4, P5, EJ, N0,
  ST_TET, COLOR1, P5, P4, P7, N0,
  ST_TET, COLOR1, P5, EF, EJ, N0,
  ST_PYR, COLOR1, P5, P7, EG, EF, N0,
  ST_PYR, COLOR1, EG, P7, P3, EC, N0,
  ST_TET, COLOR1, EC, P3, ED, N0,
 // Case #185: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EG, EC, EA, EF, EJ,
  ST_PYR, COLOR0, P2, EC, EA, P1, N0,
  ST_TET, COLOR0, P1, EA, EJ, N0,
  ST_PYR, COLOR0, P6, EG, EC, P2, N0,
  ST_TET, COLOR0, EF, EG, P6, N0,
  ST_PYR, COLOR0, EJ, EF, P6, P1, N0,
  ST_TET, COLOR0, P1, P6, P2, N0,
  ST_PYR, COLOR1, P4, P0, P3, P7, N0,
  ST_TET, COLOR1, P0, P4, P5, N0,
  ST_TET, COLOR1, P5, P4, P7, N0,
  ST_PYR, COLOR1, P5, P7, EG, EF, N0,
  ST_PYR, COLOR1, EG, P7, P3, EC, N0,
  ST_PYR, COLOR1, EC, P3, P0, EA, N0,
  ST_PYR, COLOR1, EA, P0, P5, EJ, N0,
  ST_TET, COLOR1, EF, EJ, P5, N0,
 // Case #186: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P4, P3, P7, P5, EB, EF,
  ST_WDG, COLOR0, EF, P6, EG, EB, P2, EC,
  ST_TET, COLOR0, EI, P0, ED, EA,
  ST_WDG, COLOR1, P4, P1, P3, EI, EA, ED,
  ST_PYR, COLOR1, EF, EB, P1, P5, N0,
  ST_TET, COLOR1, P5, P1, P4, N0,
  ST_TET, COLOR1, P5, P4, P7, N0,
  ST_PYR, COLOR1, EG, EF, P5, P7, N0,
  ST_PYR, COLOR1, EC, EG, P7, P3, N0,
  ST_TET, COLOR1, P7, P4, P3, N0,
  ST_PYR, COLOR1, EB, EF, EG, EC, N0,
  ST_TET, COLOR1, P3, P4, P1, N0,
  ST_PYR, COLOR1, EB, EC, P3, P1, N0,
 // Case #187: (cloned #63)
  ST_WDG, COLOR0, P6, EG, EF, P2, EC, EB,
  ST_HEX, COLOR1, EC, EB, EF, EG, P3, P1, P5, P7,
  ST_WDG, COLOR1, P0, P3, P1, P4, P7, P5,
 // Case #188: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P7, P5, P4, P3, EB, ED,
  ST_WDG, COLOR0, ED, P0, EI, EB, P1, EJ,
  ST_TET, COLOR0, EG, P6, EF, EL,
  ST_WDG, COLOR1, P7, P2, P5, EG, EL, EF,
  ST_PYR, COLOR1, ED, EB, P2, P3, N0,
  ST_TET, COLOR1, P3, P2, P7, N0,
  ST_TET, COLOR1, P3, P7, P4, N0,
  ST_PYR, COLOR1, EI, ED, P3, P4, N0,
  ST_PYR, COLOR1, EJ, EI, P4, P5, N0,
  ST_TET, COLOR1, P4, P7, P5, N0,
  ST_PYR, COLOR1, EB, ED, EI, EJ, N0,
  ST_TET, COLOR1, P5, P7, P2, N0,
  ST_PYR, COLOR1, EB, EJ, P5, P2, N0,
 // Case #189: (cloned #95)
  ST_TET, COLOR0, EJ, EA, EB, P1,
  ST_TET, COLOR0, EF, EL, EG, P6,
  ST_WDG, COLOR1, P2, P5, P7, EL, EF, EG,
  ST_WDG, COLOR1, EB, EJ, EA, P2, P5, P0,
  ST_PYR, COLOR1, P3, P7, P4, P0, P2,
  ST_TET, COLOR1, P5, P0, P4, P2,
  ST_TET, COLOR1, P5, P4, P7, P2,
 // Case #190: (cloned #125)
  ST_TET, COLOR0, EA, EI, ED, P0,
  ST_TET, COLOR0, EL, EG, EF, P6,
  ST_WDG, COLOR1, ED, EA, EI, P3, P1, P4,
  ST_WDG, COLOR1, EL, EG, EF, P2, P7, P5,
  ST_TET, COLOR1, P5, P4, P7, P2,
  ST_TET, COLOR1, P2, P3, P1, P4,
  ST_TET, COLOR1, P3, P2, P7, P4,
  ST_TET, COLOR1, P2, P1, P5, P4,
 // Case #191: (cloned #127)
  ST_PNT, 0, COLOR1, 7, P1, P0, P3, P2, P5, P4, P7,
  ST_TET, COLOR0, EF, EL, EG, P6,
  ST_WDG, COLOR1, P5, P7, P2, EF, EG, EL,
  ST_TET, COLOR1, P5, P7, P2, N0,
  ST_PYR, COLOR1, P4, P0, P3, P7, N0,
  ST_TET, COLOR1, P7, P3, P2, N0,
  ST_PYR, COLOR1, P1, P2, P3, P0, N0,
  ST_TET, COLOR1, P1, P5, P2, N0,
  ST_PYR, COLOR1, P1, P0, P4, P5, N0,
  ST_TET, COLOR1, P5, P4, P7, N0,
 // Case #192: (cloned #3)
  ST_HEX, COLOR0, EF, P5, P4, EH, EL, P2, P3, EK,
  ST_WDG, COLOR0, P5, P1, P2, P4, P0, P3,
  ST_WDG, COLOR1, P6, EF, EL, P7, EH, EK,
 // Case #193: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EL, EF, EA, EA,
  ST_PYR, COLOR0, P1, EA, EI, P4, N0,
  ST_TET, COLOR0, EI, EH, P4, N0,
  ST_PYR, COLOR0, P5, P4, EH, EF, N0,
  ST_TET, COLOR0, P1, P4, P5, N0,
  ST_TET, COLOR0, P3, EK, ED, N0,
  ST_TET, COLOR0, P2, P3, P1, N0,
  ST_PYR, COLOR0, P3, ED, EA, P1, N0,
  ST_TET, COLOR0, P2, P1, P5, N0,
  ST_PYR, COLOR0, P2, P5, EF, EL, N0,
  ST_PYR, COLOR0, P2, EL, EK, P3, N0,
  ST_PYR, COLOR1, P0, P7, EH, EI, N0,
  ST_PYR, COLOR1, EK, P7, P0, ED, N0,
  ST_TET, COLOR1, P0, EI, EA, N0,
  ST_TET, COLOR1, ED, P0, EA, N0,
  ST_PYR, COLOR1, EH, P7, P6, EF, N0,
  ST_PYR, COLOR1, P7, EK, EL, P6, N0,
  ST_TET, COLOR1, EL, EF, P6, N0,
 // Case #194: (cloned #21)
  ST_PNT, 0, NOCOLOR, 4, EK, EH, EA, EA,
  ST_PYR, COLOR0, P0, P5, EJ, EA, N0,
  ST_TET, COLOR0, EJ, P5, EF, N0,
  ST_PYR, COLOR0, P4, EH, EF, P5, N0,
  ST_TET, COLOR0, P0, P4, P5, N0,
  ST_TET, COLOR0, P2, EB, EL, N0,
  ST_TET, COLOR0, P3, P0, P2, N0,
  ST_PYR, COLOR0, P2, P0, EA, EB, N0,
  ST_TET, COLOR0, P3, P4, P0, N0,
  ST_PYR, COLOR0, P3, EK, EH, P4, N0,
  ST_PYR, COLOR0, P3, P2, EL, EK, N0,
  ST_PYR, COLOR1, P1, EJ, EF, P6, N0,
  ST_PYR, COLOR1, EL, EB, P1, P6, N0,
  ST_TET, COLOR1, P1, EA, EJ, N0,
  ST_TET, COLOR1, EB, EA, P1, N0,
  ST_PYR, COLOR1, EF, EH, P7, P6, N0,
  ST_PYR, COLOR1, P6, P7, EK, EL, N0,
  ST_TET, COLOR1, EK, P7, EH, N0,
 // Case #195: (cloned #60)
  ST_WDG, COLOR0, P3, ED, EK, P2, EB, EL,
  ST_WDG, COLOR0, P4, EH, EI, P5, EF, EJ,
  ST_HEX, COLOR1, EI, EH, EF, EJ, P0, P7, P6, P1,
  ST_HEX, COLOR1, P0, P7, P6, P1, ED, EK, EL, EB,
 // Case #196: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EH, EF, EK, EC, EB,
  ST_PYR, COLOR0, P4, P5, P1, P0, N0,
  ST_TET, COLOR0, P1, N0, P3, P0,
  ST_PYR, COLOR0, P5, EF, EB, P1, N0,
  ST_PYR, COLOR0, EH, EF, P5, P4, N0,
  ST_TET, COLOR0, P3, P4, P0, N0,
  ST_PYR, COLOR0, P3, EK, EH, P4, N0,
  ST_TET, COLOR0, P3, EC, EK, N0,
  ST_PYR, COLOR0, EB, EC, P3, P1, N0,
  ST_PYR, COLOR1, EF, P6, P2, EB, N0,
  ST_PYR, COLOR1, EH, P7, P6, EF, N0,
  ST_TET, COLOR1, EK, P7, EH, N0,
  ST_TET, COLOR1, P7, P2, P6, N0,
  ST_PYR, COLOR1, EK, EC, P2, P7, N0,
  ST_TET, COLOR1, P2, EC, EB, N0,
 // Case #197: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EF, EB, EH, EI, EA,
  ST_TET, COLOR0, P3, EC, EK, ED,
  ST_PYR, COLOR0, P1, P5, EF, EB, N0,
  ST_PYR, COLOR0, P5, P4, EH, EF, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_TET, COLOR0, P5, P1, P4, N0,
  ST_PYR, COLOR0, P4, P1, EA, EI, N0,
  ST_TET, COLOR0, P1, EB, EA, N0,
  ST_WDG, COLOR1, EK, EC, ED, P7, P2, P0,
  ST_TET, COLOR1, P2, P7, P0, N0,
  ST_PYR, COLOR1, P7, EH, EI, P0, N0,
  ST_TET, COLOR1, P6, P7, P2, N0,
  ST_PYR, COLOR1, EB, EF, P6, P2, N0,
  ST_PYR, COLOR1, EF, EH, P7, P6, N0,
  ST_PYR, COLOR1, P0, EA, EB, P2, N0,
  ST_TET, COLOR1, EI, EA, P0, N0,
 // Case #198: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EC, EK, EH, EF, EJ, EA,
  ST_TET, COLOR0, P4, P5, P0, N0,
  ST_PYR, COLOR0, P4, EH, EF, P5, N0,
  ST_TET, COLOR0, P3, P4, P0, N0,
  ST_PYR, COLOR0, P3, EK, EH, P4, N0,
  ST_TET, COLOR0, EC, EK, P3, N0,
  ST_PYR, COLOR0, P3, P0, EA, EC, N0,
  ST_PYR, COLOR0, EA, P0, P5, EJ, N0,
  ST_TET, COLOR0, P5, EF, EJ, N0,
  ST_TET, COLOR1, P2, P6, P7, N0,
  ST_PYR, COLOR1, EC, P2, P7, EK, N0,
  ST_PYR, COLOR1, EC, EA, P1, P2, N0,
  ST_TET, COLOR1, P1, P6, P2, N0,
  ST_TET, COLOR1, P1, EA, EJ, N0,
  ST_PYR, COLOR1, P1, EJ, EF, P6, N0,
  ST_PYR, COLOR1, EF, EH, P7, P6, N0,
  ST_TET, COLOR1, EH, EK, P7, N0,
 // Case #199: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P2, P0, P1, P6, EH, EF,
  ST_WDG, COLOR0, EF, P5, EJ, EH, P4, EI,
  ST_TET, COLOR0, EC, P3, ED, EK,
  ST_WDG, COLOR1, P2, P7, P0, EC, EK, ED,
  ST_PYR, COLOR1, EF, EH, P7, P6, N0,
  ST_TET, COLOR1, P6, P7, P2, N0,
  ST_TET, COLOR1, P6, P2, P1, N0,
  ST_PYR, COLOR1, EJ, EF, P6, P1, N0,
  ST_PYR, COLOR1, EI, EJ, P1, P0, N0,
  ST_TET, COLOR1, P1, P2, P0, N0,
  ST_PYR, COLOR1, EH, EF, EJ, EI, N0,
  ST_TET, COLOR1, P0, P2, P7, N0,
  ST_PYR, COLOR1, EH, EI, P0, P7, N0,
 // Case #200: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, ED, EH, EC, EL, EF,
  ST_PYR, COLOR0, P0, P4, P5, P1, N0,
  ST_TET, COLOR0, P5, N0, P2, P1,
  ST_PYR, COLOR0, P4, EH, EF, P5, N0,
  ST_PYR, COLOR0, ED, EH, P4, P0, N0,
  ST_TET, COLOR0, P2, P0, P1, N0,
  ST_PYR, COLOR0, P2, EC, ED, P0, N0,
  ST_TET, COLOR0, P2, EL, EC, N0,
  ST_PYR, COLOR0, EF, EL, P2, P5, N0,
  ST_PYR, COLOR1, EH, P7, P6, EF, N0,
  ST_PYR, COLOR1, ED, P3, P7, EH, N0,
  ST_TET, COLOR1, EC, P3, ED, N0,
  ST_TET, COLOR1, P3, P6, P7, N0,
  ST_PYR, COLOR1, EC, EL, P6, P3, N0,
  ST_TET, COLOR1, P6, EL, EF, N0,
 // Case #201: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EC, EL, EF, EH, EI, EA,
  ST_TET, COLOR0, P5, P1, P4, N0,
  ST_PYR, COLOR0, P5, P4, EH, EF, N0,
  ST_TET, COLOR0, P2, P1, P5, N0,
  ST_PYR, COLOR0, P2, P5, EF, EL, N0,
  ST_TET, COLOR0, EC, P2, EL, N0,
  ST_PYR, COLOR0, P2, EC, EA, P1, N0,
  ST_PYR, COLOR0, EA, EI, P4, P1, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_TET, COLOR1, P3, P6, P7, N0,
  ST_PYR, COLOR1, EC, EL, P6, P3, N0,
  ST_PYR, COLOR1, EC, P3, P0, EA, N0,
  ST_TET, COLOR1, P0, P3, P7, N0,
  ST_TET, COLOR1, P0, EI, EA, N0,
  ST_PYR, COLOR1, P0, P7, EH, EI, N0,
  ST_PYR, COLOR1, EH, P7, P6, EF, N0,
  ST_TET, COLOR1, EF, P6, EL, N0,
 // Case #202: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EH, EF, ED, EA, EJ,
  ST_TET, COLOR0, P2, EL, EC, EB,
  ST_PYR, COLOR0, P5, P4, EH, EF, N0,
  ST_PYR, COLOR0, P4, P0, ED, EH, N0,
  ST_TET, COLOR0, P0, EA, ED, N0,
  ST_TET, COLOR0, P4, P5, P0, N0,
  ST_PYR, COLOR0, P0, P5, EJ, EA, N0,
  ST_TET, COLOR0, P5, EF, EJ, N0,
  ST_WDG, COLOR1, EC, EL, EB, P3, P6, P1,
  ST_TET, COLOR1, P6, P3, P1, N0,
  ST_PYR, COLOR1, P3, ED, EA, P1, N0,
  ST_TET, COLOR1, P7, P3, P6, N0,
  ST_PYR, COLOR1, EF, EH, P7, P6, N0,
  ST_PYR, COLOR1, EH, ED, P3, P7, N0,
  ST_PYR, COLOR1, P1, EJ, EF, P6, N0,
  ST_TET, COLOR1, EA, EJ, P1, N0,
 // Case #203: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P3, P1, P0, P7, EF, EH,
  ST_WDG, COLOR0, EF, P5, EJ, EH, P4, EI,
  ST_TET, COLOR0, EC, EB, P2, EL,
  ST_WDG, COLOR1, EC, EL, EB, P3, P6, P1,
  ST_PYR, COLOR1, EH, P7, P6, EF, N0,
  ST_TET, COLOR1, P7, P3, P6, N0,
  ST_TET, COLOR1, P7, P0, P3, N0,
  ST_PYR, COLOR1, EI, P0, P7, EH, N0,
  ST_PYR, COLOR1, EJ, P1, P0, EI, N0,
  ST_TET, COLOR1, P0, P1, P3, N0,
  ST_PYR, COLOR1, EF, EJ, EI, EH, N0,
  ST_TET, COLOR1, P1, P6, P3, N0,
  ST_PYR, COLOR1, EF, P6, P1, EJ, N0,
 // Case #204: (cloned #15)
  ST_HEX, COLOR0, ED, EB, EF, EH, P0, P1, P5, P4,
  ST_HEX, COLOR1, P3, P2, P6, P7, ED, EB, EF, EH,
 // Case #205: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EB, EF, EH, EA, EI,
  ST_PYR, COLOR0, P5, P4, EH, EF, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_PYR, COLOR0, P1, P5, EF, EB, N0,
  ST_TET, COLOR0, EA, P1, EB, N0,
  ST_PYR, COLOR0, EI, P4, P1, EA, N0,
  ST_TET, COLOR0, P4, P5, P1, N0,
  ST_PYR, COLOR1, P3, P2, P6, P7, N0,
  ST_TET, COLOR1, P7, P0, P3, N0,
  ST_TET, COLOR1, P0, P2, P3, N0,
  ST_PYR, COLOR1, P0, EA, EB, P2, N0,
  ST_PYR, COLOR1, EB, EF, P6, P2, N0,
  ST_PYR, COLOR1, EF, EH, P7, P6, N0,
  ST_PYR, COLOR1, EH, EI, P0, P7, N0,
  ST_TET, COLOR1, EA, P0, EI, N0,
 // Case #206: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EF, EH, ED, EJ, EA,
  ST_PYR, COLOR0, P4, P0, ED, EH, N0,
  ST_TET, COLOR0, P0, EA, ED, N0,
  ST_PYR, COLOR0, P5, P4, EH, EF, N0,
  ST_TET, COLOR0, EJ, P5, EF, N0,
  ST_PYR, COLOR0, EA, P0, P5, EJ, N0,
  ST_TET, COLOR0, P0, P4, P5, N0,
  ST_PYR, COLOR1, P2, P6, P7, P3, N0,
  ST_TET, COLOR1, P3, P1, P2, N0,
  ST_TET, COLOR1, P1, P6, P2, N0,
  ST_PYR, COLOR1, P1, EJ, EF, P6, N0,
  ST_PYR, COLOR1, EF, EH, P7, P6, N0,
  ST_PYR, COLOR1, EH, ED, P3, P7, N0,
  ST_PYR, COLOR1, ED, EA, P1, P3, N0,
  ST_TET, COLOR1, EJ, P1, EA, N0,
 // Case #207: (cloned #63)
  ST_WDG, COLOR0, P5, EJ, EF, P4, EI, EH,
  ST_HEX, COLOR1, EI, EH, EF, EJ, P0, P7, P6, P1,
  ST_WDG, COLOR1, P3, P0, P7, P2, P1, P6,
 // Case #208: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EI, EK, EE, EF, EL,
  ST_PYR, COLOR0, P0, P1, P2, P3, N0,
  ST_TET, COLOR0, P2, P5, N0, P1,
  ST_PYR, COLOR0, P3, P2, EL, EK, N0,
  ST_PYR, COLOR0, EI, P0, P3, EK, N0,
  ST_TET, COLOR0, P5, P1, P0, N0,
  ST_PYR, COLOR0, P5, P0, EI, EE, N0,
  ST_TET, COLOR0, P5, EE, EF, N0,
  ST_PYR, COLOR0, EL, P2, P5, EF, N0,
  ST_PYR, COLOR1, EK, EL, P6, P7, N0,
  ST_PYR, COLOR1, EI, EK, P7, P4, N0,
  ST_TET, COLOR1, EE, EI, P4, N0,
  ST_TET, COLOR1, P4, P7, P6, N0,
  ST_PYR, COLOR1, EE, P4, P6, EF, N0,
  ST_TET, COLOR1, P6, EL, EF, N0,
 // Case #209: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EE, EF, EL, EK, ED, EA,
  ST_TET, COLOR0, P2, P3, P1, N0,
  ST_PYR, COLOR0, P2, EL, EK, P3, N0,
  ST_TET, COLOR0, P5, P2, P1, N0,
  ST_PYR, COLOR0, P5, EF, EL, P2, N0,
  ST_TET, COLOR0, EE, EF, P5, N0,
  ST_PYR, COLOR0, P5, P1, EA, EE, N0,
  ST_PYR, COLOR0, EA, P1, P3, ED, N0,
  ST_TET, COLOR0, P3, EK, ED, N0,
  ST_TET, COLOR1, P4, P7, P6, N0,
  ST_PYR, COLOR1, EE, P4, P6, EF, N0,
  ST_PYR, COLOR1, EE, EA, P0, P4, N0,
  ST_TET, COLOR1, P0, P7, P4, N0,
  ST_TET, COLOR1, P0, EA, ED, N0,
  ST_PYR, COLOR1, P0, ED, EK, P7, N0,
  ST_PYR, COLOR1, EK, EL, P6, P7, N0,
  ST_TET, COLOR1, EL, EF, P6, N0,
 // Case #210: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EK, EL, EI, EA, EB,
  ST_TET, COLOR0, P5, EE, EF, EJ,
  ST_PYR, COLOR0, P2, EL, EK, P3, N0,
  ST_PYR, COLOR0, P3, EK, EI, P0, N0,
  ST_TET, COLOR0, P0, EI, EA, N0,
  ST_TET, COLOR0, P3, P0, P2, N0,
  ST_PYR, COLOR0, P0, EA, EB, P2, N0,
  ST_TET, COLOR0, P2, EB, EL, N0,
  ST_WDG, COLOR1, P4, P6, P1, EE, EF, EJ,
  ST_TET, COLOR1, P6, P1, P4, N0,
  ST_PYR, COLOR1, P4, P1, EA, EI, N0,
  ST_TET, COLOR1, P7, P6, P4, N0,
  ST_PYR, COLOR1, EL, P6, P7, EK, N0,
  ST_PYR, COLOR1, EK, P7, P4, EI, N0,
  ST_PYR, COLOR1, P1, P6, EL, EB, N0,
  ST_TET, COLOR1, EA, P1, EB, N0,
 // Case #211: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P4, P1, P0, P7, EL, EK,
  ST_WDG, COLOR0, EK, P3, ED, EL, P2, EB,
  ST_TET, COLOR0, EE, P5, EJ, EF,
  ST_WDG, COLOR1, P4, P6, P1, EE, EF, EJ,
  ST_PYR, COLOR1, EK, EL, P6, P7, N0,
  ST_TET, COLOR1, P7, P6, P4, N0,
  ST_TET, COLOR1, P7, P4, P0, N0,
  ST_PYR, COLOR1, ED, EK, P7, P0, N0,
  ST_PYR, COLOR1, EB, ED, P0, P1, N0,
  ST_TET, COLOR1, P0, P4, P1, N0,
  ST_PYR, COLOR1, EL, EK, ED, EB, N0,
  ST_TET, COLOR1, P1, P4, P6, N0,
  ST_PYR, COLOR1, EL, EB, P1, P6, N0,
 // Case #212: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EK, EC, EB, EF, EE, EI,
  ST_TET, COLOR0, P1, P0, P5, N0,
  ST_PYR, COLOR0, P1, P5, EF, EB, N0,
  ST_TET, COLOR0, P3, P0, P1, N0,
  ST_PYR, COLOR0, P3, P1, EB, EC, N0,
  ST_TET, COLOR0, EK, P3, EC, N0,
  ST_PYR, COLOR0, P3, EK, EI, P0, N0,
  ST_PYR, COLOR0, EI, EE, P5, P0, N0,
  ST_TET, COLOR0, P5, EE, EF, N0,
  ST_TET, COLOR1, P7, P2, P6, N0,
  ST_PYR, COLOR1, EK, EC, P2, P7, N0,
  ST_PYR, COLOR1, EK, P7, P4, EI, N0,
  ST_TET, COLOR1, P4, P7, P6, N0,
  ST_TET, COLOR1, P4, EE, EI, N0,
  ST_PYR, COLOR1, P4, P6, EF, EE, N0,
  ST_PYR, COLOR1, EF, P6, P2, EB, N0,
  ST_TET, COLOR1, EB, P2, EC, N0,
 // Case #213: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P7, P0, P4, P6, EB, EF,
  ST_WDG, COLOR0, EB, P1, EA, EF, P5, EE,
  ST_TET, COLOR0, EK, ED, P3, EC,
  ST_WDG, COLOR1, EK, EC, ED, P7, P2, P0,
  ST_PYR, COLOR1, EF, P6, P2, EB, N0,
  ST_TET, COLOR1, P6, P7, P2, N0,
  ST_TET, COLOR1, P6, P4, P7, N0,
  ST_PYR, COLOR1, EE, P4, P6, EF, N0,
  ST_PYR, COLOR1, EA, P0, P4, EE, N0,
  ST_TET, COLOR1, P4, P0, P7, N0,
  ST_PYR, COLOR1, EB, EA, EE, EF, N0,
  ST_TET, COLOR1, P0, P2, P7, N0,
  ST_PYR, COLOR1, EB, P2, P0, EA, N0,
 // Case #214: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P6, P1, P2, P7, EI, EK,
  ST_WDG, COLOR0, EI, P0, EA, EK, P3, EC,
  ST_TET, COLOR0, EF, EJ, P5, EE,
  ST_WDG, COLOR1, EF, EE, EJ, P6, P4, P1,
  ST_PYR, COLOR1, EK, P7, P4, EI, N0,
  ST_TET, COLOR1, P7, P6, P4, N0,
  ST_TET, COLOR1, P7, P2, P6, N0,
  ST_PYR, COLOR1, EC, P2, P7, EK, N0,
  ST_PYR, COLOR1, EA, P1, P2, EC, N0,
  ST_TET, COLOR1, P2, P1, P6, N0,
  ST_PYR, COLOR1, EI, EA, EC, EK, N0,
  ST_TET, COLOR1, P1, P4, P6, N0,
  ST_PYR, COLOR1, EI, P4, P1, EA, N0,
 // Case #215: (cloned #125)
  ST_TET, COLOR0, EF, EE, EJ, P5,
  ST_TET, COLOR0, EC, ED, EK, P3,
  ST_WDG, COLOR1, EJ, EF, EE, P1, P6, P4,
  ST_WDG, COLOR1, EC, ED, EK, P2, P0, P7,
  ST_TET, COLOR1, P7, P4, P0, P2,
  ST_TET, COLOR1, P2, P1, P6, P4,
  ST_TET, COLOR1, P1, P2, P0, P4,
  ST_TET, COLOR1, P2, P6, P7, P4,
 // Case #216: (cloned #27)
  ST_TET, COLOR0, P2, P0, P1, P5,
  ST_PYR, COLOR0, EE, P5, P0, EI, ED,
  ST_PYR, COLOR0, EF, EL, P2, P5, EC,
  ST_PYR, COLOR0, P0, P2, EC, ED, P5,
  ST_PYR, COLOR0, ED, EC, EF, EE, P5,
  ST_TET, COLOR1, P7, P6, P4, P3,
  ST_PYR, COLOR1, ED, P3, P4, EI, EE,
  ST_PYR, COLOR1, EC, EL, P6, P3, EF,
  ST_PYR, COLOR1, P4, P6, EF, EE, P3,
  ST_PYR, COLOR1, ED, EE, EF, EC, P3,
 // Case #217: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EC, EA, EE, EL, EF,
  ST_PYR, COLOR0, P1, EA, EE, P5, N0,
  ST_TET, COLOR0, P5, EE, EF, N0,
  ST_PYR, COLOR0, P2, EC, EA, P1, N0,
  ST_TET, COLOR0, EL, EC, P2, N0,
  ST_PYR, COLOR0, EF, EL, P2, P5, N0,
  ST_TET, COLOR0, P5, P2, P1, N0,
  ST_PYR, COLOR1, P7, P4, P0, P3, N0,
  ST_TET, COLOR1, P4, P7, P6, N0,
  ST_TET, COLOR1, P6, P7, P3, N0,
  ST_PYR, COLOR1, P6, P3, EC, EL, N0,
  ST_PYR, COLOR1, EC, P3, P0, EA, N0,
  ST_PYR, COLOR1, EA, P0, P4, EE, N0,
  ST_PYR, COLOR1, EE, P4, P6, EF, N0,
  ST_TET, COLOR1, EL, EF, P6, N0,
 // Case #218: (cloned #91)
  ST_TET, COLOR0, EC, EL, EB, P2,
  ST_TET, COLOR0, ED, EI, P0, EA,
  ST_TET, COLOR0, EF, P5, EE, EJ,
  ST_WDG, COLOR1, P1, P4, P6, EJ, EE, EF,
  ST_WDG, COLOR1, ED, EA, EI, P3, P1, P4,
  ST_WDG, COLOR1, P3, P1, P6, EC, EB, EL,
  ST_TET, COLOR1, P1, P4, P6, P3,
  ST_TET, COLOR1, P3, P6, P7, P4,
 // Case #219: (cloned #95)
  ST_TET, COLOR0, EL, EB, EC, P2,
  ST_TET, COLOR0, EF, EE, EJ, P5,
  ST_WDG, COLOR1, EJ, EF, EE, P1, P6, P4,
  ST_WDG, COLOR1, P1, P6, P3, EB, EL, EC,
  ST_PYR, COLOR1, P0, P3, P7, P4, P1,
  ST_TET, COLOR1, P6, P7, P3, P1,
  ST_TET, COLOR1, P6, P4, P7, P1,
 // Case #220: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EF, EB, ED, EE, EI,
  ST_PYR, COLOR0, P1, EB, ED, P0, N0,
  ST_TET, COLOR0, P0, ED, EI, N0,
  ST_PYR, COLOR0, P5, EF, EB, P1, N0,
  ST_TET, COLOR0, EE, EF, P5, N0,
  ST_PYR, COLOR0, EI, EE, P5, P0, N0,
  ST_TET, COLOR0, P0, P5, P1, N0,
  ST_PYR, COLOR1, P7, P3, P2, P6, N0,
  ST_TET, COLOR1, P3, P7, P4, N0,
  ST_TET, COLOR1, P4, P7, P6, N0,
  ST_PYR, COLOR1, P4, P6, EF, EE, N0,
  ST_PYR, COLOR1, EF, P6, P2, EB, N0,
  ST_PYR, COLOR1, EB, P2, P3, ED, N0,
  ST_PYR, COLOR1, ED, P3, P4, EI, N0,
  ST_TET, COLOR1, EE, EI, P4, N0,
 // Case #221: (cloned #63)
  ST_WDG, COLOR0, P1, EA, EB, P5, EE, EF,
  ST_HEX, COLOR1, P0, P2, P6, P4, EA, EB, EF, EE,
  ST_WDG, COLOR1, P7, P4, P6, P3, P0, P2,
 // Case #222: (cloned #95)
  ST_TET, COLOR0, EA, EI, ED, P0,
  ST_TET, COLOR0, EJ, EF, EE, P5,
  ST_WDG, COLOR1, EE, EJ, EF, P4, P1, P6,
  ST_WDG, COLOR1, P4, P1, P3, EI, EA, ED,
  ST_PYR, COLOR1, P7, P3, P2, P6, P4,
  ST_TET, COLOR1, P1, P2, P3, P4,
  ST_TET, COLOR1, P1, P6, P2, P4,
 // Case #223: (cloned #127)
  ST_PNT, 0, COLOR1, 7, P0, P3, P7, P4, P1, P2, P6,
  ST_TET, COLOR0, EJ, EF, EE, P5,
  ST_WDG, COLOR1, EJ, EF, EE, P1, P6, P4,
  ST_TET, COLOR1, P1, P4, P6, N0,
  ST_PYR, COLOR1, P2, P6, P7, P3, N0,
  ST_TET, COLOR1, P6, P4, P7, N0,
  ST_PYR, COLOR1, P0, P3, P7, P4, N0,
  ST_TET, COLOR1, P0, P4, P1, N0,
  ST_PYR, COLOR1, P0, P1, P2, P3, N0,
  ST_TET, COLOR1, P1, P6, P2, N0,
 // Case #224: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EK, EL, EH, EE, EJ,
  ST_PYR, COLOR0, P3, P0, P1, P2, N0,
  ST_TET, COLOR0, P1, P4, N0, P0,
  ST_PYR, COLOR0, P2, P1, EJ, EL, N0,
  ST_PYR, COLOR0, EK, P3, P2, EL, N0,
  ST_TET, COLOR0, P4, P0, P3, N0,
  ST_PYR, COLOR0, P4, P3, EK, EH, N0,
  ST_TET, COLOR0, P4, EH, EE, N0,
  ST_PYR, COLOR0, EJ, P1, P4, EE, N0,
  ST_PYR, COLOR1, EL, EJ, P5, P6, N0,
  ST_PYR, COLOR1, EK, EL, P6, P7, N0,
  ST_TET, COLOR1, EH, EK, P7, N0,
  ST_TET, COLOR1, P7, P6, P5, N0,
  ST_PYR, COLOR1, EH, P7, P5, EE, N0,
  ST_TET, COLOR1, P5, EJ, EE, N0,
 // Case #225: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EL, EJ, EK, ED, EA,
  ST_TET, COLOR0, P4, EH, EE, EI,
  ST_PYR, COLOR0, P1, EJ, EL, P2, N0,
  ST_PYR, COLOR0, P2, EL, EK, P3, N0,
  ST_TET, COLOR0, P3, EK, ED, N0,
  ST_TET, COLOR0, P2, P3, P1, N0,
  ST_PYR, COLOR0, P3, ED, EA, P1, N0,
  ST_TET, COLOR0, P1, EA, EJ, N0,
  ST_WDG, COLOR1, P7, P5, P0, EH, EE, EI,
  ST_TET, COLOR1, P5, P0, P7, N0,
  ST_PYR, COLOR1, P7, P0, ED, EK, N0,
  ST_TET, COLOR1, P6, P5, P7, N0,
  ST_PYR, COLOR1, EJ, P5, P6, EL, N0,
  ST_PYR, COLOR1, EL, P6, P7, EK, N0,
  ST_PYR, COLOR1, P0, P5, EJ, EA, N0,
  ST_TET, COLOR1, ED, P0, EA, N0,
 // Case #226: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EE, EH, EK, EL, EB, EA,
  ST_TET, COLOR0, P3, P0, P2, N0,
  ST_PYR, COLOR0, P3, P2, EL, EK, N0,
  ST_TET, COLOR0, P4, P0, P3, N0,
  ST_PYR, COLOR0, P4, P3, EK, EH, N0,
  ST_TET, COLOR0, EE, P4, EH, N0,
  ST_PYR, COLOR0, P4, EE, EA, P0, N0,
  ST_PYR, COLOR0, EA, EB, P2, P0, N0,
  ST_TET, COLOR0, P2, EB, EL, N0,
  ST_TET, COLOR1, P5, P7, P6, N0,
  ST_PYR, COLOR1, EE, EH, P7, P5, N0,
  ST_PYR, COLOR1, EE, P5, P1, EA, N0,
  ST_TET, COLOR1, P1, P5, P6, N0,
  ST_TET, COLOR1, P1, EB, EA, N0,
  ST_PYR, COLOR1, P1, P6, EL, EB, N0,
  ST_PYR, COLOR1, EL, P6, P7, EK, N0,
  ST_TET, COLOR1, EK, P7, EH, N0,
 // Case #227: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P5, P0, P1, P6, EK, EL,
  ST_WDG, COLOR0, EK, P3, ED, EL, P2, EB,
  ST_TET, COLOR0, EE, EI, P4, EH,
  ST_WDG, COLOR1, EE, EH, EI, P5, P7, P0,
  ST_PYR, COLOR1, EL, P6, P7, EK, N0,
  ST_TET, COLOR1, P6, P5, P7, N0,
  ST_TET, COLOR1, P6, P1, P5, N0,
  ST_PYR, COLOR1, EB, P1, P6, EL, N0,
  ST_PYR, COLOR1, ED, P0, P1, EB, N0,
  ST_TET, COLOR1, P1, P0, P5, N0,
  ST_PYR, COLOR1, EK, ED, EB, EL, N0,
  ST_TET, COLOR1, P0, P7, P5, N0,
  ST_PYR, COLOR1, EK, P7, P0, ED, N0,
 // Case #228: (cloned #27)
  ST_TET, COLOR0, P3, P0, P1, P4,
  ST_PYR, COLOR0, EE, EJ, P1, P4, EB,
  ST_PYR, COLOR0, EH, P4, P3, EK, EC,
  ST_PYR, COLOR0, P1, EB, EC, P3, P4,
  ST_PYR, COLOR0, EB, EE, EH, EC, P4,
  ST_TET, COLOR1, P6, P5, P7, P2,
  ST_PYR, COLOR1, EB, EJ, P5, P2, EE,
  ST_PYR, COLOR1, EC, P2, P7, EK, EH,
  ST_PYR, COLOR1, P5, EE, EH, P7, P2,
  ST_PYR, COLOR1, EB, EC, EH, EE, P2,
 // Case #229: (cloned #91)
  ST_TET, COLOR0, EC, ED, EK, P3,
  ST_TET, COLOR0, EB, P1, EJ, EA,
  ST_TET, COLOR0, EH, EE, P4, EI,
  ST_WDG, COLOR1, EI, EE, EH, P0, P5, P7,
  ST_WDG, COLOR1, P2, P0, P5, EB, EA, EJ,
  ST_WDG, COLOR1, EC, ED, EK, P2, P0, P7,
  ST_TET, COLOR1, P0, P7, P5, P2,
  ST_TET, COLOR1, P2, P6, P7, P5,
 // Case #230: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EC, EA, EE, EK, EH,
  ST_PYR, COLOR0, P0, P4, EE, EA, N0,
  ST_TET, COLOR0, P4, EH, EE, N0,
  ST_PYR, COLOR0, P3, P0, EA, EC, N0,
  ST_TET, COLOR0, EK, P3, EC, N0,
  ST_PYR, COLOR0, EH, P4, P3, EK, N0,
  ST_TET, COLOR0, P4, P0, P3, N0,
  ST_PYR, COLOR1, P6, P2, P1, P5, N0,
  ST_TET, COLOR1, P5, P7, P6, N0,
  ST_TET, COLOR1, P7, P2, P6, N0,
  ST_PYR, COLOR1, P7, EK, EC, P2, N0,
  ST_PYR, COLOR1, EC, EA, P1, P2, N0,
  ST_PYR, COLOR1, EA, EE, P5, P1, N0,
  ST_PYR, COLOR1, EE, EH, P7, P5, N0,
  ST_TET, COLOR1, EK, P7, EH, N0,
 // Case #231: (cloned #95)
  ST_TET, COLOR0, EK, EC, ED, P3,
  ST_TET, COLOR0, EH, EI, EE, P4,
  ST_WDG, COLOR1, P0, P7, P5, EI, EH, EE,
  ST_WDG, COLOR1, ED, EK, EC, P0, P7, P2,
  ST_PYR, COLOR1, P1, P5, P6, P2, P0,
  ST_TET, COLOR1, P7, P2, P6, P0,
  ST_TET, COLOR1, P7, P6, P5, P0,
 // Case #232: (cloned #23)
  ST_PNT, 0, NOCOLOR, 6, EH, EE, EJ, EL, EC, ED,
  ST_TET, COLOR0, P1, P2, P0, N0,
  ST_PYR, COLOR0, P1, EJ, EL, P2, N0,
  ST_TET, COLOR0, P4, P1, P0, N0,
  ST_PYR, COLOR0, P4, EE, EJ, P1, N0,
  ST_TET, COLOR0, EH, EE, P4, N0,
  ST_PYR, COLOR0, P4, P0, ED, EH, N0,
  ST_PYR, COLOR0, ED, P0, P2, EC, N0,
  ST_TET, COLOR0, P2, EL, EC, N0,
  ST_TET, COLOR1, P7, P6, P5, N0,
  ST_PYR, COLOR1, EH, P7, P5, EE, N0,
  ST_PYR, COLOR1, EH, ED, P3, P7, N0,
  ST_TET, COLOR1, P3, P6, P7, N0,
  ST_TET, COLOR1, P3, ED, EC, N0,
  ST_PYR, COLOR1, P3, EC, EL, P6, N0,
  ST_PYR, COLOR1, EL, EJ, P5, P6, N0,
  ST_TET, COLOR1, EJ, EE, P5, N0,
 // Case #233: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P7, P0, P3, P6, EJ, EL,
  ST_WDG, COLOR0, EL, P2, EC, EJ, P1, EA,
  ST_TET, COLOR0, EH, P4, EI, EE,
  ST_WDG, COLOR1, P7, P5, P0, EH, EE, EI,
  ST_PYR, COLOR1, EL, EJ, P5, P6, N0,
  ST_TET, COLOR1, P6, P5, P7, N0,
  ST_TET, COLOR1, P6, P7, P3, N0,
  ST_PYR, COLOR1, EC, EL, P6, P3, N0,
  ST_PYR, COLOR1, EA, EC, P3, P0, N0,
  ST_TET, COLOR1, P3, P7, P0, N0,
  ST_PYR, COLOR1, EJ, EL, EC, EA, N0,
  ST_TET, COLOR1, P0, P7, P5, N0,
  ST_PYR, COLOR1, EJ, EA, P0, P5, N0,
 // Case #234: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P6, P1, P5, P7, ED, EH,
  ST_WDG, COLOR0, EH, P4, EE, ED, P0, EA,
  ST_TET, COLOR0, EL, P2, EB, EC,
  ST_WDG, COLOR1, P6, P3, P1, EL, EC, EB,
  ST_PYR, COLOR1, EH, ED, P3, P7, N0,
  ST_TET, COLOR1, P7, P3, P6, N0,
  ST_TET, COLOR1, P7, P6, P5, N0,
  ST_PYR, COLOR1, EE, EH, P7, P5, N0,
  ST_PYR, COLOR1, EA, EE, P5, P1, N0,
  ST_TET, COLOR1, P5, P6, P1, N0,
  ST_PYR, COLOR1, ED, EH, EE, EA, N0,
  ST_TET, COLOR1, P1, P6, P3, N0,
  ST_PYR, COLOR1, ED, EA, P1, P3, N0,
 // Case #235: (cloned #125)
  ST_TET, COLOR0, EL, EB, EC, P2,
  ST_TET, COLOR0, EE, EH, EI, P4,
  ST_WDG, COLOR1, P1, P6, P3, EB, EL, EC,
  ST_WDG, COLOR1, P5, P0, P7, EE, EI, EH,
  ST_TET, COLOR1, P7, P0, P3, P5,
  ST_TET, COLOR1, P5, P6, P1, P3,
  ST_TET, COLOR1, P1, P0, P5, P3,
  ST_TET, COLOR1, P5, P7, P6, P3,
 // Case #236: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EH, ED, EB, EE, EJ,
  ST_PYR, COLOR0, P0, P1, EB, ED, N0,
  ST_TET, COLOR0, P1, EJ, EB, N0,
  ST_PYR, COLOR0, P4, P0, ED, EH, N0,
  ST_TET, COLOR0, EE, P4, EH, N0,
  ST_PYR, COLOR0, EJ, P1, P4, EE, N0,
  ST_TET, COLOR0, P1, P0, P4, N0,
  ST_PYR, COLOR1, P6, P7, P3, P2, N0,
  ST_TET, COLOR1, P2, P5, P6, N0,
  ST_TET, COLOR1, P5, P7, P6, N0,
  ST_PYR, COLOR1, P5, EE, EH, P7, N0,
  ST_PYR, COLOR1, EH, ED, P3, P7, N0,
  ST_PYR, COLOR1, ED, EB, P2, P3, N0,
  ST_PYR, COLOR1, EB, EJ, P5, P2, N0,
  ST_TET, COLOR1, EE, P5, EJ, N0,
 // Case #237: (cloned #95)
  ST_TET, COLOR0, EE, EH, EI, P4,
  ST_TET, COLOR0, EJ, EA, EB, P1,
  ST_WDG, COLOR1, P0, P5, P2, EA, EJ, EB,
  ST_WDG, COLOR1, EI, EE, EH, P0, P5, P7,
  ST_PYR, COLOR1, P3, P2, P6, P7, P0,
  ST_TET, COLOR1, P5, P7, P6, P0,
  ST_TET, COLOR1, P5, P6, P2, P0,
 // Case #238: (cloned #63)
  ST_WDG, COLOR0, P4, EE, EH, P0, EA, ED,
  ST_HEX, COLOR1, EA, ED, EH, EE, P1, P3, P7, P5,
  ST_WDG, COLOR1, P2, P1, P3, P6, P5, P7,
 // Case #239: (cloned #127)
  ST_PNT, 0, COLOR1, 7, P3, P2, P1, P0, P7, P6, P5,
  ST_TET, COLOR0, EH, EI, EE, P4,
  ST_WDG, COLOR1, P7, P5, P0, EH, EE, EI,
  ST_TET, COLOR1, P7, P5, P0, N0,
  ST_PYR, COLOR1, P6, P2, P1, P5, N0,
  ST_TET, COLOR1, P5, P1, P0, N0,
  ST_PYR, COLOR1, P3, P0, P1, P2, N0,
  ST_TET, COLOR1, P3, P7, P0, N0,
  ST_PYR, COLOR1, P3, P2, P6, P7, N0,
  ST_TET, COLOR1, P7, P6, P5, N0,
 // Case #240: (cloned #15)
  ST_HEX, COLOR0, P0, P1, P2, P3, EI, EJ, EL, EK,
  ST_HEX, COLOR1, EI, EJ, EL, EK, P4, P5, P6, P7,
 // Case #241: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EJ, EL, EK, EA, ED,
  ST_PYR, COLOR0, P2, EL, EK, P3, N0,
  ST_TET, COLOR0, P3, EK, ED, N0,
  ST_PYR, COLOR0, P1, EJ, EL, P2, N0,
  ST_TET, COLOR0, EA, EJ, P1, N0,
  ST_PYR, COLOR0, ED, EA, P1, P3, N0,
  ST_TET, COLOR0, P3, P1, P2, N0,
  ST_PYR, COLOR1, P4, P7, P6, P5, N0,
  ST_TET, COLOR1, P7, P4, P0, N0,
  ST_TET, COLOR1, P0, P4, P5, N0,
  ST_PYR, COLOR1, P0, P5, EJ, EA, N0,
  ST_PYR, COLOR1, EJ, P5, P6, EL, N0,
  ST_PYR, COLOR1, EL, P6, P7, EK, N0,
  ST_PYR, COLOR1, EK, P7, P0, ED, N0,
  ST_TET, COLOR1, EA, ED, P0, N0,
 // Case #242: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EL, EK, EI, EB, EA,
  ST_PYR, COLOR0, P3, EK, EI, P0, N0,
  ST_TET, COLOR0, P0, EI, EA, N0,
  ST_PYR, COLOR0, P2, EL, EK, P3, N0,
  ST_TET, COLOR0, EB, EL, P2, N0,
  ST_PYR, COLOR0, EA, EB, P2, P0, N0,
  ST_TET, COLOR0, P0, P2, P3, N0,
  ST_PYR, COLOR1, P5, P4, P7, P6, N0,
  ST_TET, COLOR1, P4, P5, P1, N0,
  ST_TET, COLOR1, P1, P5, P6, N0,
  ST_PYR, COLOR1, P1, P6, EL, EB, N0,
  ST_PYR, COLOR1, EL, P6, P7, EK, N0,
  ST_PYR, COLOR1, EK, P7, P4, EI, N0,
  ST_PYR, COLOR1, EI, P4, P1, EA, N0,
  ST_TET, COLOR1, EB, EA, P1, N0,
 // Case #243: (cloned #63)
  ST_WDG, COLOR0, P3, ED, EK, P2, EB, EL,
  ST_HEX, COLOR1, P0, P7, P6, P1, ED, EK, EL, EB,
  ST_WDG, COLOR1, P5, P1, P6, P4, P0, P7,
 // Case #244: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EK, EI, EJ, EC, EB,
  ST_PYR, COLOR0, P0, EI, EJ, P1, N0,
  ST_TET, COLOR0, P1, EJ, EB, N0,
  ST_PYR, COLOR0, P3, EK, EI, P0, N0,
  ST_TET, COLOR0, EC, EK, P3, N0,
  ST_PYR, COLOR0, EB, EC, P3, P1, N0,
  ST_TET, COLOR0, P1, P3, P0, N0,
  ST_PYR, COLOR1, P6, P5, P4, P7, N0,
  ST_TET, COLOR1, P5, P6, P2, N0,
  ST_TET, COLOR1, P2, P6, P7, N0,
  ST_PYR, COLOR1, P2, P7, EK, EC, N0,
  ST_PYR, COLOR1, EK, P7, P4, EI, N0,
  ST_PYR, COLOR1, EI, P4, P5, EJ, N0,
  ST_PYR, COLOR1, EJ, P5, P2, EB, N0,
  ST_TET, COLOR1, EC, EB, P2, N0,
 // Case #245: (cloned #95)
  ST_TET, COLOR0, EC, ED, EK, P3,
  ST_TET, COLOR0, EB, EJ, EA, P1,
  ST_WDG, COLOR1, EA, EB, EJ, P0, P2, P5,
  ST_WDG, COLOR1, P0, P2, P7, ED, EC, EK,
  ST_PYR, COLOR1, P4, P7, P6, P5, P0,
  ST_TET, COLOR1, P2, P6, P7, P0,
  ST_TET, COLOR1, P2, P5, P6, P0,
 // Case #246: (cloned #63)
  ST_WDG, COLOR0, P0, EA, EI, P3, EC, EK,
  ST_HEX, COLOR1, P1, P4, P7, P2, EA, EI, EK, EC,
  ST_WDG, COLOR1, P6, P2, P7, P5, P1, P4,
 // Case #247: (cloned #127)
  ST_PNT, 0, COLOR1, 7, P4, P5, P1, P0, P7, P6, P2,
  ST_TET, COLOR0, EK, EC, ED, P3,
  ST_WDG, COLOR1, EK, EC, ED, P7, P2, P0,
  ST_TET, COLOR1, P7, P0, P2, N0,
  ST_PYR, COLOR1, P6, P2, P1, P5, N0,
  ST_TET, COLOR1, P2, P0, P1, N0,
  ST_PYR, COLOR1, P4, P5, P1, P0, N0,
  ST_TET, COLOR1, P4, P0, P7, N0,
  ST_PYR, COLOR1, P4, P7, P6, P5, N0,
  ST_TET, COLOR1, P7, P2, P6, N0,
 // Case #248: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EL, EJ, EI, EC, ED,
  ST_PYR, COLOR0, P1, P0, EI, EJ, N0,
  ST_TET, COLOR0, P0, ED, EI, N0,
  ST_PYR, COLOR0, P2, P1, EJ, EL, N0,
  ST_TET, COLOR0, EC, P2, EL, N0,
  ST_PYR, COLOR0, ED, P0, P2, EC, N0,
  ST_TET, COLOR0, P0, P1, P2, N0,
  ST_PYR, COLOR1, P7, P6, P5, P4, N0,
  ST_TET, COLOR1, P4, P3, P7, N0,
  ST_TET, COLOR1, P3, P6, P7, N0,
  ST_PYR, COLOR1, P3, EC, EL, P6, N0,
  ST_PYR, COLOR1, EL, EJ, P5, P6, N0,
  ST_PYR, COLOR1, EJ, EI, P4, P5, N0,
  ST_PYR, COLOR1, EI, ED, P3, P4, N0,
  ST_TET, COLOR1, EC, P3, ED, N0,
 // Case #249: (cloned #63)
  ST_WDG, COLOR0, P2, EC, EL, P1, EA, EJ,
  ST_HEX, COLOR1, EA, EJ, EL, EC, P0, P5, P6, P3,
  ST_WDG, COLOR1, P4, P0, P5, P7, P3, P6,
 // Case #250: (cloned #95)
  ST_TET, COLOR0, EA, EI, ED, P0,
  ST_TET, COLOR0, EB, EC, EL, P2,
  ST_WDG, COLOR1, P3, P1, P6, EC, EB, EL,
  ST_WDG, COLOR1, ED, EA, EI, P3, P1, P4,
  ST_PYR, COLOR1, P7, P6, P5, P4, P3,
  ST_TET, COLOR1, P1, P4, P5, P3,
  ST_TET, COLOR1, P1, P5, P6, P3,
 // Case #251: (cloned #127)
  ST_PNT, 0, COLOR1, 7, P0, P4, P7, P3, P1, P5, P6,
  ST_TET, COLOR0, EB, EC, EL, P2,
  ST_WDG, COLOR1, P1, P6, P3, EB, EL, EC,
  ST_TET, COLOR1, P1, P6, P3, N0,
  ST_PYR, COLOR1, P5, P4, P7, P6, N0,
  ST_TET, COLOR1, P6, P7, P3, N0,
  ST_PYR, COLOR1, P0, P3, P7, P4, N0,
  ST_TET, COLOR1, P0, P1, P3, N0,
  ST_PYR, COLOR1, P0, P4, P5, P1, N0,
  ST_TET, COLOR1, P1, P5, P6, N0,
 // Case #252: (cloned #63)
  ST_WDG, COLOR0, P0, EI, ED, P1, EJ, EB,
  ST_HEX, COLOR1, P4, P3, P2, P5, EI, ED, EB, EJ,
  ST_WDG, COLOR1, P6, P5, P2, P7, P4, P3,
 // Case #253: (cloned #127)
  ST_PNT, 0, COLOR1, 7, P3, P7, P4, P0, P2, P6, P5,
  ST_TET, COLOR0, EB, EJ, EA, P1,
  ST_WDG, COLOR1, EB, EJ, EA, P2, P5, P0,
  ST_TET, COLOR1, P2, P0, P5, N0,
  ST_PYR, COLOR1, P6, P5, P4, P7, N0,
  ST_TET, COLOR1, P5, P0, P4, N0,
  ST_PYR, COLOR1, P3, P7, P4, P0, N0,
  ST_TET, COLOR1, P3, P0, P2, N0,
  ST_PYR, COLOR1, P3, P2, P6, P7, N0,
  ST_TET, COLOR1, P2, P5, P6, N0,
 // Case #254: (cloned #127)
  ST_PNT, 0, COLOR1, 7, P7, P6, P5, P4, P3, P2, P1,
  ST_TET, COLOR0, ED, EA, EI, P0,
  ST_WDG, COLOR1, ED, EA, EI, P3, P1, P4,
  ST_TET, COLOR1, P3, P4, P1, N0,
  ST_PYR, COLOR1, P2, P1, P5, P6, N0,
  ST_TET, COLOR1, P1, P4, P5, N0,
  ST_PYR, COLOR1, P7, P6, P5, P4, N0,
  ST_TET, COLOR1, P7, P4, P3, N0,
  ST_PYR, COLOR1, P7, P3, P2, P6, N0,
  ST_TET, COLOR1, P3, P1, P2, N0,
 // Case #255: Unique case #22
  ST_HEX, COLOR1, P0, P1, P2, P3, P4, P5, P6, P7,
 // Dummy
  0
};

// ---- ClipCasesHex.C ( end )
// ----------------------------------------------------------------------------


// ----------------------------------------------------------------------------
// ---- ClipCasesVox.C (begin)
const int NumClipCasesVox = 256;

const int NumClipShapesVox[256] = {
  1,  10,  10,  3,  10,  3,  18,  15, // cases 0 - 7
  10,  18,  3,  15,  3,  15,  15,  2, // cases 8 - 15
  10,  3,  18,  15,  18,  15,  11,  10, // cases 16 - 23
  8,  18,  18,  17,  18,  17,  16,  15, // cases 24 - 31
  10,  18,  3,  15,  8,  18,  18,  17, // cases 32 - 39
  18,  11,  15,  10,  18,  16,  17,  15, // cases 40 - 47
  3,  15,  15,  2,  18,  17,  16,  15, // cases 48 - 55
  18,  16,  17,  15,  4,  13,  13,  3, // cases 56 - 63
  10,  18,  8,  18,  3,  15,  18,  17, // cases 64 - 71
  18,  11,  18,  16,  15,  10,  17,  15, // cases 72 - 79
  3,  15,  18,  17,  15,  2,  16,  15, // cases 80 - 87
  18,  16,  4,  13,  17,  15,  13,  3, // cases 88 - 95
  18,  11,  18,  16,  18,  16,  4,  13, // cases 96 - 103
  11,  9,  16,  8,  16,  8,  13,  7, // cases 104 - 111
  15,  10,  17,  15,  17,  15,  13,  3, // cases 112 - 119
  16,  8,  13,  7,  13,  7,  8,  10, // cases 120 - 127
  10,  8,  18,  18,  18,  18,  11,  16, // cases 128 - 135
  3,  18,  15,  17,  15,  17,  10,  15, // cases 136 - 143
  18,  18,  11,  16,  11,  16,  9,  8, // cases 144 - 151
  18,  4,  16,  13,  16,  13,  8,  7, // cases 152 - 159
  3,  18,  15,  17,  18,  4,  16,  13, // cases 160 - 167
  15,  16,  2,  15,  17,  13,  15,  3, // cases 168 - 175
  15,  17,  10,  15,  16,  13,  8,  7, // cases 176 - 183
  17,  13,  15,  3,  13,  8,  7,  10, // cases 184 - 191
  3,  18,  18,  4,  15,  17,  16,  13, // cases 192 - 199
  15,  16,  17,  13,  2,  15,  15,  3, // cases 200 - 207
  15,  17,  16,  13,  10,  15,  8,  7, // cases 208 - 215
  17,  13,  13,  8,  15,  3,  7,  10, // cases 216 - 223
  15,  16,  17,  13,  17,  13,  13,  8, // cases 224 - 231
  10,  8,  15,  7,  15,  7,  3,  10, // cases 232 - 239
  2,  15,  15,  3,  15,  3,  7,  10, // cases 240 - 247
  15,  7,  3,  10,  3,  10,  10,  1  // cases 248 - 255
};

const int StartClipShapesVox[256] = {
  0, 10, 80, 150, 176, 246, 272, 387, // cases 0 - 7
  488, 558, 673, 699, 800, 826, 927, 1028, // cases 8 - 15
  1048, 1118, 1144, 1259, 1360, 1475, 1576, 1652, // cases 16 - 23
  1720, 1772, 1891, 2010, 2124, 2243, 2357, 2465, // cases 24 - 31
  2566, 2636, 2751, 2777, 2878, 2930, 3049, 3168, // cases 32 - 39
  3282, 3397, 3473, 3574, 3642, 3761, 3869, 3983, // cases 40 - 47
  4084, 4110, 4211, 4312, 4332, 4451, 4565, 4673, // cases 48 - 55
  4774, 4893, 5001, 5115, 5216, 5252, 5343, 5434, // cases 56 - 63
  5460, 5530, 5645, 5697, 5816, 5842, 5943, 6062, // cases 64 - 71
  6176, 6291, 6367, 6486, 6594, 6695, 6763, 6877, // cases 72 - 79
  6978, 7004, 7105, 7224, 7338, 7439, 7459, 7567, // cases 80 - 87
  7668, 7787, 7895, 7931, 8022, 8136, 8237, 8328, // cases 88 - 95
  8354, 8469, 8545, 8664, 8772, 8891, 8999, 9035, // cases 96 - 103
  9126, 9202, 9264, 9372, 9426, 9534, 9588, 9679, // cases 104 - 111
  9726, 9827, 9895, 10009, 10110, 10224, 10325, 10416, // cases 112 - 119
  10442, 10550, 10604, 10695, 10742, 10833, 10880, 10932, // cases 120 - 127
  11002, 11072, 11124, 11239, 11358, 11473, 11592, 11668, // cases 128 - 135
  11776, 11802, 11921, 12022, 12136, 12237, 12351, 12419, // cases 136 - 143
  12520, 12635, 12754, 12830, 12938, 13014, 13122, 13184, // cases 144 - 151
  13238, 13357, 13393, 13501, 13592, 13700, 13791, 13845, // cases 152 - 159
  13892, 13918, 14037, 14138, 14252, 14371, 14407, 14515, // cases 160 - 167
  14606, 14707, 14815, 14835, 14936, 15050, 15141, 15242, // cases 168 - 175
  15268, 15369, 15483, 15551, 15652, 15760, 15851, 15905, // cases 176 - 183
  15952, 16066, 16157, 16258, 16284, 16375, 16427, 16474, // cases 184 - 191
  16544, 16570, 16689, 16808, 16844, 16945, 17059, 17167, // cases 192 - 199
  17258, 17359, 17467, 17581, 17672, 17692, 17793, 17894, // cases 200 - 207
  17920, 18021, 18135, 18243, 18334, 18402, 18503, 18557, // cases 208 - 215
  18604, 18718, 18809, 18900, 18952, 19053, 19079, 19126, // cases 216 - 223
  19196, 19297, 19405, 19519, 19610, 19724, 19815, 19906, // cases 224 - 231
  19958, 20026, 20080, 20181, 20228, 20329, 20376, 20402, // cases 232 - 239
  20472, 20492, 20593, 20694, 20720, 20821, 20847, 20894, // cases 240 - 247
  20964, 21065, 21112, 21138, 21208, 21234, 21304, 21374  // cases 248 - 255
};

static unsigned char ClipShapesVox[] = {
 // Case #0: Unique case #1
  ST_HEX, COLOR0, P0, P1, P3, P2, P4, P5, P7, P6,
 // Case #1: Unique case #2
  ST_PNT, 0, COLOR0, 7, P1, P3, P2, P4, P5, P7, P6,
  ST_WDG, COLOR0, P1, P2, P4, EA, ED, EI,
  ST_TET, COLOR0, P1, P2, P4, N0,
  ST_TET, COLOR0, P1, P3, P2, N0,
  ST_PYR, COLOR0, P7, P6, P2, P3, N0,
  ST_PYR, COLOR0, P5, P7, P3, P1, N0,
  ST_PYR, COLOR0, P4, P6, P7, P5, N0,
  ST_TET, COLOR0, P2, P6, P4, N0,
  ST_TET, COLOR0, P4, P5, P1, N0,
  ST_TET, COLOR1, P0, EA, ED, EI,
 // Case #2: (cloned #1)
  ST_PNT, 0, COLOR0, 7, P5, P4, P0, P3, P7, P6, P2,
  ST_WDG, COLOR0, EJ, EA, EB, P5, P0, P3,
  ST_TET, COLOR0, P5, P3, P0, N0,
  ST_TET, COLOR0, P5, P0, P4, N0,
  ST_PYR, COLOR0, P6, P4, P0, P2, N0,
  ST_PYR, COLOR0, P7, P5, P4, P6, N0,
  ST_PYR, COLOR0, P3, P7, P6, P2, N0,
  ST_TET, COLOR0, P0, P3, P2, N0,
  ST_TET, COLOR0, P3, P5, P7, N0,
  ST_TET, COLOR1, P1, EA, EJ, EB,
 // Case #3: Unique case #3
  ST_HEX, COLOR0, EB, P3, P2, ED, EJ, P5, P4, EI,
  ST_WDG, COLOR0, P3, P7, P5, P2, P6, P4,
  ST_WDG, COLOR1, P1, EB, EJ, P0, ED, EI,
 // Case #4: (cloned #1)
  ST_PNT, 0, COLOR0, 7, P3, P1, P0, P6, P7, P5, P4,
  ST_WDG, COLOR0, EC, ED, EK, P3, P0, P6,
  ST_TET, COLOR0, P3, P6, P0, N0,
  ST_TET, COLOR0, P3, P0, P1, N0,
  ST_PYR, COLOR0, P5, P1, P0, P4, N0,
  ST_PYR, COLOR0, P7, P3, P1, P5, N0,
  ST_PYR, COLOR0, P6, P7, P5, P4, N0,
  ST_TET, COLOR0, P0, P6, P4, N0,
  ST_TET, COLOR0, P6, P3, P7, N0,
  ST_TET, COLOR1, P2, ED, EC, EK,
 // Case #5: (cloned #3)
  ST_HEX, COLOR0, EK, P6, P4, EI, EC, P3, P1, EA,
  ST_WDG, COLOR0, P6, P7, P3, P4, P5, P1,
  ST_WDG, COLOR1, P2, EK, EC, P0, EI, EA,
 // Case #6: Unique case #4
  ST_PNT, 0, NOCOLOR, 2, EK, EJ,
  ST_PYR, COLOR0, P6, P7, P5, P4, N0,
  ST_TET, COLOR0, P7, P3, P5, N0,
  ST_TET, COLOR0, P6, P3, P7, N0,
  ST_TET, COLOR0, P0, P6, P4, N0,
  ST_TET, COLOR0, P5, P0, P4, N0,
  ST_PYR, COLOR0, P5, EJ, EA, P0, N0,
  ST_PYR, COLOR0, P3, EB, EJ, P5, N0,
  ST_TET, COLOR0, P3, EC, EB, N0,
  ST_PYR, COLOR0, P6, EK, EC, P3, N0,
  ST_PYR, COLOR0, P6, P0, ED, EK, N0,
  ST_TET, COLOR0, P0, EA, ED, N0,
  ST_PYR, COLOR1, P2, ED, EA, P1, N0,
  ST_PYR, COLOR1, EC, P2, P1, EB, N0,
  ST_TET, COLOR1, EB, P1, EJ, N0,
  ST_TET, COLOR1, P1, EA, EJ, N0,
  ST_TET, COLOR1, EC, P2, N0, EK,
  ST_TET, COLOR1, ED, P2, EK, N0,
 // Case #7: Unique case #5
  ST_PNT, 0, NOCOLOR, 5, EJ, EI, EB, EC, EK,
  ST_PYR, COLOR0, P5, P4, P6, P7, N0,
  ST_TET, COLOR0, P6, N0, P3, P7,
  ST_PYR, COLOR0, P4, EI, EK, P6, N0,
  ST_PYR, COLOR0, EJ, EI, P4, P5, N0,
  ST_TET, COLOR0, P3, P5, P7, N0,
  ST_PYR, COLOR0, P3, EB, EJ, P5, N0,
  ST_TET, COLOR0, P3, EC, EB, N0,
  ST_PYR, COLOR0, EK, EC, P3, P6, N0,
  ST_PYR, COLOR1, EI, P0, P2, EK, N0,
  ST_PYR, COLOR1, EJ, P1, P0, EI, N0,
  ST_TET, COLOR1, EB, P1, EJ, N0,
  ST_TET, COLOR1, P1, P2, P0, N0,
  ST_PYR, COLOR1, EB, EC, P2, P1, N0,
  ST_TET, COLOR1, P2, EC, EK, N0,
 // Case #8: (cloned #1)
  ST_PNT, 0, COLOR0, 7, P7, P5, P1, P2, P6, P4, P0,
  ST_WDG, COLOR0, EL, EB, EC, P7, P1, P2,
  ST_TET, COLOR0, P7, P2, P1, N0,
  ST_TET, COLOR0, P7, P1, P5, N0,
  ST_PYR, COLOR0, P4, P5, P1, P0, N0,
  ST_PYR, COLOR0, P6, P7, P5, P4, N0,
  ST_PYR, COLOR0, P2, P6, P4, P0, N0,
  ST_TET, COLOR0, P1, P2, P0, N0,
  ST_TET, COLOR0, P2, P7, P6, N0,
  ST_TET, COLOR1, P3, EB, EL, EC,
 // Case #9: (cloned #6)
  ST_PNT, 0, NOCOLOR, 2, EI, EL,
  ST_PYR, COLOR0, P4, P6, P7, P5, N0,
  ST_TET, COLOR0, P5, P7, P1, N0,
  ST_TET, COLOR0, P4, P5, P1, N0,
  ST_TET, COLOR0, P2, P6, P4, N0,
  ST_TET, COLOR0, P7, P6, P2, N0,
  ST_PYR, COLOR0, P7, P2, EC, EL, N0,
  ST_PYR, COLOR0, P1, P7, EL, EB, N0,
  ST_TET, COLOR0, P1, EB, EA, N0,
  ST_PYR, COLOR0, P4, P1, EA, EI, N0,
  ST_PYR, COLOR0, P4, EI, ED, P2, N0,
  ST_TET, COLOR0, P2, ED, EC, N0,
  ST_PYR, COLOR1, P0, P3, EC, ED, N0,
  ST_PYR, COLOR1, EA, EB, P3, P0, N0,
  ST_TET, COLOR1, EB, EL, P3, N0,
  ST_TET, COLOR1, P3, EL, EC, N0,
  ST_TET, COLOR1, EA, N0, P0, EI,
  ST_TET, COLOR1, ED, EI, P0, N0,
 // Case #10: (cloned #3)
  ST_HEX, COLOR0, EC, P2, P0, EA, EL, P7, P5, EJ,
  ST_WDG, COLOR0, P2, P6, P7, P0, P4, P5,
  ST_WDG, COLOR1, P3, EC, EL, P1, EA, EJ,
 // Case #11: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EL, EJ, EC, ED, EI,
  ST_PYR, COLOR0, P7, P5, P4, P6, N0,
  ST_TET, COLOR0, P4, N0, P2, P6,
  ST_PYR, COLOR0, P5, EJ, EI, P4, N0,
  ST_PYR, COLOR0, EL, EJ, P5, P7, N0,
  ST_TET, COLOR0, P2, P7, P6, N0,
  ST_PYR, COLOR0, P2, EC, EL, P7, N0,
  ST_TET, COLOR0, P2, ED, EC, N0,
  ST_PYR, COLOR0, EI, ED, P2, P4, N0,
  ST_PYR, COLOR1, EJ, P1, P0, EI, N0,
  ST_PYR, COLOR1, EL, P3, P1, EJ, N0,
  ST_TET, COLOR1, EC, P3, EL, N0,
  ST_TET, COLOR1, P3, P0, P1, N0,
  ST_PYR, COLOR1, EC, ED, P0, P3, N0,
  ST_TET, COLOR1, P0, ED, EI, N0,
 // Case #12: (cloned #3)
  ST_HEX, COLOR0, EL, P7, P6, EK, EB, P1, P0, ED,
  ST_WDG, COLOR0, P0, P4, P6, P1, P5, P7,
  ST_WDG, COLOR1, P2, ED, EK, P3, EB, EL,
 // Case #13: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EL, EK, EB, EA, EI,
  ST_PYR, COLOR0, P7, P5, P4, P6, N0,
  ST_TET, COLOR0, P4, P1, N0, P5,
  ST_PYR, COLOR0, P6, P4, EI, EK, N0,
  ST_PYR, COLOR0, EL, P7, P6, EK, N0,
  ST_TET, COLOR0, P1, P5, P7, N0,
  ST_PYR, COLOR0, P1, P7, EL, EB, N0,
  ST_TET, COLOR0, P1, EB, EA, N0,
  ST_PYR, COLOR0, EI, P4, P1, EA, N0,
  ST_PYR, COLOR1, EK, EI, P0, P2, N0,
  ST_PYR, COLOR1, EL, EK, P2, P3, N0,
  ST_TET, COLOR1, EB, EL, P3, N0,
  ST_TET, COLOR1, P3, P2, P0, N0,
  ST_PYR, COLOR1, EB, P3, P0, EA, N0,
  ST_TET, COLOR1, P0, EI, EA, N0,
 // Case #14: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EK, EL, ED, EA, EJ,
  ST_PYR, COLOR0, P6, P7, P5, P4, N0,
  ST_TET, COLOR0, P5, N0, P0, P4,
  ST_PYR, COLOR0, P7, EL, EJ, P5, N0,
  ST_PYR, COLOR0, EK, EL, P7, P6, N0,
  ST_TET, COLOR0, P0, P6, P4, N0,
  ST_PYR, COLOR0, P0, ED, EK, P6, N0,
  ST_TET, COLOR0, P0, EA, ED, N0,
  ST_PYR, COLOR0, EJ, EA, P0, P5, N0,
  ST_PYR, COLOR1, EL, P3, P1, EJ, N0,
  ST_PYR, COLOR1, EK, P2, P3, EL, N0,
  ST_TET, COLOR1, ED, P2, EK, N0,
  ST_TET, COLOR1, P2, P1, P3, N0,
  ST_PYR, COLOR1, ED, EA, P1, P2, N0,
  ST_TET, COLOR1, P1, EA, EJ, N0,
 // Case #15: Unique case #6
  ST_HEX, COLOR0, EI, EJ, EL, EK, P4, P5, P7, P6,
  ST_HEX, COLOR1, P0, P1, P3, P2, EI, EJ, EL, EK,
 // Case #16: (cloned #1)
  ST_PNT, 0, COLOR0, 7, P5, P1, P0, P6, P7, P3, P2,
  ST_WDG, COLOR0, P5, P0, P6, EE, EI, EH,
  ST_TET, COLOR0, P5, P0, P6, N0,
  ST_TET, COLOR0, P5, P1, P0, N0,
  ST_PYR, COLOR0, P3, P2, P0, P1, N0,
  ST_PYR, COLOR0, P7, P3, P1, P5, N0,
  ST_PYR, COLOR0, P6, P2, P3, P7, N0,
  ST_TET, COLOR0, P0, P2, P6, N0,
  ST_TET, COLOR0, P6, P7, P5, N0,
  ST_TET, COLOR1, P4, EE, EI, EH,
 // Case #17: (cloned #3)
  ST_HEX, COLOR0, EE, P5, P1, EA, EH, P6, P2, ED,
  ST_WDG, COLOR0, P2, P3, P1, P6, P7, P5,
  ST_WDG, COLOR1, P0, ED, EA, P4, EH, EE,
 // Case #18: (cloned #6)
  ST_PNT, 0, NOCOLOR, 2, EH, EB,
  ST_PYR, COLOR0, P6, P2, P3, P7, N0,
  ST_TET, COLOR0, P7, P3, P5, N0,
  ST_TET, COLOR0, P6, P7, P5, N0,
  ST_TET, COLOR0, P0, P2, P6, N0,
  ST_TET, COLOR0, P3, P2, P0, N0,
  ST_PYR, COLOR0, P3, P0, EA, EB, N0,
  ST_PYR, COLOR0, P5, P3, EB, EJ, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_PYR, COLOR0, P6, P5, EE, EH, N0,
  ST_PYR, COLOR0, P6, EH, EI, P0, N0,
  ST_TET, COLOR0, P0, EI, EA, N0,
  ST_PYR, COLOR1, P4, P1, EA, EI, N0,
  ST_PYR, COLOR1, EE, EJ, P1, P4, N0,
  ST_TET, COLOR1, EJ, EB, P1, N0,
  ST_TET, COLOR1, P1, EB, EA, N0,
  ST_TET, COLOR1, EE, N0, P4, EH,
  ST_TET, COLOR1, EI, EH, P4, N0,
 // Case #19: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EB, ED, EJ, EE, EH,
  ST_PYR, COLOR0, P3, P7, P6, P2, N0,
  ST_TET, COLOR0, P6, P5, N0, P7,
  ST_PYR, COLOR0, P2, P6, EH, ED, N0,
  ST_PYR, COLOR0, EB, P3, P2, ED, N0,
  ST_TET, COLOR0, P5, P7, P3, N0,
  ST_PYR, COLOR0, P5, P3, EB, EJ, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_PYR, COLOR0, EH, P6, P5, EE, N0,
  ST_PYR, COLOR1, ED, EH, P4, P0, N0,
  ST_PYR, COLOR1, EB, ED, P0, P1, N0,
  ST_TET, COLOR1, EJ, EB, P1, N0,
  ST_TET, COLOR1, P1, P0, P4, N0,
  ST_PYR, COLOR1, EJ, P1, P4, EE, N0,
  ST_TET, COLOR1, P4, EH, EE, N0,
 // Case #20: (cloned #6)
  ST_PNT, 0, NOCOLOR, 2, EC, EE,
  ST_PYR, COLOR0, P3, P1, P5, P7, N0,
  ST_TET, COLOR0, P7, P5, P6, N0,
  ST_TET, COLOR0, P3, P7, P6, N0,
  ST_TET, COLOR0, P0, P1, P3, N0,
  ST_TET, COLOR0, P5, P1, P0, N0,
  ST_PYR, COLOR0, P5, P0, EI, EE, N0,
  ST_PYR, COLOR0, P6, P5, EE, EH, N0,
  ST_TET, COLOR0, P6, EH, EK, N0,
  ST_PYR, COLOR0, P3, P6, EK, EC, N0,
  ST_PYR, COLOR0, P3, EC, ED, P0, N0,
  ST_TET, COLOR0, P0, ED, EI, N0,
  ST_PYR, COLOR1, P2, P4, EI, ED, N0,
  ST_PYR, COLOR1, EK, EH, P4, P2, N0,
  ST_TET, COLOR1, EH, EE, P4, N0,
  ST_TET, COLOR1, P4, EE, EI, N0,
  ST_TET, COLOR1, EK, N0, P2, EC,
  ST_TET, COLOR1, ED, EC, P2, N0,
 // Case #21: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EE, EA, EH, EK, EC,
  ST_PYR, COLOR0, P5, P7, P3, P1, N0,
  ST_TET, COLOR0, P3, P6, N0, P7,
  ST_PYR, COLOR0, P1, P3, EC, EA, N0,
  ST_PYR, COLOR0, EE, P5, P1, EA, N0,
  ST_TET, COLOR0, P6, P7, P5, N0,
  ST_PYR, COLOR0, P6, P5, EE, EH, N0,
  ST_TET, COLOR0, P6, EH, EK, N0,
  ST_PYR, COLOR0, EC, P3, P6, EK, N0,
  ST_PYR, COLOR1, EA, EC, P2, P0, N0,
  ST_PYR, COLOR1, EE, EA, P0, P4, N0,
  ST_TET, COLOR1, EH, EE, P4, N0,
  ST_TET, COLOR1, P4, P0, P2, N0,
  ST_PYR, COLOR1, EH, P4, P2, EK, N0,
  ST_TET, COLOR1, P2, EC, EK, N0,
 // Case #22: Unique case #7
  ST_TET, COLOR0, P0, EA, ED, EI,
  ST_TET, COLOR0, P5, P6, P7, P3,
  ST_PYR, COLOR0, EC, P3, P6, EK, EH,
  ST_PYR, COLOR0, EB, EJ, P5, P3, EE,
  ST_PYR, COLOR0, P6, P5, EE, EH, P3,
  ST_PYR, COLOR0, EH, EE, EB, EC, P3,
  ST_WDG, COLOR1, ED, EA, EI, P2, P1, P4,
  ST_PYR, COLOR1, P2, EK, EH, P4, EC,
  ST_PYR, COLOR1, EE, EJ, P1, P4, EB,
  ST_PYR, COLOR1, EC, P2, P1, EB, P4,
  ST_PYR, COLOR1, EC, EB, EE, EH, P4,
 // Case #23: Unique case #8
  ST_TET, COLOR0, P5, P6, P7, P3,
  ST_PYR, COLOR0, EC, P3, P6, EK, EH,
  ST_PYR, COLOR0, EB, EJ, P5, P3, EE,
  ST_PYR, COLOR0, P6, P5, EE, EH, P3,
  ST_PYR, COLOR0, EH, EE, EB, EC, P3,
  ST_TET, COLOR1, P0, P1, P2, P4,
  ST_PYR, COLOR1, EH, P4, P2, EK, EC,
  ST_PYR, COLOR1, EE, EJ, P1, P4, EB,
  ST_PYR, COLOR1, P2, P1, EB, EC, P4,
  ST_PYR, COLOR1, EH, EC, EB, EE, P4,
 // Case #24: Unique case #9
  ST_WDG, COLOR0, EB, EC, EL, P1, P2, P7,
  ST_WDG, COLOR0, P0, P6, P5, EI, EH, EE,
  ST_TET, COLOR0, P2, P1, P7, P6,
  ST_TET, COLOR0, P5, P6, P7, P1,
  ST_TET, COLOR0, P0, P5, P1, P6,
  ST_TET, COLOR0, P2, P6, P0, P1,
  ST_TET, COLOR1, P4, EE, EI, EH,
  ST_TET, COLOR1, P3, EC, EB, EL,
 // Case #25: Unique case #10
  ST_PNT, 0, NOCOLOR, 4, EE, EH, EL, EL,
  ST_PYR, COLOR0, P7, P2, EC, EL, N0,
  ST_TET, COLOR0, EC, P2, ED, N0,
  ST_PYR, COLOR0, P6, EH, ED, P2, N0,
  ST_TET, COLOR0, P7, P6, P2, N0,
  ST_TET, COLOR0, P1, EB, EA, N0,
  ST_TET, COLOR0, P5, P7, P1, N0,
  ST_PYR, COLOR0, P1, P7, EL, EB, N0,
  ST_TET, COLOR0, P5, P6, P7, N0,
  ST_PYR, COLOR0, P5, EE, EH, P6, N0,
  ST_PYR, COLOR0, P5, P1, EA, EE, N0,
  ST_PYR, COLOR1, P3, EC, ED, P0, N0,
  ST_PYR, COLOR1, EA, EB, P3, P0, N0,
  ST_TET, COLOR1, P3, EL, EC, N0,
  ST_TET, COLOR1, EB, EL, P3, N0,
  ST_PYR, COLOR1, ED, EH, P4, P0, N0,
  ST_PYR, COLOR1, P0, P4, EE, EA, N0,
  ST_TET, COLOR1, EE, P4, EH, N0,
 // Case #26: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EL, EC, EH, EH,
  ST_PYR, COLOR0, P6, EH, EI, P0, N0,
  ST_TET, COLOR0, EI, EA, P0, N0,
  ST_PYR, COLOR0, P2, P0, EA, EC, N0,
  ST_TET, COLOR0, P6, P0, P2, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_TET, COLOR0, P7, P5, P6, N0,
  ST_PYR, COLOR0, P5, EE, EH, P6, N0,
  ST_TET, COLOR0, P7, P6, P2, N0,
  ST_PYR, COLOR0, P7, P2, EC, EL, N0,
  ST_PYR, COLOR0, P7, EL, EJ, P5, N0,
  ST_PYR, COLOR1, P4, P1, EA, EI, N0,
  ST_PYR, COLOR1, EJ, P1, P4, EE, N0,
  ST_TET, COLOR1, P4, EI, EH, N0,
  ST_TET, COLOR1, EE, P4, EH, N0,
  ST_PYR, COLOR1, EA, P1, P3, EC, N0,
  ST_PYR, COLOR1, P1, EJ, EL, P3, N0,
  ST_TET, COLOR1, EL, EC, P3, N0,
 // Case #27: Unique case #11
  ST_PNT, 0, NOCOLOR, 6, ED, EC, EL, EJ, EE, EH,
  ST_TET, COLOR0, P7, P5, P6, N0,
  ST_PYR, COLOR0, P7, EL, EJ, P5, N0,
  ST_TET, COLOR0, P2, P7, P6, N0,
  ST_PYR, COLOR0, P2, EC, EL, P7, N0,
  ST_TET, COLOR0, ED, EC, P2, N0,
  ST_PYR, COLOR0, P2, P6, EH, ED, N0,
  ST_PYR, COLOR0, EH, P6, P5, EE, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_TET, COLOR1, P0, P1, P3, N0,
  ST_PYR, COLOR1, ED, P0, P3, EC, N0,
  ST_PYR, COLOR1, ED, EH, P4, P0, N0,
  ST_TET, COLOR1, P4, P1, P0, N0,
  ST_TET, COLOR1, P4, EH, EE, N0,
  ST_PYR, COLOR1, P4, EE, EJ, P1, N0,
  ST_PYR, COLOR1, EJ, EL, P3, P1, N0,
  ST_TET, COLOR1, EL, EC, P3, N0,
 // Case #28: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EL, EB, EE, EE,
  ST_PYR, COLOR0, P5, P0, EI, EE, N0,
  ST_TET, COLOR0, EI, P0, ED, N0,
  ST_PYR, COLOR0, P1, EB, ED, P0, N0,
  ST_TET, COLOR0, P5, P1, P0, N0,
  ST_TET, COLOR0, P6, EH, EK, N0,
  ST_TET, COLOR0, P7, P5, P6, N0,
  ST_PYR, COLOR0, P6, P5, EE, EH, N0,
  ST_TET, COLOR0, P7, P1, P5, N0,
  ST_PYR, COLOR0, P7, EL, EB, P1, N0,
  ST_PYR, COLOR0, P7, P6, EK, EL, N0,
  ST_PYR, COLOR1, P4, EI, ED, P2, N0,
  ST_PYR, COLOR1, EK, EH, P4, P2, N0,
  ST_TET, COLOR1, P4, EE, EI, N0,
  ST_TET, COLOR1, EH, EE, P4, N0,
  ST_PYR, COLOR1, ED, EB, P3, P2, N0,
  ST_PYR, COLOR1, P2, P3, EL, EK, N0,
  ST_TET, COLOR1, EL, P3, EB, N0,
 // Case #29: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EA, EB, EL, EK, EH, EE,
  ST_TET, COLOR0, P7, P5, P6, N0,
  ST_PYR, COLOR0, P7, P6, EK, EL, N0,
  ST_TET, COLOR0, P1, P5, P7, N0,
  ST_PYR, COLOR0, P1, P7, EL, EB, N0,
  ST_TET, COLOR0, EA, P1, EB, N0,
  ST_PYR, COLOR0, P1, EA, EE, P5, N0,
  ST_PYR, COLOR0, EE, EH, P6, P5, N0,
  ST_TET, COLOR0, P6, EH, EK, N0,
  ST_TET, COLOR1, P0, P3, P2, N0,
  ST_PYR, COLOR1, EA, EB, P3, P0, N0,
  ST_PYR, COLOR1, EA, P0, P4, EE, N0,
  ST_TET, COLOR1, P4, P0, P2, N0,
  ST_TET, COLOR1, P4, EH, EE, N0,
  ST_PYR, COLOR1, P4, P2, EK, EH, N0,
  ST_PYR, COLOR1, EK, P2, P3, EL, N0,
  ST_TET, COLOR1, EL, P3, EB, N0,
 // Case #30: Unique case #12
  ST_PNT, 0, NOCOLOR, 5, EL, EJ, EK, EH, EE,
  ST_TET, COLOR0, P0, EA, ED, EI,
  ST_PYR, COLOR0, P5, P7, EL, EJ, N0,
  ST_PYR, COLOR0, P7, P6, EK, EL, N0,
  ST_TET, COLOR0, P6, EH, EK, N0,
  ST_TET, COLOR0, P7, P5, P6, N0,
  ST_PYR, COLOR0, P6, P5, EE, EH, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_WDG, COLOR1, ED, EA, EI, P2, P1, P4,
  ST_TET, COLOR1, P1, P2, P4, N0,
  ST_PYR, COLOR1, P2, EK, EH, P4, N0,
  ST_TET, COLOR1, P3, P2, P1, N0,
  ST_PYR, COLOR1, EJ, EL, P3, P1, N0,
  ST_PYR, COLOR1, EL, EK, P2, P3, N0,
  ST_PYR, COLOR1, P4, EE, EJ, P1, N0,
  ST_TET, COLOR1, EH, EE, P4, N0,
 // Case #31: Unique case #13
  ST_PNT, 0, NOCOLOR, 5, EJ, EL, EK, EE, EH,
  ST_PYR, COLOR0, P7, P6, EK, EL, N0,
  ST_TET, COLOR0, P6, EH, EK, N0,
  ST_PYR, COLOR0, P5, P7, EL, EJ, N0,
  ST_TET, COLOR0, EE, P5, EJ, N0,
  ST_PYR, COLOR0, EH, P6, P5, EE, N0,
  ST_TET, COLOR0, P6, P7, P5, N0,
  ST_PYR, COLOR1, P0, P1, P3, P2, N0,
  ST_TET, COLOR1, P2, P4, P0, N0,
  ST_TET, COLOR1, P4, P1, P0, N0,
  ST_PYR, COLOR1, P4, EE, EJ, P1, N0,
  ST_PYR, COLOR1, EJ, EL, P3, P1, N0,
  ST_PYR, COLOR1, EL, EK, P2, P3, N0,
  ST_PYR, COLOR1, EK, EH, P4, P2, N0,
  ST_TET, COLOR1, EE, P4, EH, N0,
 // Case #32: (cloned #1)
  ST_PNT, 0, COLOR0, 7, P7, P3, P1, P4, P6, P2, P0,
  ST_WDG, COLOR0, P7, P1, P4, EF, EJ, EE,
  ST_TET, COLOR0, P7, P1, P4, N0,
  ST_TET, COLOR0, P7, P3, P1, N0,
  ST_PYR, COLOR0, P2, P0, P1, P3, N0,
  ST_PYR, COLOR0, P6, P2, P3, P7, N0,
  ST_PYR, COLOR0, P4, P0, P2, P6, N0,
  ST_TET, COLOR0, P1, P0, P4, N0,
  ST_TET, COLOR0, P4, P6, P7, N0,
  ST_TET, COLOR1, P5, EF, EJ, EE,
 // Case #33: (cloned #6)
  ST_PNT, 0, NOCOLOR, 2, ED, EF,
  ST_PYR, COLOR0, P2, P3, P7, P6, N0,
  ST_TET, COLOR0, P3, P1, P7, N0,
  ST_TET, COLOR0, P2, P1, P3, N0,
  ST_TET, COLOR0, P4, P2, P6, N0,
  ST_TET, COLOR0, P7, P4, P6, N0,
  ST_PYR, COLOR0, P7, EF, EE, P4, N0,
  ST_PYR, COLOR0, P1, EJ, EF, P7, N0,
  ST_TET, COLOR0, P1, EA, EJ, N0,
  ST_PYR, COLOR0, P2, ED, EA, P1, N0,
  ST_PYR, COLOR0, P2, P4, EI, ED, N0,
  ST_TET, COLOR0, P4, EE, EI, N0,
  ST_PYR, COLOR1, P0, EI, EE, P5, N0,
  ST_PYR, COLOR1, EA, P0, P5, EJ, N0,
  ST_TET, COLOR1, EJ, P5, EF, N0,
  ST_TET, COLOR1, P5, EE, EF, N0,
  ST_TET, COLOR1, EA, P0, N0, ED,
  ST_TET, COLOR1, EI, P0, ED, N0,
 // Case #34: (cloned #3)
  ST_HEX, COLOR0, EF, P7, P3, EB, EE, P4, P0, EA,
  ST_WDG, COLOR0, P0, P2, P3, P4, P6, P7,
  ST_WDG, COLOR1, P1, EA, EB, P5, EE, EF,
 // Case #35: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EF, EB, EE, EI, ED,
  ST_PYR, COLOR0, P7, P6, P2, P3, N0,
  ST_TET, COLOR0, P2, P4, N0, P6,
  ST_PYR, COLOR0, P3, P2, ED, EB, N0,
  ST_PYR, COLOR0, EF, P7, P3, EB, N0,
  ST_TET, COLOR0, P4, P6, P7, N0,
  ST_PYR, COLOR0, P4, P7, EF, EE, N0,
  ST_TET, COLOR0, P4, EE, EI, N0,
  ST_PYR, COLOR0, ED, P2, P4, EI, N0,
  ST_PYR, COLOR1, EB, ED, P0, P1, N0,
  ST_PYR, COLOR1, EF, EB, P1, P5, N0,
  ST_TET, COLOR1, EE, EF, P5, N0,
  ST_TET, COLOR1, P5, P1, P0, N0,
  ST_PYR, COLOR1, EE, P5, P0, EI, N0,
  ST_TET, COLOR1, P0, ED, EI, N0,
 // Case #36: (cloned #24)
  ST_WDG, COLOR0, P1, P4, P7, EJ, EE, EF,
  ST_WDG, COLOR0, ED, EK, EC, P0, P6, P3,
  ST_TET, COLOR0, P4, P7, P1, P6,
  ST_TET, COLOR0, P3, P7, P6, P1,
  ST_TET, COLOR0, P0, P1, P3, P6,
  ST_TET, COLOR0, P4, P0, P6, P1,
  ST_TET, COLOR1, P2, ED, EC, EK,
  ST_TET, COLOR1, P5, EJ, EE, EF,
 // Case #37: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EC, EK, EF, EF,
  ST_PYR, COLOR0, P7, EF, EE, P4, N0,
  ST_TET, COLOR0, EE, EI, P4, N0,
  ST_PYR, COLOR0, P6, P4, EI, EK, N0,
  ST_TET, COLOR0, P7, P4, P6, N0,
  ST_TET, COLOR0, P1, EA, EJ, N0,
  ST_TET, COLOR0, P3, P1, P7, N0,
  ST_PYR, COLOR0, P1, EJ, EF, P7, N0,
  ST_TET, COLOR0, P3, P7, P6, N0,
  ST_PYR, COLOR0, P3, P6, EK, EC, N0,
  ST_PYR, COLOR0, P3, EC, EA, P1, N0,
  ST_PYR, COLOR1, P5, P0, EI, EE, N0,
  ST_PYR, COLOR1, EA, P0, P5, EJ, N0,
  ST_TET, COLOR1, P5, EE, EF, N0,
  ST_TET, COLOR1, EJ, P5, EF, N0,
  ST_PYR, COLOR1, EI, P0, P2, EK, N0,
  ST_PYR, COLOR1, P0, EA, EC, P2, N0,
  ST_TET, COLOR1, EC, EK, P2, N0,
 // Case #38: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EF, EE, EK, EK,
  ST_PYR, COLOR0, P6, P0, ED, EK, N0,
  ST_TET, COLOR0, ED, P0, EA, N0,
  ST_PYR, COLOR0, P4, EE, EA, P0, N0,
  ST_TET, COLOR0, P6, P4, P0, N0,
  ST_TET, COLOR0, P3, EC, EB, N0,
  ST_TET, COLOR0, P7, P6, P3, N0,
  ST_PYR, COLOR0, P3, P6, EK, EC, N0,
  ST_TET, COLOR0, P7, P4, P6, N0,
  ST_PYR, COLOR0, P7, EF, EE, P4, N0,
  ST_PYR, COLOR0, P7, P3, EB, EF, N0,
  ST_PYR, COLOR1, P2, ED, EA, P1, N0,
  ST_PYR, COLOR1, EB, EC, P2, P1, N0,
  ST_TET, COLOR1, P2, EK, ED, N0,
  ST_TET, COLOR1, EC, EK, P2, N0,
  ST_PYR, COLOR1, EA, EE, P5, P1, N0,
  ST_PYR, COLOR1, P1, P5, EF, EB, N0,
  ST_TET, COLOR1, EF, P5, EE, N0,
 // Case #39: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EI, EE, EF, EB, EC, EK,
  ST_TET, COLOR0, P7, P6, P3, N0,
  ST_PYR, COLOR0, P7, P3, EB, EF, N0,
  ST_TET, COLOR0, P4, P6, P7, N0,
  ST_PYR, COLOR0, P4, P7, EF, EE, N0,
  ST_TET, COLOR0, EI, P4, EE, N0,
  ST_PYR, COLOR0, P4, EI, EK, P6, N0,
  ST_PYR, COLOR0, EK, EC, P3, P6, N0,
  ST_TET, COLOR0, P3, EC, EB, N0,
  ST_TET, COLOR1, P0, P5, P1, N0,
  ST_PYR, COLOR1, EI, EE, P5, P0, N0,
  ST_PYR, COLOR1, EI, P0, P2, EK, N0,
  ST_TET, COLOR1, P2, P0, P1, N0,
  ST_TET, COLOR1, P2, EC, EK, N0,
  ST_PYR, COLOR1, P2, P1, EB, EC, N0,
  ST_PYR, COLOR1, EB, P1, P5, EF, N0,
  ST_TET, COLOR1, EF, P5, EE, N0,
 // Case #40: (cloned #6)
  ST_PNT, 0, NOCOLOR, 2, EC, EE,
  ST_PYR, COLOR0, P2, P6, P4, P0, N0,
  ST_TET, COLOR0, P6, P7, P4, N0,
  ST_TET, COLOR0, P2, P7, P6, N0,
  ST_TET, COLOR0, P1, P2, P0, N0,
  ST_TET, COLOR0, P4, P1, P0, N0,
  ST_PYR, COLOR0, P4, EE, EJ, P1, N0,
  ST_PYR, COLOR0, P7, EF, EE, P4, N0,
  ST_TET, COLOR0, P7, EL, EF, N0,
  ST_PYR, COLOR0, P2, EC, EL, P7, N0,
  ST_PYR, COLOR0, P2, P1, EB, EC, N0,
  ST_TET, COLOR0, P1, EJ, EB, N0,
  ST_PYR, COLOR1, P3, EB, EJ, P5, N0,
  ST_PYR, COLOR1, EL, P3, P5, EF, N0,
  ST_TET, COLOR1, EF, P5, EE, N0,
  ST_TET, COLOR1, P5, EJ, EE, N0,
  ST_TET, COLOR1, EL, P3, N0, EC,
  ST_TET, COLOR1, EB, P3, EC, N0,
 // Case #41: (cloned #22)
  ST_TET, COLOR0, P1, EA, EJ, EB,
  ST_TET, COLOR0, P7, P6, P2, P4,
  ST_PYR, COLOR0, EI, ED, P2, P4, EC,
  ST_PYR, COLOR0, EE, P4, P7, EF, EL,
  ST_PYR, COLOR0, P2, EC, EL, P7, P4,
  ST_PYR, COLOR0, EC, EI, EE, EL, P4,
  ST_WDG, COLOR1, P0, P5, P3, EA, EJ, EB,
  ST_PYR, COLOR1, P0, P3, EC, ED, EI,
  ST_PYR, COLOR1, EL, P3, P5, EF, EE,
  ST_PYR, COLOR1, EI, EE, P5, P0, P3,
  ST_PYR, COLOR1, EI, EC, EL, EE, P3,
 // Case #42: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EE, EA, EF, EL, EC,
  ST_PYR, COLOR0, P4, P0, P2, P6, N0,
  ST_TET, COLOR0, P2, N0, P7, P6,
  ST_PYR, COLOR0, P0, EA, EC, P2, N0,
  ST_PYR, COLOR0, EE, EA, P0, P4, N0,
  ST_TET, COLOR0, P7, P4, P6, N0,
  ST_PYR, COLOR0, P7, EF, EE, P4, N0,
  ST_TET, COLOR0, P7, EL, EF, N0,
  ST_PYR, COLOR0, EC, EL, P7, P2, N0,
  ST_PYR, COLOR1, EA, P1, P3, EC, N0,
  ST_PYR, COLOR1, EE, P5, P1, EA, N0,
  ST_TET, COLOR1, EF, P5, EE, N0,
  ST_TET, COLOR1, P5, P3, P1, N0,
  ST_PYR, COLOR1, EF, EL, P3, P5, N0,
  ST_TET, COLOR1, P3, EL, EC, N0,
 // Case #43: (cloned #23)
  ST_TET, COLOR0, P7, P6, P2, P4,
  ST_PYR, COLOR0, EI, ED, P2, P4, EC,
  ST_PYR, COLOR0, EE, P4, P7, EF, EL,
  ST_PYR, COLOR0, P2, EC, EL, P7, P4,
  ST_PYR, COLOR0, EC, EI, EE, EL, P4,
  ST_TET, COLOR1, P1, P0, P5, P3,
  ST_PYR, COLOR1, EC, ED, P0, P3, EI,
  ST_PYR, COLOR1, EL, P3, P5, EF, EE,
  ST_PYR, COLOR1, P0, EI, EE, P5, P3,
  ST_PYR, COLOR1, EC, EL, EE, EI, P3,
 // Case #44: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EK, ED, EE, EE,
  ST_PYR, COLOR0, P4, EE, EJ, P1, N0,
  ST_TET, COLOR0, EJ, EB, P1, N0,
  ST_PYR, COLOR0, P0, P1, EB, ED, N0,
  ST_TET, COLOR0, P4, P1, P0, N0,
  ST_TET, COLOR0, P7, EL, EF, N0,
  ST_TET, COLOR0, P6, P7, P4, N0,
  ST_PYR, COLOR0, P7, EF, EE, P4, N0,
  ST_TET, COLOR0, P6, P4, P0, N0,
  ST_PYR, COLOR0, P6, P0, ED, EK, N0,
  ST_PYR, COLOR0, P6, EK, EL, P7, N0,
  ST_PYR, COLOR1, P5, P3, EB, EJ, N0,
  ST_PYR, COLOR1, EL, P3, P5, EF, N0,
  ST_TET, COLOR1, P5, EJ, EE, N0,
  ST_TET, COLOR1, EF, P5, EE, N0,
  ST_PYR, COLOR1, EB, P3, P2, ED, N0,
  ST_PYR, COLOR1, P3, EL, EK, P2, N0,
  ST_TET, COLOR1, EK, ED, P2, N0,
 // Case #45: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EK, EL, EI, EE, EF,
  ST_TET, COLOR0, P1, EB, EA, EJ,
  ST_PYR, COLOR0, P7, P6, EK, EL, N0,
  ST_PYR, COLOR0, P6, P4, EI, EK, N0,
  ST_TET, COLOR0, P4, EE, EI, N0,
  ST_TET, COLOR0, P6, P7, P4, N0,
  ST_PYR, COLOR0, P4, P7, EF, EE, N0,
  ST_TET, COLOR0, P7, EL, EF, N0,
  ST_WDG, COLOR1, EA, EB, EJ, P0, P3, P5,
  ST_TET, COLOR1, P3, P0, P5, N0,
  ST_PYR, COLOR1, P0, EI, EE, P5, N0,
  ST_TET, COLOR1, P2, P0, P3, N0,
  ST_PYR, COLOR1, EL, EK, P2, P3, N0,
  ST_PYR, COLOR1, EK, EI, P0, P2, N0,
  ST_PYR, COLOR1, P5, EF, EL, P3, N0,
  ST_TET, COLOR1, EE, EF, P5, N0,
 // Case #46: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EA, ED, EK, EL, EF, EE,
  ST_TET, COLOR0, P6, P7, P4, N0,
  ST_PYR, COLOR0, P6, EK, EL, P7, N0,
  ST_TET, COLOR0, P0, P6, P4, N0,
  ST_PYR, COLOR0, P0, ED, EK, P6, N0,
  ST_TET, COLOR0, EA, ED, P0, N0,
  ST_PYR, COLOR0, P0, P4, EE, EA, N0,
  ST_PYR, COLOR0, EE, P4, P7, EF, N0,
  ST_TET, COLOR0, P7, EL, EF, N0,
  ST_TET, COLOR1, P1, P3, P2, N0,
  ST_PYR, COLOR1, EA, P1, P2, ED, N0,
  ST_PYR, COLOR1, EA, EE, P5, P1, N0,
  ST_TET, COLOR1, P5, P3, P1, N0,
  ST_TET, COLOR1, P5, EE, EF, N0,
  ST_PYR, COLOR1, P5, EF, EL, P3, N0,
  ST_PYR, COLOR1, EL, EK, P2, P3, N0,
  ST_TET, COLOR1, EK, ED, P2, N0,
 // Case #47: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EL, EK, EI, EF, EE,
  ST_PYR, COLOR0, P6, P4, EI, EK, N0,
  ST_TET, COLOR0, P4, EE, EI, N0,
  ST_PYR, COLOR0, P7, P6, EK, EL, N0,
  ST_TET, COLOR0, EF, P7, EL, N0,
  ST_PYR, COLOR0, EE, P4, P7, EF, N0,
  ST_TET, COLOR0, P4, P6, P7, N0,
  ST_PYR, COLOR1, P1, P3, P2, P0, N0,
  ST_TET, COLOR1, P0, P5, P1, N0,
  ST_TET, COLOR1, P5, P3, P1, N0,
  ST_PYR, COLOR1, P5, EF, EL, P3, N0,
  ST_PYR, COLOR1, EL, EK, P2, P3, N0,
  ST_PYR, COLOR1, EK, EI, P0, P2, N0,
  ST_PYR, COLOR1, EI, EE, P5, P0, N0,
  ST_TET, COLOR1, EF, P5, EE, N0,
 // Case #48: (cloned #3)
  ST_HEX, COLOR0, EJ, P1, P0, EI, EF, P7, P6, EH,
  ST_WDG, COLOR0, P1, P3, P7, P0, P2, P6,
  ST_WDG, COLOR1, P5, EJ, EF, P4, EI, EH,
 // Case #49: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EF, EH, EJ, EA, ED,
  ST_PYR, COLOR0, P7, P6, P2, P3, N0,
  ST_TET, COLOR0, P2, N0, P1, P3,
  ST_PYR, COLOR0, P6, EH, ED, P2, N0,
  ST_PYR, COLOR0, EF, EH, P6, P7, N0,
  ST_TET, COLOR0, P1, P7, P3, N0,
  ST_PYR, COLOR0, P1, EJ, EF, P7, N0,
  ST_TET, COLOR0, P1, EA, EJ, N0,
  ST_PYR, COLOR0, ED, EA, P1, P2, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_PYR, COLOR1, EF, P5, P4, EH, N0,
  ST_TET, COLOR1, EJ, P5, EF, N0,
  ST_TET, COLOR1, P5, P0, P4, N0,
  ST_PYR, COLOR1, EJ, EA, P0, P5, N0,
  ST_TET, COLOR1, P0, EA, ED, N0,
 // Case #50: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EH, EF, EI, EA, EB,
  ST_PYR, COLOR0, P6, P2, P3, P7, N0,
  ST_TET, COLOR0, P3, P0, N0, P2,
  ST_PYR, COLOR0, P7, P3, EB, EF, N0,
  ST_PYR, COLOR0, EH, P6, P7, EF, N0,
  ST_TET, COLOR0, P0, P2, P6, N0,
  ST_PYR, COLOR0, P0, P6, EH, EI, N0,
  ST_TET, COLOR0, P0, EI, EA, N0,
  ST_PYR, COLOR0, EB, P3, P0, EA, N0,
  ST_PYR, COLOR1, EF, EB, P1, P5, N0,
  ST_PYR, COLOR1, EH, EF, P5, P4, N0,
  ST_TET, COLOR1, EI, EH, P4, N0,
  ST_TET, COLOR1, P4, P5, P1, N0,
  ST_PYR, COLOR1, EI, P4, P1, EA, N0,
  ST_TET, COLOR1, P1, EB, EA, N0,
 // Case #51: (cloned #15)
  ST_HEX, COLOR0, P2, P3, P7, P6, ED, EB, EF, EH,
  ST_HEX, COLOR1, ED, EB, EF, EH, P0, P1, P5, P4,
 // Case #52: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EF, EJ, EC, EC,
  ST_PYR, COLOR0, P3, EC, ED, P0, N0,
  ST_TET, COLOR0, ED, EI, P0, N0,
  ST_PYR, COLOR0, P1, P0, EI, EJ, N0,
  ST_TET, COLOR0, P3, P0, P1, N0,
  ST_TET, COLOR0, P6, EH, EK, N0,
  ST_TET, COLOR0, P7, P6, P3, N0,
  ST_PYR, COLOR0, P6, EK, EC, P3, N0,
  ST_TET, COLOR0, P7, P3, P1, N0,
  ST_PYR, COLOR0, P7, P1, EJ, EF, N0,
  ST_PYR, COLOR0, P7, EF, EH, P6, N0,
  ST_PYR, COLOR1, P2, P4, EI, ED, N0,
  ST_PYR, COLOR1, EH, P4, P2, EK, N0,
  ST_TET, COLOR1, P2, ED, EC, N0,
  ST_TET, COLOR1, EK, P2, EC, N0,
  ST_PYR, COLOR1, EI, P4, P5, EJ, N0,
  ST_PYR, COLOR1, P4, EH, EF, P5, N0,
  ST_TET, COLOR1, EF, EJ, P5, N0,
 // Case #53: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EA, EJ, EF, EH, EK, EC,
  ST_TET, COLOR0, P7, P6, P3, N0,
  ST_PYR, COLOR0, P7, EF, EH, P6, N0,
  ST_TET, COLOR0, P1, P7, P3, N0,
  ST_PYR, COLOR0, P1, EJ, EF, P7, N0,
  ST_TET, COLOR0, EA, EJ, P1, N0,
  ST_PYR, COLOR0, P1, P3, EC, EA, N0,
  ST_PYR, COLOR0, EC, P3, P6, EK, N0,
  ST_TET, COLOR0, P6, EH, EK, N0,
  ST_TET, COLOR1, P0, P4, P5, N0,
  ST_PYR, COLOR1, EA, P0, P5, EJ, N0,
  ST_PYR, COLOR1, EA, EC, P2, P0, N0,
  ST_TET, COLOR1, P2, P4, P0, N0,
  ST_TET, COLOR1, P2, EC, EK, N0,
  ST_PYR, COLOR1, P2, EK, EH, P4, N0,
  ST_PYR, COLOR1, EH, EF, P5, P4, N0,
  ST_TET, COLOR1, EF, EJ, P5, N0,
 // Case #54: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EF, EB, EH, EK, EC,
  ST_TET, COLOR0, P0, EI, EA, ED,
  ST_PYR, COLOR0, P3, EB, EF, P7, N0,
  ST_PYR, COLOR0, P7, EF, EH, P6, N0,
  ST_TET, COLOR0, P6, EH, EK, N0,
  ST_TET, COLOR0, P7, P6, P3, N0,
  ST_PYR, COLOR0, P6, EK, EC, P3, N0,
  ST_TET, COLOR0, P3, EC, EB, N0,
  ST_WDG, COLOR1, P4, P1, P2, EI, EA, ED,
  ST_TET, COLOR1, P1, P2, P4, N0,
  ST_PYR, COLOR1, P4, P2, EK, EH, N0,
  ST_TET, COLOR1, P5, P1, P4, N0,
  ST_PYR, COLOR1, EB, P1, P5, EF, N0,
  ST_PYR, COLOR1, EF, P5, P4, EH, N0,
  ST_PYR, COLOR1, P2, P1, EB, EC, N0,
  ST_TET, COLOR1, EK, P2, EC, N0,
 // Case #55: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EB, EF, EH, EC, EK,
  ST_PYR, COLOR0, P7, EF, EH, P6, N0,
  ST_TET, COLOR0, P6, EH, EK, N0,
  ST_PYR, COLOR0, P3, EB, EF, P7, N0,
  ST_TET, COLOR0, EC, EB, P3, N0,
  ST_PYR, COLOR0, EK, EC, P3, P6, N0,
  ST_TET, COLOR0, P6, P3, P7, N0,
  ST_PYR, COLOR1, P0, P4, P5, P1, N0,
  ST_TET, COLOR1, P4, P0, P2, N0,
  ST_TET, COLOR1, P2, P0, P1, N0,
  ST_PYR, COLOR1, P2, P1, EB, EC, N0,
  ST_PYR, COLOR1, EB, P1, P5, EF, N0,
  ST_PYR, COLOR1, EF, P5, P4, EH, N0,
  ST_PYR, COLOR1, EH, P4, P2, EK, N0,
  ST_TET, COLOR1, EC, EK, P2, N0,
 // Case #56: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EH, EI, EC, EC,
  ST_PYR, COLOR0, P2, P1, EB, EC, N0,
  ST_TET, COLOR0, EB, P1, EJ, N0,
  ST_PYR, COLOR0, P0, EI, EJ, P1, N0,
  ST_TET, COLOR0, P2, P0, P1, N0,
  ST_TET, COLOR0, P7, EL, EF, N0,
  ST_TET, COLOR0, P6, P2, P7, N0,
  ST_PYR, COLOR0, P7, P2, EC, EL, N0,
  ST_TET, COLOR0, P6, P0, P2, N0,
  ST_PYR, COLOR0, P6, EH, EI, P0, N0,
  ST_PYR, COLOR0, P6, P7, EF, EH, N0,
  ST_PYR, COLOR1, P3, EB, EJ, P5, N0,
  ST_PYR, COLOR1, EF, EL, P3, P5, N0,
  ST_TET, COLOR1, P3, EC, EB, N0,
  ST_TET, COLOR1, EL, EC, P3, N0,
  ST_PYR, COLOR1, EJ, EI, P4, P5, N0,
  ST_PYR, COLOR1, P5, P4, EH, EF, N0,
  ST_TET, COLOR1, EH, P4, EI, N0,
 // Case #57: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EH, EF, ED, EC, EL,
  ST_TET, COLOR0, P1, EA, EJ, EB,
  ST_PYR, COLOR0, P7, EF, EH, P6, N0,
  ST_PYR, COLOR0, P6, EH, ED, P2, N0,
  ST_TET, COLOR0, P2, ED, EC, N0,
  ST_TET, COLOR0, P6, P2, P7, N0,
  ST_PYR, COLOR0, P2, EC, EL, P7, N0,
  ST_TET, COLOR0, P7, EL, EF, N0,
  ST_WDG, COLOR1, P0, P5, P3, EA, EJ, EB,
  ST_TET, COLOR1, P5, P3, P0, N0,
  ST_PYR, COLOR1, P0, P3, EC, ED, N0,
  ST_TET, COLOR1, P4, P5, P0, N0,
  ST_PYR, COLOR1, EF, P5, P4, EH, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_PYR, COLOR1, P3, P5, EF, EL, N0,
  ST_TET, COLOR1, EC, P3, EL, N0,
 // Case #58: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EA, EI, EH, EF, EL, EC,
  ST_TET, COLOR0, P6, P2, P7, N0,
  ST_PYR, COLOR0, P6, P7, EF, EH, N0,
  ST_TET, COLOR0, P0, P2, P6, N0,
  ST_PYR, COLOR0, P0, P6, EH, EI, N0,
  ST_TET, COLOR0, EA, P0, EI, N0,
  ST_PYR, COLOR0, P0, EA, EC, P2, N0,
  ST_PYR, COLOR0, EC, EL, P7, P2, N0,
  ST_TET, COLOR0, P7, EL, EF, N0,
  ST_TET, COLOR1, P1, P4, P5, N0,
  ST_PYR, COLOR1, EA, EI, P4, P1, N0,
  ST_PYR, COLOR1, EA, P1, P3, EC, N0,
  ST_TET, COLOR1, P3, P1, P5, N0,
  ST_TET, COLOR1, P3, EL, EC, N0,
  ST_PYR, COLOR1, P3, P5, EF, EL, N0,
  ST_PYR, COLOR1, EF, P5, P4, EH, N0,
  ST_TET, COLOR1, EH, P4, EI, N0,
 // Case #59: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EF, EH, ED, EL, EC,
  ST_PYR, COLOR0, P6, EH, ED, P2, N0,
  ST_TET, COLOR0, P2, ED, EC, N0,
  ST_PYR, COLOR0, P7, EF, EH, P6, N0,
  ST_TET, COLOR0, EL, EF, P7, N0,
  ST_PYR, COLOR0, EC, EL, P7, P2, N0,
  ST_TET, COLOR0, P2, P7, P6, N0,
  ST_PYR, COLOR1, P1, P0, P4, P5, N0,
  ST_TET, COLOR1, P0, P1, P3, N0,
  ST_TET, COLOR1, P3, P1, P5, N0,
  ST_PYR, COLOR1, P3, P5, EF, EL, N0,
  ST_PYR, COLOR1, EF, P5, P4, EH, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_PYR, COLOR1, ED, P0, P3, EC, N0,
  ST_TET, COLOR1, EL, EC, P3, N0,
 // Case #60: Unique case #14
  ST_WDG, COLOR0, P1, EB, EJ, P0, ED, EI,
  ST_WDG, COLOR0, P7, EF, EL, P6, EH, EK,
  ST_HEX, COLOR1, P2, P4, P5, P3, EK, EH, EF, EL,
  ST_HEX, COLOR1, ED, EI, EJ, EB, P2, P4, P5, P3,
 // Case #61: Unique case #15
  ST_PNT, 0, COLOR1, 6, P0, P3, P2, P4, EF, EH,
  ST_WDG, COLOR0, EH, P6, EK, EF, P7, EL,
  ST_TET, COLOR0, EA, P1, EB, EJ,
  ST_WDG, COLOR1, P0, P5, P3, EA, EJ, EB,
  ST_PYR, COLOR1, EH, EF, P5, P4, N0,
  ST_TET, COLOR1, P4, P5, P0, N0,
  ST_TET, COLOR1, P4, P0, P2, N0,
  ST_PYR, COLOR1, EK, EH, P4, P2, N0,
  ST_PYR, COLOR1, EL, EK, P2, P3, N0,
  ST_TET, COLOR1, P2, P0, P3, N0,
  ST_PYR, COLOR1, EF, EH, EK, EL, N0,
  ST_TET, COLOR1, P3, P0, P5, N0,
  ST_PYR, COLOR1, EF, EL, P3, P5, N0,
 // Case #62: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P1, P2, P3, P5, EH, EF,
  ST_WDG, COLOR0, EH, P6, EK, EF, P7, EL,
  ST_TET, COLOR0, EA, ED, P0, EI,
  ST_WDG, COLOR1, EA, EI, ED, P1, P4, P2,
  ST_PYR, COLOR1, EF, P5, P4, EH, N0,
  ST_TET, COLOR1, P5, P1, P4, N0,
  ST_TET, COLOR1, P5, P3, P1, N0,
  ST_PYR, COLOR1, EL, P3, P5, EF, N0,
  ST_PYR, COLOR1, EK, P2, P3, EL, N0,
  ST_TET, COLOR1, P3, P2, P1, N0,
  ST_PYR, COLOR1, EH, EK, EL, EF, N0,
  ST_TET, COLOR1, P2, P4, P1, N0,
  ST_PYR, COLOR1, EH, P4, P2, EK, N0,
 // Case #63: Unique case #16
  ST_WDG, COLOR0, P6, EK, EH, P7, EL, EF,
  ST_HEX, COLOR1, P2, P4, P5, P3, EK, EH, EF, EL,
  ST_WDG, COLOR1, P1, P3, P5, P0, P2, P4,
 // Case #64: (cloned #1)
  ST_PNT, 0, COLOR0, 7, P7, P5, P4, P2, P3, P1, P0,
  ST_WDG, COLOR0, P7, P4, P2, EG, EH, EK,
  ST_TET, COLOR0, P7, P4, P2, N0,
  ST_TET, COLOR0, P7, P5, P4, N0,
  ST_PYR, COLOR0, P1, P0, P4, P5, N0,
  ST_PYR, COLOR0, P3, P1, P5, P7, N0,
  ST_PYR, COLOR0, P2, P0, P1, P3, N0,
  ST_TET, COLOR0, P4, P0, P2, N0,
  ST_TET, COLOR0, P2, P3, P7, N0,
  ST_TET, COLOR1, P6, EG, EH, EK,
 // Case #65: (cloned #6)
  ST_PNT, 0, NOCOLOR, 2, EA, EG,
  ST_PYR, COLOR0, P1, P5, P7, P3, N0,
  ST_TET, COLOR0, P5, P4, P7, N0,
  ST_TET, COLOR0, P1, P4, P5, N0,
  ST_TET, COLOR0, P2, P1, P3, N0,
  ST_TET, COLOR0, P7, P2, P3, N0,
  ST_PYR, COLOR0, P7, EG, EK, P2, N0,
  ST_PYR, COLOR0, P4, EH, EG, P7, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_PYR, COLOR0, P1, EA, EI, P4, N0,
  ST_PYR, COLOR0, P1, P2, ED, EA, N0,
  ST_TET, COLOR0, P2, EK, ED, N0,
  ST_PYR, COLOR1, P0, ED, EK, P6, N0,
  ST_PYR, COLOR1, EI, P0, P6, EH, N0,
  ST_TET, COLOR1, EH, P6, EG, N0,
  ST_TET, COLOR1, P6, EK, EG, N0,
  ST_TET, COLOR1, EI, P0, N0, EA,
  ST_TET, COLOR1, ED, P0, EA, N0,
 // Case #66: (cloned #24)
  ST_WDG, COLOR0, P4, P2, P7, EH, EK, EG,
  ST_WDG, COLOR0, EA, EB, EJ, P0, P3, P5,
  ST_TET, COLOR0, P2, P7, P4, P3,
  ST_TET, COLOR0, P5, P7, P3, P4,
  ST_TET, COLOR0, P0, P4, P5, P3,
  ST_TET, COLOR0, P2, P0, P3, P4,
  ST_TET, COLOR1, P1, EA, EJ, EB,
  ST_TET, COLOR1, P6, EH, EK, EG,
 // Case #67: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EJ, EB, EG, EG,
  ST_PYR, COLOR0, P7, EG, EK, P2, N0,
  ST_TET, COLOR0, EK, ED, P2, N0,
  ST_PYR, COLOR0, P3, P2, ED, EB, N0,
  ST_TET, COLOR0, P7, P2, P3, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_TET, COLOR0, P5, P4, P7, N0,
  ST_PYR, COLOR0, P4, EH, EG, P7, N0,
  ST_TET, COLOR0, P5, P7, P3, N0,
  ST_PYR, COLOR0, P5, P3, EB, EJ, N0,
  ST_PYR, COLOR0, P5, EJ, EI, P4, N0,
  ST_PYR, COLOR1, P6, P0, ED, EK, N0,
  ST_PYR, COLOR1, EI, P0, P6, EH, N0,
  ST_TET, COLOR1, P6, EK, EG, N0,
  ST_TET, COLOR1, EH, P6, EG, N0,
  ST_PYR, COLOR1, ED, P0, P1, EB, N0,
  ST_PYR, COLOR1, P0, EI, EJ, P1, N0,
  ST_TET, COLOR1, EJ, EB, P1, N0,
 // Case #68: (cloned #3)
  ST_HEX, COLOR0, EH, P4, P0, ED, EG, P7, P3, EC,
  ST_WDG, COLOR0, P4, P5, P7, P0, P1, P3,
  ST_WDG, COLOR1, P6, EH, EG, P2, ED, EC,
 // Case #69: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EG, EC, EH, EI, EA,
  ST_PYR, COLOR0, P7, P3, P1, P5, N0,
  ST_TET, COLOR0, P1, N0, P4, P5,
  ST_PYR, COLOR0, P3, EC, EA, P1, N0,
  ST_PYR, COLOR0, EG, EC, P3, P7, N0,
  ST_TET, COLOR0, P4, P7, P5, N0,
  ST_PYR, COLOR0, P4, EH, EG, P7, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_PYR, COLOR0, EA, EI, P4, P1, N0,
  ST_PYR, COLOR1, EC, P2, P0, EA, N0,
  ST_PYR, COLOR1, EG, P6, P2, EC, N0,
  ST_TET, COLOR1, EH, P6, EG, N0,
  ST_TET, COLOR1, P6, P0, P2, N0,
  ST_PYR, COLOR1, EH, EI, P0, P6, N0,
  ST_TET, COLOR1, P0, EI, EA, N0,
 // Case #70: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EG, EH, EJ, EJ,
  ST_PYR, COLOR0, P5, EJ, EA, P0, N0,
  ST_TET, COLOR0, EA, ED, P0, N0,
  ST_PYR, COLOR0, P4, P0, ED, EH, N0,
  ST_TET, COLOR0, P5, P0, P4, N0,
  ST_TET, COLOR0, P3, EC, EB, N0,
  ST_TET, COLOR0, P7, P3, P5, N0,
  ST_PYR, COLOR0, P3, EB, EJ, P5, N0,
  ST_TET, COLOR0, P7, P5, P4, N0,
  ST_PYR, COLOR0, P7, P4, EH, EG, N0,
  ST_PYR, COLOR0, P7, EG, EC, P3, N0,
  ST_PYR, COLOR1, P1, P2, ED, EA, N0,
  ST_PYR, COLOR1, EC, P2, P1, EB, N0,
  ST_TET, COLOR1, P1, EA, EJ, N0,
  ST_TET, COLOR1, EB, P1, EJ, N0,
  ST_PYR, COLOR1, ED, P2, P6, EH, N0,
  ST_PYR, COLOR1, P2, EC, EG, P6, N0,
  ST_TET, COLOR1, EG, EH, P6, N0,
 // Case #71: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EI, EH, EG, EC, EB, EJ,
  ST_TET, COLOR0, P7, P3, P5, N0,
  ST_PYR, COLOR0, P7, EG, EC, P3, N0,
  ST_TET, COLOR0, P4, P7, P5, N0,
  ST_PYR, COLOR0, P4, EH, EG, P7, N0,
  ST_TET, COLOR0, EI, EH, P4, N0,
  ST_PYR, COLOR0, P4, P5, EJ, EI, N0,
  ST_PYR, COLOR0, EJ, P5, P3, EB, N0,
  ST_TET, COLOR0, P3, EC, EB, N0,
  ST_TET, COLOR1, P0, P2, P6, N0,
  ST_PYR, COLOR1, EI, P0, P6, EH, N0,
  ST_PYR, COLOR1, EI, EJ, P1, P0, N0,
  ST_TET, COLOR1, P1, P2, P0, N0,
  ST_TET, COLOR1, P1, EJ, EB, N0,
  ST_PYR, COLOR1, P1, EB, EC, P2, N0,
  ST_PYR, COLOR1, EC, EG, P6, P2, N0,
  ST_TET, COLOR1, EG, EH, P6, N0,
 // Case #72: (cloned #6)
  ST_PNT, 0, NOCOLOR, 2, EH, EB,
  ST_PYR, COLOR0, P4, P5, P1, P0, N0,
  ST_TET, COLOR0, P5, P7, P1, N0,
  ST_TET, COLOR0, P4, P7, P5, N0,
  ST_TET, COLOR0, P2, P4, P0, N0,
  ST_TET, COLOR0, P1, P2, P0, N0,
  ST_PYR, COLOR0, P1, EB, EC, P2, N0,
  ST_PYR, COLOR0, P7, EL, EB, P1, N0,
  ST_TET, COLOR0, P7, EG, EL, N0,
  ST_PYR, COLOR0, P4, EH, EG, P7, N0,
  ST_PYR, COLOR0, P4, P2, EK, EH, N0,
  ST_TET, COLOR0, P2, EC, EK, N0,
  ST_PYR, COLOR1, P6, EK, EC, P3, N0,
  ST_PYR, COLOR1, EG, P6, P3, EL, N0,
  ST_TET, COLOR1, EL, P3, EB, N0,
  ST_TET, COLOR1, P3, EC, EB, N0,
  ST_TET, COLOR1, EG, P6, N0, EH,
  ST_TET, COLOR1, EK, P6, EH, N0,
 // Case #73: (cloned #22)
  ST_TET, COLOR0, P2, ED, EC, EK,
  ST_TET, COLOR0, P7, P5, P4, P1,
  ST_PYR, COLOR0, EA, EI, P4, P1, EH,
  ST_PYR, COLOR0, EB, P1, P7, EL, EG,
  ST_PYR, COLOR0, P4, EH, EG, P7, P1,
  ST_PYR, COLOR0, EH, EA, EB, EG, P1,
  ST_WDG, COLOR1, P0, P3, P6, ED, EC, EK,
  ST_PYR, COLOR1, P0, P6, EH, EI, EA,
  ST_PYR, COLOR1, EG, P6, P3, EL, EB,
  ST_PYR, COLOR1, EA, EB, P3, P0, P6,
  ST_PYR, COLOR1, EA, EH, EG, EB, P6,
 // Case #74: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EJ, EA, EH, EH,
  ST_PYR, COLOR0, P4, P2, EK, EH, N0,
  ST_TET, COLOR0, EK, P2, EC, N0,
  ST_PYR, COLOR0, P0, EA, EC, P2, N0,
  ST_TET, COLOR0, P4, P0, P2, N0,
  ST_TET, COLOR0, P7, EG, EL, N0,
  ST_TET, COLOR0, P5, P4, P7, N0,
  ST_PYR, COLOR0, P7, P4, EH, EG, N0,
  ST_TET, COLOR0, P5, P0, P4, N0,
  ST_PYR, COLOR0, P5, EJ, EA, P0, N0,
  ST_PYR, COLOR0, P5, P7, EL, EJ, N0,
  ST_PYR, COLOR1, P6, EK, EC, P3, N0,
  ST_PYR, COLOR1, EL, EG, P6, P3, N0,
  ST_TET, COLOR1, P6, EH, EK, N0,
  ST_TET, COLOR1, EG, EH, P6, N0,
  ST_PYR, COLOR1, EC, EA, P1, P3, N0,
  ST_PYR, COLOR1, P3, P1, EJ, EL, N0,
  ST_TET, COLOR1, EJ, P1, EA, N0,
 // Case #75: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EJ, EL, EI, EH, EG,
  ST_TET, COLOR0, P2, ED, EC, EK,
  ST_PYR, COLOR0, P7, EL, EJ, P5, N0,
  ST_PYR, COLOR0, P5, EJ, EI, P4, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_TET, COLOR0, P5, P4, P7, N0,
  ST_PYR, COLOR0, P4, EH, EG, P7, N0,
  ST_TET, COLOR0, P7, EG, EL, N0,
  ST_WDG, COLOR1, P0, P3, P6, ED, EC, EK,
  ST_TET, COLOR1, P3, P6, P0, N0,
  ST_PYR, COLOR1, P0, P6, EH, EI, N0,
  ST_TET, COLOR1, P1, P3, P0, N0,
  ST_PYR, COLOR1, EL, P3, P1, EJ, N0,
  ST_PYR, COLOR1, EJ, P1, P0, EI, N0,
  ST_PYR, COLOR1, P6, P3, EL, EG, N0,
  ST_TET, COLOR1, EH, P6, EG, N0,
 // Case #76: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EB, ED, EL, EG, EH,
  ST_PYR, COLOR0, P1, P0, P4, P5, N0,
  ST_TET, COLOR0, P4, N0, P7, P5,
  ST_PYR, COLOR0, P0, ED, EH, P4, N0,
  ST_PYR, COLOR0, EB, ED, P0, P1, N0,
  ST_TET, COLOR0, P7, P1, P5, N0,
  ST_PYR, COLOR0, P7, EL, EB, P1, N0,
  ST_TET, COLOR0, P7, EG, EL, N0,
  ST_PYR, COLOR0, EH, EG, P7, P4, N0,
  ST_PYR, COLOR1, ED, P2, P6, EH, N0,
  ST_PYR, COLOR1, EB, P3, P2, ED, N0,
  ST_TET, COLOR1, EL, P3, EB, N0,
  ST_TET, COLOR1, P3, P6, P2, N0,
  ST_PYR, COLOR1, EL, EG, P6, P3, N0,
  ST_TET, COLOR1, P6, EG, EH, N0,
 // Case #77: (cloned #23)
  ST_TET, COLOR0, P7, P5, P4, P1,
  ST_PYR, COLOR0, EA, EI, P4, P1, EH,
  ST_PYR, COLOR0, EB, P1, P7, EL, EG,
  ST_PYR, COLOR0, P4, EH, EG, P7, P1,
  ST_PYR, COLOR0, EH, EA, EB, EG, P1,
  ST_TET, COLOR1, P2, P0, P3, P6,
  ST_PYR, COLOR1, EH, EI, P0, P6, EA,
  ST_PYR, COLOR1, EG, P6, P3, EL, EB,
  ST_PYR, COLOR1, P0, EA, EB, P3, P6,
  ST_PYR, COLOR1, EH, EG, EB, EA, P6,
 // Case #78: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, ED, EA, EJ, EL, EG, EH,
  ST_TET, COLOR0, P5, P4, P7, N0,
  ST_PYR, COLOR0, P5, P7, EL, EJ, N0,
  ST_TET, COLOR0, P0, P4, P5, N0,
  ST_PYR, COLOR0, P0, P5, EJ, EA, N0,
  ST_TET, COLOR0, ED, P0, EA, N0,
  ST_PYR, COLOR0, P0, ED, EH, P4, N0,
  ST_PYR, COLOR0, EH, EG, P7, P4, N0,
  ST_TET, COLOR0, P7, EG, EL, N0,
  ST_TET, COLOR1, P2, P1, P3, N0,
  ST_PYR, COLOR1, ED, EA, P1, P2, N0,
  ST_PYR, COLOR1, ED, P2, P6, EH, N0,
  ST_TET, COLOR1, P6, P2, P3, N0,
  ST_TET, COLOR1, P6, EG, EH, N0,
  ST_PYR, COLOR1, P6, P3, EL, EG, N0,
  ST_PYR, COLOR1, EL, P3, P1, EJ, N0,
  ST_TET, COLOR1, EJ, P1, EA, N0,
 // Case #79: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EL, EJ, EI, EG, EH,
  ST_PYR, COLOR0, P5, EJ, EI, P4, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_PYR, COLOR0, P7, EL, EJ, P5, N0,
  ST_TET, COLOR0, EG, EL, P7, N0,
  ST_PYR, COLOR0, EH, EG, P7, P4, N0,
  ST_TET, COLOR0, P4, P7, P5, N0,
  ST_PYR, COLOR1, P2, P0, P1, P3, N0,
  ST_TET, COLOR1, P0, P2, P6, N0,
  ST_TET, COLOR1, P6, P2, P3, N0,
  ST_PYR, COLOR1, P6, P3, EL, EG, N0,
  ST_PYR, COLOR1, EL, P3, P1, EJ, N0,
  ST_PYR, COLOR1, EJ, P1, P0, EI, N0,
  ST_PYR, COLOR1, EI, P0, P6, EH, N0,
  ST_TET, COLOR1, EG, EH, P6, N0,
 // Case #80: (cloned #3)
  ST_HEX, COLOR0, EG, P7, P5, EE, EK, P2, P0, EI,
  ST_WDG, COLOR0, P0, P1, P5, P2, P3, P7,
  ST_WDG, COLOR1, P4, EI, EE, P6, EK, EG,
 // Case #81: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EG, EE, EK, ED, EA,
  ST_PYR, COLOR0, P7, P3, P1, P5, N0,
  ST_TET, COLOR0, P1, P2, N0, P3,
  ST_PYR, COLOR0, P5, P1, EA, EE, N0,
  ST_PYR, COLOR0, EG, P7, P5, EE, N0,
  ST_TET, COLOR0, P2, P3, P7, N0,
  ST_PYR, COLOR0, P2, P7, EG, EK, N0,
  ST_TET, COLOR0, P2, EK, ED, N0,
  ST_PYR, COLOR0, EA, P1, P2, ED, N0,
  ST_PYR, COLOR1, EE, EA, P0, P4, N0,
  ST_PYR, COLOR1, EG, EE, P4, P6, N0,
  ST_TET, COLOR1, EK, EG, P6, N0,
  ST_TET, COLOR1, P6, P4, P0, N0,
  ST_PYR, COLOR1, EK, P6, P0, ED, N0,
  ST_TET, COLOR1, P0, EA, ED, N0,
 // Case #82: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EG, EK, EB, EB,
  ST_PYR, COLOR0, P3, P0, EA, EB, N0,
  ST_TET, COLOR0, EA, P0, EI, N0,
  ST_PYR, COLOR0, P2, EK, EI, P0, N0,
  ST_TET, COLOR0, P3, P2, P0, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_TET, COLOR0, P7, P3, P5, N0,
  ST_PYR, COLOR0, P5, P3, EB, EJ, N0,
  ST_TET, COLOR0, P7, P2, P3, N0,
  ST_PYR, COLOR0, P7, EG, EK, P2, N0,
  ST_PYR, COLOR0, P7, P5, EE, EG, N0,
  ST_PYR, COLOR1, P1, EA, EI, P4, N0,
  ST_PYR, COLOR1, EE, EJ, P1, P4, N0,
  ST_TET, COLOR1, P1, EB, EA, N0,
  ST_TET, COLOR1, EJ, EB, P1, N0,
  ST_PYR, COLOR1, EI, EK, P6, P4, N0,
  ST_PYR, COLOR1, P4, P6, EG, EE, N0,
  ST_TET, COLOR1, EG, P6, EK, N0,
 // Case #83: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, ED, EK, EG, EE, EJ, EB,
  ST_TET, COLOR0, P7, P3, P5, N0,
  ST_PYR, COLOR0, P7, P5, EE, EG, N0,
  ST_TET, COLOR0, P2, P3, P7, N0,
  ST_PYR, COLOR0, P2, P7, EG, EK, N0,
  ST_TET, COLOR0, ED, P2, EK, N0,
  ST_PYR, COLOR0, P2, ED, EB, P3, N0,
  ST_PYR, COLOR0, EB, EJ, P5, P3, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_TET, COLOR1, P0, P6, P4, N0,
  ST_PYR, COLOR1, ED, EK, P6, P0, N0,
  ST_PYR, COLOR1, ED, P0, P1, EB, N0,
  ST_TET, COLOR1, P1, P0, P4, N0,
  ST_TET, COLOR1, P1, EJ, EB, N0,
  ST_PYR, COLOR1, P1, P4, EE, EJ, N0,
  ST_PYR, COLOR1, EE, P4, P6, EG, N0,
  ST_TET, COLOR1, EG, P6, EK, N0,
 // Case #84: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EC, EG, ED, EI, EE,
  ST_PYR, COLOR0, P3, P1, P5, P7, N0,
  ST_TET, COLOR0, P5, P0, N0, P1,
  ST_PYR, COLOR0, P7, P5, EE, EG, N0,
  ST_PYR, COLOR0, EC, P3, P7, EG, N0,
  ST_TET, COLOR0, P0, P1, P3, N0,
  ST_PYR, COLOR0, P0, P3, EC, ED, N0,
  ST_TET, COLOR0, P0, ED, EI, N0,
  ST_PYR, COLOR0, EE, P5, P0, EI, N0,
  ST_PYR, COLOR1, EG, EE, P4, P6, N0,
  ST_PYR, COLOR1, EC, EG, P6, P2, N0,
  ST_TET, COLOR1, ED, EC, P2, N0,
  ST_TET, COLOR1, P2, P6, P4, N0,
  ST_PYR, COLOR1, ED, P2, P4, EI, N0,
  ST_TET, COLOR1, P4, EE, EI, N0,
 // Case #85: (cloned #15)
  ST_HEX, COLOR0, P1, P5, P7, P3, EA, EE, EG, EC,
  ST_HEX, COLOR1, EA, EE, EG, EC, P0, P4, P6, P2,
 // Case #86: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EG, EE, EC, EB, EJ,
  ST_TET, COLOR0, P0, ED, EI, EA,
  ST_PYR, COLOR0, P5, EE, EG, P7, N0,
  ST_PYR, COLOR0, P7, EG, EC, P3, N0,
  ST_TET, COLOR0, P3, EC, EB, N0,
  ST_TET, COLOR0, P7, P3, P5, N0,
  ST_PYR, COLOR0, P3, EB, EJ, P5, N0,
  ST_TET, COLOR0, P5, EJ, EE, N0,
  ST_WDG, COLOR1, P2, P4, P1, ED, EI, EA,
  ST_TET, COLOR1, P4, P1, P2, N0,
  ST_PYR, COLOR1, P2, P1, EB, EC, N0,
  ST_TET, COLOR1, P6, P4, P2, N0,
  ST_PYR, COLOR1, EE, P4, P6, EG, N0,
  ST_PYR, COLOR1, EG, P6, P2, EC, N0,
  ST_PYR, COLOR1, P1, P4, EE, EJ, N0,
  ST_TET, COLOR1, EB, P1, EJ, N0,
 // Case #87: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EE, EG, EC, EJ, EB,
  ST_PYR, COLOR0, P7, EG, EC, P3, N0,
  ST_TET, COLOR0, P3, EC, EB, N0,
  ST_PYR, COLOR0, P5, EE, EG, P7, N0,
  ST_TET, COLOR0, EJ, EE, P5, N0,
  ST_PYR, COLOR0, EB, EJ, P5, P3, N0,
  ST_TET, COLOR0, P3, P5, P7, N0,
  ST_PYR, COLOR1, P0, P2, P6, P4, N0,
  ST_TET, COLOR1, P2, P0, P1, N0,
  ST_TET, COLOR1, P1, P0, P4, N0,
  ST_PYR, COLOR1, P1, P4, EE, EJ, N0,
  ST_PYR, COLOR1, EE, P4, P6, EG, N0,
  ST_PYR, COLOR1, EG, P6, P2, EC, N0,
  ST_PYR, COLOR1, EC, P2, P1, EB, N0,
  ST_TET, COLOR1, EJ, EB, P1, N0,
 // Case #88: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EE, EI, EB, EB,
  ST_PYR, COLOR0, P1, EB, EC, P2, N0,
  ST_TET, COLOR0, EC, EK, P2, N0,
  ST_PYR, COLOR0, P0, P2, EK, EI, N0,
  ST_TET, COLOR0, P1, P2, P0, N0,
  ST_TET, COLOR0, P7, EG, EL, N0,
  ST_TET, COLOR0, P5, P7, P1, N0,
  ST_PYR, COLOR0, P7, EL, EB, P1, N0,
  ST_TET, COLOR0, P5, P1, P0, N0,
  ST_PYR, COLOR0, P5, P0, EI, EE, N0,
  ST_PYR, COLOR0, P5, EE, EG, P7, N0,
  ST_PYR, COLOR1, P3, P6, EK, EC, N0,
  ST_PYR, COLOR1, EG, P6, P3, EL, N0,
  ST_TET, COLOR1, P3, EC, EB, N0,
  ST_TET, COLOR1, EL, P3, EB, N0,
  ST_PYR, COLOR1, EK, P6, P4, EI, N0,
  ST_PYR, COLOR1, P6, EG, EE, P4, N0,
  ST_TET, COLOR1, EE, EI, P4, N0,
 // Case #89: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EE, EG, EA, EB, EL,
  ST_TET, COLOR0, P2, EK, ED, EC,
  ST_PYR, COLOR0, P7, P5, EE, EG, N0,
  ST_PYR, COLOR0, P5, P1, EA, EE, N0,
  ST_TET, COLOR0, P1, EB, EA, N0,
  ST_TET, COLOR0, P5, P7, P1, N0,
  ST_PYR, COLOR0, P1, P7, EL, EB, N0,
  ST_TET, COLOR0, P7, EG, EL, N0,
  ST_WDG, COLOR1, ED, EK, EC, P0, P6, P3,
  ST_TET, COLOR1, P6, P0, P3, N0,
  ST_PYR, COLOR1, P0, EA, EB, P3, N0,
  ST_TET, COLOR1, P4, P0, P6, N0,
  ST_PYR, COLOR1, EG, EE, P4, P6, N0,
  ST_PYR, COLOR1, EE, EA, P0, P4, N0,
  ST_PYR, COLOR1, P3, EL, EG, P6, N0,
  ST_TET, COLOR1, EB, EL, P3, N0,
 // Case #90: (cloned #60)
  ST_WDG, COLOR0, P2, EK, EC, P0, EI, EA,
  ST_WDG, COLOR0, P7, EL, EG, P5, EJ, EE,
  ST_HEX, COLOR1, P4, P1, P3, P6, EE, EJ, EL, EG,
  ST_HEX, COLOR1, EI, EA, EC, EK, P4, P1, P3, P6,
 // Case #91: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P0, P6, P4, P1, EL, EJ,
  ST_WDG, COLOR0, EJ, P5, EE, EL, P7, EG,
  ST_TET, COLOR0, ED, P2, EK, EC,
  ST_WDG, COLOR1, P0, P3, P6, ED, EC, EK,
  ST_PYR, COLOR1, EJ, EL, P3, P1, N0,
  ST_TET, COLOR1, P1, P3, P0, N0,
  ST_TET, COLOR1, P1, P0, P4, N0,
  ST_PYR, COLOR1, EE, EJ, P1, P4, N0,
  ST_PYR, COLOR1, EG, EE, P4, P6, N0,
  ST_TET, COLOR1, P4, P0, P6, N0,
  ST_PYR, COLOR1, EL, EJ, EE, EG, N0,
  ST_TET, COLOR1, P6, P0, P3, N0,
  ST_PYR, COLOR1, EL, EG, P6, P3, N0,
 // Case #92: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, ED, EI, EE, EG, EL, EB,
  ST_TET, COLOR0, P5, P7, P1, N0,
  ST_PYR, COLOR0, P5, EE, EG, P7, N0,
  ST_TET, COLOR0, P0, P5, P1, N0,
  ST_PYR, COLOR0, P0, EI, EE, P5, N0,
  ST_TET, COLOR0, ED, EI, P0, N0,
  ST_PYR, COLOR0, P0, P1, EB, ED, N0,
  ST_PYR, COLOR0, EB, P1, P7, EL, N0,
  ST_TET, COLOR0, P7, EG, EL, N0,
  ST_TET, COLOR1, P2, P6, P4, N0,
  ST_PYR, COLOR1, ED, P2, P4, EI, N0,
  ST_PYR, COLOR1, ED, EB, P3, P2, N0,
  ST_TET, COLOR1, P3, P6, P2, N0,
  ST_TET, COLOR1, P3, EB, EL, N0,
  ST_PYR, COLOR1, P3, EL, EG, P6, N0,
  ST_PYR, COLOR1, EG, EE, P4, P6, N0,
  ST_TET, COLOR1, EE, EI, P4, N0,
 // Case #93: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EG, EE, EA, EL, EB,
  ST_PYR, COLOR0, P5, P1, EA, EE, N0,
  ST_TET, COLOR0, P1, EB, EA, N0,
  ST_PYR, COLOR0, P7, P5, EE, EG, N0,
  ST_TET, COLOR0, EL, P7, EG, N0,
  ST_PYR, COLOR0, EB, P1, P7, EL, N0,
  ST_TET, COLOR0, P1, P5, P7, N0,
  ST_PYR, COLOR1, P2, P6, P4, P0, N0,
  ST_TET, COLOR1, P0, P3, P2, N0,
  ST_TET, COLOR1, P3, P6, P2, N0,
  ST_PYR, COLOR1, P3, EL, EG, P6, N0,
  ST_PYR, COLOR1, EG, EE, P4, P6, N0,
  ST_PYR, COLOR1, EE, EA, P0, P4, N0,
  ST_PYR, COLOR1, EA, EB, P3, P0, N0,
  ST_TET, COLOR1, EL, P3, EB, N0,
 // Case #94: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P2, P4, P6, P3, EJ, EL,
  ST_WDG, COLOR0, EJ, P5, EE, EL, P7, EG,
  ST_TET, COLOR0, ED, EI, P0, EA,
  ST_WDG, COLOR1, ED, EA, EI, P2, P1, P4,
  ST_PYR, COLOR1, EL, P3, P1, EJ, N0,
  ST_TET, COLOR1, P3, P2, P1, N0,
  ST_TET, COLOR1, P3, P6, P2, N0,
  ST_PYR, COLOR1, EG, P6, P3, EL, N0,
  ST_PYR, COLOR1, EE, P4, P6, EG, N0,
  ST_TET, COLOR1, P6, P4, P2, N0,
  ST_PYR, COLOR1, EJ, EE, EG, EL, N0,
  ST_TET, COLOR1, P4, P1, P2, N0,
  ST_PYR, COLOR1, EJ, P1, P4, EE, N0,
 // Case #95: (cloned #63)
  ST_WDG, COLOR0, P5, EE, EJ, P7, EG, EL,
  ST_HEX, COLOR1, P4, P1, P3, P6, EE, EJ, EL, EG,
  ST_WDG, COLOR1, P2, P6, P3, P0, P4, P1,
 // Case #96: (cloned #6)
  ST_PNT, 0, NOCOLOR, 2, EK, EJ,
  ST_PYR, COLOR0, P2, P0, P1, P3, N0,
  ST_TET, COLOR0, P3, P1, P7, N0,
  ST_TET, COLOR0, P2, P3, P7, N0,
  ST_TET, COLOR0, P4, P0, P2, N0,
  ST_TET, COLOR0, P1, P0, P4, N0,
  ST_PYR, COLOR0, P1, P4, EE, EJ, N0,
  ST_PYR, COLOR0, P7, P1, EJ, EF, N0,
  ST_TET, COLOR0, P7, EF, EG, N0,
  ST_PYR, COLOR0, P2, P7, EG, EK, N0,
  ST_PYR, COLOR0, P2, EK, EH, P4, N0,
  ST_TET, COLOR0, P4, EH, EE, N0,
  ST_PYR, COLOR1, P6, P5, EE, EH, N0,
  ST_PYR, COLOR1, EG, EF, P5, P6, N0,
  ST_TET, COLOR1, EF, EJ, P5, N0,
  ST_TET, COLOR1, P5, EJ, EE, N0,
  ST_TET, COLOR1, EG, N0, P6, EK,
  ST_TET, COLOR1, EH, EK, P6, N0,
 // Case #97: (cloned #22)
  ST_TET, COLOR0, P4, EE, EI, EH,
  ST_TET, COLOR0, P7, P2, P3, P1,
  ST_PYR, COLOR0, EA, P1, P2, ED, EK,
  ST_PYR, COLOR0, EJ, EF, P7, P1, EG,
  ST_PYR, COLOR0, P2, P7, EG, EK, P1,
  ST_PYR, COLOR0, EK, EG, EJ, EA, P1,
  ST_WDG, COLOR1, EI, EE, EH, P0, P5, P6,
  ST_PYR, COLOR1, P0, ED, EK, P6, EA,
  ST_PYR, COLOR1, EG, EF, P5, P6, EJ,
  ST_PYR, COLOR1, EA, P0, P5, EJ, P6,
  ST_PYR, COLOR1, EA, EJ, EG, EK, P6,
 // Case #98: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EB, EA, EK, EK,
  ST_PYR, COLOR0, P2, EK, EH, P4, N0,
  ST_TET, COLOR0, EH, EE, P4, N0,
  ST_PYR, COLOR0, P0, P4, EE, EA, N0,
  ST_TET, COLOR0, P2, P4, P0, N0,
  ST_TET, COLOR0, P7, EF, EG, N0,
  ST_TET, COLOR0, P3, P7, P2, N0,
  ST_PYR, COLOR0, P7, EG, EK, P2, N0,
  ST_TET, COLOR0, P3, P2, P0, N0,
  ST_PYR, COLOR0, P3, P0, EA, EB, N0,
  ST_PYR, COLOR0, P3, EB, EF, P7, N0,
  ST_PYR, COLOR1, P6, P5, EE, EH, N0,
  ST_PYR, COLOR1, EF, P5, P6, EG, N0,
  ST_TET, COLOR1, P6, EH, EK, N0,
  ST_TET, COLOR1, EG, P6, EK, N0,
  ST_PYR, COLOR1, EE, P5, P1, EA, N0,
  ST_PYR, COLOR1, P5, EF, EB, P1, N0,
  ST_TET, COLOR1, EB, EA, P1, N0,
 // Case #99: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EB, EF, ED, EK, EG,
  ST_TET, COLOR0, P4, EE, EI, EH,
  ST_PYR, COLOR0, P7, P3, EB, EF, N0,
  ST_PYR, COLOR0, P3, P2, ED, EB, N0,
  ST_TET, COLOR0, P2, EK, ED, N0,
  ST_TET, COLOR0, P3, P7, P2, N0,
  ST_PYR, COLOR0, P2, P7, EG, EK, N0,
  ST_TET, COLOR0, P7, EF, EG, N0,
  ST_WDG, COLOR1, EI, EE, EH, P0, P5, P6,
  ST_TET, COLOR1, P5, P0, P6, N0,
  ST_PYR, COLOR1, P0, ED, EK, P6, N0,
  ST_TET, COLOR1, P1, P0, P5, N0,
  ST_PYR, COLOR1, EF, EB, P1, P5, N0,
  ST_PYR, COLOR1, EB, ED, P0, P1, N0,
  ST_PYR, COLOR1, P6, EG, EF, P5, N0,
  ST_TET, COLOR1, EK, EG, P6, N0,
 // Case #100: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EC, ED, EJ, EJ,
  ST_PYR, COLOR0, P1, P4, EE, EJ, N0,
  ST_TET, COLOR0, EE, P4, EH, N0,
  ST_PYR, COLOR0, P0, ED, EH, P4, N0,
  ST_TET, COLOR0, P1, P0, P4, N0,
  ST_TET, COLOR0, P7, EF, EG, N0,
  ST_TET, COLOR0, P3, P1, P7, N0,
  ST_PYR, COLOR0, P7, P1, EJ, EF, N0,
  ST_TET, COLOR0, P3, P0, P1, N0,
  ST_PYR, COLOR0, P3, EC, ED, P0, N0,
  ST_PYR, COLOR0, P3, P7, EG, EC, N0,
  ST_PYR, COLOR1, P5, EE, EH, P6, N0,
  ST_PYR, COLOR1, EG, EF, P5, P6, N0,
  ST_TET, COLOR1, P5, EJ, EE, N0,
  ST_TET, COLOR1, EF, EJ, P5, N0,
  ST_PYR, COLOR1, EH, ED, P2, P6, N0,
  ST_PYR, COLOR1, P6, P2, EC, EG, N0,
  ST_TET, COLOR1, EC, P2, ED, N0,
 // Case #101: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EC, EG, EA, EJ, EF,
  ST_TET, COLOR0, P4, EI, EH, EE,
  ST_PYR, COLOR0, P7, EG, EC, P3, N0,
  ST_PYR, COLOR0, P3, EC, EA, P1, N0,
  ST_TET, COLOR0, P1, EA, EJ, N0,
  ST_TET, COLOR0, P3, P1, P7, N0,
  ST_PYR, COLOR0, P1, EJ, EF, P7, N0,
  ST_TET, COLOR0, P7, EF, EG, N0,
  ST_WDG, COLOR1, P0, P6, P5, EI, EH, EE,
  ST_TET, COLOR1, P6, P5, P0, N0,
  ST_PYR, COLOR1, P0, P5, EJ, EA, N0,
  ST_TET, COLOR1, P2, P6, P0, N0,
  ST_PYR, COLOR1, EG, P6, P2, EC, N0,
  ST_PYR, COLOR1, EC, P2, P0, EA, N0,
  ST_PYR, COLOR1, P5, P6, EG, EF, N0,
  ST_TET, COLOR1, EJ, P5, EF, N0,
 // Case #102: (cloned #60)
  ST_WDG, COLOR0, P0, ED, EA, P4, EH, EE,
  ST_WDG, COLOR0, P3, EB, EC, P7, EF, EG,
  ST_HEX, COLOR1, EC, EB, EF, EG, P2, P1, P5, P6,
  ST_HEX, COLOR1, P2, P1, P5, P6, ED, EA, EE, EH,
 // Case #103: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P0, P6, P2, P1, EF, EB,
  ST_WDG, COLOR0, EF, P7, EG, EB, P3, EC,
  ST_TET, COLOR0, EI, EH, P4, EE,
  ST_WDG, COLOR1, EI, EE, EH, P0, P5, P6,
  ST_PYR, COLOR1, EB, P1, P5, EF, N0,
  ST_TET, COLOR1, P1, P0, P5, N0,
  ST_TET, COLOR1, P1, P2, P0, N0,
  ST_PYR, COLOR1, EC, P2, P1, EB, N0,
  ST_PYR, COLOR1, EG, P6, P2, EC, N0,
  ST_TET, COLOR1, P2, P6, P0, N0,
  ST_PYR, COLOR1, EF, EG, EC, EB, N0,
  ST_TET, COLOR1, P6, P5, P0, N0,
  ST_PYR, COLOR1, EF, P5, P6, EG, N0,
 // Case #104: (cloned #22)
  ST_TET, COLOR0, P7, EF, EG, EL,
  ST_TET, COLOR0, P2, P0, P1, P4,
  ST_PYR, COLOR0, EE, EJ, P1, P4, EB,
  ST_PYR, COLOR0, EH, P4, P2, EK, EC,
  ST_PYR, COLOR0, P1, EB, EC, P2, P4,
  ST_PYR, COLOR0, EB, EE, EH, EC, P4,
  ST_WDG, COLOR1, P5, P6, P3, EF, EG, EL,
  ST_PYR, COLOR1, P5, P3, EB, EJ, EE,
  ST_PYR, COLOR1, EC, P3, P6, EK, EH,
  ST_PYR, COLOR1, EE, EH, P6, P5, P3,
  ST_PYR, COLOR1, EE, EB, EC, EH, P3,
 // Case #105: Unique case #17
  ST_TET, COLOR0, EH, EI, EE, P4,
  ST_TET, COLOR0, EK, EC, ED, P2,
  ST_TET, COLOR0, EG, EF, EL, P7,
  ST_TET, COLOR0, EB, EJ, EA, P1,
  ST_WDG, COLOR1, P3, P0, P5, EB, EA, EJ,
  ST_TET, COLOR1, P3, P0, P5, P6,
  ST_WDG, COLOR1, EG, EF, EL, P6, P5, P3,
  ST_WDG, COLOR1, ED, EK, EC, P0, P6, P3,
  ST_WDG, COLOR1, EE, EH, EI, P5, P6, P0,
 // Case #106: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EA, EC, EE, EH, EK,
  ST_TET, COLOR0, P7, EL, EF, EG,
  ST_PYR, COLOR0, P2, P0, EA, EC, N0,
  ST_PYR, COLOR0, P0, P4, EE, EA, N0,
  ST_TET, COLOR0, P4, EH, EE, N0,
  ST_TET, COLOR0, P0, P2, P4, N0,
  ST_PYR, COLOR0, P4, P2, EK, EH, N0,
  ST_TET, COLOR0, P2, EC, EK, N0,
  ST_WDG, COLOR1, EF, EL, EG, P5, P3, P6,
  ST_TET, COLOR1, P3, P5, P6, N0,
  ST_PYR, COLOR1, P5, EE, EH, P6, N0,
  ST_TET, COLOR1, P1, P5, P3, N0,
  ST_PYR, COLOR1, EC, EA, P1, P3, N0,
  ST_PYR, COLOR1, EA, EE, P5, P1, N0,
  ST_PYR, COLOR1, P6, EK, EC, P3, N0,
  ST_TET, COLOR1, EH, EK, P6, N0,
 // Case #107: Unique case #18
  ST_TET, COLOR0, EL, EG, EF, P7,
  ST_TET, COLOR0, EC, P2, ED, EK,
  ST_TET, COLOR0, EE, EI, P4, EH,
  ST_WDG, COLOR1, EH, EI, EE, P6, P0, P5,
  ST_WDG, COLOR1, P3, P6, P0, EC, EK, ED,
  ST_WDG, COLOR1, EL, EG, EF, P3, P6, P5,
  ST_TET, COLOR1, P6, P5, P0, P3,
  ST_TET, COLOR1, P3, P1, P5, P0,
 // Case #108: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, ED, EH, EB, EJ, EE,
  ST_TET, COLOR0, P7, EG, EL, EF,
  ST_PYR, COLOR0, P4, P0, ED, EH, N0,
  ST_PYR, COLOR0, P0, P1, EB, ED, N0,
  ST_TET, COLOR0, P1, EJ, EB, N0,
  ST_TET, COLOR0, P0, P4, P1, N0,
  ST_PYR, COLOR0, P1, P4, EE, EJ, N0,
  ST_TET, COLOR0, P4, EH, EE, N0,
  ST_WDG, COLOR1, EL, EG, EF, P3, P6, P5,
  ST_TET, COLOR1, P6, P3, P5, N0,
  ST_PYR, COLOR1, P3, EB, EJ, P5, N0,
  ST_TET, COLOR1, P2, P3, P6, N0,
  ST_PYR, COLOR1, EH, ED, P2, P6, N0,
  ST_PYR, COLOR1, ED, EB, P3, P2, N0,
  ST_PYR, COLOR1, P5, EE, EH, P6, N0,
  ST_TET, COLOR1, EJ, EE, P5, N0,
 // Case #109: (cloned #107)
  ST_TET, COLOR0, EG, EF, EL, P7,
  ST_TET, COLOR0, EH, P4, EI, EE,
  ST_TET, COLOR0, EB, EA, P1, EJ,
  ST_WDG, COLOR1, EJ, EA, EB, P5, P0, P3,
  ST_WDG, COLOR1, P6, P5, P0, EH, EE, EI,
  ST_WDG, COLOR1, EG, EF, EL, P6, P5, P3,
  ST_TET, COLOR1, P5, P3, P0, P6,
  ST_TET, COLOR1, P6, P2, P3, P0,
 // Case #110: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P3, P5, P1, P2, EH, ED,
  ST_WDG, COLOR0, EH, P4, EE, ED, P0, EA,
  ST_TET, COLOR0, EL, EF, P7, EG,
  ST_WDG, COLOR1, EL, EG, EF, P3, P6, P5,
  ST_PYR, COLOR1, ED, P2, P6, EH, N0,
  ST_TET, COLOR1, P2, P3, P6, N0,
  ST_TET, COLOR1, P2, P1, P3, N0,
  ST_PYR, COLOR1, EA, P1, P2, ED, N0,
  ST_PYR, COLOR1, EE, P5, P1, EA, N0,
  ST_TET, COLOR1, P1, P5, P3, N0,
  ST_PYR, COLOR1, EH, EE, EA, ED, N0,
  ST_TET, COLOR1, P5, P6, P3, N0,
  ST_PYR, COLOR1, EH, P6, P5, EE, N0,
 // Case #111: Unique case #19
  ST_TET, COLOR0, EE, EH, EI, P4,
  ST_TET, COLOR0, EF, EL, EG, P7,
  ST_WDG, COLOR1, EG, EF, EL, P6, P5, P3,
  ST_WDG, COLOR1, P6, P5, P0, EH, EE, EI,
  ST_PYR, COLOR1, P2, P0, P1, P3, P6,
  ST_TET, COLOR1, P5, P1, P0, P6,
  ST_TET, COLOR1, P5, P3, P1, P6,
 // Case #112: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EJ, EI, EF, EG, EK,
  ST_PYR, COLOR0, P1, P3, P2, P0, N0,
  ST_TET, COLOR0, P2, P7, N0, P3,
  ST_PYR, COLOR0, P0, P2, EK, EI, N0,
  ST_PYR, COLOR0, EJ, P1, P0, EI, N0,
  ST_TET, COLOR0, P7, P3, P1, N0,
  ST_PYR, COLOR0, P7, P1, EJ, EF, N0,
  ST_TET, COLOR0, P7, EF, EG, N0,
  ST_PYR, COLOR0, EK, P2, P7, EG, N0,
  ST_PYR, COLOR1, EI, EK, P6, P4, N0,
  ST_PYR, COLOR1, EJ, EI, P4, P5, N0,
  ST_TET, COLOR1, EF, EJ, P5, N0,
  ST_TET, COLOR1, P5, P4, P6, N0,
  ST_PYR, COLOR1, EF, P5, P6, EG, N0,
  ST_TET, COLOR1, P6, EK, EG, N0,
 // Case #113: (cloned #23)
  ST_TET, COLOR0, P7, P2, P3, P1,
  ST_PYR, COLOR0, EA, P1, P2, ED, EK,
  ST_PYR, COLOR0, EJ, EF, P7, P1, EG,
  ST_PYR, COLOR0, P2, P7, EG, EK, P1,
  ST_PYR, COLOR0, EK, EG, EJ, EA, P1,
  ST_TET, COLOR1, P4, P5, P0, P6,
  ST_PYR, COLOR1, EK, P6, P0, ED, EA,
  ST_PYR, COLOR1, EG, EF, P5, P6, EJ,
  ST_PYR, COLOR1, P0, P5, EJ, EA, P6,
  ST_PYR, COLOR1, EK, EA, EJ, EG, P6,
 // Case #114: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EI, EA, EB, EF, EG, EK,
  ST_TET, COLOR0, P3, P7, P2, N0,
  ST_PYR, COLOR0, P3, EB, EF, P7, N0,
  ST_TET, COLOR0, P0, P3, P2, N0,
  ST_PYR, COLOR0, P0, EA, EB, P3, N0,
  ST_TET, COLOR0, EI, EA, P0, N0,
  ST_PYR, COLOR0, P0, P2, EK, EI, N0,
  ST_PYR, COLOR0, EK, P2, P7, EG, N0,
  ST_TET, COLOR0, P7, EF, EG, N0,
  ST_TET, COLOR1, P4, P5, P1, N0,
  ST_PYR, COLOR1, EI, P4, P1, EA, N0,
  ST_PYR, COLOR1, EI, EK, P6, P4, N0,
  ST_TET, COLOR1, P6, P5, P4, N0,
  ST_TET, COLOR1, P6, EK, EG, N0,
  ST_PYR, COLOR1, P6, EG, EF, P5, N0,
  ST_PYR, COLOR1, EF, EB, P1, P5, N0,
  ST_TET, COLOR1, EB, EA, P1, N0,
 // Case #115: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EF, EB, ED, EG, EK,
  ST_PYR, COLOR0, P3, P2, ED, EB, N0,
  ST_TET, COLOR0, P2, EK, ED, N0,
  ST_PYR, COLOR0, P7, P3, EB, EF, N0,
  ST_TET, COLOR0, EG, P7, EF, N0,
  ST_PYR, COLOR0, EK, P2, P7, EG, N0,
  ST_TET, COLOR0, P2, P3, P7, N0,
  ST_PYR, COLOR1, P4, P5, P1, P0, N0,
  ST_TET, COLOR1, P0, P6, P4, N0,
  ST_TET, COLOR1, P6, P5, P4, N0,
  ST_PYR, COLOR1, P6, EG, EF, P5, N0,
  ST_PYR, COLOR1, EF, EB, P1, P5, N0,
  ST_PYR, COLOR1, EB, ED, P0, P1, N0,
  ST_PYR, COLOR1, ED, EK, P6, P0, N0,
  ST_TET, COLOR1, EG, P6, EK, N0,
 // Case #116: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EI, ED, EC, EG, EF, EJ,
  ST_TET, COLOR0, P3, P1, P7, N0,
  ST_PYR, COLOR0, P3, P7, EG, EC, N0,
  ST_TET, COLOR0, P0, P1, P3, N0,
  ST_PYR, COLOR0, P0, P3, EC, ED, N0,
  ST_TET, COLOR0, EI, P0, ED, N0,
  ST_PYR, COLOR0, P0, EI, EJ, P1, N0,
  ST_PYR, COLOR0, EJ, EF, P7, P1, N0,
  ST_TET, COLOR0, P7, EF, EG, N0,
  ST_TET, COLOR1, P4, P2, P6, N0,
  ST_PYR, COLOR1, EI, ED, P2, P4, N0,
  ST_PYR, COLOR1, EI, P4, P5, EJ, N0,
  ST_TET, COLOR1, P5, P4, P6, N0,
  ST_TET, COLOR1, P5, EF, EJ, N0,
  ST_PYR, COLOR1, P5, P6, EG, EF, N0,
  ST_PYR, COLOR1, EG, P6, P2, EC, N0,
  ST_TET, COLOR1, EC, P2, ED, N0,
 // Case #117: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EG, EC, EA, EF, EJ,
  ST_PYR, COLOR0, P3, EC, EA, P1, N0,
  ST_TET, COLOR0, P1, EA, EJ, N0,
  ST_PYR, COLOR0, P7, EG, EC, P3, N0,
  ST_TET, COLOR0, EF, EG, P7, N0,
  ST_PYR, COLOR0, EJ, EF, P7, P1, N0,
  ST_TET, COLOR0, P1, P7, P3, N0,
  ST_PYR, COLOR1, P4, P0, P2, P6, N0,
  ST_TET, COLOR1, P0, P4, P5, N0,
  ST_TET, COLOR1, P5, P4, P6, N0,
  ST_PYR, COLOR1, P5, P6, EG, EF, N0,
  ST_PYR, COLOR1, EG, P6, P2, EC, N0,
  ST_PYR, COLOR1, EC, P2, P0, EA, N0,
  ST_PYR, COLOR1, EA, P0, P5, EJ, N0,
  ST_TET, COLOR1, EF, EJ, P5, N0,
 // Case #118: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P4, P2, P6, P5, EB, EF,
  ST_WDG, COLOR0, EF, P7, EG, EB, P3, EC,
  ST_TET, COLOR0, EI, P0, ED, EA,
  ST_WDG, COLOR1, P4, P1, P2, EI, EA, ED,
  ST_PYR, COLOR1, EF, EB, P1, P5, N0,
  ST_TET, COLOR1, P5, P1, P4, N0,
  ST_TET, COLOR1, P5, P4, P6, N0,
  ST_PYR, COLOR1, EG, EF, P5, P6, N0,
  ST_PYR, COLOR1, EC, EG, P6, P2, N0,
  ST_TET, COLOR1, P6, P4, P2, N0,
  ST_PYR, COLOR1, EB, EF, EG, EC, N0,
  ST_TET, COLOR1, P2, P4, P1, N0,
  ST_PYR, COLOR1, EB, EC, P2, P1, N0,
 // Case #119: (cloned #63)
  ST_WDG, COLOR0, P7, EG, EF, P3, EC, EB,
  ST_HEX, COLOR1, EC, EB, EF, EG, P2, P1, P5, P6,
  ST_WDG, COLOR1, P0, P2, P1, P4, P6, P5,
 // Case #120: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EI, EK, EJ, EB, EC,
  ST_TET, COLOR0, P7, EF, EG, EL,
  ST_PYR, COLOR0, P2, EK, EI, P0, N0,
  ST_PYR, COLOR0, P0, EI, EJ, P1, N0,
  ST_TET, COLOR0, P1, EJ, EB, N0,
  ST_TET, COLOR0, P0, P1, P2, N0,
  ST_PYR, COLOR0, P1, EB, EC, P2, N0,
  ST_TET, COLOR0, P2, EC, EK, N0,
  ST_WDG, COLOR1, P5, P6, P3, EF, EG, EL,
  ST_TET, COLOR1, P6, P3, P5, N0,
  ST_PYR, COLOR1, P5, P3, EB, EJ, N0,
  ST_TET, COLOR1, P4, P6, P5, N0,
  ST_PYR, COLOR1, EK, P6, P4, EI, N0,
  ST_PYR, COLOR1, EI, P4, P5, EJ, N0,
  ST_PYR, COLOR1, P3, P6, EK, EC, N0,
  ST_TET, COLOR1, EB, P3, EC, N0,
 // Case #121: (cloned #107)
  ST_TET, COLOR0, EG, EF, EL, P7,
  ST_TET, COLOR0, EK, ED, P2, EC,
  ST_TET, COLOR0, EJ, P1, EA, EB,
  ST_WDG, COLOR1, P3, P0, P5, EB, EA, EJ,
  ST_WDG, COLOR1, EK, EC, ED, P6, P3, P0,
  ST_WDG, COLOR1, P6, P3, P5, EG, EL, EF,
  ST_TET, COLOR1, P3, P0, P5, P6,
  ST_TET, COLOR1, P6, P5, P4, P0,
 // Case #122: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P5, P3, P1, P4, EK, EI,
  ST_WDG, COLOR0, EI, P0, EA, EK, P2, EC,
  ST_TET, COLOR0, EF, P7, EL, EG,
  ST_WDG, COLOR1, P5, P6, P3, EF, EG, EL,
  ST_PYR, COLOR1, EI, EK, P6, P4, N0,
  ST_TET, COLOR1, P4, P6, P5, N0,
  ST_TET, COLOR1, P4, P5, P1, N0,
  ST_PYR, COLOR1, EA, EI, P4, P1, N0,
  ST_PYR, COLOR1, EC, EA, P1, P3, N0,
  ST_TET, COLOR1, P1, P5, P3, N0,
  ST_PYR, COLOR1, EK, EI, EA, EC, N0,
  ST_TET, COLOR1, P3, P5, P6, N0,
  ST_PYR, COLOR1, EK, EC, P3, P6, N0,
 // Case #123: (cloned #111)
  ST_TET, COLOR0, EC, ED, EK, P2,
  ST_TET, COLOR0, EL, EG, EF, P7,
  ST_WDG, COLOR1, P6, P3, P5, EG, EL, EF,
  ST_WDG, COLOR1, EK, EC, ED, P6, P3, P0,
  ST_PYR, COLOR1, P4, P5, P1, P0, P6,
  ST_TET, COLOR1, P3, P0, P1, P6,
  ST_TET, COLOR1, P3, P1, P5, P6,
 // Case #124: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P6, P5, P4, P2, EB, ED,
  ST_WDG, COLOR0, ED, P0, EI, EB, P1, EJ,
  ST_TET, COLOR0, EG, P7, EF, EL,
  ST_WDG, COLOR1, P6, P3, P5, EG, EL, EF,
  ST_PYR, COLOR1, ED, EB, P3, P2, N0,
  ST_TET, COLOR1, P2, P3, P6, N0,
  ST_TET, COLOR1, P2, P6, P4, N0,
  ST_PYR, COLOR1, EI, ED, P2, P4, N0,
  ST_PYR, COLOR1, EJ, EI, P4, P5, N0,
  ST_TET, COLOR1, P4, P6, P5, N0,
  ST_PYR, COLOR1, EB, ED, EI, EJ, N0,
  ST_TET, COLOR1, P5, P6, P3, N0,
  ST_PYR, COLOR1, EB, EJ, P5, P3, N0,
 // Case #125: (cloned #111)
  ST_TET, COLOR0, EJ, EA, EB, P1,
  ST_TET, COLOR0, EF, EL, EG, P7,
  ST_WDG, COLOR1, P3, P5, P6, EL, EF, EG,
  ST_WDG, COLOR1, EB, EJ, EA, P3, P5, P0,
  ST_PYR, COLOR1, P2, P6, P4, P0, P3,
  ST_TET, COLOR1, P5, P0, P4, P3,
  ST_TET, COLOR1, P5, P4, P6, P3,
 // Case #126: Unique case #20
  ST_TET, COLOR0, EA, EI, ED, P0,
  ST_TET, COLOR0, EL, EG, EF, P7,
  ST_WDG, COLOR1, ED, EA, EI, P2, P1, P4,
  ST_WDG, COLOR1, EL, EG, EF, P3, P6, P5,
  ST_TET, COLOR1, P5, P4, P6, P3,
  ST_TET, COLOR1, P3, P2, P1, P4,
  ST_TET, COLOR1, P2, P3, P6, P4,
  ST_TET, COLOR1, P3, P1, P5, P4,
 // Case #127: Unique case #21
  ST_PNT, 0, COLOR1, 7, P1, P0, P2, P3, P5, P4, P6,
  ST_TET, COLOR0, EF, EL, EG, P7,
  ST_WDG, COLOR1, P5, P6, P3, EF, EG, EL,
  ST_TET, COLOR1, P5, P6, P3, N0,
  ST_PYR, COLOR1, P4, P0, P2, P6, N0,
  ST_TET, COLOR1, P6, P2, P3, N0,
  ST_PYR, COLOR1, P1, P3, P2, P0, N0,
  ST_TET, COLOR1, P1, P5, P3, N0,
  ST_PYR, COLOR1, P1, P0, P4, P5, N0,
  ST_TET, COLOR1, P5, P4, P6, N0,
 // Case #128: (cloned #1)
  ST_PNT, 0, COLOR0, 7, P6, P4, P5, P3, P2, P0, P1,
  ST_WDG, COLOR0, EG, EF, EL, P6, P5, P3,
  ST_TET, COLOR0, P6, P3, P5, N0,
  ST_TET, COLOR0, P6, P5, P4, N0,
  ST_PYR, COLOR0, P0, P4, P5, P1, N0,
  ST_PYR, COLOR0, P2, P6, P4, P0, N0,
  ST_PYR, COLOR0, P3, P2, P0, P1, N0,
  ST_TET, COLOR0, P5, P3, P1, N0,
  ST_TET, COLOR0, P3, P6, P2, N0,
  ST_TET, COLOR1, P7, EF, EG, EL,
 // Case #129: (cloned #24)
  ST_WDG, COLOR0, P5, P6, P3, EF, EG, EL,
  ST_WDG, COLOR0, EI, ED, EA, P4, P2, P1,
  ST_TET, COLOR0, P6, P3, P5, P2,
  ST_TET, COLOR0, P1, P3, P2, P5,
  ST_TET, COLOR0, P4, P5, P1, P2,
  ST_TET, COLOR0, P6, P4, P2, P5,
  ST_TET, COLOR1, P0, EI, EA, ED,
  ST_TET, COLOR1, P7, EF, EG, EL,
 // Case #130: (cloned #6)
  ST_PNT, 0, NOCOLOR, 2, EA, EG,
  ST_PYR, COLOR0, P0, P2, P6, P4, N0,
  ST_TET, COLOR0, P4, P6, P5, N0,
  ST_TET, COLOR0, P0, P4, P5, N0,
  ST_TET, COLOR0, P3, P2, P0, N0,
  ST_TET, COLOR0, P6, P2, P3, N0,
  ST_PYR, COLOR0, P6, P3, EL, EG, N0,
  ST_PYR, COLOR0, P5, P6, EG, EF, N0,
  ST_TET, COLOR0, P5, EF, EJ, N0,
  ST_PYR, COLOR0, P0, P5, EJ, EA, N0,
  ST_PYR, COLOR0, P0, EA, EB, P3, N0,
  ST_TET, COLOR0, P3, EB, EL, N0,
  ST_PYR, COLOR1, P1, P7, EL, EB, N0,
  ST_PYR, COLOR1, EJ, EF, P7, P1, N0,
  ST_TET, COLOR1, EF, EG, P7, N0,
  ST_TET, COLOR1, P7, EG, EL, N0,
  ST_TET, COLOR1, EJ, N0, P1, EA,
  ST_TET, COLOR1, EB, EA, P1, N0,
 // Case #131: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EI, ED, EG, EG,
  ST_PYR, COLOR0, P6, P3, EL, EG, N0,
  ST_TET, COLOR0, EL, P3, EB, N0,
  ST_PYR, COLOR0, P2, ED, EB, P3, N0,
  ST_TET, COLOR0, P6, P2, P3, N0,
  ST_TET, COLOR0, P5, EF, EJ, N0,
  ST_TET, COLOR0, P4, P6, P5, N0,
  ST_PYR, COLOR0, P5, P6, EG, EF, N0,
  ST_TET, COLOR0, P4, P2, P6, N0,
  ST_PYR, COLOR0, P4, EI, ED, P2, N0,
  ST_PYR, COLOR0, P4, P5, EJ, EI, N0,
  ST_PYR, COLOR1, P7, EL, EB, P1, N0,
  ST_PYR, COLOR1, EJ, EF, P7, P1, N0,
  ST_TET, COLOR1, P7, EG, EL, N0,
  ST_TET, COLOR1, EF, EG, P7, N0,
  ST_PYR, COLOR1, EB, ED, P0, P1, N0,
  ST_PYR, COLOR1, P1, P0, EI, EJ, N0,
  ST_TET, COLOR1, EI, P0, ED, N0,
 // Case #132: (cloned #6)
  ST_PNT, 0, NOCOLOR, 2, ED, EF,
  ST_PYR, COLOR0, P0, P4, P5, P1, N0,
  ST_TET, COLOR0, P1, P5, P3, N0,
  ST_TET, COLOR0, P0, P1, P3, N0,
  ST_TET, COLOR0, P6, P4, P0, N0,
  ST_TET, COLOR0, P5, P4, P6, N0,
  ST_PYR, COLOR0, P5, P6, EG, EF, N0,
  ST_PYR, COLOR0, P3, P5, EF, EL, N0,
  ST_TET, COLOR0, P3, EL, EC, N0,
  ST_PYR, COLOR0, P0, P3, EC, ED, N0,
  ST_PYR, COLOR0, P0, ED, EK, P6, N0,
  ST_TET, COLOR0, P6, EK, EG, N0,
  ST_PYR, COLOR1, P2, P7, EG, EK, N0,
  ST_PYR, COLOR1, EC, EL, P7, P2, N0,
  ST_TET, COLOR1, EL, EF, P7, N0,
  ST_TET, COLOR1, P7, EF, EG, N0,
  ST_TET, COLOR1, EC, N0, P2, ED,
  ST_TET, COLOR1, EK, ED, P2, N0,
 // Case #133: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EA, EI, EF, EF,
  ST_PYR, COLOR0, P5, P6, EG, EF, N0,
  ST_TET, COLOR0, EG, P6, EK, N0,
  ST_PYR, COLOR0, P4, EI, EK, P6, N0,
  ST_TET, COLOR0, P5, P4, P6, N0,
  ST_TET, COLOR0, P3, EL, EC, N0,
  ST_TET, COLOR0, P1, P5, P3, N0,
  ST_PYR, COLOR0, P3, P5, EF, EL, N0,
  ST_TET, COLOR0, P1, P4, P5, N0,
  ST_PYR, COLOR0, P1, EA, EI, P4, N0,
  ST_PYR, COLOR0, P1, P3, EC, EA, N0,
  ST_PYR, COLOR1, P7, EG, EK, P2, N0,
  ST_PYR, COLOR1, EC, EL, P7, P2, N0,
  ST_TET, COLOR1, P7, EF, EG, N0,
  ST_TET, COLOR1, EL, EF, P7, N0,
  ST_PYR, COLOR1, EK, EI, P0, P2, N0,
  ST_PYR, COLOR1, P2, P0, EA, EC, N0,
  ST_TET, COLOR1, EA, P0, EI, N0,
 // Case #134: (cloned #22)
  ST_TET, COLOR0, P3, EB, EL, EC,
  ST_TET, COLOR0, P6, P4, P0, P5,
  ST_PYR, COLOR0, EJ, EA, P0, P5, ED,
  ST_PYR, COLOR0, EF, P5, P6, EG, EK,
  ST_PYR, COLOR0, P0, ED, EK, P6, P5,
  ST_PYR, COLOR0, ED, EJ, EF, EK, P5,
  ST_WDG, COLOR1, P1, P7, P2, EB, EL, EC,
  ST_PYR, COLOR1, P1, P2, ED, EA, EJ,
  ST_PYR, COLOR1, EK, P2, P7, EG, EF,
  ST_PYR, COLOR1, EJ, EF, P7, P1, P2,
  ST_PYR, COLOR1, EJ, ED, EK, EF, P2,
 // Case #135: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EI, EK, EJ, EF, EG,
  ST_TET, COLOR0, P3, EC, EB, EL,
  ST_PYR, COLOR0, P6, P4, EI, EK, N0,
  ST_PYR, COLOR0, P4, P5, EJ, EI, N0,
  ST_TET, COLOR0, P5, EF, EJ, N0,
  ST_TET, COLOR0, P4, P6, P5, N0,
  ST_PYR, COLOR0, P5, P6, EG, EF, N0,
  ST_TET, COLOR0, P6, EK, EG, N0,
  ST_WDG, COLOR1, EB, EC, EL, P1, P2, P7,
  ST_TET, COLOR1, P2, P1, P7, N0,
  ST_PYR, COLOR1, P1, EJ, EF, P7, N0,
  ST_TET, COLOR1, P0, P1, P2, N0,
  ST_PYR, COLOR1, EK, EI, P0, P2, N0,
  ST_PYR, COLOR1, EI, EJ, P1, P0, N0,
  ST_PYR, COLOR1, P7, EG, EK, P2, N0,
  ST_TET, COLOR1, EF, EG, P7, N0,
 // Case #136: (cloned #3)
  ST_HEX, COLOR0, EG, P6, P2, EC, EF, P5, P1, EB,
  ST_WDG, COLOR0, P1, P0, P2, P5, P4, P6,
  ST_WDG, COLOR1, P3, EB, EC, P7, EF, EG,
 // Case #137: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EG, EF, EI, EI,
  ST_PYR, COLOR0, P4, P1, EA, EI, N0,
  ST_TET, COLOR0, EA, P1, EB, N0,
  ST_PYR, COLOR0, P5, EF, EB, P1, N0,
  ST_TET, COLOR0, P4, P5, P1, N0,
  ST_TET, COLOR0, P2, ED, EC, N0,
  ST_TET, COLOR0, P6, P4, P2, N0,
  ST_PYR, COLOR0, P2, P4, EI, ED, N0,
  ST_TET, COLOR0, P6, P5, P4, N0,
  ST_PYR, COLOR0, P6, EG, EF, P5, N0,
  ST_PYR, COLOR0, P6, P2, EC, EG, N0,
  ST_PYR, COLOR1, P0, EA, EB, P3, N0,
  ST_PYR, COLOR1, EC, ED, P0, P3, N0,
  ST_TET, COLOR1, P0, EI, EA, N0,
  ST_TET, COLOR1, ED, EI, P0, N0,
  ST_PYR, COLOR1, EB, EF, P7, P3, N0,
  ST_PYR, COLOR1, P3, P7, EG, EC, N0,
  ST_TET, COLOR1, EG, P7, EF, N0,
 // Case #138: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EG, EC, EF, EJ, EA,
  ST_PYR, COLOR0, P6, P4, P0, P2, N0,
  ST_TET, COLOR0, P0, P5, N0, P4,
  ST_PYR, COLOR0, P2, P0, EA, EC, N0,
  ST_PYR, COLOR0, EG, P6, P2, EC, N0,
  ST_TET, COLOR0, P5, P4, P6, N0,
  ST_PYR, COLOR0, P5, P6, EG, EF, N0,
  ST_TET, COLOR0, P5, EF, EJ, N0,
  ST_PYR, COLOR0, EA, P0, P5, EJ, N0,
  ST_PYR, COLOR1, EC, EA, P1, P3, N0,
  ST_PYR, COLOR1, EG, EC, P3, P7, N0,
  ST_TET, COLOR1, EF, EG, P7, N0,
  ST_TET, COLOR1, P7, P3, P1, N0,
  ST_PYR, COLOR1, EF, P7, P1, EJ, N0,
  ST_TET, COLOR1, P1, EA, EJ, N0,
 // Case #139: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EJ, EF, EG, EC, ED, EI,
  ST_TET, COLOR0, P6, P4, P2, N0,
  ST_PYR, COLOR0, P6, P2, EC, EG, N0,
  ST_TET, COLOR0, P5, P4, P6, N0,
  ST_PYR, COLOR0, P5, P6, EG, EF, N0,
  ST_TET, COLOR0, EJ, P5, EF, N0,
  ST_PYR, COLOR0, P5, EJ, EI, P4, N0,
  ST_PYR, COLOR0, EI, ED, P2, P4, N0,
  ST_TET, COLOR0, P2, ED, EC, N0,
  ST_TET, COLOR1, P1, P7, P3, N0,
  ST_PYR, COLOR1, EJ, EF, P7, P1, N0,
  ST_PYR, COLOR1, EJ, P1, P0, EI, N0,
  ST_TET, COLOR1, P0, P1, P3, N0,
  ST_TET, COLOR1, P0, ED, EI, N0,
  ST_PYR, COLOR1, P0, P3, EC, ED, N0,
  ST_PYR, COLOR1, EC, P3, P7, EG, N0,
  ST_TET, COLOR1, EG, P7, EF, N0,
 // Case #140: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EF, EB, EG, EK, ED,
  ST_PYR, COLOR0, P5, P1, P0, P4, N0,
  ST_TET, COLOR0, P0, N0, P6, P4,
  ST_PYR, COLOR0, P1, EB, ED, P0, N0,
  ST_PYR, COLOR0, EF, EB, P1, P5, N0,
  ST_TET, COLOR0, P6, P5, P4, N0,
  ST_PYR, COLOR0, P6, EG, EF, P5, N0,
  ST_TET, COLOR0, P6, EK, EG, N0,
  ST_PYR, COLOR0, ED, EK, P6, P0, N0,
  ST_PYR, COLOR1, EB, P3, P2, ED, N0,
  ST_PYR, COLOR1, EF, P7, P3, EB, N0,
  ST_TET, COLOR1, EG, P7, EF, N0,
  ST_TET, COLOR1, P7, P2, P3, N0,
  ST_PYR, COLOR1, EG, EK, P2, P7, N0,
  ST_TET, COLOR1, P2, EK, ED, N0,
 // Case #141: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EK, EG, EF, EB, EA, EI,
  ST_TET, COLOR0, P5, P1, P4, N0,
  ST_PYR, COLOR0, P5, EF, EB, P1, N0,
  ST_TET, COLOR0, P6, P5, P4, N0,
  ST_PYR, COLOR0, P6, EG, EF, P5, N0,
  ST_TET, COLOR0, EK, EG, P6, N0,
  ST_PYR, COLOR0, P6, P4, EI, EK, N0,
  ST_PYR, COLOR0, EI, P4, P1, EA, N0,
  ST_TET, COLOR0, P1, EB, EA, N0,
  ST_TET, COLOR1, P2, P3, P7, N0,
  ST_PYR, COLOR1, EK, P2, P7, EG, N0,
  ST_PYR, COLOR1, EK, EI, P0, P2, N0,
  ST_TET, COLOR1, P0, P3, P2, N0,
  ST_TET, COLOR1, P0, EI, EA, N0,
  ST_PYR, COLOR1, P0, EA, EB, P3, N0,
  ST_PYR, COLOR1, EB, EF, P7, P3, N0,
  ST_TET, COLOR1, EF, EG, P7, N0,
 // Case #142: (cloned #23)
  ST_TET, COLOR0, P6, P4, P0, P5,
  ST_PYR, COLOR0, EJ, EA, P0, P5, ED,
  ST_PYR, COLOR0, EF, P5, P6, EG, EK,
  ST_PYR, COLOR0, P0, ED, EK, P6, P5,
  ST_PYR, COLOR0, ED, EJ, EF, EK, P5,
  ST_TET, COLOR1, P3, P1, P7, P2,
  ST_PYR, COLOR1, ED, EA, P1, P2, EJ,
  ST_PYR, COLOR1, EK, P2, P7, EG, EF,
  ST_PYR, COLOR1, P1, EJ, EF, P7, P2,
  ST_PYR, COLOR1, ED, EK, EF, EJ, P2,
 // Case #143: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EK, EI, EJ, EG, EF,
  ST_PYR, COLOR0, P4, P5, EJ, EI, N0,
  ST_TET, COLOR0, P5, EF, EJ, N0,
  ST_PYR, COLOR0, P6, P4, EI, EK, N0,
  ST_TET, COLOR0, EG, P6, EK, N0,
  ST_PYR, COLOR0, EF, P5, P6, EG, N0,
  ST_TET, COLOR0, P5, P4, P6, N0,
  ST_PYR, COLOR1, P3, P2, P0, P1, N0,
  ST_TET, COLOR1, P1, P7, P3, N0,
  ST_TET, COLOR1, P7, P2, P3, N0,
  ST_PYR, COLOR1, P7, EG, EK, P2, N0,
  ST_PYR, COLOR1, EK, EI, P0, P2, N0,
  ST_PYR, COLOR1, EI, EJ, P1, P0, N0,
  ST_PYR, COLOR1, EJ, EF, P7, P1, N0,
  ST_TET, COLOR1, EG, P7, EF, N0,
 // Case #144: (cloned #6)
  ST_PNT, 0, NOCOLOR, 2, EI, EL,
  ST_PYR, COLOR0, P0, P1, P3, P2, N0,
  ST_TET, COLOR0, P1, P5, P3, N0,
  ST_TET, COLOR0, P0, P5, P1, N0,
  ST_TET, COLOR0, P6, P0, P2, N0,
  ST_TET, COLOR0, P3, P6, P2, N0,
  ST_PYR, COLOR0, P3, EL, EG, P6, N0,
  ST_PYR, COLOR0, P5, EF, EL, P3, N0,
  ST_TET, COLOR0, P5, EE, EF, N0,
  ST_PYR, COLOR0, P0, EI, EE, P5, N0,
  ST_PYR, COLOR0, P0, P6, EH, EI, N0,
  ST_TET, COLOR0, P6, EG, EH, N0,
  ST_PYR, COLOR1, P4, EH, EG, P7, N0,
  ST_PYR, COLOR1, EE, P4, P7, EF, N0,
  ST_TET, COLOR1, EF, P7, EL, N0,
  ST_TET, COLOR1, P7, EG, EL, N0,
  ST_TET, COLOR1, EE, P4, N0, EI,
  ST_TET, COLOR1, EH, P4, EI, N0,
 // Case #145: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EA, ED, EL, EL,
  ST_PYR, COLOR0, P3, EL, EG, P6, N0,
  ST_TET, COLOR0, EG, EH, P6, N0,
  ST_PYR, COLOR0, P2, P6, EH, ED, N0,
  ST_TET, COLOR0, P3, P6, P2, N0,
  ST_TET, COLOR0, P5, EE, EF, N0,
  ST_TET, COLOR0, P1, P5, P3, N0,
  ST_PYR, COLOR0, P5, EF, EL, P3, N0,
  ST_TET, COLOR0, P1, P3, P2, N0,
  ST_PYR, COLOR0, P1, P2, ED, EA, N0,
  ST_PYR, COLOR0, P1, EA, EE, P5, N0,
  ST_PYR, COLOR1, P7, P4, EH, EG, N0,
  ST_PYR, COLOR1, EE, P4, P7, EF, N0,
  ST_TET, COLOR1, P7, EG, EL, N0,
  ST_TET, COLOR1, EF, P7, EL, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_PYR, COLOR1, P4, EE, EA, P0, N0,
  ST_TET, COLOR1, EA, ED, P0, N0,
 // Case #146: (cloned #22)
  ST_TET, COLOR0, P5, EF, EJ, EE,
  ST_TET, COLOR0, P6, P0, P2, P3,
  ST_PYR, COLOR0, EB, P3, P0, EA, EI,
  ST_PYR, COLOR0, EL, EG, P6, P3, EH,
  ST_PYR, COLOR0, P0, P6, EH, EI, P3,
  ST_PYR, COLOR0, EI, EH, EL, EB, P3,
  ST_WDG, COLOR1, EJ, EF, EE, P1, P7, P4,
  ST_PYR, COLOR1, P1, EA, EI, P4, EB,
  ST_PYR, COLOR1, EH, EG, P7, P4, EL,
  ST_PYR, COLOR1, EB, P1, P7, EL, P4,
  ST_PYR, COLOR1, EB, EL, EH, EI, P4,
 // Case #147: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, ED, EH, EB, EL, EG,
  ST_TET, COLOR0, P5, EJ, EE, EF,
  ST_PYR, COLOR0, P6, EH, ED, P2, N0,
  ST_PYR, COLOR0, P2, ED, EB, P3, N0,
  ST_TET, COLOR0, P3, EB, EL, N0,
  ST_TET, COLOR0, P2, P3, P6, N0,
  ST_PYR, COLOR0, P3, EL, EG, P6, N0,
  ST_TET, COLOR0, P6, EG, EH, N0,
  ST_WDG, COLOR1, P1, P4, P7, EJ, EE, EF,
  ST_TET, COLOR1, P4, P7, P1, N0,
  ST_PYR, COLOR1, P1, P7, EL, EB, N0,
  ST_TET, COLOR1, P0, P4, P1, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_PYR, COLOR1, ED, P0, P1, EB, N0,
  ST_PYR, COLOR1, P7, P4, EH, EG, N0,
  ST_TET, COLOR1, EL, P7, EG, N0,
 // Case #148: (cloned #22)
  ST_TET, COLOR0, P6, EG, EH, EK,
  ST_TET, COLOR0, P3, P0, P1, P5,
  ST_PYR, COLOR0, EE, P5, P0, EI, ED,
  ST_PYR, COLOR0, EF, EL, P3, P5, EC,
  ST_PYR, COLOR0, P0, P3, EC, ED, P5,
  ST_PYR, COLOR0, ED, EC, EF, EE, P5,
  ST_WDG, COLOR1, EH, EG, EK, P4, P7, P2,
  ST_PYR, COLOR1, P4, EI, ED, P2, EE,
  ST_PYR, COLOR1, EC, EL, P7, P2, EF,
  ST_PYR, COLOR1, EE, P4, P7, EF, P2,
  ST_PYR, COLOR1, EE, EF, EC, ED, P2,
 // Case #149: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EA, EC, EE, EF, EL,
  ST_TET, COLOR0, P6, EH, EK, EG,
  ST_PYR, COLOR0, P3, EC, EA, P1, N0,
  ST_PYR, COLOR0, P1, EA, EE, P5, N0,
  ST_TET, COLOR0, P5, EE, EF, N0,
  ST_TET, COLOR0, P1, P5, P3, N0,
  ST_PYR, COLOR0, P5, EF, EL, P3, N0,
  ST_TET, COLOR0, P3, EL, EC, N0,
  ST_WDG, COLOR1, P4, P2, P7, EH, EK, EG,
  ST_TET, COLOR1, P2, P7, P4, N0,
  ST_PYR, COLOR1, P4, P7, EF, EE, N0,
  ST_TET, COLOR1, P0, P2, P4, N0,
  ST_PYR, COLOR1, EC, P2, P0, EA, N0,
  ST_PYR, COLOR1, EA, P0, P4, EE, N0,
  ST_PYR, COLOR1, P7, P2, EC, EL, N0,
  ST_TET, COLOR1, EF, P7, EL, N0,
 // Case #150: (cloned #105)
  ST_TET, COLOR0, EH, EG, EK, P6,
  ST_TET, COLOR0, EI, ED, EA, P0,
  ST_TET, COLOR0, EE, EJ, EF, P5,
  ST_TET, COLOR0, EB, EC, EL, P3,
  ST_WDG, COLOR1, EB, EC, EL, P1, P2, P7,
  ST_TET, COLOR1, P1, P7, P2, P4,
  ST_WDG, COLOR1, P4, P7, P1, EE, EF, EJ,
  ST_WDG, COLOR1, P2, P4, P1, ED, EI, EA,
  ST_WDG, COLOR1, P7, P4, P2, EG, EH, EK,
 // Case #151: (cloned #107)
  ST_TET, COLOR0, EK, EH, EG, P6,
  ST_TET, COLOR0, EC, EB, P3, EL,
  ST_TET, COLOR0, EE, P5, EJ, EF,
  ST_WDG, COLOR1, P7, P1, P4, EF, EJ, EE,
  ST_WDG, COLOR1, EC, EL, EB, P2, P7, P1,
  ST_WDG, COLOR1, P2, P7, P4, EK, EG, EH,
  ST_TET, COLOR1, P7, P1, P4, P2,
  ST_TET, COLOR1, P2, P4, P0, P1,
 // Case #152: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EC, EB, EI, EI,
  ST_PYR, COLOR0, P0, EI, EE, P5, N0,
  ST_TET, COLOR0, EE, EF, P5, N0,
  ST_PYR, COLOR0, P1, P5, EF, EB, N0,
  ST_TET, COLOR0, P0, P5, P1, N0,
  ST_TET, COLOR0, P6, EG, EH, N0,
  ST_TET, COLOR0, P2, P6, P0, N0,
  ST_PYR, COLOR0, P6, EH, EI, P0, N0,
  ST_TET, COLOR0, P2, P0, P1, N0,
  ST_PYR, COLOR0, P2, P1, EB, EC, N0,
  ST_PYR, COLOR0, P2, EC, EG, P6, N0,
  ST_PYR, COLOR1, P4, P7, EF, EE, N0,
  ST_PYR, COLOR1, EG, P7, P4, EH, N0,
  ST_TET, COLOR1, P4, EE, EI, N0,
  ST_TET, COLOR1, EH, P4, EI, N0,
  ST_PYR, COLOR1, EF, P7, P3, EB, N0,
  ST_PYR, COLOR1, P7, EG, EC, P3, N0,
  ST_TET, COLOR1, EC, EB, P3, N0,
 // Case #153: (cloned #60)
  ST_WDG, COLOR0, P6, EH, EG, P2, ED, EC,
  ST_WDG, COLOR0, P5, EF, EE, P1, EB, EA,
  ST_HEX, COLOR1, P0, P3, P7, P4, EA, EB, EF, EE,
  ST_HEX, COLOR1, ED, EC, EG, EH, P0, P3, P7, P4,
 // Case #154: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EC, EG, EA, EI, EH,
  ST_TET, COLOR0, P5, EF, EJ, EE,
  ST_PYR, COLOR0, P6, P2, EC, EG, N0,
  ST_PYR, COLOR0, P2, P0, EA, EC, N0,
  ST_TET, COLOR0, P0, EI, EA, N0,
  ST_TET, COLOR0, P2, P6, P0, N0,
  ST_PYR, COLOR0, P0, P6, EH, EI, N0,
  ST_TET, COLOR0, P6, EG, EH, N0,
  ST_WDG, COLOR1, EJ, EF, EE, P1, P7, P4,
  ST_TET, COLOR1, P7, P1, P4, N0,
  ST_PYR, COLOR1, P1, EA, EI, P4, N0,
  ST_TET, COLOR1, P3, P1, P7, N0,
  ST_PYR, COLOR1, EG, EC, P3, P7, N0,
  ST_PYR, COLOR1, EC, EA, P1, P3, N0,
  ST_PYR, COLOR1, P4, EH, EG, P7, N0,
  ST_TET, COLOR1, EI, EH, P4, N0,
 // Case #155: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P1, P4, P0, P3, EG, EC,
  ST_WDG, COLOR0, EG, P6, EH, EC, P2, ED,
  ST_TET, COLOR0, EJ, EE, P5, EF,
  ST_WDG, COLOR1, EJ, EF, EE, P1, P7, P4,
  ST_PYR, COLOR1, EC, P3, P7, EG, N0,
  ST_TET, COLOR1, P3, P1, P7, N0,
  ST_TET, COLOR1, P3, P0, P1, N0,
  ST_PYR, COLOR1, ED, P0, P3, EC, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_TET, COLOR1, P0, P4, P1, N0,
  ST_PYR, COLOR1, EG, EH, ED, EC, N0,
  ST_TET, COLOR1, P4, P7, P1, N0,
  ST_PYR, COLOR1, EG, P7, P4, EH, N0,
 // Case #156: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EB, EF, ED, EI, EE,
  ST_TET, COLOR0, P6, EK, EG, EH,
  ST_PYR, COLOR0, P5, EF, EB, P1, N0,
  ST_PYR, COLOR0, P1, EB, ED, P0, N0,
  ST_TET, COLOR0, P0, ED, EI, N0,
  ST_TET, COLOR0, P1, P0, P5, N0,
  ST_PYR, COLOR0, P0, EI, EE, P5, N0,
  ST_TET, COLOR0, P5, EE, EF, N0,
  ST_WDG, COLOR1, P2, P7, P4, EK, EG, EH,
  ST_TET, COLOR1, P7, P4, P2, N0,
  ST_PYR, COLOR1, P2, P4, EI, ED, N0,
  ST_TET, COLOR1, P3, P7, P2, N0,
  ST_PYR, COLOR1, EF, P7, P3, EB, N0,
  ST_PYR, COLOR1, EB, P3, P2, ED, N0,
  ST_PYR, COLOR1, P4, P7, EF, EE, N0,
  ST_TET, COLOR1, EI, P4, EE, N0,
 // Case #157: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P2, P4, P0, P3, EF, EB,
  ST_WDG, COLOR0, EB, P1, EA, EF, P5, EE,
  ST_TET, COLOR0, EK, P6, EH, EG,
  ST_WDG, COLOR1, P2, P7, P4, EK, EG, EH,
  ST_PYR, COLOR1, EB, EF, P7, P3, N0,
  ST_TET, COLOR1, P3, P7, P2, N0,
  ST_TET, COLOR1, P3, P2, P0, N0,
  ST_PYR, COLOR1, EA, EB, P3, P0, N0,
  ST_PYR, COLOR1, EE, EA, P0, P4, N0,
  ST_TET, COLOR1, P0, P2, P4, N0,
  ST_PYR, COLOR1, EF, EB, EA, EE, N0,
  ST_TET, COLOR1, P4, P2, P7, N0,
  ST_PYR, COLOR1, EF, EE, P4, P7, N0,
 // Case #158: (cloned #107)
  ST_TET, COLOR0, EJ, EF, EE, P5,
  ST_TET, COLOR0, EA, ED, P0, EI,
  ST_TET, COLOR0, EG, P6, EK, EH,
  ST_WDG, COLOR1, P4, P2, P7, EH, EK, EG,
  ST_WDG, COLOR1, EA, EI, ED, P1, P4, P2,
  ST_WDG, COLOR1, P1, P4, P7, EJ, EE, EF,
  ST_TET, COLOR1, P4, P2, P7, P1,
  ST_TET, COLOR1, P1, P7, P3, P2,
 // Case #159: (cloned #111)
  ST_TET, COLOR0, EG, EK, EH, P6,
  ST_TET, COLOR0, EF, EE, EJ, P5,
  ST_WDG, COLOR1, P4, P7, P1, EE, EF, EJ,
  ST_WDG, COLOR1, EH, EG, EK, P4, P7, P2,
  ST_PYR, COLOR1, P0, P1, P3, P2, P4,
  ST_TET, COLOR1, P7, P2, P3, P4,
  ST_TET, COLOR1, P7, P3, P1, P4,
 // Case #160: (cloned #3)
  ST_HEX, COLOR0, EL, P3, P1, EJ, EG, P6, P4, EE,
  ST_WDG, COLOR0, P3, P2, P6, P1, P0, P4,
  ST_WDG, COLOR1, P7, EL, EG, P5, EJ, EE,
 // Case #161: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EG, EL, ED, ED,
  ST_PYR, COLOR0, P2, ED, EA, P1, N0,
  ST_TET, COLOR0, EA, EJ, P1, N0,
  ST_PYR, COLOR0, P3, P1, EJ, EL, N0,
  ST_TET, COLOR0, P2, P1, P3, N0,
  ST_TET, COLOR0, P4, EE, EI, N0,
  ST_TET, COLOR0, P6, P4, P2, N0,
  ST_PYR, COLOR0, P4, EI, ED, P2, N0,
  ST_TET, COLOR0, P6, P2, P3, N0,
  ST_PYR, COLOR0, P6, P3, EL, EG, N0,
  ST_PYR, COLOR0, P6, EG, EE, P4, N0,
  ST_PYR, COLOR1, P0, P5, EJ, EA, N0,
  ST_PYR, COLOR1, EE, P5, P0, EI, N0,
  ST_TET, COLOR1, P0, EA, ED, N0,
  ST_TET, COLOR1, EI, P0, ED, N0,
  ST_PYR, COLOR1, EJ, P5, P7, EL, N0,
  ST_PYR, COLOR1, P5, EE, EG, P7, N0,
  ST_TET, COLOR1, EG, EL, P7, N0,
 // Case #162: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EG, EE, EL, EB, EA,
  ST_PYR, COLOR0, P6, P4, P0, P2, N0,
  ST_TET, COLOR0, P0, N0, P3, P2,
  ST_PYR, COLOR0, P4, EE, EA, P0, N0,
  ST_PYR, COLOR0, EG, EE, P4, P6, N0,
  ST_TET, COLOR0, P3, P6, P2, N0,
  ST_PYR, COLOR0, P3, EL, EG, P6, N0,
  ST_TET, COLOR0, P3, EB, EL, N0,
  ST_PYR, COLOR0, EA, EB, P3, P0, N0,
  ST_PYR, COLOR1, EE, P5, P1, EA, N0,
  ST_PYR, COLOR1, EG, P7, P5, EE, N0,
  ST_TET, COLOR1, EL, P7, EG, N0,
  ST_TET, COLOR1, P7, P1, P5, N0,
  ST_PYR, COLOR1, EL, EB, P1, P7, N0,
  ST_TET, COLOR1, P1, EB, EA, N0,
 // Case #163: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EB, EL, EG, EE, EI, ED,
  ST_TET, COLOR0, P6, P4, P2, N0,
  ST_PYR, COLOR0, P6, EG, EE, P4, N0,
  ST_TET, COLOR0, P3, P6, P2, N0,
  ST_PYR, COLOR0, P3, EL, EG, P6, N0,
  ST_TET, COLOR0, EB, EL, P3, N0,
  ST_PYR, COLOR0, P3, P2, ED, EB, N0,
  ST_PYR, COLOR0, ED, P2, P4, EI, N0,
  ST_TET, COLOR0, P4, EE, EI, N0,
  ST_TET, COLOR1, P1, P5, P7, N0,
  ST_PYR, COLOR1, EB, P1, P7, EL, N0,
  ST_PYR, COLOR1, EB, ED, P0, P1, N0,
  ST_TET, COLOR1, P0, P5, P1, N0,
  ST_TET, COLOR1, P0, ED, EI, N0,
  ST_PYR, COLOR1, P0, EI, EE, P5, N0,
  ST_PYR, COLOR1, EE, EG, P7, P5, N0,
  ST_TET, COLOR1, EG, EL, P7, N0,
 // Case #164: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EE, EJ, ED, ED,
  ST_PYR, COLOR0, P0, P3, EC, ED, N0,
  ST_TET, COLOR0, EC, P3, EL, N0,
  ST_PYR, COLOR0, P1, EJ, EL, P3, N0,
  ST_TET, COLOR0, P0, P1, P3, N0,
  ST_TET, COLOR0, P6, EK, EG, N0,
  ST_TET, COLOR0, P4, P0, P6, N0,
  ST_PYR, COLOR0, P6, P0, ED, EK, N0,
  ST_TET, COLOR0, P4, P1, P0, N0,
  ST_PYR, COLOR0, P4, EE, EJ, P1, N0,
  ST_PYR, COLOR0, P4, P6, EG, EE, N0,
  ST_PYR, COLOR1, P2, EC, EL, P7, N0,
  ST_PYR, COLOR1, EG, EK, P2, P7, N0,
  ST_TET, COLOR1, P2, ED, EC, N0,
  ST_TET, COLOR1, EK, ED, P2, N0,
  ST_PYR, COLOR1, EL, EJ, P5, P7, N0,
  ST_PYR, COLOR1, P7, P5, EE, EG, N0,
  ST_TET, COLOR1, EE, P5, EJ, N0,
 // Case #165: (cloned #60)
  ST_WDG, COLOR0, P4, EI, EE, P6, EK, EG,
  ST_WDG, COLOR0, P1, EJ, EA, P3, EL, EC,
  ST_HEX, COLOR1, EA, EJ, EL, EC, P0, P5, P7, P2,
  ST_HEX, COLOR1, P0, P5, P7, P2, EI, EE, EG, EK,
 // Case #166: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EE, EG, EA, ED, EK,
  ST_TET, COLOR0, P3, EB, EL, EC,
  ST_PYR, COLOR0, P6, EG, EE, P4, N0,
  ST_PYR, COLOR0, P4, EE, EA, P0, N0,
  ST_TET, COLOR0, P0, EA, ED, N0,
  ST_TET, COLOR0, P4, P0, P6, N0,
  ST_PYR, COLOR0, P0, ED, EK, P6, N0,
  ST_TET, COLOR0, P6, EK, EG, N0,
  ST_WDG, COLOR1, P1, P7, P2, EB, EL, EC,
  ST_TET, COLOR1, P7, P2, P1, N0,
  ST_PYR, COLOR1, P1, P2, ED, EA, N0,
  ST_TET, COLOR1, P5, P7, P1, N0,
  ST_PYR, COLOR1, EG, P7, P5, EE, N0,
  ST_PYR, COLOR1, EE, P5, P1, EA, N0,
  ST_PYR, COLOR1, P2, P7, EG, EK, N0,
  ST_TET, COLOR1, ED, P2, EK, N0,
 // Case #167: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P1, P2, P0, P5, EG, EE,
  ST_WDG, COLOR0, EE, P4, EI, EG, P6, EK,
  ST_TET, COLOR0, EB, P3, EC, EL,
  ST_WDG, COLOR1, P1, P7, P2, EB, EL, EC,
  ST_PYR, COLOR1, EE, EG, P7, P5, N0,
  ST_TET, COLOR1, P5, P7, P1, N0,
  ST_TET, COLOR1, P5, P1, P0, N0,
  ST_PYR, COLOR1, EI, EE, P5, P0, N0,
  ST_PYR, COLOR1, EK, EI, P0, P2, N0,
  ST_TET, COLOR1, P0, P1, P2, N0,
  ST_PYR, COLOR1, EG, EE, EI, EK, N0,
  ST_TET, COLOR1, P2, P1, P7, N0,
  ST_PYR, COLOR1, EG, EK, P2, P7, N0,
 // Case #168: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EC, EG, EB, EJ, EE,
  ST_PYR, COLOR0, P2, P6, P4, P0, N0,
  ST_TET, COLOR0, P4, N0, P1, P0,
  ST_PYR, COLOR0, P6, EG, EE, P4, N0,
  ST_PYR, COLOR0, EC, EG, P6, P2, N0,
  ST_TET, COLOR0, P1, P2, P0, N0,
  ST_PYR, COLOR0, P1, EB, EC, P2, N0,
  ST_TET, COLOR0, P1, EJ, EB, N0,
  ST_PYR, COLOR0, EE, EJ, P1, P4, N0,
  ST_PYR, COLOR1, EG, P7, P5, EE, N0,
  ST_PYR, COLOR1, EC, P3, P7, EG, N0,
  ST_TET, COLOR1, EB, P3, EC, N0,
  ST_TET, COLOR1, P3, P5, P7, N0,
  ST_PYR, COLOR1, EB, EJ, P5, P3, N0,
  ST_TET, COLOR1, P5, EJ, EE, N0,
 // Case #169: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EG, EE, EC, ED, EI,
  ST_TET, COLOR0, P1, EJ, EB, EA,
  ST_PYR, COLOR0, P4, P6, EG, EE, N0,
  ST_PYR, COLOR0, P6, P2, EC, EG, N0,
  ST_TET, COLOR0, P2, ED, EC, N0,
  ST_TET, COLOR0, P6, P4, P2, N0,
  ST_PYR, COLOR0, P2, P4, EI, ED, N0,
  ST_TET, COLOR0, P4, EE, EI, N0,
  ST_WDG, COLOR1, EB, EJ, EA, P3, P5, P0,
  ST_TET, COLOR1, P5, P3, P0, N0,
  ST_PYR, COLOR1, P3, EC, ED, P0, N0,
  ST_TET, COLOR1, P7, P3, P5, N0,
  ST_PYR, COLOR1, EE, EG, P7, P5, N0,
  ST_PYR, COLOR1, EG, EC, P3, P7, N0,
  ST_PYR, COLOR1, P0, EI, EE, P5, N0,
  ST_TET, COLOR1, ED, EI, P0, N0,
 // Case #170: (cloned #15)
  ST_HEX, COLOR0, EA, EE, EG, EC, P0, P4, P6, P2,
  ST_HEX, COLOR1, P1, P5, P7, P3, EA, EE, EG, EC,
 // Case #171: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EE, EG, EC, EI, ED,
  ST_PYR, COLOR0, P6, P2, EC, EG, N0,
  ST_TET, COLOR0, P2, ED, EC, N0,
  ST_PYR, COLOR0, P4, P6, EG, EE, N0,
  ST_TET, COLOR0, EI, P4, EE, N0,
  ST_PYR, COLOR0, ED, P2, P4, EI, N0,
  ST_TET, COLOR0, P2, P6, P4, N0,
  ST_PYR, COLOR1, P1, P5, P7, P3, N0,
  ST_TET, COLOR1, P3, P0, P1, N0,
  ST_TET, COLOR1, P0, P5, P1, N0,
  ST_PYR, COLOR1, P0, EI, EE, P5, N0,
  ST_PYR, COLOR1, EE, EG, P7, P5, N0,
  ST_PYR, COLOR1, EG, EC, P3, P7, N0,
  ST_PYR, COLOR1, EC, ED, P0, P3, N0,
  ST_TET, COLOR1, EI, P0, ED, N0,
 // Case #172: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EB, EJ, EE, EG, EK, ED,
  ST_TET, COLOR0, P4, P0, P6, N0,
  ST_PYR, COLOR0, P4, P6, EG, EE, N0,
  ST_TET, COLOR0, P1, P0, P4, N0,
  ST_PYR, COLOR0, P1, P4, EE, EJ, N0,
  ST_TET, COLOR0, EB, P1, EJ, N0,
  ST_PYR, COLOR0, P1, EB, ED, P0, N0,
  ST_PYR, COLOR0, ED, EK, P6, P0, N0,
  ST_TET, COLOR0, P6, EK, EG, N0,
  ST_TET, COLOR1, P3, P5, P7, N0,
  ST_PYR, COLOR1, EB, EJ, P5, P3, N0,
  ST_PYR, COLOR1, EB, P3, P2, ED, N0,
  ST_TET, COLOR1, P2, P3, P7, N0,
  ST_TET, COLOR1, P2, EK, ED, N0,
  ST_PYR, COLOR1, P2, P7, EG, EK, N0,
  ST_PYR, COLOR1, EG, P7, P5, EE, N0,
  ST_TET, COLOR1, EE, P5, EJ, N0,
 // Case #173: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P3, P0, P2, P7, EE, EG,
  ST_WDG, COLOR0, EE, P4, EI, EG, P6, EK,
  ST_TET, COLOR0, EB, EA, P1, EJ,
  ST_WDG, COLOR1, EB, EJ, EA, P3, P5, P0,
  ST_PYR, COLOR1, EG, P7, P5, EE, N0,
  ST_TET, COLOR1, P7, P3, P5, N0,
  ST_TET, COLOR1, P7, P2, P3, N0,
  ST_PYR, COLOR1, EK, P2, P7, EG, N0,
  ST_PYR, COLOR1, EI, P0, P2, EK, N0,
  ST_TET, COLOR1, P2, P0, P3, N0,
  ST_PYR, COLOR1, EE, EI, EK, EG, N0,
  ST_TET, COLOR1, P0, P5, P3, N0,
  ST_PYR, COLOR1, EE, P5, P0, EI, N0,
 // Case #174: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EG, EE, EA, EK, ED,
  ST_PYR, COLOR0, P4, EE, EA, P0, N0,
  ST_TET, COLOR0, P0, EA, ED, N0,
  ST_PYR, COLOR0, P6, EG, EE, P4, N0,
  ST_TET, COLOR0, EK, EG, P6, N0,
  ST_PYR, COLOR0, ED, EK, P6, P0, N0,
  ST_TET, COLOR0, P0, P6, P4, N0,
  ST_PYR, COLOR1, P3, P1, P5, P7, N0,
  ST_TET, COLOR1, P1, P3, P2, N0,
  ST_TET, COLOR1, P2, P3, P7, N0,
  ST_PYR, COLOR1, P2, P7, EG, EK, N0,
  ST_PYR, COLOR1, EG, P7, P5, EE, N0,
  ST_PYR, COLOR1, EE, P5, P1, EA, N0,
  ST_PYR, COLOR1, EA, P1, P2, ED, N0,
  ST_TET, COLOR1, EK, ED, P2, N0,
 // Case #175: (cloned #63)
  ST_WDG, COLOR0, P4, EI, EE, P6, EK, EG,
  ST_HEX, COLOR1, P0, P5, P7, P2, EI, EE, EG, EK,
  ST_WDG, COLOR1, P3, P2, P7, P1, P0, P5,
 // Case #176: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EL, EJ, EG, EH, EI,
  ST_PYR, COLOR0, P3, P2, P0, P1, N0,
  ST_TET, COLOR0, P0, P6, N0, P2,
  ST_PYR, COLOR0, P1, P0, EI, EJ, N0,
  ST_PYR, COLOR0, EL, P3, P1, EJ, N0,
  ST_TET, COLOR0, P6, P2, P3, N0,
  ST_PYR, COLOR0, P6, P3, EL, EG, N0,
  ST_TET, COLOR0, P6, EG, EH, N0,
  ST_PYR, COLOR0, EI, P0, P6, EH, N0,
  ST_PYR, COLOR1, EJ, EI, P4, P5, N0,
  ST_PYR, COLOR1, EL, EJ, P5, P7, N0,
  ST_TET, COLOR1, EG, EL, P7, N0,
  ST_TET, COLOR1, P7, P5, P4, N0,
  ST_PYR, COLOR1, EG, P7, P4, EH, N0,
  ST_TET, COLOR1, P4, EI, EH, N0,
 // Case #177: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EH, EG, EL, EJ, EA, ED,
  ST_TET, COLOR0, P3, P2, P1, N0,
  ST_PYR, COLOR0, P3, P1, EJ, EL, N0,
  ST_TET, COLOR0, P6, P2, P3, N0,
  ST_PYR, COLOR0, P6, P3, EL, EG, N0,
  ST_TET, COLOR0, EH, P6, EG, N0,
  ST_PYR, COLOR0, P6, EH, ED, P2, N0,
  ST_PYR, COLOR0, ED, EA, P1, P2, N0,
  ST_TET, COLOR0, P1, EA, EJ, N0,
  ST_TET, COLOR1, P4, P7, P5, N0,
  ST_PYR, COLOR1, EH, EG, P7, P4, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_TET, COLOR1, P0, P4, P5, N0,
  ST_TET, COLOR1, P0, EA, ED, N0,
  ST_PYR, COLOR1, P0, P5, EJ, EA, N0,
  ST_PYR, COLOR1, EJ, P5, P7, EL, N0,
  ST_TET, COLOR1, EL, P7, EG, N0,
 // Case #178: (cloned #23)
  ST_TET, COLOR0, P6, P0, P2, P3,
  ST_PYR, COLOR0, EB, P3, P0, EA, EI,
  ST_PYR, COLOR0, EL, EG, P6, P3, EH,
  ST_PYR, COLOR0, P0, P6, EH, EI, P3,
  ST_PYR, COLOR0, EI, EH, EL, EB, P3,
  ST_TET, COLOR1, P5, P7, P1, P4,
  ST_PYR, COLOR1, EI, P4, P1, EA, EB,
  ST_PYR, COLOR1, EH, EG, P7, P4, EL,
  ST_PYR, COLOR1, P1, P7, EL, EB, P4,
  ST_PYR, COLOR1, EI, EB, EL, EH, P4,
 // Case #179: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EH, ED, EB, EG, EL,
  ST_PYR, COLOR0, P2, ED, EB, P3, N0,
  ST_TET, COLOR0, P3, EB, EL, N0,
  ST_PYR, COLOR0, P6, EH, ED, P2, N0,
  ST_TET, COLOR0, EG, EH, P6, N0,
  ST_PYR, COLOR0, EL, EG, P6, P3, N0,
  ST_TET, COLOR0, P3, P6, P2, N0,
  ST_PYR, COLOR1, P5, P1, P0, P4, N0,
  ST_TET, COLOR1, P1, P5, P7, N0,
  ST_TET, COLOR1, P7, P5, P4, N0,
  ST_PYR, COLOR1, P7, P4, EH, EG, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_PYR, COLOR1, ED, P0, P1, EB, N0,
  ST_PYR, COLOR1, EB, P1, P7, EL, N0,
  ST_TET, COLOR1, EG, EL, P7, N0,
 // Case #180: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EJ, EL, EI, ED, EC,
  ST_TET, COLOR0, P6, EG, EH, EK,
  ST_PYR, COLOR0, P3, P1, EJ, EL, N0,
  ST_PYR, COLOR0, P1, P0, EI, EJ, N0,
  ST_TET, COLOR0, P0, ED, EI, N0,
  ST_TET, COLOR0, P1, P3, P0, N0,
  ST_PYR, COLOR0, P0, P3, EC, ED, N0,
  ST_TET, COLOR0, P3, EL, EC, N0,
  ST_WDG, COLOR1, EH, EG, EK, P4, P7, P2,
  ST_TET, COLOR1, P7, P4, P2, N0,
  ST_PYR, COLOR1, P4, EI, ED, P2, N0,
  ST_TET, COLOR1, P5, P4, P7, N0,
  ST_PYR, COLOR1, EL, EJ, P5, P7, N0,
  ST_PYR, COLOR1, EJ, EI, P4, P5, N0,
  ST_PYR, COLOR1, P2, EC, EL, P7, N0,
  ST_TET, COLOR1, ED, EC, P2, N0,
 // Case #181: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P4, P2, P0, P5, EL, EJ,
  ST_WDG, COLOR0, EL, P3, EC, EJ, P1, EA,
  ST_TET, COLOR0, EH, EK, P6, EG,
  ST_WDG, COLOR1, EH, EG, EK, P4, P7, P2,
  ST_PYR, COLOR1, EJ, P5, P7, EL, N0,
  ST_TET, COLOR1, P5, P4, P7, N0,
  ST_TET, COLOR1, P5, P0, P4, N0,
  ST_PYR, COLOR1, EA, P0, P5, EJ, N0,
  ST_PYR, COLOR1, EC, P2, P0, EA, N0,
  ST_TET, COLOR1, P0, P2, P4, N0,
  ST_PYR, COLOR1, EL, EC, EA, EJ, N0,
  ST_TET, COLOR1, P2, P7, P4, N0,
  ST_PYR, COLOR1, EL, P7, P2, EC, N0,
 // Case #182: (cloned #107)
  ST_TET, COLOR0, EB, EC, EL, P3,
  ST_TET, COLOR0, EA, P0, EI, ED,
  ST_TET, COLOR0, EG, EH, P6, EK,
  ST_WDG, COLOR1, EK, EH, EG, P2, P4, P7,
  ST_WDG, COLOR1, P1, P2, P4, EA, ED, EI,
  ST_WDG, COLOR1, EB, EC, EL, P1, P2, P7,
  ST_TET, COLOR1, P2, P7, P4, P1,
  ST_TET, COLOR1, P1, P5, P7, P4,
 // Case #183: (cloned #111)
  ST_TET, COLOR0, EG, EK, EH, P6,
  ST_TET, COLOR0, EL, EB, EC, P3,
  ST_WDG, COLOR1, EC, EL, EB, P2, P7, P1,
  ST_WDG, COLOR1, P2, P7, P4, EK, EG, EH,
  ST_PYR, COLOR1, P0, P4, P5, P1, P2,
  ST_TET, COLOR1, P7, P5, P4, P2,
  ST_TET, COLOR1, P7, P1, P5, P2,
 // Case #184: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EJ, EB, EC, EG, EH, EI,
  ST_TET, COLOR0, P2, P6, P0, N0,
  ST_PYR, COLOR0, P2, EC, EG, P6, N0,
  ST_TET, COLOR0, P1, P2, P0, N0,
  ST_PYR, COLOR0, P1, EB, EC, P2, N0,
  ST_TET, COLOR0, EJ, EB, P1, N0,
  ST_PYR, COLOR0, P1, P0, EI, EJ, N0,
  ST_PYR, COLOR0, EI, P0, P6, EH, N0,
  ST_TET, COLOR0, P6, EG, EH, N0,
  ST_TET, COLOR1, P5, P7, P3, N0,
  ST_PYR, COLOR1, EJ, P5, P3, EB, N0,
  ST_PYR, COLOR1, EJ, EI, P4, P5, N0,
  ST_TET, COLOR1, P4, P7, P5, N0,
  ST_TET, COLOR1, P4, EI, EH, N0,
  ST_PYR, COLOR1, P4, EH, EG, P7, N0,
  ST_PYR, COLOR1, EG, EC, P3, P7, N0,
  ST_TET, COLOR1, EC, EB, P3, N0,
 // Case #185: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P5, P0, P4, P7, EC, EG,
  ST_WDG, COLOR0, EG, P6, EH, EC, P2, ED,
  ST_TET, COLOR0, EJ, P1, EA, EB,
  ST_WDG, COLOR1, P5, P3, P0, EJ, EB, EA,
  ST_PYR, COLOR1, EG, EC, P3, P7, N0,
  ST_TET, COLOR1, P7, P3, P5, N0,
  ST_TET, COLOR1, P7, P5, P4, N0,
  ST_PYR, COLOR1, EH, EG, P7, P4, N0,
  ST_PYR, COLOR1, ED, EH, P4, P0, N0,
  ST_TET, COLOR1, P4, P5, P0, N0,
  ST_PYR, COLOR1, EC, EG, EH, ED, N0,
  ST_TET, COLOR1, P0, P5, P3, N0,
  ST_PYR, COLOR1, EC, ED, P0, P3, N0,
 // Case #186: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EG, EC, EA, EH, EI,
  ST_PYR, COLOR0, P2, P0, EA, EC, N0,
  ST_TET, COLOR0, P0, EI, EA, N0,
  ST_PYR, COLOR0, P6, P2, EC, EG, N0,
  ST_TET, COLOR0, EH, P6, EG, N0,
  ST_PYR, COLOR0, EI, P0, P6, EH, N0,
  ST_TET, COLOR0, P0, P2, P6, N0,
  ST_PYR, COLOR1, P5, P7, P3, P1, N0,
  ST_TET, COLOR1, P1, P4, P5, N0,
  ST_TET, COLOR1, P4, P7, P5, N0,
  ST_PYR, COLOR1, P4, EH, EG, P7, N0,
  ST_PYR, COLOR1, EG, EC, P3, P7, N0,
  ST_PYR, COLOR1, EC, EA, P1, P3, N0,
  ST_PYR, COLOR1, EA, EI, P4, P1, N0,
  ST_TET, COLOR1, EH, P4, EI, N0,
 // Case #187: (cloned #63)
  ST_WDG, COLOR0, P6, EH, EG, P2, ED, EC,
  ST_HEX, COLOR1, ED, EC, EG, EH, P0, P3, P7, P4,
  ST_WDG, COLOR1, P1, P0, P3, P5, P4, P7,
 // Case #188: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P7, P4, P5, P3, ED, EB,
  ST_WDG, COLOR0, ED, P0, EI, EB, P1, EJ,
  ST_TET, COLOR0, EG, EH, P6, EK,
  ST_WDG, COLOR1, EG, EK, EH, P7, P2, P4,
  ST_PYR, COLOR1, EB, P3, P2, ED, N0,
  ST_TET, COLOR1, P3, P7, P2, N0,
  ST_TET, COLOR1, P3, P5, P7, N0,
  ST_PYR, COLOR1, EJ, P5, P3, EB, N0,
  ST_PYR, COLOR1, EI, P4, P5, EJ, N0,
  ST_TET, COLOR1, P5, P4, P7, N0,
  ST_PYR, COLOR1, ED, EI, EJ, EB, N0,
  ST_TET, COLOR1, P4, P2, P7, N0,
  ST_PYR, COLOR1, ED, P2, P4, EI, N0,
 // Case #189: (cloned #126)
  ST_TET, COLOR0, EJ, EA, EB, P1,
  ST_TET, COLOR0, EH, EG, EK, P6,
  ST_WDG, COLOR1, P0, P5, P3, EA, EJ, EB,
  ST_WDG, COLOR1, P4, P2, P7, EH, EK, EG,
  ST_TET, COLOR1, P7, P2, P3, P4,
  ST_TET, COLOR1, P4, P5, P0, P3,
  ST_TET, COLOR1, P0, P2, P4, P3,
  ST_TET, COLOR1, P4, P7, P5, P3,
 // Case #190: (cloned #111)
  ST_TET, COLOR0, EI, ED, EA, P0,
  ST_TET, COLOR0, EH, EG, EK, P6,
  ST_WDG, COLOR1, EK, EH, EG, P2, P4, P7,
  ST_WDG, COLOR1, P2, P4, P1, ED, EI, EA,
  ST_PYR, COLOR1, P3, P1, P5, P7, P2,
  ST_TET, COLOR1, P4, P5, P1, P2,
  ST_TET, COLOR1, P4, P7, P5, P2,
 // Case #191: (cloned #127)
  ST_PNT, 0, COLOR1, 7, P5, P1, P0, P4, P7, P3, P2,
  ST_TET, COLOR0, EG, EK, EH, P6,
  ST_WDG, COLOR1, EG, EK, EH, P7, P2, P4,
  ST_TET, COLOR1, P7, P4, P2, N0,
  ST_PYR, COLOR1, P3, P2, P0, P1, N0,
  ST_TET, COLOR1, P2, P4, P0, N0,
  ST_PYR, COLOR1, P5, P1, P0, P4, N0,
  ST_TET, COLOR1, P5, P4, P7, N0,
  ST_PYR, COLOR1, P5, P7, P3, P1, N0,
  ST_TET, COLOR1, P7, P2, P3, N0,
 // Case #192: (cloned #3)
  ST_HEX, COLOR0, EF, P5, P4, EH, EL, P3, P2, EK,
  ST_WDG, COLOR0, P5, P1, P3, P4, P0, P2,
  ST_WDG, COLOR1, P7, EF, EL, P6, EH, EK,
 // Case #193: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EL, EF, EA, EA,
  ST_PYR, COLOR0, P1, EA, EI, P4, N0,
  ST_TET, COLOR0, EI, EH, P4, N0,
  ST_PYR, COLOR0, P5, P4, EH, EF, N0,
  ST_TET, COLOR0, P1, P4, P5, N0,
  ST_TET, COLOR0, P2, EK, ED, N0,
  ST_TET, COLOR0, P3, P2, P1, N0,
  ST_PYR, COLOR0, P2, ED, EA, P1, N0,
  ST_TET, COLOR0, P3, P1, P5, N0,
  ST_PYR, COLOR0, P3, P5, EF, EL, N0,
  ST_PYR, COLOR0, P3, EL, EK, P2, N0,
  ST_PYR, COLOR1, P0, P6, EH, EI, N0,
  ST_PYR, COLOR1, EK, P6, P0, ED, N0,
  ST_TET, COLOR1, P0, EI, EA, N0,
  ST_TET, COLOR1, ED, P0, EA, N0,
  ST_PYR, COLOR1, EH, P6, P7, EF, N0,
  ST_PYR, COLOR1, P6, EK, EL, P7, N0,
  ST_TET, COLOR1, EL, EF, P7, N0,
 // Case #194: (cloned #25)
  ST_PNT, 0, NOCOLOR, 4, EK, EH, EA, EA,
  ST_PYR, COLOR0, P0, P5, EJ, EA, N0,
  ST_TET, COLOR0, EJ, P5, EF, N0,
  ST_PYR, COLOR0, P4, EH, EF, P5, N0,
  ST_TET, COLOR0, P0, P4, P5, N0,
  ST_TET, COLOR0, P3, EB, EL, N0,
  ST_TET, COLOR0, P2, P0, P3, N0,
  ST_PYR, COLOR0, P3, P0, EA, EB, N0,
  ST_TET, COLOR0, P2, P4, P0, N0,
  ST_PYR, COLOR0, P2, EK, EH, P4, N0,
  ST_PYR, COLOR0, P2, P3, EL, EK, N0,
  ST_PYR, COLOR1, P1, EJ, EF, P7, N0,
  ST_PYR, COLOR1, EL, EB, P1, P7, N0,
  ST_TET, COLOR1, P1, EA, EJ, N0,
  ST_TET, COLOR1, EB, EA, P1, N0,
  ST_PYR, COLOR1, EF, EH, P6, P7, N0,
  ST_PYR, COLOR1, P7, P6, EK, EL, N0,
  ST_TET, COLOR1, EK, P6, EH, N0,
 // Case #195: (cloned #60)
  ST_WDG, COLOR0, P2, ED, EK, P3, EB, EL,
  ST_WDG, COLOR0, P4, EH, EI, P5, EF, EJ,
  ST_HEX, COLOR1, EI, EH, EF, EJ, P0, P6, P7, P1,
  ST_HEX, COLOR1, P0, P6, P7, P1, ED, EK, EL, EB,
 // Case #196: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EF, EH, EL, EC, ED,
  ST_PYR, COLOR0, P5, P1, P0, P4, N0,
  ST_TET, COLOR0, P0, P3, N0, P1,
  ST_PYR, COLOR0, P4, P0, ED, EH, N0,
  ST_PYR, COLOR0, EF, P5, P4, EH, N0,
  ST_TET, COLOR0, P3, P1, P5, N0,
  ST_PYR, COLOR0, P3, P5, EF, EL, N0,
  ST_TET, COLOR0, P3, EL, EC, N0,
  ST_PYR, COLOR0, ED, P0, P3, EC, N0,
  ST_PYR, COLOR1, EH, ED, P2, P6, N0,
  ST_PYR, COLOR1, EF, EH, P6, P7, N0,
  ST_TET, COLOR1, EL, EF, P7, N0,
  ST_TET, COLOR1, P7, P6, P2, N0,
  ST_PYR, COLOR1, EL, P7, P2, EC, N0,
  ST_TET, COLOR1, P2, ED, EC, N0,
 // Case #197: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EC, EL, EF, EH, EI, EA,
  ST_TET, COLOR0, P5, P1, P4, N0,
  ST_PYR, COLOR0, P5, P4, EH, EF, N0,
  ST_TET, COLOR0, P3, P1, P5, N0,
  ST_PYR, COLOR0, P3, P5, EF, EL, N0,
  ST_TET, COLOR0, EC, P3, EL, N0,
  ST_PYR, COLOR0, P3, EC, EA, P1, N0,
  ST_PYR, COLOR0, EA, EI, P4, P1, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_TET, COLOR1, P2, P7, P6, N0,
  ST_PYR, COLOR1, EC, EL, P7, P2, N0,
  ST_PYR, COLOR1, EC, P2, P0, EA, N0,
  ST_TET, COLOR1, P0, P2, P6, N0,
  ST_TET, COLOR1, P0, EI, EA, N0,
  ST_PYR, COLOR1, P0, P6, EH, EI, N0,
  ST_PYR, COLOR1, EH, P6, P7, EF, N0,
  ST_TET, COLOR1, EF, P7, EL, N0,
 // Case #198: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EH, EF, ED, EA, EJ,
  ST_TET, COLOR0, P3, EL, EC, EB,
  ST_PYR, COLOR0, P5, P4, EH, EF, N0,
  ST_PYR, COLOR0, P4, P0, ED, EH, N0,
  ST_TET, COLOR0, P0, EA, ED, N0,
  ST_TET, COLOR0, P4, P5, P0, N0,
  ST_PYR, COLOR0, P0, P5, EJ, EA, N0,
  ST_TET, COLOR0, P5, EF, EJ, N0,
  ST_WDG, COLOR1, EC, EL, EB, P2, P7, P1,
  ST_TET, COLOR1, P7, P2, P1, N0,
  ST_PYR, COLOR1, P2, ED, EA, P1, N0,
  ST_TET, COLOR1, P6, P2, P7, N0,
  ST_PYR, COLOR1, EF, EH, P6, P7, N0,
  ST_PYR, COLOR1, EH, ED, P2, P6, N0,
  ST_PYR, COLOR1, P1, EJ, EF, P7, N0,
  ST_TET, COLOR1, EA, EJ, P1, N0,
 // Case #199: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P2, P1, P0, P6, EF, EH,
  ST_WDG, COLOR0, EF, P5, EJ, EH, P4, EI,
  ST_TET, COLOR0, EC, EB, P3, EL,
  ST_WDG, COLOR1, EC, EL, EB, P2, P7, P1,
  ST_PYR, COLOR1, EH, P6, P7, EF, N0,
  ST_TET, COLOR1, P6, P2, P7, N0,
  ST_TET, COLOR1, P6, P0, P2, N0,
  ST_PYR, COLOR1, EI, P0, P6, EH, N0,
  ST_PYR, COLOR1, EJ, P1, P0, EI, N0,
  ST_TET, COLOR1, P0, P1, P2, N0,
  ST_PYR, COLOR1, EF, EJ, EI, EH, N0,
  ST_TET, COLOR1, P1, P7, P2, N0,
  ST_PYR, COLOR1, EF, P7, P1, EJ, N0,
 // Case #200: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EH, EF, EK, EC, EB,
  ST_PYR, COLOR0, P4, P5, P1, P0, N0,
  ST_TET, COLOR0, P1, N0, P2, P0,
  ST_PYR, COLOR0, P5, EF, EB, P1, N0,
  ST_PYR, COLOR0, EH, EF, P5, P4, N0,
  ST_TET, COLOR0, P2, P4, P0, N0,
  ST_PYR, COLOR0, P2, EK, EH, P4, N0,
  ST_TET, COLOR0, P2, EC, EK, N0,
  ST_PYR, COLOR0, EB, EC, P2, P1, N0,
  ST_PYR, COLOR1, EF, P7, P3, EB, N0,
  ST_PYR, COLOR1, EH, P6, P7, EF, N0,
  ST_TET, COLOR1, EK, P6, EH, N0,
  ST_TET, COLOR1, P6, P3, P7, N0,
  ST_PYR, COLOR1, EK, EC, P3, P6, N0,
  ST_TET, COLOR1, P3, EC, EB, N0,
 // Case #201: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EF, EB, EH, EI, EA,
  ST_TET, COLOR0, P2, EC, EK, ED,
  ST_PYR, COLOR0, P1, P5, EF, EB, N0,
  ST_PYR, COLOR0, P5, P4, EH, EF, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_TET, COLOR0, P5, P1, P4, N0,
  ST_PYR, COLOR0, P4, P1, EA, EI, N0,
  ST_TET, COLOR0, P1, EB, EA, N0,
  ST_WDG, COLOR1, EK, EC, ED, P6, P3, P0,
  ST_TET, COLOR1, P3, P6, P0, N0,
  ST_PYR, COLOR1, P6, EH, EI, P0, N0,
  ST_TET, COLOR1, P7, P6, P3, N0,
  ST_PYR, COLOR1, EB, EF, P7, P3, N0,
  ST_PYR, COLOR1, EF, EH, P6, P7, N0,
  ST_PYR, COLOR1, P0, EA, EB, P3, N0,
  ST_TET, COLOR1, EI, EA, P0, N0,
 // Case #202: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EC, EK, EH, EF, EJ, EA,
  ST_TET, COLOR0, P4, P5, P0, N0,
  ST_PYR, COLOR0, P4, EH, EF, P5, N0,
  ST_TET, COLOR0, P2, P4, P0, N0,
  ST_PYR, COLOR0, P2, EK, EH, P4, N0,
  ST_TET, COLOR0, EC, EK, P2, N0,
  ST_PYR, COLOR0, P2, P0, EA, EC, N0,
  ST_PYR, COLOR0, EA, P0, P5, EJ, N0,
  ST_TET, COLOR0, P5, EF, EJ, N0,
  ST_TET, COLOR1, P3, P7, P6, N0,
  ST_PYR, COLOR1, EC, P3, P6, EK, N0,
  ST_PYR, COLOR1, EC, EA, P1, P3, N0,
  ST_TET, COLOR1, P1, P7, P3, N0,
  ST_TET, COLOR1, P1, EA, EJ, N0,
  ST_PYR, COLOR1, P1, EJ, EF, P7, N0,
  ST_PYR, COLOR1, EF, EH, P6, P7, N0,
  ST_TET, COLOR1, EH, EK, P6, N0,
 // Case #203: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P3, P0, P1, P7, EH, EF,
  ST_WDG, COLOR0, EF, P5, EJ, EH, P4, EI,
  ST_TET, COLOR0, EC, P2, ED, EK,
  ST_WDG, COLOR1, P3, P6, P0, EC, EK, ED,
  ST_PYR, COLOR1, EF, EH, P6, P7, N0,
  ST_TET, COLOR1, P7, P6, P3, N0,
  ST_TET, COLOR1, P7, P3, P1, N0,
  ST_PYR, COLOR1, EJ, EF, P7, P1, N0,
  ST_PYR, COLOR1, EI, EJ, P1, P0, N0,
  ST_TET, COLOR1, P1, P3, P0, N0,
  ST_PYR, COLOR1, EH, EF, EJ, EI, N0,
  ST_TET, COLOR1, P0, P3, P6, N0,
  ST_PYR, COLOR1, EH, EI, P0, P6, N0,
 // Case #204: (cloned #15)
  ST_HEX, COLOR0, ED, EB, EF, EH, P0, P1, P5, P4,
  ST_HEX, COLOR1, P2, P3, P7, P6, ED, EB, EF, EH,
 // Case #205: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EB, EF, EH, EA, EI,
  ST_PYR, COLOR0, P5, P4, EH, EF, N0,
  ST_TET, COLOR0, P4, EI, EH, N0,
  ST_PYR, COLOR0, P1, P5, EF, EB, N0,
  ST_TET, COLOR0, EA, P1, EB, N0,
  ST_PYR, COLOR0, EI, P4, P1, EA, N0,
  ST_TET, COLOR0, P4, P5, P1, N0,
  ST_PYR, COLOR1, P2, P3, P7, P6, N0,
  ST_TET, COLOR1, P6, P0, P2, N0,
  ST_TET, COLOR1, P0, P3, P2, N0,
  ST_PYR, COLOR1, P0, EA, EB, P3, N0,
  ST_PYR, COLOR1, EB, EF, P7, P3, N0,
  ST_PYR, COLOR1, EF, EH, P6, P7, N0,
  ST_PYR, COLOR1, EH, EI, P0, P6, N0,
  ST_TET, COLOR1, EA, P0, EI, N0,
 // Case #206: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EF, EH, ED, EJ, EA,
  ST_PYR, COLOR0, P4, P0, ED, EH, N0,
  ST_TET, COLOR0, P0, EA, ED, N0,
  ST_PYR, COLOR0, P5, P4, EH, EF, N0,
  ST_TET, COLOR0, EJ, P5, EF, N0,
  ST_PYR, COLOR0, EA, P0, P5, EJ, N0,
  ST_TET, COLOR0, P0, P4, P5, N0,
  ST_PYR, COLOR1, P3, P7, P6, P2, N0,
  ST_TET, COLOR1, P2, P1, P3, N0,
  ST_TET, COLOR1, P1, P7, P3, N0,
  ST_PYR, COLOR1, P1, EJ, EF, P7, N0,
  ST_PYR, COLOR1, EF, EH, P6, P7, N0,
  ST_PYR, COLOR1, EH, ED, P2, P6, N0,
  ST_PYR, COLOR1, ED, EA, P1, P2, N0,
  ST_TET, COLOR1, EJ, P1, EA, N0,
 // Case #207: (cloned #63)
  ST_WDG, COLOR0, P5, EJ, EF, P4, EI, EH,
  ST_HEX, COLOR1, EI, EH, EF, EJ, P0, P6, P7, P1,
  ST_WDG, COLOR1, P2, P0, P6, P3, P1, P7,
 // Case #208: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EL, EK, EF, EE, EI,
  ST_PYR, COLOR0, P3, P2, P0, P1, N0,
  ST_TET, COLOR0, P0, N0, P5, P1,
  ST_PYR, COLOR0, P2, EK, EI, P0, N0,
  ST_PYR, COLOR0, EL, EK, P2, P3, N0,
  ST_TET, COLOR0, P5, P3, P1, N0,
  ST_PYR, COLOR0, P5, EF, EL, P3, N0,
  ST_TET, COLOR0, P5, EE, EF, N0,
  ST_PYR, COLOR0, EI, EE, P5, P0, N0,
  ST_PYR, COLOR1, EK, P6, P4, EI, N0,
  ST_PYR, COLOR1, EL, P7, P6, EK, N0,
  ST_TET, COLOR1, EF, P7, EL, N0,
  ST_TET, COLOR1, P7, P4, P6, N0,
  ST_PYR, COLOR1, EF, EE, P4, P7, N0,
  ST_TET, COLOR1, P4, EE, EI, N0,
 // Case #209: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EE, EF, EL, EK, ED, EA,
  ST_TET, COLOR0, P3, P2, P1, N0,
  ST_PYR, COLOR0, P3, EL, EK, P2, N0,
  ST_TET, COLOR0, P5, P3, P1, N0,
  ST_PYR, COLOR0, P5, EF, EL, P3, N0,
  ST_TET, COLOR0, EE, EF, P5, N0,
  ST_PYR, COLOR0, P5, P1, EA, EE, N0,
  ST_PYR, COLOR0, EA, P1, P2, ED, N0,
  ST_TET, COLOR0, P2, EK, ED, N0,
  ST_TET, COLOR1, P4, P6, P7, N0,
  ST_PYR, COLOR1, EE, P4, P7, EF, N0,
  ST_PYR, COLOR1, EE, EA, P0, P4, N0,
  ST_TET, COLOR1, P0, P6, P4, N0,
  ST_TET, COLOR1, P0, EA, ED, N0,
  ST_PYR, COLOR1, P0, ED, EK, P6, N0,
  ST_PYR, COLOR1, EK, EL, P7, P6, N0,
  ST_TET, COLOR1, EL, EF, P7, N0,
 // Case #210: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EK, EL, EI, EA, EB,
  ST_TET, COLOR0, P5, EE, EF, EJ,
  ST_PYR, COLOR0, P3, EL, EK, P2, N0,
  ST_PYR, COLOR0, P2, EK, EI, P0, N0,
  ST_TET, COLOR0, P0, EI, EA, N0,
  ST_TET, COLOR0, P2, P0, P3, N0,
  ST_PYR, COLOR0, P0, EA, EB, P3, N0,
  ST_TET, COLOR0, P3, EB, EL, N0,
  ST_WDG, COLOR1, P4, P7, P1, EE, EF, EJ,
  ST_TET, COLOR1, P7, P1, P4, N0,
  ST_PYR, COLOR1, P4, P1, EA, EI, N0,
  ST_TET, COLOR1, P6, P7, P4, N0,
  ST_PYR, COLOR1, EL, P7, P6, EK, N0,
  ST_PYR, COLOR1, EK, P6, P4, EI, N0,
  ST_PYR, COLOR1, P1, P7, EL, EB, N0,
  ST_TET, COLOR1, EA, P1, EB, N0,
 // Case #211: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P4, P1, P0, P6, EL, EK,
  ST_WDG, COLOR0, EK, P2, ED, EL, P3, EB,
  ST_TET, COLOR0, EE, P5, EJ, EF,
  ST_WDG, COLOR1, P4, P7, P1, EE, EF, EJ,
  ST_PYR, COLOR1, EK, EL, P7, P6, N0,
  ST_TET, COLOR1, P6, P7, P4, N0,
  ST_TET, COLOR1, P6, P4, P0, N0,
  ST_PYR, COLOR1, ED, EK, P6, P0, N0,
  ST_PYR, COLOR1, EB, ED, P0, P1, N0,
  ST_TET, COLOR1, P0, P4, P1, N0,
  ST_PYR, COLOR1, EL, EK, ED, EB, N0,
  ST_TET, COLOR1, P1, P4, P7, N0,
  ST_PYR, COLOR1, EL, EB, P1, P7, N0,
 // Case #212: (cloned #23)
  ST_TET, COLOR0, P3, P0, P1, P5,
  ST_PYR, COLOR0, EE, P5, P0, EI, ED,
  ST_PYR, COLOR0, EF, EL, P3, P5, EC,
  ST_PYR, COLOR0, P0, P3, EC, ED, P5,
  ST_PYR, COLOR0, ED, EC, EF, EE, P5,
  ST_TET, COLOR1, P6, P7, P4, P2,
  ST_PYR, COLOR1, ED, P2, P4, EI, EE,
  ST_PYR, COLOR1, EC, EL, P7, P2, EF,
  ST_PYR, COLOR1, P4, P7, EF, EE, P2,
  ST_PYR, COLOR1, ED, EE, EF, EC, P2,
 // Case #213: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EC, EA, EE, EL, EF,
  ST_PYR, COLOR0, P1, EA, EE, P5, N0,
  ST_TET, COLOR0, P5, EE, EF, N0,
  ST_PYR, COLOR0, P3, EC, EA, P1, N0,
  ST_TET, COLOR0, EL, EC, P3, N0,
  ST_PYR, COLOR0, EF, EL, P3, P5, N0,
  ST_TET, COLOR0, P5, P3, P1, N0,
  ST_PYR, COLOR1, P6, P4, P0, P2, N0,
  ST_TET, COLOR1, P4, P6, P7, N0,
  ST_TET, COLOR1, P7, P6, P2, N0,
  ST_PYR, COLOR1, P7, P2, EC, EL, N0,
  ST_PYR, COLOR1, EC, P2, P0, EA, N0,
  ST_PYR, COLOR1, EA, P0, P4, EE, N0,
  ST_PYR, COLOR1, EE, P4, P7, EF, N0,
  ST_TET, COLOR1, EL, EF, P7, N0,
 // Case #214: (cloned #107)
  ST_TET, COLOR0, EE, EJ, EF, P5,
  ST_TET, COLOR0, EI, P0, ED, EA,
  ST_TET, COLOR0, EL, EC, P3, EB,
  ST_WDG, COLOR1, EB, EC, EL, P1, P2, P7,
  ST_WDG, COLOR1, P4, P1, P2, EI, EA, ED,
  ST_WDG, COLOR1, EE, EJ, EF, P4, P1, P7,
  ST_TET, COLOR1, P1, P7, P2, P4,
  ST_TET, COLOR1, P4, P6, P7, P2,
 // Case #215: (cloned #111)
  ST_TET, COLOR0, EL, EB, EC, P3,
  ST_TET, COLOR0, EF, EE, EJ, P5,
  ST_WDG, COLOR1, EJ, EF, EE, P1, P7, P4,
  ST_WDG, COLOR1, P1, P7, P2, EB, EL, EC,
  ST_PYR, COLOR1, P0, P2, P6, P4, P1,
  ST_TET, COLOR1, P7, P6, P2, P1,
  ST_TET, COLOR1, P7, P4, P6, P1,
 // Case #216: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EK, EC, EB, EF, EE, EI,
  ST_TET, COLOR0, P1, P0, P5, N0,
  ST_PYR, COLOR0, P1, P5, EF, EB, N0,
  ST_TET, COLOR0, P2, P0, P1, N0,
  ST_PYR, COLOR0, P2, P1, EB, EC, N0,
  ST_TET, COLOR0, EK, P2, EC, N0,
  ST_PYR, COLOR0, P2, EK, EI, P0, N0,
  ST_PYR, COLOR0, EI, EE, P5, P0, N0,
  ST_TET, COLOR0, P5, EE, EF, N0,
  ST_TET, COLOR1, P6, P3, P7, N0,
  ST_PYR, COLOR1, EK, EC, P3, P6, N0,
  ST_PYR, COLOR1, EK, P6, P4, EI, N0,
  ST_TET, COLOR1, P4, P6, P7, N0,
  ST_TET, COLOR1, P4, EE, EI, N0,
  ST_PYR, COLOR1, P4, P7, EF, EE, N0,
  ST_PYR, COLOR1, EF, P7, P3, EB, N0,
  ST_TET, COLOR1, EB, P3, EC, N0,
 // Case #217: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P6, P0, P4, P7, EB, EF,
  ST_WDG, COLOR0, EB, P1, EA, EF, P5, EE,
  ST_TET, COLOR0, EK, ED, P2, EC,
  ST_WDG, COLOR1, EK, EC, ED, P6, P3, P0,
  ST_PYR, COLOR1, EF, P7, P3, EB, N0,
  ST_TET, COLOR1, P7, P6, P3, N0,
  ST_TET, COLOR1, P7, P4, P6, N0,
  ST_PYR, COLOR1, EE, P4, P7, EF, N0,
  ST_PYR, COLOR1, EA, P0, P4, EE, N0,
  ST_TET, COLOR1, P4, P0, P6, N0,
  ST_PYR, COLOR1, EB, EA, EE, EF, N0,
  ST_TET, COLOR1, P0, P3, P6, N0,
  ST_PYR, COLOR1, EB, P3, P0, EA, N0,
 // Case #218: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P7, P1, P3, P6, EI, EK,
  ST_WDG, COLOR0, EI, P0, EA, EK, P2, EC,
  ST_TET, COLOR0, EF, EJ, P5, EE,
  ST_WDG, COLOR1, EF, EE, EJ, P7, P4, P1,
  ST_PYR, COLOR1, EK, P6, P4, EI, N0,
  ST_TET, COLOR1, P6, P7, P4, N0,
  ST_TET, COLOR1, P6, P3, P7, N0,
  ST_PYR, COLOR1, EC, P3, P6, EK, N0,
  ST_PYR, COLOR1, EA, P1, P3, EC, N0,
  ST_TET, COLOR1, P3, P1, P7, N0,
  ST_PYR, COLOR1, EI, EA, EC, EK, N0,
  ST_TET, COLOR1, P1, P4, P7, N0,
  ST_PYR, COLOR1, EI, P4, P1, EA, N0,
 // Case #219: (cloned #126)
  ST_TET, COLOR0, EC, ED, EK, P2,
  ST_TET, COLOR0, EJ, EF, EE, P5,
  ST_WDG, COLOR1, P0, P3, P6, ED, EC, EK,
  ST_WDG, COLOR1, P1, P4, P7, EJ, EE, EF,
  ST_TET, COLOR1, P7, P4, P6, P1,
  ST_TET, COLOR1, P1, P3, P0, P6,
  ST_TET, COLOR1, P0, P4, P1, P6,
  ST_TET, COLOR1, P1, P7, P3, P6,
 // Case #220: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EF, EB, ED, EE, EI,
  ST_PYR, COLOR0, P1, EB, ED, P0, N0,
  ST_TET, COLOR0, P0, ED, EI, N0,
  ST_PYR, COLOR0, P5, EF, EB, P1, N0,
  ST_TET, COLOR0, EE, EF, P5, N0,
  ST_PYR, COLOR0, EI, EE, P5, P0, N0,
  ST_TET, COLOR0, P0, P5, P1, N0,
  ST_PYR, COLOR1, P6, P2, P3, P7, N0,
  ST_TET, COLOR1, P2, P6, P4, N0,
  ST_TET, COLOR1, P4, P6, P7, N0,
  ST_PYR, COLOR1, P4, P7, EF, EE, N0,
  ST_PYR, COLOR1, EF, P7, P3, EB, N0,
  ST_PYR, COLOR1, EB, P3, P2, ED, N0,
  ST_PYR, COLOR1, ED, P2, P4, EI, N0,
  ST_TET, COLOR1, EE, EI, P4, N0,
 // Case #221: (cloned #63)
  ST_WDG, COLOR0, P1, EA, EB, P5, EE, EF,
  ST_HEX, COLOR1, P0, P3, P7, P4, EA, EB, EF, EE,
  ST_WDG, COLOR1, P6, P4, P7, P2, P0, P3,
 // Case #222: (cloned #111)
  ST_TET, COLOR0, EA, EI, ED, P0,
  ST_TET, COLOR0, EJ, EF, EE, P5,
  ST_WDG, COLOR1, EE, EJ, EF, P4, P1, P7,
  ST_WDG, COLOR1, P4, P1, P2, EI, EA, ED,
  ST_PYR, COLOR1, P6, P2, P3, P7, P4,
  ST_TET, COLOR1, P1, P3, P2, P4,
  ST_TET, COLOR1, P1, P7, P3, P4,
 // Case #223: (cloned #127)
  ST_PNT, 0, COLOR1, 7, P3, P2, P0, P1, P7, P6, P4,
  ST_TET, COLOR0, EF, EE, EJ, P5,
  ST_WDG, COLOR1, EF, EE, EJ, P7, P4, P1,
  ST_TET, COLOR1, P7, P1, P4, N0,
  ST_PYR, COLOR1, P6, P4, P0, P2, N0,
  ST_TET, COLOR1, P4, P1, P0, N0,
  ST_PYR, COLOR1, P3, P2, P0, P1, N0,
  ST_TET, COLOR1, P3, P1, P7, N0,
  ST_PYR, COLOR1, P3, P7, P6, P2, N0,
  ST_TET, COLOR1, P7, P4, P6, N0,
 // Case #224: (cloned #7)
  ST_PNT, 0, NOCOLOR, 5, EK, EL, EH, EE, EJ,
  ST_PYR, COLOR0, P2, P0, P1, P3, N0,
  ST_TET, COLOR0, P1, P4, N0, P0,
  ST_PYR, COLOR0, P3, P1, EJ, EL, N0,
  ST_PYR, COLOR0, EK, P2, P3, EL, N0,
  ST_TET, COLOR0, P4, P0, P2, N0,
  ST_PYR, COLOR0, P4, P2, EK, EH, N0,
  ST_TET, COLOR0, P4, EH, EE, N0,
  ST_PYR, COLOR0, EJ, P1, P4, EE, N0,
  ST_PYR, COLOR1, EL, EJ, P5, P7, N0,
  ST_PYR, COLOR1, EK, EL, P7, P6, N0,
  ST_TET, COLOR1, EH, EK, P6, N0,
  ST_TET, COLOR1, P6, P7, P5, N0,
  ST_PYR, COLOR1, EH, P6, P5, EE, N0,
  ST_TET, COLOR1, P5, EJ, EE, N0,
 // Case #225: (cloned #30)
  ST_PNT, 0, NOCOLOR, 5, EL, EJ, EK, ED, EA,
  ST_TET, COLOR0, P4, EH, EE, EI,
  ST_PYR, COLOR0, P1, EJ, EL, P3, N0,
  ST_PYR, COLOR0, P3, EL, EK, P2, N0,
  ST_TET, COLOR0, P2, EK, ED, N0,
  ST_TET, COLOR0, P3, P2, P1, N0,
  ST_PYR, COLOR0, P2, ED, EA, P1, N0,
  ST_TET, COLOR0, P1, EA, EJ, N0,
  ST_WDG, COLOR1, P6, P5, P0, EH, EE, EI,
  ST_TET, COLOR1, P5, P0, P6, N0,
  ST_PYR, COLOR1, P6, P0, ED, EK, N0,
  ST_TET, COLOR1, P7, P5, P6, N0,
  ST_PYR, COLOR1, EJ, P5, P7, EL, N0,
  ST_PYR, COLOR1, EL, P7, P6, EK, N0,
  ST_PYR, COLOR1, P0, P5, EJ, EA, N0,
  ST_TET, COLOR1, ED, P0, EA, N0,
 // Case #226: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EE, EH, EK, EL, EB, EA,
  ST_TET, COLOR0, P2, P0, P3, N0,
  ST_PYR, COLOR0, P2, P3, EL, EK, N0,
  ST_TET, COLOR0, P4, P0, P2, N0,
  ST_PYR, COLOR0, P4, P2, EK, EH, N0,
  ST_TET, COLOR0, EE, P4, EH, N0,
  ST_PYR, COLOR0, P4, EE, EA, P0, N0,
  ST_PYR, COLOR0, EA, EB, P3, P0, N0,
  ST_TET, COLOR0, P3, EB, EL, N0,
  ST_TET, COLOR1, P5, P6, P7, N0,
  ST_PYR, COLOR1, EE, EH, P6, P5, N0,
  ST_PYR, COLOR1, EE, P5, P1, EA, N0,
  ST_TET, COLOR1, P1, P5, P7, N0,
  ST_TET, COLOR1, P1, EB, EA, N0,
  ST_PYR, COLOR1, P1, P7, EL, EB, N0,
  ST_PYR, COLOR1, EL, P7, P6, EK, N0,
  ST_TET, COLOR1, EK, P6, EH, N0,
 // Case #227: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P5, P0, P1, P7, EK, EL,
  ST_WDG, COLOR0, EK, P2, ED, EL, P3, EB,
  ST_TET, COLOR0, EE, EI, P4, EH,
  ST_WDG, COLOR1, EE, EH, EI, P5, P6, P0,
  ST_PYR, COLOR1, EL, P7, P6, EK, N0,
  ST_TET, COLOR1, P7, P5, P6, N0,
  ST_TET, COLOR1, P7, P1, P5, N0,
  ST_PYR, COLOR1, EB, P1, P7, EL, N0,
  ST_PYR, COLOR1, ED, P0, P1, EB, N0,
  ST_TET, COLOR1, P1, P0, P5, N0,
  ST_PYR, COLOR1, EK, ED, EB, EL, N0,
  ST_TET, COLOR1, P0, P6, P5, N0,
  ST_PYR, COLOR1, EK, P6, P0, ED, N0,
 // Case #228: (cloned #27)
  ST_PNT, 0, NOCOLOR, 6, EH, EE, EJ, EL, EC, ED,
  ST_TET, COLOR0, P1, P3, P0, N0,
  ST_PYR, COLOR0, P1, EJ, EL, P3, N0,
  ST_TET, COLOR0, P4, P1, P0, N0,
  ST_PYR, COLOR0, P4, EE, EJ, P1, N0,
  ST_TET, COLOR0, EH, EE, P4, N0,
  ST_PYR, COLOR0, P4, P0, ED, EH, N0,
  ST_PYR, COLOR0, ED, P0, P3, EC, N0,
  ST_TET, COLOR0, P3, EL, EC, N0,
  ST_TET, COLOR1, P6, P7, P5, N0,
  ST_PYR, COLOR1, EH, P6, P5, EE, N0,
  ST_PYR, COLOR1, EH, ED, P2, P6, N0,
  ST_TET, COLOR1, P2, P7, P6, N0,
  ST_TET, COLOR1, P2, ED, EC, N0,
  ST_PYR, COLOR1, P2, EC, EL, P7, N0,
  ST_PYR, COLOR1, EL, EJ, P5, P7, N0,
  ST_TET, COLOR1, EJ, EE, P5, N0,
 // Case #229: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P6, P0, P2, P7, EJ, EL,
  ST_WDG, COLOR0, EL, P3, EC, EJ, P1, EA,
  ST_TET, COLOR0, EH, P4, EI, EE,
  ST_WDG, COLOR1, P6, P5, P0, EH, EE, EI,
  ST_PYR, COLOR1, EL, EJ, P5, P7, N0,
  ST_TET, COLOR1, P7, P5, P6, N0,
  ST_TET, COLOR1, P7, P6, P2, N0,
  ST_PYR, COLOR1, EC, EL, P7, P2, N0,
  ST_PYR, COLOR1, EA, EC, P2, P0, N0,
  ST_TET, COLOR1, P2, P6, P0, N0,
  ST_PYR, COLOR1, EJ, EL, EC, EA, N0,
  ST_TET, COLOR1, P0, P6, P5, N0,
  ST_PYR, COLOR1, EJ, EA, P0, P5, N0,
 // Case #230: (cloned #61)
  ST_PNT, 0, COLOR1, 6, P7, P1, P5, P6, ED, EH,
  ST_WDG, COLOR0, EH, P4, EE, ED, P0, EA,
  ST_TET, COLOR0, EL, P3, EB, EC,
  ST_WDG, COLOR1, P7, P2, P1, EL, EC, EB,
  ST_PYR, COLOR1, EH, ED, P2, P6, N0,
  ST_TET, COLOR1, P6, P2, P7, N0,
  ST_TET, COLOR1, P6, P7, P5, N0,
  ST_PYR, COLOR1, EE, EH, P6, P5, N0,
  ST_PYR, COLOR1, EA, EE, P5, P1, N0,
  ST_TET, COLOR1, P5, P7, P1, N0,
  ST_PYR, COLOR1, ED, EH, EE, EA, N0,
  ST_TET, COLOR1, P1, P7, P2, N0,
  ST_PYR, COLOR1, ED, EA, P1, P2, N0,
 // Case #231: (cloned #126)
  ST_TET, COLOR0, EE, EH, EI, P4,
  ST_TET, COLOR0, EB, EC, EL, P3,
  ST_WDG, COLOR1, EI, EE, EH, P0, P5, P6,
  ST_WDG, COLOR1, EB, EC, EL, P1, P2, P7,
  ST_TET, COLOR1, P7, P6, P2, P1,
  ST_TET, COLOR1, P1, P0, P5, P6,
  ST_TET, COLOR1, P0, P1, P2, P6,
  ST_TET, COLOR1, P1, P5, P7, P6,
 // Case #232: (cloned #23)
  ST_TET, COLOR0, P2, P0, P1, P4,
  ST_PYR, COLOR0, EE, EJ, P1, P4, EB,
  ST_PYR, COLOR0, EH, P4, P2, EK, EC,
  ST_PYR, COLOR0, P1, EB, EC, P2, P4,
  ST_PYR, COLOR0, EB, EE, EH, EC, P4,
  ST_TET, COLOR1, P7, P5, P6, P3,
  ST_PYR, COLOR1, EB, EJ, P5, P3, EE,
  ST_PYR, COLOR1, EC, P3, P6, EK, EH,
  ST_PYR, COLOR1, P5, EE, EH, P6, P3,
  ST_PYR, COLOR1, EB, EC, EH, EE, P3,
 // Case #233: (cloned #107)
  ST_TET, COLOR0, EJ, EA, EB, P1,
  ST_TET, COLOR0, EE, P4, EH, EI,
  ST_TET, COLOR0, EC, EK, P2, ED,
  ST_WDG, COLOR1, ED, EK, EC, P0, P6, P3,
  ST_WDG, COLOR1, P5, P0, P6, EE, EI, EH,
  ST_WDG, COLOR1, EJ, EA, EB, P5, P0, P3,
  ST_TET, COLOR1, P0, P3, P6, P5,
  ST_TET, COLOR1, P5, P7, P3, P6,
 // Case #234: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EC, EA, EE, EK, EH,
  ST_PYR, COLOR0, P0, P4, EE, EA, N0,
  ST_TET, COLOR0, P4, EH, EE, N0,
  ST_PYR, COLOR0, P2, P0, EA, EC, N0,
  ST_TET, COLOR0, EK, P2, EC, N0,
  ST_PYR, COLOR0, EH, P4, P2, EK, N0,
  ST_TET, COLOR0, P4, P0, P2, N0,
  ST_PYR, COLOR1, P7, P3, P1, P5, N0,
  ST_TET, COLOR1, P5, P6, P7, N0,
  ST_TET, COLOR1, P6, P3, P7, N0,
  ST_PYR, COLOR1, P6, EK, EC, P3, N0,
  ST_PYR, COLOR1, EC, EA, P1, P3, N0,
  ST_PYR, COLOR1, EA, EE, P5, P1, N0,
  ST_PYR, COLOR1, EE, EH, P6, P5, N0,
  ST_TET, COLOR1, EK, P6, EH, N0,
 // Case #235: (cloned #111)
  ST_TET, COLOR0, EK, EC, ED, P2,
  ST_TET, COLOR0, EH, EI, EE, P4,
  ST_WDG, COLOR1, P0, P6, P5, EI, EH, EE,
  ST_WDG, COLOR1, ED, EK, EC, P0, P6, P3,
  ST_PYR, COLOR1, P1, P5, P7, P3, P0,
  ST_TET, COLOR1, P6, P3, P7, P0,
  ST_TET, COLOR1, P6, P7, P5, P0,
 // Case #236: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EH, ED, EB, EE, EJ,
  ST_PYR, COLOR0, P0, P1, EB, ED, N0,
  ST_TET, COLOR0, P1, EJ, EB, N0,
  ST_PYR, COLOR0, P4, P0, ED, EH, N0,
  ST_TET, COLOR0, EE, P4, EH, N0,
  ST_PYR, COLOR0, EJ, P1, P4, EE, N0,
  ST_TET, COLOR0, P1, P0, P4, N0,
  ST_PYR, COLOR1, P7, P6, P2, P3, N0,
  ST_TET, COLOR1, P3, P5, P7, N0,
  ST_TET, COLOR1, P5, P6, P7, N0,
  ST_PYR, COLOR1, P5, EE, EH, P6, N0,
  ST_PYR, COLOR1, EH, ED, P2, P6, N0,
  ST_PYR, COLOR1, ED, EB, P3, P2, N0,
  ST_PYR, COLOR1, EB, EJ, P5, P3, N0,
  ST_TET, COLOR1, EE, P5, EJ, N0,
 // Case #237: (cloned #111)
  ST_TET, COLOR0, EE, EH, EI, P4,
  ST_TET, COLOR0, EJ, EA, EB, P1,
  ST_WDG, COLOR1, P0, P5, P3, EA, EJ, EB,
  ST_WDG, COLOR1, EI, EE, EH, P0, P5, P6,
  ST_PYR, COLOR1, P2, P3, P7, P6, P0,
  ST_TET, COLOR1, P5, P6, P7, P0,
  ST_TET, COLOR1, P5, P7, P3, P0,
 // Case #238: (cloned #63)
  ST_WDG, COLOR0, P4, EE, EH, P0, EA, ED,
  ST_HEX, COLOR1, EA, ED, EH, EE, P1, P2, P6, P5,
  ST_WDG, COLOR1, P3, P1, P2, P7, P5, P6,
 // Case #239: (cloned #127)
  ST_PNT, 0, COLOR1, 7, P7, P3, P1, P5, P6, P2, P0,
  ST_TET, COLOR0, EH, EI, EE, P4,
  ST_WDG, COLOR1, EH, EI, EE, P6, P0, P5,
  ST_TET, COLOR1, P6, P5, P0, N0,
  ST_PYR, COLOR1, P2, P0, P1, P3, N0,
  ST_TET, COLOR1, P0, P5, P1, N0,
  ST_PYR, COLOR1, P7, P3, P1, P5, N0,
  ST_TET, COLOR1, P7, P5, P6, N0,
  ST_PYR, COLOR1, P7, P6, P2, P3, N0,
  ST_TET, COLOR1, P6, P0, P2, N0,
 // Case #240: (cloned #15)
  ST_HEX, COLOR0, P0, P1, P3, P2, EI, EJ, EL, EK,
  ST_HEX, COLOR1, EI, EJ, EL, EK, P4, P5, P7, P6,
 // Case #241: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EJ, EL, EK, EA, ED,
  ST_PYR, COLOR0, P3, EL, EK, P2, N0,
  ST_TET, COLOR0, P2, EK, ED, N0,
  ST_PYR, COLOR0, P1, EJ, EL, P3, N0,
  ST_TET, COLOR0, EA, EJ, P1, N0,
  ST_PYR, COLOR0, ED, EA, P1, P2, N0,
  ST_TET, COLOR0, P2, P1, P3, N0,
  ST_PYR, COLOR1, P4, P6, P7, P5, N0,
  ST_TET, COLOR1, P6, P4, P0, N0,
  ST_TET, COLOR1, P0, P4, P5, N0,
  ST_PYR, COLOR1, P0, P5, EJ, EA, N0,
  ST_PYR, COLOR1, EJ, P5, P7, EL, N0,
  ST_PYR, COLOR1, EL, P7, P6, EK, N0,
  ST_PYR, COLOR1, EK, P6, P0, ED, N0,
  ST_TET, COLOR1, EA, ED, P0, N0,
 // Case #242: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EL, EK, EI, EB, EA,
  ST_PYR, COLOR0, P2, EK, EI, P0, N0,
  ST_TET, COLOR0, P0, EI, EA, N0,
  ST_PYR, COLOR0, P3, EL, EK, P2, N0,
  ST_TET, COLOR0, EB, EL, P3, N0,
  ST_PYR, COLOR0, EA, EB, P3, P0, N0,
  ST_TET, COLOR0, P0, P3, P2, N0,
  ST_PYR, COLOR1, P5, P4, P6, P7, N0,
  ST_TET, COLOR1, P4, P5, P1, N0,
  ST_TET, COLOR1, P1, P5, P7, N0,
  ST_PYR, COLOR1, P1, P7, EL, EB, N0,
  ST_PYR, COLOR1, EL, P7, P6, EK, N0,
  ST_PYR, COLOR1, EK, P6, P4, EI, N0,
  ST_PYR, COLOR1, EI, P4, P1, EA, N0,
  ST_TET, COLOR1, EB, EA, P1, N0,
 // Case #243: (cloned #63)
  ST_WDG, COLOR0, P2, ED, EK, P3, EB, EL,
  ST_HEX, COLOR1, P0, P6, P7, P1, ED, EK, EL, EB,
  ST_WDG, COLOR1, P5, P1, P7, P4, P0, P6,
 // Case #244: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EL, EJ, EI, EC, ED,
  ST_PYR, COLOR0, P1, P0, EI, EJ, N0,
  ST_TET, COLOR0, P0, ED, EI, N0,
  ST_PYR, COLOR0, P3, P1, EJ, EL, N0,
  ST_TET, COLOR0, EC, P3, EL, N0,
  ST_PYR, COLOR0, ED, P0, P3, EC, N0,
  ST_TET, COLOR0, P0, P1, P3, N0,
  ST_PYR, COLOR1, P6, P7, P5, P4, N0,
  ST_TET, COLOR1, P4, P2, P6, N0,
  ST_TET, COLOR1, P2, P7, P6, N0,
  ST_PYR, COLOR1, P2, EC, EL, P7, N0,
  ST_PYR, COLOR1, EL, EJ, P5, P7, N0,
  ST_PYR, COLOR1, EJ, EI, P4, P5, N0,
  ST_PYR, COLOR1, EI, ED, P2, P4, N0,
  ST_TET, COLOR1, EC, P2, ED, N0,
 // Case #245: (cloned #63)
  ST_WDG, COLOR0, P3, EC, EL, P1, EA, EJ,
  ST_HEX, COLOR1, EA, EJ, EL, EC, P0, P5, P7, P2,
  ST_WDG, COLOR1, P4, P0, P5, P6, P2, P7,
 // Case #246: (cloned #111)
  ST_TET, COLOR0, EA, EI, ED, P0,
  ST_TET, COLOR0, EB, EC, EL, P3,
  ST_WDG, COLOR1, P2, P1, P7, EC, EB, EL,
  ST_WDG, COLOR1, ED, EA, EI, P2, P1, P4,
  ST_PYR, COLOR1, P6, P7, P5, P4, P2,
  ST_TET, COLOR1, P1, P4, P5, P2,
  ST_TET, COLOR1, P1, P5, P7, P2,
 // Case #247: (cloned #127)
  ST_PNT, 0, COLOR1, 7, P5, P4, P0, P1, P7, P6, P2,
  ST_TET, COLOR0, EL, EB, EC, P3,
  ST_WDG, COLOR1, P7, P2, P1, EL, EC, EB,
  ST_TET, COLOR1, P7, P2, P1, N0,
  ST_PYR, COLOR1, P6, P4, P0, P2, N0,
  ST_TET, COLOR1, P2, P0, P1, N0,
  ST_PYR, COLOR1, P5, P1, P0, P4, N0,
  ST_TET, COLOR1, P5, P7, P1, N0,
  ST_PYR, COLOR1, P5, P4, P6, P7, N0,
  ST_TET, COLOR1, P7, P6, P2, N0,
 // Case #248: (cloned #31)
  ST_PNT, 0, NOCOLOR, 5, EK, EI, EJ, EC, EB,
  ST_PYR, COLOR0, P0, EI, EJ, P1, N0,
  ST_TET, COLOR0, P1, EJ, EB, N0,
  ST_PYR, COLOR0, P2, EK, EI, P0, N0,
  ST_TET, COLOR0, EC, EK, P2, N0,
  ST_PYR, COLOR0, EB, EC, P2, P1, N0,
  ST_TET, COLOR0, P1, P2, P0, N0,
  ST_PYR, COLOR1, P7, P5, P4, P6, N0,
  ST_TET, COLOR1, P5, P7, P3, N0,
  ST_TET, COLOR1, P3, P7, P6, N0,
  ST_PYR, COLOR1, P3, P6, EK, EC, N0,
  ST_PYR, COLOR1, EK, P6, P4, EI, N0,
  ST_PYR, COLOR1, EI, P4, P5, EJ, N0,
  ST_PYR, COLOR1, EJ, P5, P3, EB, N0,
  ST_TET, COLOR1, EC, EB, P3, N0,
 // Case #249: (cloned #111)
  ST_TET, COLOR0, EC, ED, EK, P2,
  ST_TET, COLOR0, EB, EJ, EA, P1,
  ST_WDG, COLOR1, EA, EB, EJ, P0, P3, P5,
  ST_WDG, COLOR1, P0, P3, P6, ED, EC, EK,
  ST_PYR, COLOR1, P4, P6, P7, P5, P0,
  ST_TET, COLOR1, P3, P7, P6, P0,
  ST_TET, COLOR1, P3, P5, P7, P0,
 // Case #250: (cloned #63)
  ST_WDG, COLOR0, P0, EA, EI, P2, EC, EK,
  ST_HEX, COLOR1, P1, P4, P6, P3, EA, EI, EK, EC,
  ST_WDG, COLOR1, P7, P3, P6, P5, P1, P4,
 // Case #251: (cloned #127)
  ST_PNT, 0, COLOR1, 7, P7, P5, P1, P3, P6, P4, P0,
  ST_TET, COLOR0, EK, EC, ED, P2,
  ST_WDG, COLOR1, P6, P0, P3, EK, ED, EC,
  ST_TET, COLOR1, P6, P0, P3, N0,
  ST_PYR, COLOR1, P4, P5, P1, P0, N0,
  ST_TET, COLOR1, P0, P1, P3, N0,
  ST_PYR, COLOR1, P7, P3, P1, P5, N0,
  ST_TET, COLOR1, P7, P6, P3, N0,
  ST_PYR, COLOR1, P7, P5, P4, P6, N0,
  ST_TET, COLOR1, P6, P4, P0, N0,
 // Case #252: (cloned #63)
  ST_WDG, COLOR0, P0, EI, ED, P1, EJ, EB,
  ST_HEX, COLOR1, P4, P2, P3, P5, EI, ED, EB, EJ,
  ST_WDG, COLOR1, P7, P5, P3, P6, P4, P2,
 // Case #253: (cloned #127)
  ST_PNT, 0, COLOR1, 7, P7, P6, P4, P5, P3, P2, P0,
  ST_TET, COLOR0, EB, EJ, EA, P1,
  ST_WDG, COLOR1, P3, P0, P5, EB, EA, EJ,
  ST_TET, COLOR1, P3, P0, P5, N0,
  ST_PYR, COLOR1, P2, P6, P4, P0, N0,
  ST_TET, COLOR1, P0, P4, P5, N0,
  ST_PYR, COLOR1, P7, P5, P4, P6, N0,
  ST_TET, COLOR1, P7, P3, P5, N0,
  ST_PYR, COLOR1, P7, P6, P2, P3, N0,
  ST_TET, COLOR1, P3, P2, P0, N0,
 // Case #254: (cloned #127)
  ST_PNT, 0, COLOR1, 7, P6, P7, P5, P4, P2, P3, P1,
  ST_TET, COLOR0, ED, EA, EI, P0,
  ST_WDG, COLOR1, ED, EA, EI, P2, P1, P4,
  ST_TET, COLOR1, P2, P4, P1, N0,
  ST_PYR, COLOR1, P3, P1, P5, P7, N0,
  ST_TET, COLOR1, P1, P4, P5, N0,
  ST_PYR, COLOR1, P6, P7, P5, P4, N0,
  ST_TET, COLOR1, P6, P4, P2, N0,
  ST_PYR, COLOR1, P6, P2, P3, P7, N0,
  ST_TET, COLOR1, P2, P1, P3, N0,
 // Case #255: Unique case #22
  ST_HEX, COLOR1, P0, P1, P3, P2, P4, P5, P7, P6,
 // Dummy
  0
};

// ---- ClipCasesVox.C ( end )
// ----------------------------------------------------------------------------


// ----------------------------------------------------------------------------
// ---- ClipCasesWdg.C (begin)

const int NumClipCasesWdg = 64;

const int NumClipShapesWdg[64] = {
  1,  3,  3,  9,  3,  9,  9,  2, // cases 0 - 7
  3,  2,  15,  13,  15,  13,  10,  9, // cases 8 - 15
  3,  15,  2,  13,  15,  10,  13,  9, // cases 16 - 23
  9,  13,  13,  2,  10,  5,  5,  3, // cases 24 - 31
  3,  15,  15,  10,  2,  13,  13,  9, // cases 32 - 39
  9,  13,  10,  5,  13,  2,  5,  3, // cases 40 - 47
  9,  10,  13,  5,  13,  5,  2,  3, // cases 48 - 55
  2,  9,  9,  3,  9,  3,  3,  1  // cases 56 - 63
};

const int StartClipShapesWdg[64] = {
  0, 8, 29, 50, 115, 136, 201, 266, // cases 0 - 7
  282, 303, 321, 421, 508, 608, 695, 768, // cases 8 - 15
  833, 854, 954, 972, 1059, 1159, 1232, 1319, // cases 16 - 23
  1384, 1449, 1536, 1623, 1641, 1714, 1748, 1782, // cases 24 - 31
  1803, 1824, 1924, 2024, 2097, 2115, 2202, 2289, // cases 32 - 39
  2354, 2419, 2506, 2579, 2613, 2700, 2718, 2752, // cases 40 - 47
  2773, 2838, 2911, 2998, 3032, 3119, 3153, 3171, // cases 48 - 55
  3192, 3208, 3273, 3338, 3359, 3424, 3445, 3466  // cases 56 - 63
};

static unsigned char ClipShapesWdg[] = {
 // Case #0: Unique case #1
  ST_WDG, COLOR0, P0, P1, P2, P3, P4, P5,
 // Case #1: Unique case #2
  ST_WDG, COLOR0, EA, EC, EG, P1, P2, P3,
  ST_PYR, COLOR0, P1, P2, P5, P4, P3,
  ST_TET, COLOR1, EG, EA, EC, P0,
 // Case #2: (cloned #1)
  ST_WDG, COLOR0, EB, EA, EH, P2, P0, P4,
  ST_PYR, COLOR0, P2, P0, P3, P5, P4,
  ST_TET, COLOR1, EH, EB, EA, P1,
 // Case #3: Unique case #3
  ST_PNT, 0, COLOR0, 7, P2, P3, P4, EB, EC, EG, EH,
  ST_TET, COLOR0, P4, P5, P3, P2,
  ST_TET, COLOR0, P2, P3, P4, N0,
  ST_PYR, COLOR0, EG, EH, P4, P3, N0,
  ST_PYR, COLOR0, EB, EH, EG, EC, N0,
  ST_TET, COLOR0, P2, EB, EC, N0,
  ST_PYR, COLOR0, P2, EC, EG, P3, N0,
  ST_PYR, COLOR0, EH, EB, P2, P4, N0,
  ST_WDG, COLOR1, EC, EG, P0, EB, EH, P1,
 // Case #4: (cloned #1)
  ST_WDG, COLOR0, EC, EB, EI, P0, P1, P5,
  ST_PYR, COLOR0, P0, P1, P4, P3, P5,
  ST_TET, COLOR1, EI, EC, EB, P2,
 // Case #5: (cloned #3)
  ST_PNT, 0, COLOR0, 7, P1, P5, P3, EA, EB, EI, EG,
  ST_TET, COLOR0, P3, P4, P5, P1,
  ST_TET, COLOR0, P1, P5, P3, N0,
  ST_PYR, COLOR0, EI, EG, P3, P5, N0,
  ST_PYR, COLOR0, EA, EG, EI, EB, N0,
  ST_TET, COLOR0, P1, EA, EB, N0,
  ST_PYR, COLOR0, P1, EB, EI, P5, N0,
  ST_PYR, COLOR0, EG, EA, P1, P3, N0,
  ST_WDG, COLOR1, EB, EI, P2, EA, EG, P0,
 // Case #6: (cloned #3)
  ST_PNT, 0, COLOR0, 7, P0, P4, P5, EC, EA, EH, EI,
  ST_TET, COLOR0, P5, P3, P4, P0,
  ST_TET, COLOR0, P0, P4, P5, N0,
  ST_PYR, COLOR0, EH, EI, P5, P4, N0,
  ST_PYR, COLOR0, EC, EI, EH, EA, N0,
  ST_TET, COLOR0, P0, EC, EA, N0,
  ST_PYR, COLOR0, P0, EA, EH, P4, N0,
  ST_PYR, COLOR0, EI, EC, P0, P5, N0,
  ST_WDG, COLOR1, EA, EH, P1, EC, EI, P2,
 // Case #7: Unique case #4
  ST_WDG, COLOR0, EG, EH, EI, P3, P4, P5,
  ST_WDG, COLOR1, P0, P1, P2, EG, EH, EI,
 // Case #8: (cloned #1)
  ST_WDG, COLOR0, P4, P5, P0, ED, EF, EG,
  ST_PYR, COLOR0, P4, P1, P2, P5, P0,
  ST_TET, COLOR1, EG, EF, ED, P3,
 // Case #9: Unique case #5
  ST_HEX, COLOR0, P1, P2, P5, P4, EA, EC, EF, ED,
  ST_WDG, COLOR1, P0, EA, EC, P3, ED, EF,
 // Case #10: Unique case #6
  ST_PNT, 0, NOCOLOR, 6, EA, EB, EH, ED, EF, EG,
  ST_PYR, COLOR0, P5, P0, EG, EF, N0,
  ST_TET, COLOR0, P0, EA, EG, N0,
  ST_PYR, COLOR0, P0, P2, EB, EA, N0,
  ST_TET, COLOR0, P5, P2, P0, N0,
  ST_PYR, COLOR0, P4, EH, EB, P2, N0,
  ST_TET, COLOR0, P5, P4, P2, N0,
  ST_PYR, COLOR0, EF, ED, P4, P5, N0,
  ST_TET, COLOR0, ED, EH, P4, N0,
  ST_PYR, COLOR1, EG, EA, P1, P3, N0,
  ST_PYR, COLOR1, P3, P1, EH, ED, N0,
  ST_TET, COLOR1, P3, ED, EF, N0,
  ST_TET, COLOR1, EF, EG, P3, N0,
  ST_TET, COLOR1, P1, EB, EH, N0,
  ST_TET, COLOR1, P1, EA, EB, N0,
 // Case #11: Unique case #7
  ST_PNT, 0, NOCOLOR, 5, EB, EC, EF, ED, EH,
  ST_PYR, COLOR0, P4, P5, EF, ED, N0,
  ST_TET, COLOR0, ED, EH, P4, N0,
  ST_PYR, COLOR0, EC, EF, P5, P2, N0,
  ST_PYR, COLOR0, EB, P2, P4, EH, N0,
  ST_TET, COLOR0, P4, P2, P5, N0,
  ST_TET, COLOR0, P2, EB, EC, N0,
  ST_TET, COLOR1, P0, P1, P3, N0,
  ST_PYR, COLOR1, EC, EB, P1, P0, N0,
  ST_PYR, COLOR1, EC, P0, P3, EF, N0,
  ST_TET, COLOR1, EF, P3, ED, N0,
  ST_PYR, COLOR1, P3, P1, EH, ED, N0,
  ST_TET, COLOR1, P1, EB, EH, N0,
 // Case #12: (cloned #10)
  ST_PNT, 0, NOCOLOR, 6, EF, ED, EG, EC, EB, EI,
  ST_PYR, COLOR0, P1, EB, EI, P5, N0,
  ST_TET, COLOR0, P5, EI, EF, N0,
  ST_PYR, COLOR0, P5, EF, ED, P4, N0,
  ST_TET, COLOR0, P1, P5, P4, N0,
  ST_PYR, COLOR0, P0, P4, ED, EG, N0,
  ST_TET, COLOR0, P1, P4, P0, N0,
  ST_PYR, COLOR0, EB, P1, P0, EC, N0,
  ST_TET, COLOR0, EC, P0, EG, N0,
  ST_PYR, COLOR1, EI, P2, P3, EF, N0,
  ST_PYR, COLOR1, P2, EC, EG, P3, N0,
  ST_TET, COLOR1, P2, EB, EC, N0,
  ST_TET, COLOR1, EB, P2, EI, N0,
  ST_TET, COLOR1, P3, EG, ED, N0,
  ST_TET, COLOR1, P3, ED, EF, N0,
 // Case #13: (cloned #11)
  ST_PNT, 0, NOCOLOR, 5, EB, EA, ED, EF, EI,
  ST_PYR, COLOR0, P5, EF, ED, P4, N0,
  ST_TET, COLOR0, EF, P5, EI, N0,
  ST_PYR, COLOR0, EA, P1, P4, ED, N0,
  ST_PYR, COLOR0, EB, EI, P5, P1, N0,
  ST_TET, COLOR0, P5, P4, P1, N0,
  ST_TET, COLOR0, P1, EA, EB, N0,
  ST_TET, COLOR1, P0, P3, P2, N0,
  ST_PYR, COLOR1, EA, P0, P2, EB, N0,
  ST_PYR, COLOR1, EA, ED, P3, P0, N0,
  ST_TET, COLOR1, ED, EF, P3, N0,
  ST_PYR, COLOR1, P3, EF, EI, P2, N0,
  ST_TET, COLOR1, P2, EI, EB, N0,
 // Case #14: Unique case #8
  ST_PNT, 0, COLOR1, 7, ED, EF, EI, EH, P3, P2, P1,
  ST_TET, COLOR0, P0, EC, EA, EG,
  ST_WDG, COLOR0, EF, EI, P5, ED, EH, P4,
  ST_WDG, COLOR1, P2, P1, P3, EC, EA, EG,
  ST_PYR, COLOR1, EF, ED, EH, EI, N0,
  ST_PYR, COLOR1, EH, P1, P2, EI, N0,
  ST_TET, COLOR1, P3, P2, P1, N0,
  ST_TET, COLOR1, P3, ED, EF, N0,
  ST_PYR, COLOR1, ED, P3, P1, EH, N0,
  ST_PYR, COLOR1, EI, P2, P3, EF, N0,
 // Case #15: Unique case #9
  ST_PNT, 0, COLOR1, 7, P1, P2, P3, EF, ED, EH, EI,
  ST_WDG, COLOR0, ED, P4, EH, EF, P5, EI,
  ST_TET, COLOR1, P0, P2, P1, P3,
  ST_PYR, COLOR1, EF, ED, EH, EI, N0,
  ST_PYR, COLOR1, EI, EH, P1, P2, N0,
  ST_TET, COLOR1, P3, P2, P1, N0,
  ST_TET, COLOR1, P3, ED, EF, N0,
  ST_PYR, COLOR1, P3, P1, EH, ED, N0,
  ST_PYR, COLOR1, P2, P3, EF, EI, N0,
 // Case #16: (cloned #1)
  ST_WDG, COLOR0, P5, P3, P1, EE, ED, EH,
  ST_PYR, COLOR0, P5, P2, P0, P3, P1,
  ST_TET, COLOR1, EH, ED, EE, P4,
 // Case #17: (cloned #10)
  ST_PNT, 0, NOCOLOR, 6, ED, EE, EH, EA, EC, EG,
  ST_PYR, COLOR0, P2, EC, EG, P3, N0,
  ST_TET, COLOR0, P3, EG, ED, N0,
  ST_PYR, COLOR0, P3, ED, EE, P5, N0,
  ST_TET, COLOR0, P2, P3, P5, N0,
  ST_PYR, COLOR0, P1, P5, EE, EH, N0,
  ST_TET, COLOR0, P2, P5, P1, N0,
  ST_PYR, COLOR0, EC, P2, P1, EA, N0,
  ST_TET, COLOR0, EA, P1, EH, N0,
  ST_PYR, COLOR1, EG, P0, P4, ED, N0,
  ST_PYR, COLOR1, P0, EA, EH, P4, N0,
  ST_TET, COLOR1, P0, EC, EA, N0,
  ST_TET, COLOR1, EC, P0, EG, N0,
  ST_TET, COLOR1, P4, EH, EE, N0,
  ST_TET, COLOR1, P4, EE, ED, N0,
 // Case #18: (cloned #9)
  ST_HEX, COLOR0, P2, P0, P3, P5, EB, EA, ED, EE,
  ST_WDG, COLOR1, P1, EB, EA, P4, EE, ED,
 // Case #19: (cloned #11)
  ST_PNT, 0, NOCOLOR, 5, EC, EB, EE, ED, EG,
  ST_PYR, COLOR0, P3, ED, EE, P5, N0,
  ST_TET, COLOR0, ED, P3, EG, N0,
  ST_PYR, COLOR0, EB, P2, P5, EE, N0,
  ST_PYR, COLOR0, EC, EG, P3, P2, N0,
  ST_TET, COLOR0, P3, P5, P2, N0,
  ST_TET, COLOR0, P2, EB, EC, N0,
  ST_TET, COLOR1, P1, P4, P0, N0,
  ST_PYR, COLOR1, EB, P1, P0, EC, N0,
  ST_PYR, COLOR1, EB, EE, P4, P1, N0,
  ST_TET, COLOR1, EE, ED, P4, N0,
  ST_PYR, COLOR1, P4, ED, EG, P0, N0,
  ST_TET, COLOR1, P0, EG, EC, N0,
 // Case #20: (cloned #10)
  ST_PNT, 0, NOCOLOR, 6, EB, EC, EI, EE, ED, EH,
  ST_PYR, COLOR0, P3, P1, EH, ED, N0,
  ST_TET, COLOR0, P1, EB, EH, N0,
  ST_PYR, COLOR0, P1, P0, EC, EB, N0,
  ST_TET, COLOR0, P3, P0, P1, N0,
  ST_PYR, COLOR0, P5, EI, EC, P0, N0,
  ST_TET, COLOR0, P3, P5, P0, N0,
  ST_PYR, COLOR0, ED, EE, P5, P3, N0,
  ST_TET, COLOR0, EE, EI, P5, N0,
  ST_PYR, COLOR1, EH, EB, P2, P4, N0,
  ST_PYR, COLOR1, P4, P2, EI, EE, N0,
  ST_TET, COLOR1, P4, EE, ED, N0,
  ST_TET, COLOR1, ED, EH, P4, N0,
  ST_TET, COLOR1, P2, EC, EI, N0,
  ST_TET, COLOR1, P2, EB, EC, N0,
 // Case #21: (cloned #14)
  ST_PNT, 0, COLOR1, 7, EE, ED, EG, EI, P4, P0, P2,
  ST_TET, COLOR0, P1, EA, EB, EH,
  ST_WDG, COLOR0, ED, EG, P3, EE, EI, P5,
  ST_WDG, COLOR1, P0, P2, P4, EA, EB, EH,
  ST_PYR, COLOR1, ED, EE, EI, EG, N0,
  ST_PYR, COLOR1, EI, P2, P0, EG, N0,
  ST_TET, COLOR1, P4, P0, P2, N0,
  ST_TET, COLOR1, P4, EE, ED, N0,
  ST_PYR, COLOR1, EE, P4, P2, EI, N0,
  ST_PYR, COLOR1, EG, P0, P4, ED, N0,
 // Case #22: (cloned #11)
  ST_PNT, 0, NOCOLOR, 5, EC, EA, ED, EE, EI,
  ST_PYR, COLOR0, P5, P3, ED, EE, N0,
  ST_TET, COLOR0, EE, EI, P5, N0,
  ST_PYR, COLOR0, EA, ED, P3, P0, N0,
  ST_PYR, COLOR0, EC, P0, P5, EI, N0,
  ST_TET, COLOR0, P5, P0, P3, N0,
  ST_TET, COLOR0, P0, EC, EA, N0,
  ST_TET, COLOR1, P1, P2, P4, N0,
  ST_PYR, COLOR1, EA, EC, P2, P1, N0,
  ST_PYR, COLOR1, EA, P1, P4, ED, N0,
  ST_TET, COLOR1, ED, P4, EE, N0,
  ST_PYR, COLOR1, P4, P2, EI, EE, N0,
  ST_TET, COLOR1, P2, EC, EI, N0,
 // Case #23: (cloned #15)
  ST_PNT, 0, COLOR1, 7, P2, P0, P4, ED, EE, EI, EG,
  ST_WDG, COLOR0, EE, P5, EI, ED, P3, EG,
  ST_TET, COLOR1, P1, P0, P2, P4,
  ST_PYR, COLOR1, ED, EE, EI, EG, N0,
  ST_PYR, COLOR1, EG, EI, P2, P0, N0,
  ST_TET, COLOR1, P4, P0, P2, N0,
  ST_TET, COLOR1, P4, EE, ED, N0,
  ST_PYR, COLOR1, P4, P2, EI, EE, N0,
  ST_PYR, COLOR1, P0, P4, ED, EG, N0,
 // Case #24: (cloned #3)
  ST_PNT, 0, COLOR0, 7, P5, P0, P1, EE, EF, EG, EH,
  ST_TET, COLOR0, P1, P0, P2, P5,
  ST_TET, COLOR0, P5, P1, P0, N0,
  ST_PYR, COLOR0, EG, P0, P1, EH, N0,
  ST_PYR, COLOR0, EE, EF, EG, EH, N0,
  ST_TET, COLOR0, P5, EF, EE, N0,
  ST_PYR, COLOR0, P5, P0, EG, EF, N0,
  ST_PYR, COLOR0, EH, P1, P5, EE, N0,
  ST_WDG, COLOR1, EE, EH, P4, EF, EG, P3,
 // Case #25: (cloned #11)
  ST_PNT, 0, NOCOLOR, 5, EE, EF, EC, EA, EH,
  ST_PYR, COLOR0, P1, EA, EC, P2, N0,
  ST_TET, COLOR0, EA, P1, EH, N0,
  ST_PYR, COLOR0, EF, P5, P2, EC, N0,
  ST_PYR, COLOR0, EE, EH, P1, P5, N0,
  ST_TET, COLOR0, P1, P2, P5, N0,
  ST_TET, COLOR0, P5, EF, EE, N0,
  ST_TET, COLOR1, P3, P0, P4, N0,
  ST_PYR, COLOR1, EF, P3, P4, EE, N0,
  ST_PYR, COLOR1, EF, EC, P0, P3, N0,
  ST_TET, COLOR1, EC, EA, P0, N0,
  ST_PYR, COLOR1, P0, EA, EH, P4, N0,
  ST_TET, COLOR1, P4, EH, EE, N0,
 // Case #26: (cloned #11)
  ST_PNT, 0, NOCOLOR, 5, EF, EE, EB, EA, EG,
  ST_PYR, COLOR0, P0, P2, EB, EA, N0,
  ST_TET, COLOR0, EA, EG, P0, N0,
  ST_PYR, COLOR0, EE, EB, P2, P5, N0,
  ST_PYR, COLOR0, EF, P5, P0, EG, N0,
  ST_TET, COLOR0, P0, P5, P2, N0,
  ST_TET, COLOR0, P5, EF, EE, N0,
  ST_TET, COLOR1, P4, P3, P1, N0,
  ST_PYR, COLOR1, EE, EF, P3, P4, N0,
  ST_PYR, COLOR1, EE, P4, P1, EB, N0,
  ST_TET, COLOR1, EB, P1, EA, N0,
  ST_PYR, COLOR1, P1, P3, EG, EA, N0,
  ST_TET, COLOR1, P3, EF, EG, N0,
 // Case #27: Unique case #10
  ST_WDG, COLOR0, EF, P5, EE, EC, P2, EB,
  ST_HEX, COLOR1, P3, P4, EE, EF, P0, P1, EB, EC,
 // Case #28: (cloned #14)
  ST_PNT, 0, COLOR1, 7, EC, EB, EH, EG, P2, P4, P3,
  ST_TET, COLOR0, P5, EF, EE, EI,
  ST_WDG, COLOR0, EC, EG, P0, EB, EH, P1,
  ST_WDG, COLOR1, EE, EF, EI, P4, P3, P2,
  ST_PYR, COLOR1, EB, EH, EG, EC, N0,
  ST_PYR, COLOR1, EG, EH, P4, P3, N0,
  ST_TET, COLOR1, P2, P3, P4, N0,
  ST_TET, COLOR1, P2, EB, EC, N0,
  ST_PYR, COLOR1, EC, EG, P3, P2, N0,
  ST_PYR, COLOR1, EH, EB, P2, P4, N0,
 // Case #29: Unique case #11
  ST_TET, COLOR0, P1, EA, EB, EH,
  ST_TET, COLOR0, EF, EE, P5, EI,
  ST_WDG, COLOR1, P2, P3, P4, EI, EF, EE,
  ST_TET, COLOR1, P2, P3, P4, P0,
  ST_WDG, COLOR1, P2, P4, P0, EB, EH, EA,
 // Case #30: (cloned #29)
  ST_TET, COLOR0, P5, EF, EE, EI,
  ST_TET, COLOR0, EA, P0, EC, EG,
  ST_WDG, COLOR1, EG, EA, EC, P3, P1, P2,
  ST_TET, COLOR1, P3, P2, P1, P4,
  ST_WDG, COLOR1, EF, EI, EE, P3, P2, P4,
 // Case #31: Unique case #12
  ST_TET, COLOR0, EF, EI, EE, P5,
  ST_WDG, COLOR1, EI, EE, EF, P2, P4, P3,
  ST_PYR, COLOR1, P0, P1, P4, P3, P2,
 // Case #32: (cloned #1)
  ST_WDG, COLOR0, P3, P4, P2, EF, EE, EI,
  ST_PYR, COLOR0, P3, P0, P1, P4, P2,
  ST_TET, COLOR1, EI, EE, EF, P5,
 // Case #33: (cloned #10)
  ST_PNT, 0, NOCOLOR, 6, EC, EA, EG, EF, EE, EI,
  ST_PYR, COLOR0, P4, P2, EI, EE, N0,
  ST_TET, COLOR0, P2, EC, EI, N0,
  ST_PYR, COLOR0, P2, P1, EA, EC, N0,
  ST_TET, COLOR0, P4, P1, P2, N0,
  ST_PYR, COLOR0, P3, EG, EA, P1, N0,
  ST_TET, COLOR0, P4, P3, P1, N0,
  ST_PYR, COLOR0, EE, EF, P3, P4, N0,
  ST_TET, COLOR0, EF, EG, P3, N0,
  ST_PYR, COLOR1, EI, EC, P0, P5, N0,
  ST_PYR, COLOR1, P5, P0, EG, EF, N0,
  ST_TET, COLOR1, P5, EF, EE, N0,
  ST_TET, COLOR1, EE, EI, P5, N0,
  ST_TET, COLOR1, P0, EA, EG, N0,
  ST_TET, COLOR1, P0, EC, EA, N0,
 // Case #34: (cloned #10)
  ST_PNT, 0, NOCOLOR, 6, EE, EF, EI, EB, EA, EH,
  ST_PYR, COLOR0, P0, EA, EH, P4, N0,
  ST_TET, COLOR0, P4, EH, EE, N0,
  ST_PYR, COLOR0, P4, EE, EF, P3, N0,
  ST_TET, COLOR0, P0, P4, P3, N0,
  ST_PYR, COLOR0, P2, P3, EF, EI, N0,
  ST_TET, COLOR0, P0, P3, P2, N0,
  ST_PYR, COLOR0, EA, P0, P2, EB, N0,
  ST_TET, COLOR0, EB, P2, EI, N0,
  ST_PYR, COLOR1, EH, P1, P5, EE, N0,
  ST_PYR, COLOR1, P1, EB, EI, P5, N0,
  ST_TET, COLOR1, P1, EA, EB, N0,
  ST_TET, COLOR1, EA, P1, EH, N0,
  ST_TET, COLOR1, P5, EI, EF, N0,
  ST_TET, COLOR1, P5, EF, EE, N0,
 // Case #35: (cloned #14)
  ST_PNT, 0, COLOR1, 7, EF, EE, EH, EG, P5, P1, P0,
  ST_TET, COLOR0, P2, EB, EC, EI,
  ST_WDG, COLOR0, EE, EH, P4, EF, EG, P3,
  ST_WDG, COLOR1, P1, P0, P5, EB, EC, EI,
  ST_PYR, COLOR1, EE, EF, EG, EH, N0,
  ST_PYR, COLOR1, EG, P0, P1, EH, N0,
  ST_TET, COLOR1, P5, P1, P0, N0,
  ST_TET, COLOR1, P5, EF, EE, N0,
  ST_PYR, COLOR1, EF, P5, P0, EG, N0,
  ST_PYR, COLOR1, EH, P1, P5, EE, N0,
 // Case #36: (cloned #9)
  ST_HEX, COLOR0, P0, P1, P4, P3, EC, EB, EE, EF,
  ST_WDG, COLOR1, P2, EC, EB, P5, EF, EE,
 // Case #37: (cloned #11)
  ST_PNT, 0, NOCOLOR, 5, EA, EB, EE, EF, EG,
  ST_PYR, COLOR0, P3, P4, EE, EF, N0,
  ST_TET, COLOR0, EF, EG, P3, N0,
  ST_PYR, COLOR0, EB, EE, P4, P1, N0,
  ST_PYR, COLOR0, EA, P1, P3, EG, N0,
  ST_TET, COLOR0, P3, P1, P4, N0,
  ST_TET, COLOR0, P1, EA, EB, N0,
  ST_TET, COLOR1, P2, P0, P5, N0,
  ST_PYR, COLOR1, EB, EA, P0, P2, N0,
  ST_PYR, COLOR1, EB, P2, P5, EE, N0,
  ST_TET, COLOR1, EE, P5, EF, N0,
  ST_PYR, COLOR1, P5, P0, EG, EF, N0,
  ST_TET, COLOR1, P0, EA, EG, N0,
 // Case #38: (cloned #11)
  ST_PNT, 0, NOCOLOR, 5, EA, EC, EF, EE, EH,
  ST_PYR, COLOR0, P4, EE, EF, P3, N0,
  ST_TET, COLOR0, EE, P4, EH, N0,
  ST_PYR, COLOR0, EC, P0, P3, EF, N0,
  ST_PYR, COLOR0, EA, EH, P4, P0, N0,
  ST_TET, COLOR0, P4, P3, P0, N0,
  ST_TET, COLOR0, P0, EC, EA, N0,
  ST_TET, COLOR1, P2, P5, P1, N0,
  ST_PYR, COLOR1, EC, P2, P1, EA, N0,
  ST_PYR, COLOR1, EC, EF, P5, P2, N0,
  ST_TET, COLOR1, EF, EE, P5, N0,
  ST_PYR, COLOR1, P5, EE, EH, P1, N0,
  ST_TET, COLOR1, P1, EH, EA, N0,
 // Case #39: (cloned #15)
  ST_PNT, 0, COLOR1, 7, P0, P1, P5, EE, EF, EG, EH,
  ST_WDG, COLOR0, EF, P3, EG, EE, P4, EH,
  ST_TET, COLOR1, P2, P1, P0, P5,
  ST_PYR, COLOR1, EE, EF, EG, EH, N0,
  ST_PYR, COLOR1, EH, EG, P0, P1, N0,
  ST_TET, COLOR1, P5, P1, P0, N0,
  ST_TET, COLOR1, P5, EF, EE, N0,
  ST_PYR, COLOR1, P5, P0, EG, EF, N0,
  ST_PYR, COLOR1, P1, P5, EE, EH, N0,
 // Case #40: (cloned #3)
  ST_PNT, 0, COLOR0, 7, P4, P2, P0, ED, EE, EI, EG,
  ST_TET, COLOR0, P0, P2, P1, P4,
  ST_TET, COLOR0, P4, P0, P2, N0,
  ST_PYR, COLOR0, EI, P2, P0, EG, N0,
  ST_PYR, COLOR0, ED, EE, EI, EG, N0,
  ST_TET, COLOR0, P4, EE, ED, N0,
  ST_PYR, COLOR0, P4, P2, EI, EE, N0,
  ST_PYR, COLOR0, EG, P0, P4, ED, N0,
  ST_WDG, COLOR1, ED, EG, P3, EE, EI, P5,
 // Case #41: (cloned #11)
  ST_PNT, 0, NOCOLOR, 5, EE, ED, EA, EC, EI,
  ST_PYR, COLOR0, P2, P1, EA, EC, N0,
  ST_TET, COLOR0, EC, EI, P2, N0,
  ST_PYR, COLOR0, ED, EA, P1, P4, N0,
  ST_PYR, COLOR0, EE, P4, P2, EI, N0,
  ST_TET, COLOR0, P2, P4, P1, N0,
  ST_TET, COLOR0, P4, EE, ED, N0,
  ST_TET, COLOR1, P3, P5, P0, N0,
  ST_PYR, COLOR1, ED, EE, P5, P3, N0,
  ST_PYR, COLOR1, ED, P3, P0, EA, N0,
  ST_TET, COLOR1, EA, P0, EC, N0,
  ST_PYR, COLOR1, P0, P5, EI, EC, N0,
  ST_TET, COLOR1, P5, EE, EI, N0,
 // Case #42: (cloned #14)
  ST_PNT, 0, COLOR1, 7, EB, EA, EG, EI, P1, P3, P5,
  ST_TET, COLOR0, P4, EE, ED, EH,
  ST_WDG, COLOR0, EB, EI, P2, EA, EG, P0,
  ST_WDG, COLOR1, ED, EE, EH, P3, P5, P1,
  ST_PYR, COLOR1, EA, EG, EI, EB, N0,
  ST_PYR, COLOR1, EI, EG, P3, P5, N0,
  ST_TET, COLOR1, P1, P5, P3, N0,
  ST_TET, COLOR1, P1, EA, EB, N0,
  ST_PYR, COLOR1, EB, EI, P5, P1, N0,
  ST_PYR, COLOR1, EG, EA, P1, P3, N0,
 // Case #43: (cloned #29)
  ST_TET, COLOR0, P4, EE, ED, EH,
  ST_TET, COLOR0, EC, P2, EB, EI,
  ST_WDG, COLOR1, EI, EC, EB, P5, P0, P1,
  ST_TET, COLOR1, P5, P1, P0, P3,
  ST_WDG, COLOR1, EE, EH, ED, P5, P1, P3,
 // Case #44: (cloned #11)
  ST_PNT, 0, NOCOLOR, 5, ED, EE, EB, EC, EG,
  ST_PYR, COLOR0, P0, EC, EB, P1, N0,
  ST_TET, COLOR0, EC, P0, EG, N0,
  ST_PYR, COLOR0, EE, P4, P1, EB, N0,
  ST_PYR, COLOR0, ED, EG, P0, P4, N0,
  ST_TET, COLOR0, P0, P1, P4, N0,
  ST_TET, COLOR0, P4, EE, ED, N0,
  ST_TET, COLOR1, P5, P2, P3, N0,
  ST_PYR, COLOR1, EE, P5, P3, ED, N0,
  ST_PYR, COLOR1, EE, EB, P2, P5, N0,
  ST_TET, COLOR1, EB, EC, P2, N0,
  ST_PYR, COLOR1, P2, EC, EG, P3, N0,
  ST_TET, COLOR1, P3, EG, ED, N0,
 // Case #45: (cloned #27)
  ST_WDG, COLOR0, EE, P4, ED, EB, P1, EA,
  ST_HEX, COLOR1, P5, P3, ED, EE, P2, P0, EA, EB,
 // Case #46: (cloned #29)
  ST_TET, COLOR0, P0, EC, EA, EG,
  ST_TET, COLOR0, EE, ED, P4, EH,
  ST_WDG, COLOR1, P1, P5, P3, EH, EE, ED,
  ST_TET, COLOR1, P1, P5, P3, P2,
  ST_WDG, COLOR1, P1, P3, P2, EA, EG, EC,
 // Case #47: (cloned #31)
  ST_TET, COLOR0, EE, EH, ED, P4,
  ST_WDG, COLOR1, EH, ED, EE, P1, P3, P5,
  ST_PYR, COLOR1, P2, P0, P3, P5, P1,
 // Case #48: (cloned #3)
  ST_PNT, 0, COLOR0, 7, P3, P1, P2, EF, ED, EH, EI,
  ST_TET, COLOR0, P2, P1, P0, P3,
  ST_TET, COLOR0, P3, P2, P1, N0,
  ST_PYR, COLOR0, EH, P1, P2, EI, N0,
  ST_PYR, COLOR0, EF, ED, EH, EI, N0,
  ST_TET, COLOR0, P3, ED, EF, N0,
  ST_PYR, COLOR0, P3, P1, EH, ED, N0,
  ST_PYR, COLOR0, EI, P2, P3, EF, N0,
  ST_WDG, COLOR1, EF, EI, P5, ED, EH, P4,
 // Case #49: (cloned #14)
  ST_PNT, 0, COLOR1, 7, EA, EC, EI, EH, P0, P5, P4,
  ST_TET, COLOR0, P3, ED, EF, EG,
  ST_WDG, COLOR0, EA, EH, P1, EC, EI, P2,
  ST_WDG, COLOR1, EF, ED, EG, P5, P4, P0,
  ST_PYR, COLOR1, EC, EI, EH, EA, N0,
  ST_PYR, COLOR1, EH, EI, P5, P4, N0,
  ST_TET, COLOR1, P0, P4, P5, N0,
  ST_TET, COLOR1, P0, EC, EA, N0,
  ST_PYR, COLOR1, EA, EH, P4, P0, N0,
  ST_PYR, COLOR1, EI, EC, P0, P5, N0,
 // Case #50: (cloned #11)
  ST_PNT, 0, NOCOLOR, 5, EF, ED, EA, EB, EI,
  ST_PYR, COLOR0, P2, EB, EA, P0, N0,
  ST_TET, COLOR0, EB, P2, EI, N0,
  ST_PYR, COLOR0, ED, P3, P0, EA, N0,
  ST_PYR, COLOR0, EF, EI, P2, P3, N0,
  ST_TET, COLOR0, P2, P0, P3, N0,
  ST_TET, COLOR0, P3, ED, EF, N0,
  ST_TET, COLOR1, P4, P1, P5, N0,
  ST_PYR, COLOR1, ED, P4, P5, EF, N0,
  ST_PYR, COLOR1, ED, EA, P1, P4, N0,
  ST_TET, COLOR1, EA, EB, P1, N0,
  ST_PYR, COLOR1, P1, EB, EI, P5, N0,
  ST_TET, COLOR1, P5, EI, EF, N0,
 // Case #51: (cloned #29)
  ST_TET, COLOR0, P2, EB, EC, EI,
  ST_TET, COLOR0, ED, EF, P3, EG,
  ST_WDG, COLOR1, P0, P4, P5, EG, ED, EF,
  ST_TET, COLOR1, P0, P4, P5, P1,
  ST_WDG, COLOR1, P0, P5, P1, EC, EI, EB,
 // Case #52: (cloned #11)
  ST_PNT, 0, NOCOLOR, 5, ED, EF, EC, EB, EH,
  ST_PYR, COLOR0, P1, P0, EC, EB, N0,
  ST_TET, COLOR0, EB, EH, P1, N0,
  ST_PYR, COLOR0, EF, EC, P0, P3, N0,
  ST_PYR, COLOR0, ED, P3, P1, EH, N0,
  ST_TET, COLOR0, P1, P3, P0, N0,
  ST_TET, COLOR0, P3, ED, EF, N0,
  ST_TET, COLOR1, P5, P4, P2, N0,
  ST_PYR, COLOR1, EF, ED, P4, P5, N0,
  ST_PYR, COLOR1, EF, P5, P2, EC, N0,
  ST_TET, COLOR1, EC, P2, EB, N0,
  ST_PYR, COLOR1, P2, P4, EH, EB, N0,
  ST_TET, COLOR1, P4, ED, EH, N0,
 // Case #53: (cloned #29)
  ST_TET, COLOR0, P3, ED, EF, EG,
  ST_TET, COLOR0, EB, P1, EA, EH,
  ST_WDG, COLOR1, EH, EB, EA, P4, P2, P0,
  ST_TET, COLOR1, P4, P0, P2, P5,
  ST_WDG, COLOR1, ED, EG, EF, P4, P0, P5,
 // Case #54: (cloned #27)
  ST_WDG, COLOR0, ED, P3, EF, EA, P0, EC,
  ST_HEX, COLOR1, P4, P5, EF, ED, P1, P2, EC, EA,
 // Case #55: (cloned #31)
  ST_TET, COLOR0, ED, EG, EF, P3,
  ST_WDG, COLOR1, EG, EF, ED, P0, P5, P4,
  ST_PYR, COLOR1, P1, P2, P5, P4, P0,
 // Case #56: (cloned #7)
  ST_WDG, COLOR0, P0, P1, P2, EG, EH, EI,
  ST_WDG, COLOR1, EG, EH, EI, P3, P4, P5,
 // Case #57: (cloned #15)
  ST_PNT, 0, COLOR1, 7, P4, P5, P0, EC, EA, EH, EI,
  ST_WDG, COLOR0, EC, P2, EI, EA, P1, EH,
  ST_TET, COLOR1, P3, P4, P5, P0,
  ST_PYR, COLOR1, EC, EI, EH, EA, N0,
  ST_PYR, COLOR1, EI, P5, P4, EH, N0,
  ST_TET, COLOR1, P0, P4, P5, N0,
  ST_TET, COLOR1, P0, EC, EA, N0,
  ST_PYR, COLOR1, P0, EA, EH, P4, N0,
  ST_PYR, COLOR1, P5, EI, EC, P0, N0,
 // Case #58: (cloned #15)
  ST_PNT, 0, COLOR1, 7, P5, P3, P1, EA, EB, EI, EG,
  ST_WDG, COLOR0, EA, P0, EG, EB, P2, EI,
  ST_TET, COLOR1, P4, P5, P3, P1,
  ST_PYR, COLOR1, EA, EG, EI, EB, N0,
  ST_PYR, COLOR1, EG, P3, P5, EI, N0,
  ST_TET, COLOR1, P1, P5, P3, N0,
  ST_TET, COLOR1, P1, EA, EB, N0,
  ST_PYR, COLOR1, P1, EB, EI, P5, N0,
  ST_PYR, COLOR1, P3, EG, EA, P1, N0,
 // Case #59: (cloned #31)
  ST_TET, COLOR0, EC, EB, EI, P2,
  ST_WDG, COLOR1, P5, P1, P0, EI, EB, EC,
  ST_PYR, COLOR1, P3, P0, P1, P4, P5,
 // Case #60: (cloned #15)
  ST_PNT, 0, COLOR1, 7, P3, P4, P2, EB, EC, EG, EH,
  ST_WDG, COLOR0, EB, P1, EH, EC, P0, EG,
  ST_TET, COLOR1, P5, P3, P4, P2,
  ST_PYR, COLOR1, EB, EH, EG, EC, N0,
  ST_PYR, COLOR1, EH, P4, P3, EG, N0,
  ST_TET, COLOR1, P2, P3, P4, N0,
  ST_TET, COLOR1, P2, EB, EC, N0,
  ST_PYR, COLOR1, P2, EC, EG, P3, N0,
  ST_PYR, COLOR1, P4, EH, EB, P2, N0,
 // Case #61: (cloned #31)
  ST_TET, COLOR0, EB, EA, EH, P1,
  ST_WDG, COLOR1, P4, P0, P2, EH, EA, EB,
  ST_PYR, COLOR1, P5, P2, P0, P3, P4,
 // Case #62: (cloned #31)
  ST_TET, COLOR0, EA, EC, EG, P0,
  ST_WDG, COLOR1, P3, P2, P1, EG, EC, EA,
  ST_PYR, COLOR1, P4, P1, P2, P5, P3,
 // Case #63: Unique case #13
  ST_WDG, COLOR1, P0, P1, P2, P3, P4, P5,
 // Dummy
  0
};

// ---- ClipCasesWdg.C ( end )
// ----------------------------------------------------------------------------


// ----------------------------------------------------------------------------
// ---- ClipCasesPyr.C (begin)

const int NumClipCasesPyr = 32;

const int NumClipShapesPyr[32] = {
  1,  3,  3,  8,  3,  4,  8,  4, // cases 0 - 7
  3,  8,  4,  4,  8,  4,  4,  2, // cases 8 - 15
  2,  4,  4,  8,  4,  4,  8,  3, // cases 16 - 23
  4,  8,  4,  3,  8,  3,  3,  1  // cases 24 - 31
};

const int StartClipShapesPyr[32] = {
  0, 7, 27, 47, 106, 126, 158, 217, // cases 0 - 7
  247, 267, 326, 358, 388, 447, 477, 507, // cases 8 - 15
  524, 541, 571, 601, 660, 690, 718, 777, // cases 16 - 23
  797, 827, 886, 914, 934, 993, 1013, 1033  // cases 24 - 31
};

static unsigned char ClipShapesPyr[] = {
 // Case #0: Unique case #1
  ST_PYR, COLOR0, P0, P1, P2, P3, P4,
 // Case #1: Unique case #2
  ST_WDG, COLOR0, EA, EE, ED, P1, P4, P3,
  ST_TET, COLOR0, P1, P2, P3, P4,
  ST_TET, COLOR1, P0, EA, ED, EE,
 // Case #2: (cloned #1)
  ST_WDG, COLOR0, EB, EF, EA, P2, P4, P0,
  ST_TET, COLOR0, P2, P3, P0, P4,
  ST_TET, COLOR1, P1, EB, EA, EF,
 // Case #3: Unique case #3
  ST_PNT, 0, COLOR0, 7, P4, EF, EE, EB, ED, P2, P3,
  ST_TET, COLOR0, EE, P4, EF, N0,
  ST_PYR, COLOR0, EB, ED, EE, EF, N0,
  ST_PYR, COLOR0, EB, EF, P4, P2, N0,
  ST_TET, COLOR0, P2, P4, P3, N0,
  ST_PYR, COLOR0, P3, P4, EE, ED, N0,
  ST_PYR, COLOR0, P2, P3, ED, EB, N0,
  ST_WDG, COLOR1, EB, EF, P1, ED, EE, P0,
 // Case #4: (cloned #1)
  ST_WDG, COLOR0, EC, EG, EB, P3, P4, P1,
  ST_TET, COLOR0, P3, P0, P1, P4,
  ST_TET, COLOR1, P2, EC, EB, EG,
 // Case #5: Unique case #4
  ST_WDG, COLOR0, EE, P4, EG, EA, P1, EB,
  ST_WDG, COLOR0, P4, EE, EG, P3, ED, EC,
  ST_WDG, COLOR1, P0, EA, EE, P2, EB, EG,
  ST_WDG, COLOR1, ED, P0, EE, EC, P2, EG,
 // Case #6: (cloned #3)
  ST_PNT, 0, COLOR0, 7, P4, EG, EF, EC, EA, P3, P0,
  ST_TET, COLOR0, EF, P4, EG, N0,
  ST_PYR, COLOR0, EC, EA, EF, EG, N0,
  ST_PYR, COLOR0, EC, EG, P4, P3, N0,
  ST_TET, COLOR0, P3, P4, P0, N0,
  ST_PYR, COLOR0, P0, P4, EF, EA, N0,
  ST_PYR, COLOR0, P3, P0, EA, EC, N0,
  ST_WDG, COLOR1, EC, EG, P2, EA, EF, P1,
 // Case #7: Unique case #5
  ST_TET, COLOR0, EE, EF, EG, P4,
  ST_WDG, COLOR0, EC, ED, P3, EG, EE, P4,
  ST_WDG, COLOR1, EE, EF, EG, P0, P1, P2,
  ST_WDG, COLOR1, P2, EC, EG, P0, ED, EE,
 // Case #8: (cloned #1)
  ST_WDG, COLOR0, ED, EH, EC, P0, P4, P2,
  ST_TET, COLOR0, P0, P1, P2, P4,
  ST_TET, COLOR1, P3, ED, EC, EH,
 // Case #9: (cloned #3)
  ST_PNT, 0, COLOR0, 7, P4, EE, EH, EA, EC, P1, P2,
  ST_TET, COLOR0, EH, P4, EE, N0,
  ST_PYR, COLOR0, EA, EC, EH, EE, N0,
  ST_PYR, COLOR0, EA, EE, P4, P1, N0,
  ST_TET, COLOR0, P1, P4, P2, N0,
  ST_PYR, COLOR0, P2, P4, EH, EC, N0,
  ST_PYR, COLOR0, P1, P2, EC, EA, N0,
  ST_WDG, COLOR1, EA, EE, P0, EC, EH, P3,
 // Case #10: (cloned #5)
  ST_WDG, COLOR0, EH, P4, EF, ED, P0, EA,
  ST_WDG, COLOR0, P4, EH, EF, P2, EC, EB,
  ST_WDG, COLOR1, P3, ED, EH, P1, EA, EF,
  ST_WDG, COLOR1, EC, P3, EH, EB, P1, EF,
 // Case #11: (cloned #7)
  ST_TET, COLOR0, EH, EE, EF, P4,
  ST_WDG, COLOR0, EB, EC, P2, EF, EH, P4,
  ST_WDG, COLOR1, EH, EE, EF, P3, P0, P1,
  ST_WDG, COLOR1, P1, EB, EF, P3, EC, EH,
 // Case #12: (cloned #3)
  ST_PNT, 0, COLOR0, 7, P4, EH, EG, ED, EB, P0, P1,
  ST_TET, COLOR0, EG, P4, EH, N0,
  ST_PYR, COLOR0, ED, EB, EG, EH, N0,
  ST_PYR, COLOR0, ED, EH, P4, P0, N0,
  ST_TET, COLOR0, P0, P4, P1, N0,
  ST_PYR, COLOR0, P1, P4, EG, EB, N0,
  ST_PYR, COLOR0, P0, P1, EB, ED, N0,
  ST_WDG, COLOR1, ED, EH, P3, EB, EG, P2,
 // Case #13: (cloned #7)
  ST_TET, COLOR0, EG, EH, EE, P4,
  ST_WDG, COLOR0, EA, EB, P1, EE, EG, P4,
  ST_WDG, COLOR1, EG, EH, EE, P2, P3, P0,
  ST_WDG, COLOR1, P0, EA, EE, P2, EB, EG,
 // Case #14: (cloned #7)
  ST_TET, COLOR0, EF, EG, EH, P4,
  ST_WDG, COLOR0, ED, EA, P0, EH, EF, P4,
  ST_WDG, COLOR1, EF, EG, EH, P1, P2, P3,
  ST_WDG, COLOR1, P3, ED, EH, P1, EA, EF,
 // Case #15: Unique case #6
  ST_PYR, COLOR0, EE, EF, EG, EH, P4,
  ST_HEX, COLOR1, P0, P1, P2, P3, EE, EF, EG, EH,
 // Case #16: Unique case #7
  ST_HEX, COLOR0, P0, P1, P2, P3, EE, EF, EG, EH,
  ST_PYR, COLOR1, EE, EF, EG, EH, P4,
 // Case #17: Unique case #8
  ST_WDG, COLOR0, ED, EH, P3, EA, EF, P1,
  ST_WDG, COLOR0, EF, EG, EH, P1, P2, P3,
  ST_WDG, COLOR1, P4, EF, EH, P0, EA, ED,
  ST_TET, COLOR1, EF, EG, EH, P4,
 // Case #18: (cloned #17)
  ST_WDG, COLOR0, EA, EE, P0, EB, EG, P2,
  ST_WDG, COLOR0, EG, EH, EE, P2, P3, P0,
  ST_WDG, COLOR1, P4, EG, EE, P1, EB, EA,
  ST_TET, COLOR1, EG, EH, EE, P4,
 // Case #19: Unique case #9
  ST_PNT, 0, COLOR1, 7, EH, EG, ED, EB, P0, P1, P4,
  ST_WDG, COLOR0, ED, EH, P3, EB, EG, P2,
  ST_PYR, COLOR1, EG, EH, ED, EB, N0,
  ST_PYR, COLOR1, ED, P0, P1, EB, N0,
  ST_TET, COLOR1, P0, P4, P1, N0,
  ST_TET, COLOR1, EH, EG, P4, N0,
  ST_PYR, COLOR1, EH, P4, P0, ED, N0,
  ST_PYR, COLOR1, P4, EG, EB, P1, N0,
 // Case #20: (cloned #17)
  ST_WDG, COLOR0, EB, EF, P1, EC, EH, P3,
  ST_WDG, COLOR0, EH, EE, EF, P3, P0, P1,
  ST_WDG, COLOR1, P4, EH, EF, P2, EC, EB,
  ST_TET, COLOR1, EH, EE, EF, P4,
 // Case #21: Unique case #10
  ST_TET, COLOR0, EA, P1, EB, EF,
  ST_TET, COLOR0, P3, ED, EC, EH,
  ST_WDG, COLOR1, EA, EB, EF, P0, P2, P4,
  ST_WDG, COLOR1, EC, ED, EH, P2, P0, P4,
 // Case #22: (cloned #19)
  ST_PNT, 0, COLOR1, 7, EE, EH, EA, EC, P1, P2, P4,
  ST_WDG, COLOR0, EA, EE, P0, EC, EH, P3,
  ST_PYR, COLOR1, EH, EE, EA, EC, N0,
  ST_PYR, COLOR1, EA, P1, P2, EC, N0,
  ST_TET, COLOR1, P1, P4, P2, N0,
  ST_TET, COLOR1, EE, EH, P4, N0,
  ST_PYR, COLOR1, EE, P4, P1, EA, N0,
  ST_PYR, COLOR1, P4, EH, EC, P2, N0,
 // Case #23: Unique case #11
  ST_TET, COLOR0, P3, ED, EC, EH,
  ST_WDG, COLOR1, P0, P2, P4, ED, EC, EH,
  ST_TET, COLOR1, P0, P1, P2, P4,
 // Case #24: (cloned #17)
  ST_WDG, COLOR0, EC, EG, P2, ED, EE, P0,
  ST_WDG, COLOR0, EE, EF, EG, P0, P1, P2,
  ST_WDG, COLOR1, P4, EE, EG, P3, ED, EC,
  ST_TET, COLOR1, EE, EF, EG, P4,
 // Case #25: (cloned #19)
  ST_PNT, 0, COLOR1, 7, EG, EF, EC, EA, P3, P0, P4,
  ST_WDG, COLOR0, EC, EG, P2, EA, EF, P1,
  ST_PYR, COLOR1, EF, EG, EC, EA, N0,
  ST_PYR, COLOR1, EC, P3, P0, EA, N0,
  ST_TET, COLOR1, P3, P4, P0, N0,
  ST_TET, COLOR1, EG, EF, P4, N0,
  ST_PYR, COLOR1, EG, P4, P3, EC, N0,
  ST_PYR, COLOR1, P4, EF, EA, P0, N0,
 // Case #26: (cloned #21)
  ST_TET, COLOR0, ED, P0, EA, EE,
  ST_TET, COLOR0, P2, EC, EB, EG,
  ST_WDG, COLOR1, ED, EA, EE, P3, P1, P4,
  ST_WDG, COLOR1, EB, EC, EG, P1, P3, P4,
 // Case #27: (cloned #23)
  ST_TET, COLOR0, P2, EC, EB, EG,
  ST_WDG, COLOR1, P3, P1, P4, EC, EB, EG,
  ST_TET, COLOR1, P3, P0, P1, P4,
 // Case #28: (cloned #19)
  ST_PNT, 0, COLOR1, 7, EF, EE, EB, ED, P2, P3, P4,
  ST_WDG, COLOR0, EB, EF, P1, ED, EE, P0,
  ST_PYR, COLOR1, EE, EF, EB, ED, N0,
  ST_PYR, COLOR1, EB, P2, P3, ED, N0,
  ST_TET, COLOR1, P2, P4, P3, N0,
  ST_TET, COLOR1, EF, EE, P4, N0,
  ST_PYR, COLOR1, EF, P4, P2, EB, N0,
  ST_PYR, COLOR1, P4, EE, ED, P3, N0,
 // Case #29: (cloned #23)
  ST_TET, COLOR0, P1, EB, EA, EF,
  ST_WDG, COLOR1, P2, P0, P4, EB, EA, EF,
  ST_TET, COLOR1, P2, P3, P0, P4,
 // Case #30: (cloned #23)
  ST_TET, COLOR0, P0, EA, ED, EE,
  ST_WDG, COLOR1, P1, P3, P4, EA, ED, EE,
  ST_TET, COLOR1, P1, P2, P3, P4,
 // Case #31: Unique case #12
  ST_PYR, COLOR1, P0, P1, P2, P3, P4,
 // Dummy
  0
};

// ---- ClipCasesPyr.C ( end )
// ----------------------------------------------------------------------------


// ----------------------------------------------------------------------------
// ---- ClipCasesTet.C (begin)

const int NumClipCasesTet = 16;

const int NumClipShapesTet[16] = {
  1,  2,  2,  2,  2,  2,  2,  2, // cases 0 - 7
  2,  2,  2,  2,  2,  2,  2,  1  // cases 8 - 15
};

const int StartClipShapesTet[16] = {
  0, 6, 20, 34, 50, 64, 80, 96, // cases 0 - 7
  110, 124, 140, 156, 170, 186, 200, 214  // cases 8 - 15
};

static unsigned char ClipShapesTet[] = {
 // Case #0: Unique case #1
  ST_TET, COLOR0, P0, P1, P2, P3,
 // Case #1: Unique case #2
  ST_WDG, COLOR0, EA, ED, EC, P1, P3, P2,
  ST_TET, COLOR1, P0, EA, EC, ED,
 // Case #2: (cloned #1)
  ST_WDG, COLOR0, P0, P3, P2, EA, EE, EB,
  ST_TET, COLOR1, P1, EB, EA, EE,
 // Case #3: Unique case #3
  ST_WDG, COLOR0, P3, ED, EE, P2, EC, EB,
  ST_WDG, COLOR1, P0, ED, EC, P1, EE, EB,
 // Case #4: (cloned #1)
  ST_WDG, COLOR0, EC, EF, EB, P0, P3, P1,
  ST_TET, COLOR1, P2, EC, EB, EF,
 // Case #5: (cloned #3)
  ST_WDG, COLOR0, P1, EA, EB, P3, ED, EF,
  ST_WDG, COLOR1, P2, EF, EB, P0, ED, EA,
 // Case #6: (cloned #3)
  ST_WDG, COLOR0, P3, EE, EF, P0, EA, EC,
  ST_WDG, COLOR1, P1, EE, EA, P2, EF, EC,
 // Case #7: Unique case #4
  ST_TET, COLOR0, ED, EE, EF, P3,
  ST_WDG, COLOR1, ED, EE, EF, P0, P1, P2,
 // Case #8: (cloned #1)
  ST_WDG, COLOR0, P0, P2, P1, ED, EF, EE,
  ST_TET, COLOR1, P3, EE, ED, EF,
 // Case #9: (cloned #3)
  ST_WDG, COLOR0, P2, EC, EF, P1, EA, EE,
  ST_WDG, COLOR1, P0, EC, EA, P3, EF, EE,
 // Case #10: (cloned #3)
  ST_WDG, COLOR0, P0, EA, ED, P2, EB, EF,
  ST_WDG, COLOR1, P3, EF, ED, P1, EB, EA,
 // Case #11: (cloned #7)
  ST_TET, COLOR0, EC, EF, EB, P2,
  ST_WDG, COLOR1, P0, P1, P3, EC, EB, EF,
 // Case #12: (cloned #3)
  ST_WDG, COLOR0, P1, EB, EE, P0, EC, ED,
  ST_WDG, COLOR1, P2, EB, EC, P3, EE, ED,
 // Case #13: (cloned #7)
  ST_TET, COLOR0, EA, EB, EE, P1,
  ST_WDG, COLOR1, EA, EB, EE, P0, P2, P3,
 // Case #14: (cloned #7)
  ST_TET, COLOR0, EA, ED, EC, P0,
  ST_WDG, COLOR1, P1, P2, P3, EA, EC, ED,
 // Case #15: Unique case #5
  ST_TET, COLOR1, P0, P1, P2, P3,
 // Dummy
  0
};

// ---- ClipCasesTet.C ( end )
// ----------------------------------------------------------------------------


// ----------------------------------------------------------------------------
// ---- ClipCasesQua.C (begin)

const int NumClipCasesQua = 16;

const int NumClipShapesQua[16] = {
  1,  3,  3,  2,  3,  4,  2,  3, // cases 0 - 7
  3,  2,  4,  3,  2,  3,  3,  1  // cases 8 - 15
};

const int StartClipShapesQua[16] = {
  0, 6, 22, 38, 50, 66, 88, 100, // cases 0 - 7
  116, 132, 144, 166, 182, 194, 210, 226  // cases 8 - 15
};

static unsigned char ClipShapesQua[] = {
 // Case #0: Unique case #1
  ST_QUA, COLOR0, P0, P1, P2, P3,
 // Case #1: Unique case #2
  ST_QUA, COLOR0, ED, EA, P1, P3,
  ST_TRI, COLOR0, P3, P1, P2,
  ST_TRI, COLOR1, P0, EA, ED,
 // Case #2: (cloned #1)
  ST_QUA, COLOR0, EA, EB, P2, P0,
  ST_TRI, COLOR0, P0, P2, P3,
  ST_TRI, COLOR1, P1, EB, EA,
 // Case #3: Unique case #3
  ST_QUA, COLOR0, ED, EB, P2, P3,
  ST_QUA, COLOR1, P0, P1, EB, ED,
 // Case #4: (cloned #1)
  ST_QUA, COLOR0, EB, EC, P3, P1,
  ST_TRI, COLOR0, P1, P3, P0,
  ST_TRI, COLOR1, P2, EC, EB,
 // Case #5: Unique case #4
  ST_TRI, COLOR0, ED, EC, P3,
  ST_TRI, COLOR0, EB, EA, P1,
  ST_QUA, COLOR1, P2, P0, EA, EB,
  ST_QUA, COLOR1, P0, P2, EC, ED,
 // Case #6: (cloned #3)
  ST_QUA, COLOR0, EA, EC, P3, P0,
  ST_QUA, COLOR1, P1, P2, EC, EA,
 // Case #7: Unique case #5
  ST_TRI, COLOR0, ED, EC, P3,
  ST_QUA, COLOR1, P0, P2, EC, ED,
  ST_TRI, COLOR1, P1, P2, P0,
 // Case #8: (cloned #1)
  ST_QUA, COLOR0, EC, ED, P0, P2,
  ST_TRI, COLOR0, P2, P0, P1,
  ST_TRI, COLOR1, P3, ED, EC,
 // Case #9: (cloned #3)
  ST_QUA, COLOR0, EC, EA, P1, P2,
  ST_QUA, COLOR1, P3, P0, EA, EC,
 // Case #10: (cloned #5)
  ST_TRI, COLOR0, EA, ED, P0,
  ST_TRI, COLOR0, EC, EB, P2,
  ST_QUA, COLOR1, P3, P1, EB, EC,
  ST_QUA, COLOR1, P1, P3, ED, EA,
 // Case #11: (cloned #7)
  ST_TRI, COLOR0, EC, EB, P2,
  ST_QUA, COLOR1, P3, P1, EB, EC,
  ST_TRI, COLOR1, P0, P1, P3,
 // Case #12: (cloned #3)
  ST_QUA, COLOR0, EB, ED, P0, P1,
  ST_QUA, COLOR1, P2, P3, ED, EB,
 // Case #13: (cloned #7)
  ST_TRI, COLOR0, EB, EA, P1,
  ST_QUA, COLOR1, P2, P0, EA, EB,
  ST_TRI, COLOR1, P3, P0, P2,
 // Case #14: (cloned #7)
  ST_TRI, COLOR0, EA, ED, P0,
  ST_QUA, COLOR1, P1, P3, ED, EA,
  ST_TRI, COLOR1, P2, P3, P1,
 // Case #15: Unique case #6
  ST_QUA, COLOR1, P0, P1, P2, P3,
 // Dummy
  0
};

// ---- ClipCasesQua.C ( end )
// ----------------------------------------------------------------------------


// ----------------------------------------------------------------------------
// ---- ClipCasesPix.C (begin)

const int NumClipCasesPix = 16;

const int NumClipShapesPix[16] = {
  1,  3,  3,  2,  3,  2,  4,  3, // cases 0 - 7
  3,  4,  2,  3,  2,  3,  3,  1  // cases 8 - 15
};

const int StartClipShapesPix[16] = {
  0, 6, 22, 38, 50, 66, 78, 100, // cases 0 - 7
  116, 132, 154, 166, 182, 194, 210, 226  // cases 8 - 15
};

static unsigned char ClipShapesPix[] = {
 // Case #0: Unique case #1
  ST_QUA, COLOR0, P0, P1, P3, P2,
 // Case #1: Unique case #2
  ST_QUA, COLOR0, ED, EA, P1, P2,
  ST_TRI, COLOR0, P2, P1, P3,
  ST_TRI, COLOR1, P0, EA, ED,
 // Case #2: (cloned #1)
  ST_QUA, COLOR0, EA, EB, P3, P0,
  ST_TRI, COLOR0, P0, P3, P2,
  ST_TRI, COLOR1, P1, EB, EA,
 // Case #3: Unique case #3
  ST_QUA, COLOR0, ED, EB, P3, P2,
  ST_QUA, COLOR1, P0, P1, EB, ED,
 // Case #4: (cloned #1)
  ST_QUA, COLOR0, EC, ED, P0, P3,
  ST_TRI, COLOR0, P3, P0, P1,
  ST_TRI, COLOR1, P2, ED, EC,
 // Case #5: (cloned #3)
  ST_QUA, COLOR0, EC, EA, P1, P3,
  ST_QUA, COLOR1, P2, P0, EA, EC,
 // Case #6: Unique case #4
  ST_TRI, COLOR0, EA, ED, P0,
  ST_TRI, COLOR0, EC, EB, P3,
  ST_QUA, COLOR1, P2, P1, EB, EC,
  ST_QUA, COLOR1, P1, P2, ED, EA,
 // Case #7: Unique case #5
  ST_TRI, COLOR0, EC, EB, P3,
  ST_QUA, COLOR1, P2, P1, EB, EC,
  ST_TRI, COLOR1, P0, P1, P2,
 // Case #8: (cloned #1)
  ST_QUA, COLOR0, EB, EC, P2, P1,
  ST_TRI, COLOR0, P1, P2, P0,
  ST_TRI, COLOR1, P3, EC, EB,
 // Case #9: (cloned #6)
  ST_TRI, COLOR0, EB, EA, P1,
  ST_TRI, COLOR0, ED, EC, P2,
  ST_QUA, COLOR1, P0, P3, EC, ED,
  ST_QUA, COLOR1, P3, P0, EA, EB,
 // Case #10: (cloned #3)
  ST_QUA, COLOR0, EA, EC, P2, P0,
  ST_QUA, COLOR1, P1, P3, EC, EA,
 // Case #11: (cloned #7)
  ST_TRI, COLOR0, ED, EC, P2,
  ST_QUA, COLOR1, P0, P3, EC, ED,
  ST_TRI, COLOR1, P1, P3, P0,
 // Case #12: (cloned #3)
  ST_QUA, COLOR0, EB, ED, P0, P1,
  ST_QUA, COLOR1, P3, P2, ED, EB,
 // Case #13: (cloned #7)
  ST_TRI, COLOR0, EB, EA, P1,
  ST_QUA, COLOR1, P3, P0, EA, EB,
  ST_TRI, COLOR1, P2, P0, P3,
 // Case #14: (cloned #7)
  ST_TRI, COLOR0, EA, ED, P0,
  ST_QUA, COLOR1, P1, P2, ED, EA,
  ST_TRI, COLOR1, P3, P2, P1,
 // Case #15: Unique case #6
  ST_QUA, COLOR1, P0, P1, P3, P2,
 // Dummy
  0
};

// ---- ClipCasesPix.C ( end )
// ----------------------------------------------------------------------------


// ----------------------------------------------------------------------------
// ---- ClipCasesTri.C (begin)

const int NumClipCasesTri = 8;

const int NumClipShapesTri[8] = {
  1,  2,  2,  2,  2,  2,  2,  1  // cases 0 - 7
};

const int StartClipShapesTri[8] = {
  0, 5, 16, 27, 38, 49, 60, 71  // cases 0 - 7
};

static unsigned char ClipShapesTri[] = {
 // Case #0: Unique case #1
  ST_TRI, COLOR0, P0, P1, P2,
 // Case #1: Unique case #2
  ST_QUA, COLOR0, P1, P2, EC, EA,
  ST_TRI, COLOR1, P0, EA, EC,
 // Case #2: (cloned #1)
  ST_QUA, COLOR0, P2, P0, EA, EB,
  ST_TRI, COLOR1, P1, EB, EA,
 // Case #3: Unique case #3
  ST_TRI, COLOR0, EC, EB, P2,
  ST_QUA, COLOR1, P0, P1, EB, EC,
 // Case #4: (cloned #1)
  ST_QUA, COLOR0, P0, P1, EB, EC,
  ST_TRI, COLOR1, P2, EC, EB,
 // Case #5: (cloned #3)
  ST_TRI, COLOR0, EB, EA, P1,
  ST_QUA, COLOR1, P2, P0, EA, EB,
 // Case #6: (cloned #3)
  ST_TRI, COLOR0, EA, EC, P0,
  ST_QUA, COLOR1, P1, P2, EC, EA,
 // Case #7: Unique case #4
  ST_TRI, COLOR1, P0, P1, P2,
 // Dummy
  0
};

// ---- ClipCasesTri.C ( end )
// ----------------------------------------------------------------------------


// ----------------------------------------------------------------------------
// ---- ClipCasesLin.C (begin)

const int NumClipCasesLin = 4;

const int NumClipShapesLin[4] = {
  1,  2,  2,  1 };

const int StartClipShapesLin[4] = {
  0, 4, 12, 20  };

static unsigned char ClipShapesLin[] = {
 // Case #0: Unique case #1
  ST_LIN, COLOR0, P0, P1,
 // Case #1: Unique case #2
  ST_LIN, COLOR0, EA, P1,
  ST_LIN, COLOR1, P0, EA,
 // Case #2: (cloned #1)
  ST_LIN, COLOR0, EA, P0,
  ST_LIN, COLOR1, P1, EA,
 // Case #3: Unique case #3
  ST_LIN, COLOR1, P0, P1,
 // Dummy
  0
};

// ---- ClipCasesLin.C ( end )
// ----------------------------------------------------------------------------


// ----------------------------------------------------------------------------
// ---- ClipCasesVtx.C (begin)

const int NumClipCasesVtx = 2;

const int NumClipShapesVtx[2] = {
  1,  1 };

const int StartClipShapesVtx[2] = {
  0, 3  };

static unsigned char ClipShapesVtx[] = {
 // Case #0: Unique case #1
  ST_VTX, COLOR0, P0,
 // Case #1: Unique case #2
  ST_VTX, COLOR1, P0,
 // Dummy
  0
};

// ---- ClipCasesVtx.C ( end )
// ----------------------------------------------------------------------------

};

// ============================================================================
// ============================= ClipCases ( end ) ============================
// ============================================================================
#endif
// VTK-HeaderTest-Exclude: vtkTableBasedClipCases.h
