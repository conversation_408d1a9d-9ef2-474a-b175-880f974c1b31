# Configured file and directory locations.
set(VTK_LIB_DIR "@CMAKE_LIBRARY_OUTPUT_DIRECTORY@")
set(VTK_INSTALL_QT_DIR "@VTK_INSTALL_QT_DIR@")
set(VTK_INSTALL_QT_PLUGIN_DIR "@VTK_INSTALL_QT_PLUGIN_DIR@")
set(BUILDTYPE_SUFFIX)
if (WIN32)
  if (BUILD_TYPE STREQUAL "Debug")
    set(BUILDTYPE_SUFFIX @CMAKE_DEBUG_POSTFIX@)
  else ()
    set(BUILD<PERSON>PE_SUFFIX @CMAKE_RELEASE_POSTFIX@)
  endif ()
endif ()
set(VTK_INSTALL_QT_PLUGIN_FILE "@CMAKE_SHARED_LIBRARY_PREFIX@QVTKWidgetPlugin${BUILDTYPE_SUFFIX}@CMAKE_SHARED_LIBRARY_SUFFIX@")
set(VTK_CONFIGURATIONS "@CMAKE_CONFIGURATION_TYPES@")

if(VTK_CONFIGURATIONS)
  set(VTK_LIBSUBDIR "/${BUILD_TYPE}")
endif()

# Install the file to the specified location.
file(INSTALL DESTINATION "${VTK_INSTALL_QT_PLUGIN_DIR}" TYPE SHARED_LIBRARY
  FILES "${VTK_LIB_DIR}${VTK_LIBSUBDIR}/${VTK_INSTALL_QT_PLUGIN_FILE}")
