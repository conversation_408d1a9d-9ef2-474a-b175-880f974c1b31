<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>vtkQtRichTextView</class>
 <widget class="QWidget" name="vtkQtRichTextView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="margin">
    <number>0</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QLabel" name="Title">
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QToolButton" name="BackButton">
       <property name="toolTip">
        <string>Go back one page</string>
       </property>
       <property name="text">
        <string>Back</string>
       </property>
       <property name="arrowType">
        <enum>Qt::LeftArrow</enum>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="ForwardButton">
       <property name="toolTip">
        <string> Go forward one page</string>
       </property>
       <property name="text">
        <string>Forward</string>
       </property>
       <property name="arrowType">
        <enum>Qt::RightArrow</enum>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="ZoomIn">
       <property name="text">
        <string>+</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="ZoomReset">
       <property name="text">
        <string>100%</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="ZoomOut">
       <property name="text">
        <string>-</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QWebView" name="WebView">
     <property name="url">
      <url>
       <string>about:blank</string>
      </url>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QWebView</class>
   <extends>QWidget</extends>
   <header>QtWebKit/QWebView</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
