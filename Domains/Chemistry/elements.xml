<?xml version="1.0" encoding="utf-8"?>
<list id="chemicalElement" convention="bodr:elements"
      title="properties of the elements"

      xmlns="http://www.xml-cml.org/schema"
      xmlns:xml="http://www.w3.org/XML/1998/namespace"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xmlns:bo="http://www.blueobelisk.org/dict/terminology"
      xmlns:boUnits="http://www.blueobelisk.org/dict/units"
      xmlns:units="http://www.xml-cml.org/units/units"
      xmlns:siUnits="http://www.xml-cml.org/units/siUnits"
      xmlns:bibx="http://bibtexml.sf.net/"

      xsi:schemaLocation="http://www.xml-cml.org/schema ../schemas/cml25.xsd
			   http://bibtexml.sf.net/       ../schemas/bibtexml.xsd">

  <metadataList>
    <!-- manually updated -->

    <metadata name="dc:title" content="Blue Obelisk Element Repository" />
    <metadata name="dc:creator" content="The Blue Obelisk Movement" />
    <metadata name="dc:license" content="The MIT License" />
    <metadata name="dc:contributor" content="Geoffrey R. Hutchison" />
    <metadata name="dc:contributor" content="Carsten Niehaus" />
    <metadata name="dc:contributor" content="Egon Willighagen" />
    <metadata name="dc:description" content="Database of elements and elemental properties (names, symbols, masses, exact masses, van der Waals radii, ionization potential, electron affinity, electronegativity, etc." />
  </metadataList>

  <atom id="Xx">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">0</scalar>
    <label dictRef="bo:symbol" value="Xx" />
    <label dictRef="bo:name" xml:lang="en" value="Dummy" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">0.0000</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">0.00000</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">0.0</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">0</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.07 0.50 0.70</array>
  </atom>
  <atom id="H">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">1</scalar>
    <label dictRef="bo:symbol" value="H" />
    <label dictRef="bo:name" xml:lang="en" value="Hydrogen" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="7">1.00794</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">1.007825032</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">13.5984</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="3">0.75420375</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.20</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'hydro' and 'gennao' for 'forms water'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">0.37</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">1.00 1.00 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">20.28</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">14.01</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">s</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1766</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">C. Cavendish</array>
    <scalar dataType="xsd:int" dictRef="bo:period">1</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">1s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Non-Metal</scalar>
  </atom>
  <atom id="He">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">2</scalar>
    <label dictRef="bo:symbol" value="He" />
    <label dictRef="bo:name" xml:lang="en" value="Helium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">4.002602</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">4.002603254</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">24.5874</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">The Greek word for the sun was 'helios'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">0.32</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.4</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.85 1.00 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">4.216</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">0.95</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" delimiter="," size="2" dictRef="bo:discoveryCountry">se,uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1895</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">P. J. Janssen;J. N. Lockyer</array>
    <scalar dataType="xsd:int" dictRef="bo:period">1</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">1s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Noblegas</scalar>
  </atom>
  <atom id="Li">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">3</scalar>
    <label dictRef="bo:symbol" value="Li" />
    <label dictRef="bo:name" xml:lang="en" value="Lithium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">6.941</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">7.01600455</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.3917</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="21">0.618049</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">0.98</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'lithos' means 'stone'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.34</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.80 0.50 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1615</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">453.7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">s</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">se</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1817</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">A. Arfvedson</array>
    <scalar dataType="xsd:int" dictRef="bo:period">2</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">He 2s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Alkali_Earth</scalar>
  </atom>
  <atom id="Be">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">4</scalar>
    <label dictRef="bo:symbol" value="Be" />
    <label dictRef="bo:name" xml:lang="en" value="Beryllium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">9.012182</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">9.0121822</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">9.3227</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.57</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'beryllos' for 'light-green stone'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">0.90</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.9</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.76 1.00 0.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3243</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1560</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">s</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">fr</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1797</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Nicholas Louis Vauquelin</array>
    <scalar dataType="xsd:int" dictRef="bo:period">2</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">He 2s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Alkaline_Earth</scalar>
  </atom>
  <atom id="B">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">5</scalar>
    <label dictRef="bo:symbol" value="B" />
    <label dictRef="bo:name" xml:lang="en" value="Boron" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="7">10.811</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">11.0093054</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">8.2980</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="25">0.279723</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.04</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Boron means 'Bor(ax) + (carb)on'. It is found in borax and behaves a lot like carbon</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">0.82</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.8</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">1.00 0.71 0.71</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">4275</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">2365</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" delimiter="," size="2" dictRef="bo:discoveryCountry">uk,fr</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1808</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">Louis Joseph Gay-Lussac;Louis Jacques Thenard</array>
    <scalar dataType="xsd:int" dictRef="bo:period">2</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">He 2s2 2p1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Metalloids</scalar>
  </atom>
  <atom id="C">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">6</scalar>
    <label dictRef="bo:symbol" value="C" />
    <label dictRef="bo:name" xml:lang="en" value="Carbon" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="8">12.0107</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">12</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">11.2603</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="20">1.262118</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.55</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'carboneum' for carbon</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">0.77</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.7</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.50 0.50 0.50</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">5100</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">3825</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ancient</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">0</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">2</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">He 2s2 2p2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Non-Metal</scalar>
  </atom>
  <atom id="N">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">7</scalar>
    <label dictRef="bo:symbol" value="N" />
    <label dictRef="bo:name" xml:lang="en" value="Nitrogen" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">14.0067</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">14.003074</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">14.5341</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="2">-0.07</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">3.04</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'nitrogenium' ('forms saltpeter')</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">0.75</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.6</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.05 0.05 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">77.344</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">63.15</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1772</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">D. Rutherford</array>
    <scalar dataType="xsd:int" dictRef="bo:period">2</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">He 2s2 2p3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Non-Metal</scalar>
  </atom>
  <atom id="O">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">8</scalar>
    <label dictRef="bo:symbol" value="O" />
    <label dictRef="bo:name" xml:lang="en" value="Oxygen" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">15.9994</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">15.99491462</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">13.6181</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="27">1.4611120</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">3.44</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'oxygenium' (forms acids)</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">0.73</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.55</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">1.00 0.05 0.05</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">90.188</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">54.8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" delimiter="," size="2" dictRef="bo:discoveryCountry">se,uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1774</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">J. Priestley</array>
    <scalar dataType="xsd:int" dictRef="bo:period">2</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">He 2s2 2p4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Non-Metal</scalar>
  </atom>
  <atom id="F">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">9</scalar>
    <label dictRef="bo:symbol" value="F" />
    <label dictRef="bo:name" xml:lang="en" value="Fluorine" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="5">18.9984032</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">18.99840322</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">17.4228</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="32">3.4011887</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">3.98</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'fluere' ('floats')</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">0.71</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.5</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.70 1.00 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">85</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">53.55</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">fr</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1886</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">H. F. Moissan</array>
    <scalar dataType="xsd:int" dictRef="bo:period">2</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">He 2s2 2p5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Halogen</scalar>
  </atom>
  <atom id="Ne">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">10</scalar>
    <label dictRef="bo:symbol" value="Ne" />
    <label dictRef="bo:name" xml:lang="en" value="Neon" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="6">20.1797</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">19.99244018</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">21.5645</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'neo'. meaning 'new'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">0.69</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.54</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.70 0.89 0.96</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">27.1</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">24.55</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1898</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">W. Ramsay;M.W. Travers</array>
    <scalar dataType="xsd:int" dictRef="bo:period">2</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">He 2s2 2p6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Noblegas</scalar>
  </atom>
  <atom id="Na">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">11</scalar>
    <label dictRef="bo:symbol" value="Na" />
    <label dictRef="bo:name" xml:lang="en" value="Sodium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">22.98976928</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">22.98976928</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.1391</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="25">0.547926</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">0.93</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Arabic 'natrun' for 'soda'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.54</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.4</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.67 0.36 0.95</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1156</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">371</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">s</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1807</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Sir Humphrey Davy</array>
    <scalar dataType="xsd:int" dictRef="bo:period">3</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ne 3s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Alkali_Earth</scalar>
  </atom>
  <atom id="Mg">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">12</scalar>
    <label dictRef="bo:symbol" value="Mg" />
    <label dictRef="bo:name" xml:lang="en" value="Magnesium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="6">24.3050</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">23.9850417</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.6462</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.31</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the city of Magnesia</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.30</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.54 1.00 0.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1380</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">922</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">s</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1808</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">H. B. Davy</array>
    <scalar dataType="xsd:int" dictRef="bo:period">3</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ne 3s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Alkaline_Earth</scalar>
  </atom>
  <atom id="Al">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">13</scalar>
    <label dictRef="bo:symbol" value="Al" />
    <label dictRef="bo:name" xml:lang="en" value="Aluminium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="8">26.9815386</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">26.98153863</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.9858</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="5">0.43283</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.61</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'alumen'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.18</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.1</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.75 0.65 0.65</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2740</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">933.5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">dk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1825</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">H. Ch. Oersted</array>
    <scalar dataType="xsd:int" dictRef="bo:period">3</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ne 3s2 3p1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Si">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">14</scalar>
    <label dictRef="bo:symbol" value="Si" />
    <label dictRef="bo:name" xml:lang="en" value="Silicon" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">28.0855</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">27.97692653</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">8.1517</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="20">1.389521</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.90</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'silex'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.11</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.1</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.50 0.60 0.60</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2630</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1683</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">se</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1823</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">J. J. Berzelius</array>
    <scalar dataType="xsd:int" dictRef="bo:period">3</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ne 3s2 3p2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Metalloids</scalar>
  </atom>
  <atom id="P">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">15</scalar>
    <label dictRef="bo:symbol" value="P" />
    <label dictRef="bo:name" xml:lang="en" value="Phosphorus" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">30.973762</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">30.97376163</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">10.4867</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="3">0.7465</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.19</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'phosphoros' for 'carries light'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.06</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.95</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">1.00 0.50 0.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">553</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">317.3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1669</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">H. Brandt</array>
    <scalar dataType="xsd:int" dictRef="bo:period">3</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ne 3s2 3p3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Non-Metal</scalar>
  </atom>
  <atom id="S">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">16</scalar>
    <label dictRef="bo:symbol" value="S" />
    <label dictRef="bo:name" xml:lang="en" value="Sulfur" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="5">32.065</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">31.972071</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">10.3600</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="10">2.0771029</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.58</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">In sanskrit 'sweb' means 'to sleep'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.02</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.8</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">1.00 1.00 0.19</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">717.82</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">392.2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ancient</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">0</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">3</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ne 3s2 3p4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Non-Metal</scalar>
  </atom>
  <atom id="Cl">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">17</scalar>
    <label dictRef="bo:symbol" value="Cl" />
    <label dictRef="bo:name" xml:lang="en" value="Chlorine" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">35.453</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">34.96885268</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">12.9676</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="27">3.612724</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">3.16</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'chloros' for 'yellow-green'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">0.99</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.8</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.12 0.94 0.12</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">239.18</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">172.17</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">se</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1774</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">C. W. Scheele</array>
    <scalar dataType="xsd:int" dictRef="bo:period">3</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ne 3s2 3p5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Halogen</scalar>
  </atom>
  <atom id="Ar">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">18</scalar>
    <label dictRef="bo:symbol" value="Ar" />
    <label dictRef="bo:name" xml:lang="en" value="Argon" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">39.948</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">39.96238312</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">15.7596</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'aergon' for 'inactive'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">0.97</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.88</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.50 0.82 0.89</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">87.45</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">83.95</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1894</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">W. Ramsay;J. Rayleigh</array>
    <scalar dataType="xsd:int" dictRef="bo:period">3</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ne 3s2 3p6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Noblegas</scalar>
  </atom>
  <atom id="K">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">19</scalar>
    <label dictRef="bo:symbol" value="K" />
    <label dictRef="bo:name" xml:lang="en" value="Potassium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">39.0983</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">38.96370668</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">4.3407</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="12">0.501459</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">0.82</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Arabic 'al qaliy' for potash</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.96</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.8</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.56 0.25 0.83</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1033</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">336.8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">s</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1807</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">H. B. Davy</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 4s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Alkali_Earth</scalar>
  </atom>
  <atom id="Ca">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">20</scalar>
    <label dictRef="bo:symbol" value="Ca" />
    <label dictRef="bo:name" xml:lang="en" value="Calcium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="4">40.078</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">39.96259098</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.1132</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="10">0.02455</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.00</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'calx' for 'lime'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.74</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.4</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.24 1.00 0.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1757</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1112</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">s</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1808</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">H. B. Davy</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 4s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Alkaline_Earth</scalar>
  </atom>
  <atom id="Sc">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">21</scalar>
    <label dictRef="bo:symbol" value="Sc" />
    <label dictRef="bo:name" xml:lang="en" value="Scandium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="6">44.955912</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">44.9559119</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.5615</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="20">0.188</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.36</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named because it was found in Scandinavia</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.44</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.3</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.90 0.90 0.90</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3109</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1814</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">se</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1879</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">L. Nilson</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d1 4s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Ti">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">22</scalar>
    <label dictRef="bo:symbol" value="Ti" />
    <label dictRef="bo:name" xml:lang="en" value="Titanium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">47.867</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">47.9479463</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.8281</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="9">0.084</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.54</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">The Titans were giants in Greek mythology</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.36</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.15</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.75 0.76 0.78</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3560</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1935</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1791</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">W. Gregor</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d2 4s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="V">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">23</scalar>
    <label dictRef="bo:symbol" value="V" />
    <label dictRef="bo:name" xml:lang="en" value="Vanadium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">50.9415</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">50.9439595</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.7462</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="12">0.525</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.63</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">'Vanadis' is another name for the Nordic goddess Freyja</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.25</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.05</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.65 0.65 0.67</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3650</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">2163</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">se</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1830</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">N. Sefström</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d3 4s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Cr">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">24</scalar>
    <label dictRef="bo:symbol" value="Cr" />
    <label dictRef="bo:name" xml:lang="en" value="Chromium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="6">51.9961</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">51.9405075</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.7665</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="12">0.67584</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.66</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'chroma' means 'color'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.27</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.05</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.54 0.60 0.78</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2945</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">2130</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">fr</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1797</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Nicholas Louis Vauquelin</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d5 4s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Mn">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">25</scalar>
    <label dictRef="bo:symbol" value="Mn" />
    <label dictRef="bo:name" xml:lang="en" value="Manganese" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="5">54.938045</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">54.9380451</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.4340</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.55</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">It was discovered near a town named Magnesia in black earth. Thus, it was named 'magnesia nigra', or for short, Manganese.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.39</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.05</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.61 0.48 0.78</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2235</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1518</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">se</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1774</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">C. W. Scheele</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d5 4s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Fe">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">26</scalar>
    <label dictRef="bo:symbol" value="Fe" />
    <label dictRef="bo:name" xml:lang="en" value="Iron" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">55.845</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">55.9349375</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.9024</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="3">0.151</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.83</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'ferrum'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.25</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.05</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.50 0.48 0.78</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3023</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1808</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ancient</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">0</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d6 4s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Co">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">27</scalar>
    <label dictRef="bo:symbol" value="Co" />
    <label dictRef="bo:name" xml:lang="en" value="Cobalt" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="5">58.933195</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">58.933195</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.8810</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="6">0.6633</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.88</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the German word 'Kobold' for 'goblin'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.26</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.44 0.48 0.78</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3143</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1768</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">se</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1737</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">G. Brandt</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d7 4s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Ni">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">28</scalar>
    <label dictRef="bo:symbol" value="Ni" />
    <label dictRef="bo:name" xml:lang="en" value="Nickel" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="4">58.6934</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">57.9353429</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.6398</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="12">1.15716</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.91</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">'Nickel' was the name of a mountain goblin</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.21</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.36 0.48 0.76</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3005</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1726</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">se</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1751</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">A. F. Cronstedt</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d8 4s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Cu">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">29</scalar>
    <label dictRef="bo:symbol" value="Cu" />
    <label dictRef="bo:name" xml:lang="en" value="Copper" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">63.546</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">62.9295975</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.7264</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="4">1.23578</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.90</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'cuprum' for Cypres</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.38</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">1.00 0.48 0.38</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2840</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1356.6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ancient</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">0</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d10 4s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Zn">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">30</scalar>
    <label dictRef="bo:symbol" value="Zn" />
    <label dictRef="bo:name" xml:lang="en" value="Zinc" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">65.38</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">63.9291422</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">9.3942</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.65</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">German 'zinking' for 'rough', because zinc ore is very rough</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.31</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.1</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.49 0.50 0.69</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1180</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">692.73</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1746</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Andreas Marggraf</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d10 4s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Ga">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">31</scalar>
    <label dictRef="bo:symbol" value="Ga" />
    <label dictRef="bo:name" xml:lang="en" value="Gallium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">69.723</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">68.9255736</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.9993</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="4">0.41</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.81</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">'Gallia' is an old name for France</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.26</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.1</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.76 0.56 0.56</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2478</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">302.92</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">fr</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1875</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">P. E. Lecoq de Boisbaudran</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d10 4s2 4p1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Ge">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">32</scalar>
    <label dictRef="bo:symbol" value="Ge" />
    <label dictRef="bo:name" xml:lang="en" value="Germanium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">72.64</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">73.9211778</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.8994</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="15">1.232712</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.01</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'germania' is an old name for Germany</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.22</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.1</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.40 0.56 0.56</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3107</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1211.5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1886</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">C. A. Winkler</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d10 4s2 4p2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Metalloids</scalar>
  </atom>
  <atom id="As">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">33</scalar>
    <label dictRef="bo:symbol" value="As" />
    <label dictRef="bo:name" xml:lang="en" value="Arsenic" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">74.92160</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">74.9215965</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">9.7886</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="8">0.814</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.18</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'arsenikos' for 'male' or 'bold'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.19</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.05</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.74 0.50 0.89</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">876</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1090</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ancient</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">0</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d10 4s2 4p3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Metalloids</scalar>
  </atom>
  <atom id="Se">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">34</scalar>
    <label dictRef="bo:symbol" value="Se" />
    <label dictRef="bo:name" xml:lang="en" value="Selenium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">78.96</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">79.9165213</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">9.7524</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="2">2.02067</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.55</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'selena' for 'moon'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.16</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.9</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">1.00 0.63 0.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">958</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">494</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">se</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1817</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">J. J. Berzelius</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d10 4s2 4p4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Non-Metal</scalar>
  </atom>
  <atom id="Br">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">35</scalar>
    <label dictRef="bo:symbol" value="Br" />
    <label dictRef="bo:name" xml:lang="en" value="Bromine" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">79.904</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">78.9183371</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">11.8138</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="20">3.3635880</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.96</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'bromos' for 'smells badly'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.14</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">1.9</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.65 0.16 0.16</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">331.85</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">265.95</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">fr</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1826</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">A. J. Balard</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d10 4s2 4p5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Halogen</scalar>
  </atom>
  <atom id="Kr">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">36</scalar>
    <label dictRef="bo:symbol" value="Kr" />
    <label dictRef="bo:name" xml:lang="en" value="Krypton" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">83.798</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">83.911507</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">13.9996</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">3.00</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'kryptos' for 'hidden'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.10</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.02</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.36 0.72 0.82</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">120.85</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">116</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1898</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">W. Ramsay;M. W. Travers</array>
    <scalar dataType="xsd:int" dictRef="bo:period">4</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Ar 3d10 4s2 4p6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Noblegas</scalar>
  </atom>
  <atom id="Rb">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">37</scalar>
    <label dictRef="bo:symbol" value="Rb" />
    <label dictRef="bo:name" xml:lang="en" value="Rubidium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">85.4678</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">84.91178974</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">4.1771</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="20">0.485916</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">0.82</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'rubidus' for 'dark red'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">2.11</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.9</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.44 0.18 0.69</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">961</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">312.63</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">s</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1861</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">Robert W. Bunsen;Gustav R. Kirchhoff</array>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 5s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Alkali_Earth</scalar>
  </atom>
  <atom id="Sr">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">38</scalar>
    <label dictRef="bo:symbol" value="Sr" />
    <label dictRef="bo:name" xml:lang="en" value="Strontium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">87.62</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">87.9056121</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.6949</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="6">0.05206</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">0.95</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the mineral Strontianit</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.92</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.55</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.00 1.00 0.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1655</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1042</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">s</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1790</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">H. B. Davy</array>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 5s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Alkaline_Earth</scalar>
  </atom>
  <atom id="Y">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">39</scalar>
    <label dictRef="bo:symbol" value="Y" />
    <label dictRef="bo:name" xml:lang="en" value="Yttrium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">88.90585</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">88.9058483</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.2173</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="12">0.307</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.22</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the small town of Ytterby near Stockholm in Sweden. Terbium. Ytterbium and Gadolinium are also named after this town.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.62</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.4</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.58 1.00 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3611</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1795</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">fi</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1794</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Johann Gadolin</array>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d1 5s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Zr">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">40</scalar>
    <label dictRef="bo:symbol" value="Zr" />
    <label dictRef="bo:name" xml:lang="en" value="Zirconium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">91.224</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">89.9047044</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.6339</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="14">0.426</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.33</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the mineral zircon</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.48</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.3</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.58 0.88 0.88</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">4682</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">2128</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1789</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Martin Heinrich Klaproth</array>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d2 5s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Nb">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">41</scalar>
    <label dictRef="bo:symbol" value="Nb" />
    <label dictRef="bo:name" xml:lang="en" value="Niobium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">92.90638</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">92.9063781</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.7589</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="25">0.893</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after Niobe, the daughter of the Greek god Tantalus.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.37</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.15</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.45 0.76 0.79</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">5015</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">2742</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1801</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Ch. Hatchett</array>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Mo">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">42</scalar>
    <label dictRef="bo:symbol" value="Mo" />
    <label dictRef="bo:name" xml:lang="en" value="Molybdenum" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">95.96</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">97.9054082</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.0924</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="2">0.7472</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.16</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">This name has Greek roots. It means 'like Platinum' - it was difficult to distinguish Molybdenum from Platinum.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.45</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.1</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.33 0.71 0.71</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">4912</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">2896</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">se</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1778</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">C. W. Scheele</array>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d5 5s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Tc">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">43</scalar>
    <label dictRef="bo:symbol" value="Tc" />
    <label dictRef="bo:name" xml:lang="en" value="Technetium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">98</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">97.907216</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.28</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="20">0.55</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.9</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'technetos' for artificial</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.56</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.05</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.23 0.62 0.62</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">4538</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">2477</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">it</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1937</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">C. Perrier;E. G. Segre</array>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d6 5s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Ru">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">44</scalar>
    <label dictRef="bo:symbol" value="Ru" />
    <label dictRef="bo:name" xml:lang="en" value="Ruthenium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">101.07</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">101.9043493</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.3605</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="25">1.04638</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Ruthenia is the old name of Russia</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.26</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.05</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.14 0.56 0.56</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">4425</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">2610</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ru</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1844</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">K. Klaus</array>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d7 5s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Rh">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">45</scalar>
    <label dictRef="bo:symbol" value="Rh" />
    <label dictRef="bo:name" xml:lang="en" value="Rhodium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">102.90550</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">102.905504</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.4589</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="20">1.14289</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.28</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'rhodeos' means 'red like a rose'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.35</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.04 0.49 0.55</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3970</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">2236</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1803</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">W. Wollaston</array>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d8 5s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Pd">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">46</scalar>
    <label dictRef="bo:symbol" value="Pd" />
    <label dictRef="bo:name" xml:lang="en" value="Palladium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">106.42</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">105.903486</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">8.3369</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="12">0.56214</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.20</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the asteroid Pallas</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.31</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.05</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.00 0.41 0.52</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3240</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1825</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1803</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d10</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Ag">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">47</scalar>
    <label dictRef="bo:symbol" value="Ag" />
    <label dictRef="bo:name" xml:lang="en" value="Silver" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">107.8682</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">106.905097</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.5762</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="2">1.30447</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.93</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'argentum' for silver</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.53</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.1</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.88 0.88 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2436</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1235.1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ancient</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">0</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d10 5s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Cd">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">48</scalar>
    <label dictRef="bo:symbol" value="Cd" />
    <label dictRef="bo:name" xml:lang="en" value="Cadmium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="8">112.411</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">113.9033585</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">8.9938</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.69</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'kadmia' ('Galmei' = Zinc carbonate)</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.48</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">1.00 0.85 0.56</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1040</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">594.26</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1817</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">F. Stromeyer</array>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d10 5s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="In">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">49</scalar>
    <label dictRef="bo:symbol" value="In" />
    <label dictRef="bo:name" xml:lang="en" value="Indium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">114.818</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">114.903878</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.7864</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="9">0.404</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.78</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after 'Indigo' because of its blue spectrum</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.44</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.65 0.46 0.45</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2350</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">429.78</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1863</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">F. Reich;H.T. Richter</array>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d10 5s2 5p1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Sn">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">50</scalar>
    <label dictRef="bo:symbol" value="Sn" />
    <label dictRef="bo:name" xml:lang="en" value="Tin" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="7">118.710</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">119.9021947</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.3439</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="15">1.112066</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.96</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'stannum' for tin</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.41</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.25</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.40 0.50 0.50</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2876</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">505.12</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ancient</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">0</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d10 5s2 5p2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Sb">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">51</scalar>
    <label dictRef="bo:symbol" value="Sb" />
    <label dictRef="bo:name" xml:lang="en" value="Antimony" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">121.760</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">120.9038157</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">8.6084</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="20">1.047401</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.05</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Arabic 'anthos ammonos' for 'blossom of the god Ammon'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.38</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.62 0.39 0.71</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1860</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">903.91</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ancient</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">0</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d10 5s2 5p3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Metalloids</scalar>
  </atom>
  <atom id="Te">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">52</scalar>
    <label dictRef="bo:symbol" value="Te" />
    <label dictRef="bo:name" xml:lang="en" value="Tellurium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">127.60</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">129.9062244</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">9.0096</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="7">1.970875</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'tellus' or 'telluris' for 'Planet Earth'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.35</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.1</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.83 0.48 0.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1261</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">722.72</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1782</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Franz Joseph Muller von Reichstein</array>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d10 5s2 5p4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Metalloids</scalar>
  </atom>
  <atom id="I">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">53</scalar>
    <label dictRef="bo:symbol" value="I" />
    <label dictRef="bo:name" xml:lang="en" value="Iodine" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">126.90447</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">126.904473</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">10.4513</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="10">3.059038</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.66</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'ioeides' for 'violet'.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.33</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.1</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.58 0.00 0.58</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">457.5</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">386.7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">fr</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1811</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Bernard Courtois</array>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d10 5s2 5p5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Halogen</scalar>
  </atom>
  <atom id="Xe">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">54</scalar>
    <label dictRef="bo:symbol" value="Xe" />
    <label dictRef="bo:name" xml:lang="en" value="Xenon" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="6">131.293</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">131.9041535</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">12.1298</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'xenos' for 'foreigner'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.30</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.16</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.26 0.62 0.69</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">165.1</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">161.39</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1898</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">W. Ramsay;M. W. Travers</array>
    <scalar dataType="xsd:int" dictRef="bo:period">5</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Kr 4d10 5s2 5p6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Noblegas</scalar>
  </atom>
  <atom id="Cs">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">55</scalar>
    <label dictRef="bo:symbol" value="Cs" />
    <label dictRef="bo:name" xml:lang="en" value="Caesium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">132.9054519</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">132.9054519</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">3.8939</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="25">0.471626</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">0.79</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'caesius' for 'heaven blue'.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">2.25</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">3</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.34 0.09 0.56</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">944</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">301.54</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">s</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1860</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">Robert Wilhelm Bunsen;Gustav Robert Kirchhoff</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 6s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Alkali_Earth</scalar>
  </atom>
  <atom id="Ba">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">56</scalar>
    <label dictRef="bo:symbol" value="Ba" />
    <label dictRef="bo:name" xml:lang="en" value="Barium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="7">137.327</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">137.9052472</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.2117</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="6">0.14462</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">0.89</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'barys' for 'heavy'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.98</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.7</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.00 0.79 0.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2078</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1002</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">s</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1808</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Humphry Bartholomew Davy</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Alkaline_Earth</scalar>
  </atom>
  <atom id="La">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">57</scalar>
    <label dictRef="bo:symbol" value="La" />
    <label dictRef="bo:name" xml:lang="en" value="Lanthanum" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="7">138.90547</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">138.9063533</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.5769</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="2">0.47</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.10</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'lanthanein' for 'hidden'. The Lanthanoids are also called the 'rare earth' elements.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.69</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.5</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.44 0.83 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3737</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1191</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">se</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1839</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">K. G. Mosander</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 5d1 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Ce">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">58</scalar>
    <label dictRef="bo:symbol" value="Ce" />
    <label dictRef="bo:name" xml:lang="en" value="Cerium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">140.116</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">139.9054387</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.5387</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0.5</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.12</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the planetoid Ceres</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.48</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">1.00 1.00 0.78</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3715</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1071</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1803</scalar>
    <array dataType="xsd:string" delimiter=";" size="3" dictRef="bo:discoverers">Jöns Jacob Berzelius;W. Hisinger;M. Klaproth</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f1 5d1 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Pr">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">59</scalar>
    <label dictRef="bo:symbol" value="Pr" />
    <label dictRef="bo:name" xml:lang="en" value="Praseodymium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">140.90765</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">140.9076528</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.473</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0.5</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.13</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'prasinos didymos' for 'green twin'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.47</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.85 1.00 0.78</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3785</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1204</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1885</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Carl F. Auer von Welsbach</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f3 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Nd">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">60</scalar>
    <label dictRef="bo:symbol" value="Nd" />
    <label dictRef="bo:name" xml:lang="en" value="Neodymium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">144.242</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">141.9077233</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.5250</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0.5</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.14</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'neos didymos' for 'new twin'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.45</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.78 1.00 0.78</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3347</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1294</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1885</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Carl F. Auer von Welsbach</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f4 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Pm">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">61</scalar>
    <label dictRef="bo:symbol" value="Pm" />
    <label dictRef="bo:name" xml:lang="en" value="Promethium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">145</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">144.912749</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.582</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0.5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the Greek Prometheus. Prometheus stole the fire from the gods and gave it to mankind.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.43</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.64 1.00 0.78</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3273</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1315</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1945</scalar>
    <array dataType="xsd:string" delimiter=";" size="3" dictRef="bo:discoverers">J. A. Marinsky;C. D. Coryell;L. E. Glendenin</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f5 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Sm">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">62</scalar>
    <label dictRef="bo:symbol" value="Sm" />
    <label dictRef="bo:name" xml:lang="en" value="Samarium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">150.36</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">151.9197324</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.6437</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0.5</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.17</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the mineral Samarskit</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.42</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.56 1.00 0.78</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2067</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1347</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1879</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">P. Lecoq de Boisbaudran</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f6 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Eu">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">63</scalar>
    <label dictRef="bo:symbol" value="Eu" />
    <label dictRef="bo:name" xml:lang="en" value="Europium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">151.964</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">152.9212303</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.6704</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0.5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after Europe</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.4</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.38 1.00 0.78</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1800</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1095</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1901</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">E. A. Demarcay</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f7 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Gd">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">64</scalar>
    <label dictRef="bo:symbol" value="Gd" />
    <label dictRef="bo:name" xml:lang="en" value="Gadolinium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">157.25</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">157.9241039</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.1498</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0.5</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.20</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the Finnish chemist Johan Gadolin</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.38</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.27 1.00 0.78</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3545</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1585</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1880</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Jean de Marignac</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f7 5d1 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Tb">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">65</scalar>
    <label dictRef="bo:symbol" value="Tb" />
    <label dictRef="bo:name" xml:lang="en" value="Terbium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">158.92535</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">158.9253468</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.8638</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0.5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the Swedish town of Ytterby</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.37</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.19 1.00 0.78</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3500</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1629</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1843</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">K. G. Mosander</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f9 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Dy">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">66</scalar>
    <label dictRef="bo:symbol" value="Dy" />
    <label dictRef="bo:name" xml:lang="en" value="Dysprosium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">162.500</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">163.9291748</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.9389</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0.5</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.22</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'dysprositor' for 'difficult to reach'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.35</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.12 1.00 0.78</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2840</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1685</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1886</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">F. E. Lecoq de Boisbaudran</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f10 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Ho">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">67</scalar>
    <label dictRef="bo:symbol" value="Ho" />
    <label dictRef="bo:name" xml:lang="en" value="Holmium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">164.93032</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">164.9303221</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.0215</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0.5</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.23</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'holmia' for the old name of Stockholm</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.33</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.00 1.00 0.61</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2968</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1747</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1878</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">J. L. Soret;P.T. Cleve</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f11 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Er">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">68</scalar>
    <label dictRef="bo:symbol" value="Er" />
    <label dictRef="bo:name" xml:lang="en" value="Erbium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">167.259</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">165.9302931</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.1077</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0.5</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.24</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named ofter the Swedish town of Ytterby. Terbium and Ytterbium are also named after this town.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.32</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.00 0.90 0.46</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3140</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1802</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1843</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">K. G. Mosander</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f12 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Tm">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">69</scalar>
    <label dictRef="bo:symbol" value="Tm" />
    <label dictRef="bo:name" xml:lang="en" value="Thulium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">168.93421</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">168.9342133</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.1843</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0.5</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.25</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the old name of Scandinavia, 'Thule'.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.3</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.00 0.83 0.32</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2223</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1818</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1879</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">P. T. Cleve</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f13 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Yb">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">70</scalar>
    <label dictRef="bo:symbol" value="Yb" />
    <label dictRef="bo:name" xml:lang="en" value="Ytterbium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="5">173.054</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">173.9388621</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.2542</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0.5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Like Terbium and Gadolinium, this is named after the Swedish town of Ytterby.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.28</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.00 0.75 0.22</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1469</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1092</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1878</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">J. Ch. Marignac</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Lu">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">71</scalar>
    <label dictRef="bo:symbol" value="Lu" />
    <label dictRef="bo:name" xml:lang="en" value="Lutetium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">174.9668</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">174.9407718</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.4259</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0.5</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.27</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the Roman name 'Lutetia' for Paris</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.60</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.27</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.00 0.67 0.14</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3668</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1936</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1907</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">Carl F. Auer von Welsbach;G. Urbain</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d1 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Rare_Earth</scalar>
  </atom>
  <atom id="Hf">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">72</scalar>
    <label dictRef="bo:symbol" value="Hf" />
    <label dictRef="bo:name" xml:lang="en" value="Hafnium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">178.49</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">179.94655</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.8251</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">'Hafnia' is the old name of Kopenhagen (Denmark)</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.50</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.25</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.30 0.76 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">4875</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">2504</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">dk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1923</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">D. Coster;G. Hevesy</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d2 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Ta">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">73</scalar>
    <label dictRef="bo:symbol" value="Ta" />
    <label dictRef="bo:name" xml:lang="en" value="Tantalum" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">180.94788</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">180.9479958</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.5496</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="12">0.322</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the Greek myth of Tantalos</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.38</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.30 0.65 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">5730</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">3293</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">se</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1802</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">A. Ekeberg</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d3 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="W">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">74</scalar>
    <label dictRef="bo:symbol" value="W" />
    <label dictRef="bo:name" xml:lang="en" value="Tungsten" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">183.84</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">183.9509312</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.8640</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="8">0.815</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.36</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">'tung sten' means 'heavy stone' in Swedish. The old name (and thus the symbol 'W') was Wolfram, named after a mineral.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.46</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.1</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.13 0.58 0.84</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">5825</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">3695</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">es</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1783</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">C. W. Scheele</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d4 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Re">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">75</scalar>
    <label dictRef="bo:symbol" value="Re" />
    <label dictRef="bo:name" xml:lang="en" value="Rhenium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">186.207</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">186.9557531</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.8335</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="15">0.15</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.9</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the German river Rhine (latin 'Rhenium').</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.59</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.05</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.15 0.49 0.67</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">5870</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">3455</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1925</scalar>
    <array dataType="xsd:string" delimiter=";" size="3" dictRef="bo:discoverers">Walter Noddack;Ida Tacke-Noddack;Otto Berg</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d5 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Os">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">76</scalar>
    <label dictRef="bo:symbol" value="Os" />
    <label dictRef="bo:name" xml:lang="en" value="Osmium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">190.23</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">191.9614807</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">8.4382</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="12">1.07780</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek for 'smell'. Its oxides smell strongly like radishes.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.28</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.15 0.40 0.59</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">5300</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">3300</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1804</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">S. Tennant</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d6 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Ir">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">77</scalar>
    <label dictRef="bo:symbol" value="Ir" />
    <label dictRef="bo:name" xml:lang="en" value="Iridium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">192.217</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">192.9629264</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">8.9670</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="15">1.56436</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.20</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'iris' for 'rainbow'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.37</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.09 0.33 0.53</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">4700</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">2720</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1804</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">S. Tennant</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d7 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Pt">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">78</scalar>
    <label dictRef="bo:symbol" value="Pt" />
    <label dictRef="bo:name" xml:lang="en" value="Platinum" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="9">195.084</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">194.9647911</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">8.9588</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="5">2.12510</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.28</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Spanish 'platina' means 'small silver'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.28</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.05</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.96 0.93 0.82</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">4100</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">2042.1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1735</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">A. de Ulloa</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d9 6s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Au">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">79</scalar>
    <label dictRef="bo:symbol" value="Au" />
    <label dictRef="bo:name" xml:lang="en" value="Gold" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="4">196.966569</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">196.9665687</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">9.2255</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="3">2.30861</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.54</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'aurum'. Named after Aurora, the goddess of sunrise</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.44</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.1</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.80 0.82 0.12</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3130</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1337.58</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ancient</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">0</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d10 6s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Hg">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">80</scalar>
    <label dictRef="bo:symbol" value="Hg" />
    <label dictRef="bo:name" xml:lang="en" value="Mercury" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">200.59</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">201.970643</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">10.4375</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.00</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Graeco-Latin 'hydrargyrum' for 'liquid silver'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.49</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.05</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.71 0.71 0.76</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">629.88</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">234.31</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ancient</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">0</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d10 6s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Tl">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">81</scalar>
    <label dictRef="bo:symbol" value="Tl" />
    <label dictRef="bo:name" xml:lang="en" value="Thallium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">204.3833</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">204.9744275</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.1082</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="13">0.377</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.62</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'tallos' for 'young twig'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.48</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.65 0.33 0.30</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1746</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">577</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">uk</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1861</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">W. Crookes</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d10 6s2 6p1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Pb">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">82</scalar>
    <label dictRef="bo:symbol" value="Pb" />
    <label dictRef="bo:name" xml:lang="en" value="Lead" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">207.2</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">207.9766521</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.4167</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="8">0.364</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.33</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'plumbum' for Lead</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.47</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.3</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.34 0.35 0.38</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2023</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">600.65</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ancient</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">0</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d10 6s2 6p2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Bi">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">83</scalar>
    <label dictRef="bo:symbol" value="Bi" />
    <label dictRef="bo:name" xml:lang="en" value="Bismuth" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="1">208.98040</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">208.9803987</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">7.2855</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="25">0.942363</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.02</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">The old name of Bismuth is 'Wismut', which stood for 'white mass'.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.46</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.3</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.62 0.31 0.71</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1837</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">544.59</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ancient</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">0</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d10 6s2 6p3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Po">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">84</scalar>
    <label dictRef="bo:symbol" value="Po" />
    <label dictRef="bo:name" xml:lang="en" value="Polonium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">209</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">208.9824304</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">8.414</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="3">1.9</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.0</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after Poland to honor Marie Curie</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.67 0.36 0.00</array>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">527</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">fr</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1898</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">M. Sklodowska-Curie;P. Curie</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d10 6s2 6p4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Metalloids</scalar>
  </atom>
  <atom id="At">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">85</scalar>
    <label dictRef="bo:symbol" value="At" />
    <label dictRef="bo:name" xml:lang="en" value="Astatine" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">210</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">209.987148</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">0</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev" errorValue="2">2.8</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">2.2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'astator' for 'changing'</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.46 0.31 0.27</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">610</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">575</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">us</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1940</scalar>
    <array dataType="xsd:string" delimiter=";" size="3" dictRef="bo:discoverers">D. R. Corson;K. R. McKenzie;E. Segre</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d10 6s2 6p5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Halogen</scalar>
  </atom>
  <atom id="Rn">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">86</scalar>
    <label dictRef="bo:symbol" value="Rn" />
    <label dictRef="bo:name" xml:lang="en" value="Radon" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">222</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">222.0175777</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">10.7485</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronAffinity" units="units:ev">0</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after Radium. It ends with 'on' to make it clear that it is a noble gas.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusCovalent" units="units:ang">1.45</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.26 0.51 0.59</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">211.4</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">202</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1898</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">E. Dorn</array>
    <scalar dataType="xsd:int" dictRef="bo:period">6</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Xe 4f14 5d10 6s2 6p6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Noblegas</scalar>
  </atom>
  <atom id="Fr">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">87</scalar>
    <label dictRef="bo:symbol" value="Fr" />
    <label dictRef="bo:name" xml:lang="en" value="Francium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">223</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">223.0197359</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">4.0727</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">0.7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after France to honor Marguerite Perey</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.26 0.00 0.40</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">950</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">300</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">s</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">fr</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1939</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">M. Perey</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 7s1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Alkali_Earth</scalar>
  </atom>
  <atom id="Ra">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">88</scalar>
    <label dictRef="bo:symbol" value="Ra" />
    <label dictRef="bo:name" xml:lang="en" value="Radium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">226</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">226.0254098</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.2784</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">0.9</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'radius' for 'beam', as it is radioactive</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.00 0.49 0.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1413</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">973</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">s</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">fr</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1898</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">M. Sklodowska-Curie;P. Curie</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Alkaline_Earth</scalar>
  </atom>
  <atom id="Ac">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">89</scalar>
    <label dictRef="bo:symbol" value="Ac" />
    <label dictRef="bo:name" xml:lang="en" value="Actinium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">227</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">227.0277521</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.17</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'aktis' for 'beam' - actinium is radioactive</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.44 0.67 0.98</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3470</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1324</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">fr</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1899</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">A. L. Debierne</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 6d1 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Th">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">90</scalar>
    <label dictRef="bo:symbol" value="Th" />
    <label dictRef="bo:name" xml:lang="en" value="Thorium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">232.03806</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">232.0380553</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.3067</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the German god of thunder: Thor</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.4</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.00 0.73 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">5060</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">2028</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1828</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">J. J. Berzelius</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 6d2 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Pa">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">91</scalar>
    <label dictRef="bo:symbol" value="Pa" />
    <label dictRef="bo:name" xml:lang="en" value="Protactinium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="2">231.03588</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">231.035884</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.89</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'protos' for 'ancester'. Protactinium is before Actinium in the periodic table.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.00 0.63 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">4300</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1845</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1917</scalar>
    <array dataType="xsd:string" delimiter=";" size="3" dictRef="bo:discoverers">O. Hahn;L. Meitern;W. Wollaston</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f2 6d1 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="U">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">92</scalar>
    <label dictRef="bo:symbol" value="U" />
    <label dictRef="bo:name" xml:lang="en" value="Uranium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass" errorValue="3">238.02891</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">238.0507882</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.1941</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.38</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Greek 'ouranos' for 'heaven'. Named after the planet Uranus.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2.3</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.00 0.56 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">4407</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1408</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1789</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">M. M. Klaproth</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f3 6d1 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Np">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">93</scalar>
    <label dictRef="bo:symbol" value="Np" />
    <label dictRef="bo:name" xml:lang="en" value="Neptunium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">237</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">237.0481734</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.2657</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.36</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the planet Neptune.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.00 0.50 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">4175</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">912</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1940</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">E. M. McMillan;P. Aberson</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f4 6d1 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Pu">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">94</scalar>
    <label dictRef="bo:symbol" value="Pu" />
    <label dictRef="bo:name" xml:lang="en" value="Plutonium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">244</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">244.064204</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.0260</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.28</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the planet Pluto.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.00 0.42 1.00</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3505</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">913</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1940</scalar>
    <array dataType="xsd:string" delimiter=";" size="4" dictRef="bo:discoverers">Glenn T. Seaborg;E. M. McMillan;J. W. Kennedy;A.C. Wahl</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f6 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Am">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">95</scalar>
    <label dictRef="bo:symbol" value="Am" />
    <label dictRef="bo:name" xml:lang="en" value="Americium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">243</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">243.0613811</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.9738</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after America.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.33 0.36 0.95</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">2880</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1449</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1945</scalar>
    <array dataType="xsd:string" delimiter=";" size="4" dictRef="bo:discoverers">Glenn T. Seaborg;L. O. Morgan;R. A. James;A. Ghiors</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f7 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Cm">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">96</scalar>
    <label dictRef="bo:symbol" value="Cm" />
    <label dictRef="bo:name" xml:lang="en" value="Curium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">247</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">247.070354</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">5.9914</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after Marie Curie.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.47 0.36 0.89</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">3383</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1620</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1944</scalar>
    <array dataType="xsd:string" delimiter=";" size="3" dictRef="bo:discoverers">Glenn T. Seaborg;R. A. James;A. Ghiors</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f7 6d1 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Bk">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">97</scalar>
    <label dictRef="bo:symbol" value="Bk" />
    <label dictRef="bo:name" xml:lang="en" value="Berkelium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">247</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">247.070307</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.1979</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the town Berkeley where it was discovered.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.54 0.31 0.89</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">983</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1258</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1949</scalar>
    <array dataType="xsd:string" delimiter=";" size="3" dictRef="bo:discoverers">Glenn T. Seaborg;A. Ghiors;S. G. Thompson</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f9 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Cf">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">98</scalar>
    <label dictRef="bo:symbol" value="Cf" />
    <label dictRef="bo:name" xml:lang="en" value="Californium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">251</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">251.079587</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.2817</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the US-State of California.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.63 0.21 0.83</array>
    <scalar dataType="xsd:float" dictRef="bo:boilingpoint"  units="siUnits:kelvin">1173</scalar>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1172</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1950</scalar>
    <array dataType="xsd:string" delimiter=";" size="3" dictRef="bo:discoverers">Glenn T. Seaborg;A. Ghiors;S. G. Thompson</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f10 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Es">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">99</scalar>
    <label dictRef="bo:symbol" value="Es" />
    <label dictRef="bo:name" xml:lang="en" value="Einsteinium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">252</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">252.08298</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.42</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the scientist Albert Einstein.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.70 0.12 0.83</array>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1130</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1952</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">Glenn T. Seaborg;et al.</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f11 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Fm">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">100</scalar>
    <label dictRef="bo:symbol" value="Fm" />
    <label dictRef="bo:name" xml:lang="en" value="Fermium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">257</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">257.095105</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.50</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the scientist Enrico Fermi.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.70 0.12 0.73</array>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1800</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1953</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">Glenn T. Seaborg;et al.</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f12 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Md">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">101</scalar>
    <label dictRef="bo:symbol" value="Md" />
    <label dictRef="bo:name" xml:lang="en" value="Mendelevium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">258</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">258.098431</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.58</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the scientist D.I. Mendeleev.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.70 0.05 0.65</array>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1100</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1955</scalar>
    <array dataType="xsd:string" delimiter=";" size="5" dictRef="bo:discoverers">Glenn T. Seaborg;Albert Ghiorso;Bernard Harvey;Gregory Choppin;Stanley G. Thompson</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f13 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="No">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">102</scalar>
    <label dictRef="bo:symbol" value="No" />
    <label dictRef="bo:name" xml:lang="en" value="Nobelium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">259</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">259.10103</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.65</scalar>
    <scalar dataType="xsd:float" dictRef="bo:electronegativityPauling" units="boUnits:paulingScaleUnit">1.3</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the scientist Alfred Nobel.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.74 0.05 0.53</array>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1100</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1958</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f14 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Lr">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">103</scalar>
    <label dictRef="bo:symbol" value="Lr" />
    <label dictRef="bo:name" xml:lang="en" value="Lawrencium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">262</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">262.10963</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">4.9</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the scientist Ernest Orlando Lawrence.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.78 0.00 0.40</array>
    <scalar dataType="xsd:float" dictRef="bo:meltingpoint"  units="siUnits:kelvin">1900</scalar>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">f</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1961</scalar>
    <array dataType="xsd:string" delimiter=";" size="4" dictRef="bo:discoverers">Albert Ghiorso;Torbjorn Sikkeland;Almon Larsh;Robert M. Latimer</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f14 7s2 7p1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Rf">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">104</scalar>
    <label dictRef="bo:symbol" value="Rf" />
    <label dictRef="bo:name" xml:lang="en" value="Rutherfordium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">267</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">261.10877</scalar>
    <scalar dataType="xsd:float" dictRef="bo:ionization" units="units:ev">6.0</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the scientist Ernest Rutherford</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.80 0.00 0.35</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" delimiter="," size="2" dictRef="bo:discoveryCountry">ru,us</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1964</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">4</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f14 6d2 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Db">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">105</scalar>
    <label dictRef="bo:symbol" value="Db" />
    <label dictRef="bo:name" xml:lang="en" value="Dubnium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">268</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">262.11408</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the science-town Dubna in Russia</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.82 0.00 0.31</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" delimiter="," size="2" dictRef="bo:discoveryCountry">ru,us</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1967</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">5</scalar>
    <scalar dataType="xsd:string" dictRef="bo:electronicConfiguration">Rn 5f14 6d3 7s2</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Sg">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">106</scalar>
    <label dictRef="bo:symbol" value="Sg" />
    <label dictRef="bo:name" xml:lang="en" value="Seaborgium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">271</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">263.11832</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the scientist G. Theodore Seaborg.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.85 0.00 0.27</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" delimiter="," size="2" dictRef="bo:discoveryCountry">ru,us</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1974</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">Albert Ghiorso;et al.</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">6</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Bh">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">107</scalar>
    <label dictRef="bo:symbol" value="Bh" />
    <label dictRef="bo:name" xml:lang="en" value="Bohrium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">272</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">264.1246</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the scientist Niels Bohr.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.88 0.00 0.22</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ru</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1981</scalar>
    <array dataType="xsd:string" delimiter=";" size="3" dictRef="bo:discoverers">Peter Armbruster;Gottfried Münzenber;et al.</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Hs">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">108</scalar>
    <label dictRef="bo:symbol" value="Hs" />
    <label dictRef="bo:name" xml:lang="en" value="Hassium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">270</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">265.13009</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Latin 'hassia' for the German county Hessen. In Hessen a lot elements have been discovered.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.90 0.00 0.18</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1984</scalar>
    <array dataType="xsd:string" delimiter=";" size="3" dictRef="bo:discoverers">Peter Armbruster;Gottfried Münzenber;et al.</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Mt">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">109</scalar>
    <label dictRef="bo:symbol" value="Mt" />
    <label dictRef="bo:name" xml:lang="en" value="Meitnerium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">276</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">268.13873</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the scientist Lise Meitner.</scalar>
    <scalar dataType="xsd:float" dictRef="bo:radiusVDW" units="units:ang">2</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.91 0.00 0.15</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1982</scalar>
    <array dataType="xsd:string" delimiter=";" size="3" dictRef="bo:discoverers">Peter Armbruster;Gottfried Münzenber;et al.</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Ds">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">110</scalar>
    <label dictRef="bo:symbol" value="Ds" />
    <label dictRef="bo:name" xml:lang="en" value="Darmstadtium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">281</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">271.14606</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after the German city Darmstadt where many elements have been discovered.</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.92 0.00 0.14</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1994</scalar>
    <array dataType="xsd:string" delimiter=";" size="8" dictRef="bo:discoverers">S. Hofmann;V. Ninov;F. P. Hessberger;P. Armbruster;H. Folger;G. Münzenberg;H. J. Schött;et al.</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">8</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Rg">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">111</scalar>
    <label dictRef="bo:symbol" value="Rg" />
    <label dictRef="bo:name" xml:lang="en" value="Roentgenium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">280</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">272.15362</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Named after Wilhelm Conrad Röntgen.</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.93 0.00 0.13</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1994</scalar>
    <array dataType="xsd:string" delimiter=";" size="7" dictRef="bo:discoverers">S. Hofmann;V. Ninov;F. P. Hessberger;P. Armbruster;H. Folger;G. Münzenberg;et al.</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:int" dictRef="bo:group">1</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Cn">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">112</scalar>
    <label dictRef="bo:symbol" value="Cn" />
    <label dictRef="bo:name" xml:lang="en" value="Copernicium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">285</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">285.17411</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Historically known as eka-mercury. Ununbium is a temporary IUPAC systematic element name.</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.94 0.00 0.12</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">d</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">de</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1996</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">First created at the Gesellschaft für Schwerionenforschung</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Transition</scalar>
  </atom>
  <atom id="Uut">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">113</scalar>
    <label dictRef="bo:symbol" value="Uut" />
    <label dictRef="bo:name" xml:lang="en" value="Ununtrium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">284</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">284.17808</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Historically known as eka-thallium. Ununtrium is a temporary IUPAC systematic element name.</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.95 0.00 0.11</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" delimiter="," size="2" dictRef="bo:discoveryCountry">ru,us</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">2003</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">Russian scientists at Dubna (JINR);American scientists at the Lawrence Livermore National Laboratory.</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Uuq">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">114</scalar>
    <label dictRef="bo:symbol" value="Uuq" />
    <label dictRef="bo:name" xml:lang="en" value="Ununquadium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">289</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">289.18728</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Historically known as eka-lead. Ununquadium is a temporary IUPAC systematic element name.</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.96 0.00 0.10</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" delimiter="," size="2" dictRef="bo:discoveryCountry">ru,us</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">1998</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Joint Institute for Nuclear Research</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Uup">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">115</scalar>
    <label dictRef="bo:symbol" value="Uup" />
    <label dictRef="bo:name" xml:lang="en" value="Ununpentium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">288</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">288.19249</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Historically known as eka-bismuth. Ununpentium is a temporary IUPAC systematic element name.</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.97 0.00 0.09</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" delimiter="," size="2" dictRef="bo:discoveryCountry">ru,us</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">2004</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">Russian scientists at Dubna (JINR);American scientists at the Lawrence Livermore National Laboratory.</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Uuh">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">116</scalar>
    <label dictRef="bo:symbol" value="Uuh" />
    <label dictRef="bo:name" xml:lang="en" value="Ununhexium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">293</scalar>
    <scalar dataType="xsd:float" dictRef="bo:exactMass" units="units:atmass">292.19979</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Historically known as eka-polonium. Ununhexium is a temporary IUPAC systematic element name.</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.98 0.00 0.08</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" dictRef="bo:discoveryCountry">ru</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">2000</scalar>
    <array dataType="xsd:string" dictRef="bo:discoverers">Joint Institute for Nuclear Research</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Other_Metal</scalar>
  </atom>
  <atom id="Uus">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">117</scalar>
    <label dictRef="bo:symbol" value="Uus" />
    <label dictRef="bo:name" xml:lang="en" value="Ununseptium" />
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Temporary symbol and name. Can also be referred to as eka-astatine.</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">0.99 0.00 0.07</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">0</scalar>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Halogen</scalar>
  </atom>
  <atom id="Uuo">
    <scalar dataType="xsd:Integer" dictRef="bo:atomicNumber">118</scalar>
    <label dictRef="bo:symbol" value="Uuo" />
    <label dictRef="bo:name" xml:lang="en" value="Ununoctium" />
    <scalar dataType="xsd:float" dictRef="bo:mass" units="units:atmass">294</scalar>
    <scalar dataType="xsd:string" dictRef="bo:nameOrigin" xml:lang="en">Historically known as eka-radon, eka-emanation before 1960. Ununoctium is a temporary IUPAC systematic element name.</scalar>
    <array title="color" dictRef="bo:elementColor" size="3" dataType="xsd:float">1.00 0.00 0.06</array>
    <scalar dataType="xsd:string" dictRef="bo:periodTableBlock">p</scalar>
    <array dataType="xsd:string" delimiter="," size="2" dictRef="bo:discoveryCountry">ru,us</array>
    <scalar dataType="xsd:date" dictRef="bo:discoveryDate">2002</scalar>
    <array dataType="xsd:string" delimiter=";" size="2" dictRef="bo:discoverers">Russian scientists at Dubna (JINR);American scientists at the Lawrence Livermore National Laboratory.</array>
    <scalar dataType="xsd:int" dictRef="bo:period">7</scalar>
    <scalar dataType="xsd:string" dictRef="bo:family">Noblegas</scalar>
  </atom>
</list>
